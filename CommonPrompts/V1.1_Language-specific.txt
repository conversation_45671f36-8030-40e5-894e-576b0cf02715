Language-Specific Guidelines (Java + Spring Boot)
==================================================

- Use Java 21 with Maven-based Spring Boot application.
- Use Flyway for schema versioning.
- Do not use Lombok. All boilerplate code (getters, setters, constructors, etc.) must be written explicitly in Java.
- Do not use Mockito. Use only built-in Spring Boot testing utilities and JUnit 5 for all tests (unit, integration, regression).
- Service controllers must not include health check endpoints.
- Include a dedicated `HealthController` class with endpoints: `/health` ,`/health/ready`, `/health/live`. This should be separate from all business/service controllers.
- The `HealthController.java` must follow the structure:
  `/v1/{capability-name}/{mode}/health`
  - `capability-name` should be lowercase and kebab-case.
  - `mode` should be either `original` or `mock`.

- Java-specific Project Structure:
/original-code/
 ├── src/
 │ ├── main/
 │ │ ├── java/ # Java source files
 │ │ └── resources/ # Configuration files
 │ └── test/ # Test files
 ├── pom.xml # Maven configuration
 └── swagger/
 └── {capability-name}-original-openapi.yaml
/mock-code/
 ├── src/
 │ ├── main/
 │ │ ├── java/
 │ │ └── resources/
 │ └── test/
 ├── pom.xml
 └── swagger/
 └── {capability-name}-mock-openapi.yaml
- Follow layered architecture:
  - Controllers → Services → Repositories
- Include:
  - Entity classes with explicit getters/setters
  - DTOs and Enums
- Include test coverage:
  - Unit tests for services and utilities
  - Integration tests with H2 or Testcontainers
  - Regression tests for key flows