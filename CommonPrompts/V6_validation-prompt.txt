Capability - Testing and Deployment Guide
-----------------------------------------

Objective:
Ensure the complete Spring Boot application — including both the original (main) and mock services — is tested end-to-end for correctness, integration, configuration, and data flow.

Test Strategy
-------------

Testing Rules:

- Do not auto-generate dummy tests.
- Manually verify both mock and main services using unit, integration, and regression tests as per the business and technical prompts (V1–V6).
- Ensure 100% prompt coverage: each prompt must be reflected in the code and verified through tests or manual validation.
- No hardcoded values: All config values must be externalized via application.yml or environment variables.

Test Execution
--------------

Run the following commands to execute all unit, integration, and regression tests:

# Test the Mock Service
cd /path/to/mock-code
mvn clean test

# Test the Main (Original) Service
cd /path/to/original-code
mvn clean test

If any tests fail, investigate by:
- Reviewing stack traces and logs
- Validating mock response formats (for tests mocking third-party APIs)
- Checking test data patterns against business rules
- Verifying DB connection, Flyway migration logs, and schema alignment
- Running mvn dependency:tree for classpath conflicts

E2E Functional Test Flow
------------------------

Once both services are built and tests pass:

1. Start PostgreSQL (ensure DB is available and Flyway has migrated schema):

sudo service postgresql start
psql -U postgres -c '\l'

# To create database if missing:
psql -U postgres -c 'CREATE DATABASE <db-name>;'

2. Start Mock Service

cd /path/to/mock-code
mvn spring-boot:run
# Default expected port: 8083

3. Start Main Service

cd /path/to/original-code
mvn spring-boot:run
# Default expected port: 8080

If startup fails:
- Run: lsof -i :8080 or lsof -i :8083 and kill conflicting process
- Check if DB is reachable and port/credentials match in application.yml
- Verify that application-{profile}.yml is active for the right environment

API Testing via CURL / Postman
-------------------------------

Health Checks

curl http://localhost:8080/api/<capability-name>/v1/original/health
curl http://localhost:8083/api/<capability-name>/internal/v1/mock/health

Sample Business API Call

curl -X POST http://localhost:8080/api/<capability-name>/v1/[original|mock]/endpoint \
  -H "Content-Type: application/json" \
  -H "X-Trace-Id: test-trace-id" \
  -d '{"sampleKey":"sampleValue"}'

Verify:
- Status code is 2xx or as defined in the OpenAPI spec
- Response body structure matches DTOs
- Errors follow standardized error structure
- Sensitive fields are redacted or hashed
- Headers like X-Trace-Id are preserved in response logs

Common Issues & Fixes
----------------------

| Issue                     | Symptom                                   | Fix                                       |
|--------------------------|--------------------------------------------|--------------------------------------------|
| Port Conflict            | "Address already in use"                  | lsof -i :<port> → kill -9 <PID>           |
| Mock unreachable         | Main service errors on HTTP call          | Check mock base URL in application.yml    |
| DB Not Found             | Flyway errors or connection refused       | Start DB, check schema, verify DB name    |
| Missing Class / Bean     | Compilation or runtime failure            | Run mvn dependency:tree, check configs    |
| Test Fails in CI         | Data mismatch or env differences          | Use env-specific data via application-*.yml |

Contract & Coverage Validation
-------------------------------

Swagger/OpenAPI Validation:
- Load swagger/{capability-name}-original-openapi.yaml and -mock-openapi.yaml in Swagger UI
- Verify endpoint paths, input/output schema, headers, and examples match generated application behavior

Code Coverage:
- Use mvn test to measure test coverage
- Target: 90%+ coverage for services and utilities

Final Checklist 
-------------------

- [ ] All mvn test executions pass for both services
- [ ] Both services boot correctly with no stack traces or port errors
- [ ] Manual endpoint tests return expected results
- [ ] Health checks (/health/live, /health/ready) return HTTP 200
- [ ] Logs redact/mask PII and carry trace headers
- [ ] Retry logic (if implemented) tested against mock failure scenarios
- [ ] Swagger specs match generated endpoints


