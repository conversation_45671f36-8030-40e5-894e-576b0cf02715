# Augment Rules

Augment Rules are structured guidelines and workflows that define how AI agents should generate, validate, and deploy microservices for capabilities. These rules ensure consistency, quality, and maintainability across all generated code and services.

## Quick Start

### Setup
1. Move the `rules` directory to `.augment` directory in the root of your project:
   ```bash
   mv augment-rules/rules .augment/
   ```

2. Prepare your Business Requirements Document (BRD) in Markdown format (.md)
   Go to file > Download > Markdown (.md) if using Google Docs

### Generate a Capability
Use the capability implementation workflow with your BRD to generate a complete microservice:

For example try this command in augment agent:
```
Follow the @capability-implementation-workflow and implement the capability from this BRD: @BRD.md
```
