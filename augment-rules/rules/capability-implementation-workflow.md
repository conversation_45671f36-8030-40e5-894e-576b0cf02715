---
type: "manual"
description: "Workflow for generating complete microservice for a capability from Business Requirements Documents"
---

# Capability Implementation Stages

## Stage 1: Code Generation: 
Generate core application code and comprehensive tests following coding standards
## Rules:
 - General Coding Guidelines (`general-coding-guidelines`)
 - Java + Spring Boot Guidelines (`java-spring-boot-guidelines`)

## Stage 2: Post-Generation Validation: 
Ensure audit-ready logging, error management, and business scenario coverage
## Rules:
 - Post-Generation Guidelines (`post-generation`)

## Stage 3: External Mock Generation: 
Generate mock implementations for external/3rd party service dependencies
## Rules:
 - External Mock Generation Guidelines (`external-mock-generation`)

## Stage 4: Testing & Deployment: 
Manual testing, debugging, and deployment validation
## Rules:
 - Testing & Deployment Guidelines (`testing-deployment`)

## Stage 5: OpenAPI Specification Generation: 
Generate complete and valid OpenAPI 3.0+ specifications
## Rules:
 - OpenAPI Specification Generation Guidelines (`openapi-spec-generation`)

# Execution Prompt

Generate a complete microservice for a capability from the attached BRD following this comprehensive 5-stage workflow:

### Pre-Execution Setup
- Analyze the BRD thoroughly to understand business requirements and technical specifications
- Identify all external integrations, data models, and API endpoints required
- Confirm database requirements and connection parameters
- Validate that all prerequisite services and dependencies are available

### Stage Execution
1. **Sequential Execution**: Execute stages in strict order (1→2→3→4→5) - never skip or reorder stages
2. **Stage Rules Compliance**: Each stage MUST strictly adhere to ALL relevant rules without exception - Review and follow all applicable rules thoroughly before and during stage execution to ensure complete compliance.
2. **Stage Validation**: Validate the completeness and correctness of each stage before proceeding to the next stage
3. **Continuous Testing**: Run comprehensive test suites after each stage completion
4. **Issue Resolution**: Debug and resolve ALL issues immediately when detected - do not proceed with unresolved problems
5. **Progress Tracking**: Provide detailed status updates and explicit confirmation when each stage is complete
6. **Incremental Commits**: Make meaningful commits after completing each stage with descriptive messages

### Quality Assurance
- **Code Standards**: Strictly follow specified guidelines, rules
- **Test Coverage**: Ensure comprehensive unit, integration, and regression test coverage
- **Service Independence**: Verify both mock and main services can run independently without conflicts
- **Port Management**: Confirm no port conflicts exist between services
- **Health Validation**: Verify all health endpoints return 200 OK status
- **API Functionality**: Test all REST endpoints manually and through automated tests
- **Error Handling**: Implement robust error management with standardized response structures
- **Logging Standards**: Implement audit-ready logging with PII masking and traceability

### Final Delivery
- **End-to-End Testing**: Complete full workflow testing from API calls to database operations
- **Service Startup**: Confirm both services start without errors on designated ports
- **OpenAPI Documentation**: Generate and validate complete OpenAPI 3.0+ specifications
- **Swagger UI**: Ensure Swagger UI is accessible and all endpoints are testable
- **Database Connectivity**: Verify database connections and data persistence
- **Integration Testing**: Test all external service integrations and fallback mechanisms

### Failure Recovery
- If any stage fails validation, immediately halt progression and resolve issues
- Document all debugging steps and resolution approaches
- Re-run complete test suites after issue resolution
- Only proceed to next stage after explicit confirmation of successful completion

