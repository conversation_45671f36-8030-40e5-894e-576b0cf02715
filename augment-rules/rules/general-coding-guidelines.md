---
type: "always_apply"
description: "Core guidelines for developing microservices to be followed for all capability implementations to ensure consistency, security, and maintainability."
---

# General Coding Guidelines

## Configuration Management
- Externalize all configuration values (no hardcoded values anywhere)
- Use configuration files (`application.yml`, `.env`, `config.json`) or environment variables
- Include all configurable items:
  - API URLs and endpoints
  - Ports and timeouts
  - Feature flags and constants
  - Environment-specific settings
  - Secrets and credentials
- Access configuration through dependency injection or environment APIs

## Environment and Deployment
- Make server port configurable via environment variable (e.g., `SERVER_PORT`)
- Configure CORS properly:
  - Use `WebMvcConfigurer` or `CorsConfigurationSource` bean for API requests
  - Configure CORS in `OpenApiConfig.java` for Swagger UI access
  - Allow all origins in development, use configurable allowlist in production

## Routing and API Design
- Follow context path patterns:
  - Original service: `/api/{capability-name}`
  - Mock service: `/api/{capability-name}/internal`
- Implement required health endpoints on all services:
  - `/health/ready`
  - `/health/live`
- Use structured health endpoint URIs: `/v1/{capability-name}/{mode}/health`

## Security and Data Privacy
- Protect sensitive data:
  - Avoid storing unnecessary PII
  - Mask or redact sensitive fields in logs (Aadhaar, PAN, etc.)
  - Use encryption or hashing for sensitive data
- Implement proper validation:
  - Use constraints like regex patterns, custom rules
  - Handle errors with standardized exception handling
  - Ensure code is audit-ready

## Logging Standards
- Use structured logging with consistent format across all services
- Include mandatory fields in all log entries:
  - Timestamp (ISO 8601 format)
  - Log level (ERROR, WARN, INFO, DEBUG)
  - Transaction/Reference ID for traceability
  - Service name and version
- Implement PII masking for sensitive data:
  - Mask Aadhaar numbers: `****-****-1234`
  - Mask PAN numbers: `*****1234A`
  - Redact personal names and addresses
- Log request/response payloads with sensitive fields masked
- Use correlation IDs to trace requests across service boundaries

## Exception Handling Structure
- Create custom exception hierarchy extending from base business exception
- Use standardized error response format:
  ```json
  {
    "errorCode": "BUSINESS_ERROR_CODE",
    "message": "Human-readable error message",
    "timestamp": "2024-01-01T10:00:00Z",
    "traceId": "unique-trace-identifier",
    "details": {}
  }
  ```
- Log full stack traces with masked sensitive information
- Define specific error codes for different business scenarios
- Include retry-able vs non-retry-able error classification

## Service Requirements
- Make all services independently runnable
- Integrate with mock third-party services during development and testing
- Define interfaces using OpenAPI specifications in `swagger/{capability-name}-[original|mock]-openapi.yaml`
