---
type: "agent_requested"
description: "External Mock Generation Guidelines: Guidelines for generating mock implementations for external/3rd party service dependencies"
---

# External Mock Generation Guidelines

> **Cross-References**: Builds upon `general-coding-guidelines.md` and `java-spring-boot-guidelines.md`. Prepares for validation in `testing-deployment.md`. Supports OpenAPI generation in `openapi-spec-generation.md`.

## Objective
Generate standalone mock services that simulate external/third-party service behavior for development, testing, and integration validation without requiring live connectivity to real external systems.

## Mock Service Requirements

### Mock-Specific Configuration
- **Default Port**: 8083+ (configurable via `SERVER_PORT`)
- **Context Path**: `/api/{capability-name}/internal` (follows mock service pattern)
- **Storage**: In-memory storage for request auditing and testing
- **Authentication**: Optional Basic Auth for simulating secure integrations

## Mock Logic Implementation

### Response Simulation Patterns
- **Pattern-Based Responses**: Use input data patterns to trigger different response scenarios
  - PAN ending patterns: 'A' = success, 'B' = partial, 'C' = incomplete, 'D' = error
  - Name patterns: containing "fail" = external failure, "delay" = timeout simulation
  - Date patterns: specific DOBs trigger different business scenarios
- **Configurable Behaviors**: Support admin APIs for runtime configuration changes
- **Realistic Data**: Generate realistic mock responses that match production data structures
- **Error Scenarios**: Simulate various failure modes (timeouts, service unavailable, invalid data)

### Business Scenario Coverage
- **Success Scenarios**: Valid responses for different business cases
- **Failure Scenarios**: Various error conditions and edge cases
- **Partial Data**: Incomplete or missing data responses
- **Retry Logic**: Simulate intermittent failures for retry testing
- **Rate Limiting**: Optional simulation of API rate limits
- **Delay Simulation**: Configurable response delays for performance testing

## Mock-Specific Endpoints

### Administrative Endpoints (Mock-Only)
- **Service Info**: `/info` - Service metadata and supported scenarios
- **Admin Configuration**: `/admin/config` - Runtime behavior configuration
- **Request History**: `/admin/requests` - Audit trail of processed requests
- **Reset/Clear**: `/admin/reset` - Clear stored data and reset state

### Monitoring Endpoints (Mock-Only)
- **Metrics**: Basic service metrics and counters
- **Status**: Current service status and configuration
- **Scenarios**: List of supported test scenarios and triggers

## Mock-Specific Configuration

### Mock Behavior Configuration
```yaml
# Mock-specific configuration (add to standard application.yml)
mock-{capability-name}:
  simulation:
    default-delay-ms: 100
    error-rate-percent: 0
    timeout-simulation-ms: 5000

  admin:
    max-request-history: 100
    enable-admin-endpoints: true
```

### Mock Environment Settings
- Support different behavior profiles (dev, test, staging)
- Configurable feature flags for different simulation modes
- Environment-specific error rates and delays

## Data Management

### Request Storage
- Store incoming requests for audit and debugging
- Implement configurable retention policies
- Support request replay for testing
- Mask sensitive data in stored requests

### Response Templates
- Maintain response templates for different scenarios
- Support dynamic data generation based on input
- Implement response variation for realistic testing
- Cache frequently used responses for performance

## Mock-Specific Security

### Authentication Simulation
- Optional Basic Auth for secure integration testing
- Support API key validation if required by external service
- Implement proper error responses for authentication failures
- Configurable security modes (disabled, basic, advanced)

## Mock Testing Requirements

### Mock-Specific Testing
- Test mock logic for all simulation scenarios
- Validate pattern-based response triggers
- Test admin endpoint functionality
- Verify request storage and replay capabilities

## Mock Documentation

### Mock-Specific Documentation
- Document all supported scenarios and triggers
- Provide test data patterns and expected responses
- Include mock configuration examples
- Create troubleshooting guide for mock-specific issues
