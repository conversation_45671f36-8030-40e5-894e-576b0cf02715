---
type: "agent_requested"
description: "Post-Generation Guidelines: Guidelines for post-generation validation and testing requirements"
---
# Post-Generation Guidelines

> **Cross-References**: Implements logging standards from `general-coding-guidelines.md`. Follows Java patterns from `java-spring-boot-guidelines.md`. Prepares for testing phase in `testing-deployment.md`.

## Audit-Ready Logging Requirements
- Log request initiation with timestamp and transaction identifiers
- Log input/output data with sensitive fields masked or redacted (Aadhaar, PAN, Name)
- Capture all failures, retries, and backoff attempts with retry count
- Log final response or status with success/failure indicator
- Include reference ID in all logs for end-to-end traceability

## Error Management Standards
- Use standardized error response structure with `errorCode`, `message`, `timestamp`, and `traceId`
- Log full error stack traces with masked sensitive information
- Implement fallback logic for all third-party or mock integrations
- Enforce data retention and purge policies with automated test validation

## Business Scenario Test Coverage
- Test both valid and invalid inputs with proper assertions
- Cover success and failure scenarios for each external integration
- Include edge cases like pattern-based triggers or boundary input values
- Test retry logic with mock failures and graceful fallback behavior
- Validate data retention rules and record expiry enforcement

## Logging and Error Validation Tests
- Assert that all logs mask or redact sensitive PII fields
- Ensure each transaction is traceable using unique reference ID
- Simulate failures and validate correct predefined error codes are logged
- Include regression tests to prevent log exposure or missing audit entries