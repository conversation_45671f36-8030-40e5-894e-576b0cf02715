Business Flow

Business Use Case:
Enable secure, structured, and metadata-rich document uploads and retrievals for use within the loan processing ecosystem, including application documents like sanction letters, KYC, and other proofs. This capability must support both base64-encoded and binary document streams.

Goals:
- Allow structured, secure uploads of documents tied to loan applications
- Enable seamless retrieval of uploaded documents using a document ID
- Ensure adherence to size constraints (≤5MB) and data formatting rules
- Provide detailed metadata tagging for categorization and traceability

This capability will serve as a core enabler for digital document management across customer journeys.

------------------------------------------------------------
Phase 1: Upload Document Request
------------------------------------------------------------

Triggered when a system (Loan Origination System or Customer App) initiates a document upload.

Request Requirements:
- File should be in base64 or binary stream format
- Must include metadata for classification

Validations:
- Document size must be ≤ 5MB
- All fields must be strings with max length of 50 characters
- Mandatory fields:
  - name
  - folder
  - payload
  - metadata.customerId
  - metadata.applicationId
  - metadata.productCode
  - metadata.loanCategory
  - metadata.descriptor
  - metadata.createdDate

------------------------------------------------------------
Phase 2: Upload Document API Call
------------------------------------------------------------

API: POST /document/<application_ID>
Content-Type: application/octet-stream or application/json

Sample Request:
{
  "document": {
    "name": "Sanction_Letter.pdf",
    "folder": "maximus/application/MLP123/",
    "payload": "<Base64EncodedPayload>",
    "metadata": {
      "customerId": "CUST001",
      "applicationId": "MLP123",
      "productCode": "FOUR_WHEELER_ETB_PA",
      "loanCategory": "ETB",
      "descriptor": "SanctionLetter",
      "createdDate": "05/06/2025",
      "field1": "AdditionalInfo1",
      "field2": "AdditionalInfo2"
    }
  }
}

Field Definitions:

document.name
- Type: String
- Max Length: 50
- Description: Name of the document

document.folder
- Type: String
- Description: Folder path (e.g., maximus/application/<MLP_ID>/)

document.payload
- Type: String
- Description: Base64 encoded document payload

metadata.customerId
- Type: String
- Max Length: 50
- Description: Unique customer identifier

metadata.applicationId
- Type: String
- Max Length: 50
- Description: Loan application ID (e.g., MLP123)

metadata.productCode
- Type: String
- Max Length: 50
- Description: Product code (e.g., FOUR_WHEELER_ETB_PA)

metadata.loanCategory
- Type: String
- Max Length: 50
- Description: Loan category (e.g., ETB, NTB)

metadata.descriptor
- Type: String
- Max Length: 50
- Description: Document type (e.g., SanctionLetter, KYC)

metadata.createdDate
- Type: String
- Max Length: 50
- Format: DD/MM/YYYY
- Description: Document creation date

metadata.field1
- Type: String
- Max Length: 50
- Description: Placeholder for future use

metadata.field2
- Type: String
- Max Length: 50
- Description: Placeholder for future use

------------------------------------------------------------
Phase 3: Upload Document Response Handling
------------------------------------------------------------

Sample Response:
{
  "documentId": "DOC123456",
  "status": "Success",
  "error": {
    "code": "ERR001",
    "message": "Invalid payload"
  }
}

Validation Rules:
- If "status" is "Success" and "documentId" is present → proceed
- If an "error" object is present → retry and log; show fallback message

------------------------------------------------------------
Phase 4: Document Download
------------------------------------------------------------

API: GET /document/<DocumentId>
Content-Type: application/octet-stream or application/json

Expected Response:
- Raw document payload in base64 or binary format
- If document not found → return HTTP 404 Not Found

------------------------------------------------------------
Phase 5: Download Response Handling
------------------------------------------------------------

Success Case:
- Receive base64 document
- Decode and process downstream

Failure Scenarios:
- HTTP 404 → show fallback message: “Document not found”
- Transient network/API issues → retry

------------------------------------------------------------
Error Scenarios & Retry Rules
------------------------------------------------------------

Scenario: Document upload fails
Action: Retry 2 times, then show fallback message

Scenario: Document ID missing in response
Action: Halt, log issue, show error

Scenario: Upload response contains error
Action: Show error message and log context

Scenario: Document download returns 404
Action: Show “Document not found”

Scenario: Network/API timeout
Action: Retry up to 3 times

------------------------------------------------------------
Success Outcomes
------------------------------------------------------------

- A unique documentId is returned for every successful upload
- Metadata is stored in the system
- Document can be decoded and used downstream
- Consistent experience across upload and download journeys

------------------------------------------------------------
Fallback Message (Common)
------------------------------------------------------------

“We are unable to proceed with your document upload/download at the moment. Please try again later.”
