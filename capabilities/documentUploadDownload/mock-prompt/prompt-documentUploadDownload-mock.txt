Mock Document Upload API Service - Specification
================================================

Overview
--------
A standalone Spring Boot application designed to simulate the behavior of a document upload and download API. This mock service is built for development and QA environments to test the document submission and retrieval workflows, metadata validation, and response handling without relying on the production system.

Purpose
-------
- Simulate document upload (with metadata) and download flows
- Test scenarios for success, validation errors, size issues, and failures
- Provide realistic API structure and behavior
- Ensure decoupled testing for systems interacting with document management

Technical Requirements
----------------------
- Spring Boot version: 3.2.3 (or compatible)
- Java version: 21
- Server Port: 8083
- OpenAPI/Swagger documentation included
- Optional Basic Authentication for API access
- In-memory document registry with metadata tracking
- UUID-based "X-Trace-Id" tagging for each request and response
- Configurable simulated latency

Implementation Details
----------------------
- DTOs for document upload, metadata, and responses
- Controllers for upload and download endpoints
- Services for mock logic and validation
- Utility classes for size validation, logging, and ID generation
- application.yml with port, logging, and simulation configs
- Swagger config for API documentation
- README for setup, usage, and testing guidance

API Endpoints
-------------
1. Upload Document
   - Path: /api/documents/upload
   - Method: POST
   - Content-Type: application/json
   - Request Body:
     {
       "document": {
         "name": "Sanction_Letter.pdf",
         "folder": "maximus/application/MLP123/",
         "payload": "<Base64EncodedPayload>",
         "metadata": {
           "customerId": "CUST001",
           "applicationId": "MLP123",
           "productCode": "FOUR_WHEELER_ETB_PA",
           "loanCategory": "ETB",
           "descriptor": "SanctionLetter",
           "createdDate": "05/06/2025",
           "field1": "AdditionalInfo1",
           "field2": "AdditionalInfo2"
         }
       }
     }

   - Response (Success):
     {
       "documentId": "DOC123456",
       "status": "Success",
       "timestamp": "2025-06-16T10:00:00Z"
     }

   - Response (Validation Error):
     {
       "status": "Failure",
       "error": {
         "code": "ERR001",
         "message": "Invalid payload: Document size exceeds 5MB"
       }
     }

2. Download Document
   - Path: /api/documents/{documentId}
   - Method: GET
   - Produces: application/json or application/octet-stream
   - Response (Success):
     {
       "documentId": "DOC123456",
       "payload": "<Base64EncodedPayload>",
       "name": "Sanction_Letter.pdf",
       "metadata": { ... }
     }

   - Response (Not Found):
     HTTP 404 with message: "Document not found"

Validation Rules
----------------
- Max file size: 5MB (Base64 length adjusted accordingly)
- All fields must be string type
- Max length: 50 characters per field
- Mandatory fields:
  - name
  - folder
  - payload
  - metadata.customerId
  - metadata.applicationId
  - metadata.productCode
  - metadata.loanCategory
  - metadata.descriptor
  - metadata.createdDate

Simulated Behavior Logic
-------------------------
Upload Behavior:
- If base64 payload size > 5MB → return validation error
- If all mandatory fields present → return Success with documentId
- If any mandatory field missing → return 400 with "MISSING_FIELD" error
- Folder or name too long → return error with "FIELD_TOO_LONG"

Download Behavior:
- Valid documentId → return document + metadata
- Unknown documentId → return HTTP 404
- Optional config to simulate expired document (e.g., "DOCEXPIRED001")

Security
--------
- Mask sensitive metadata (e.g., customerId) in logs
- Use UUID in response headers for traceability
- Optional Basic Auth for endpoint access

Admin & Configuration Interface
-------------------------------
- GET  /api/documents/admin/logs          → View upload/download logs
- POST /api/documents/admin/config        → Configure simulated response delays or failures
- POST /api/documents/admin/reset         → Clear in-memory document registry and logs

Service Layer Components
------------------------
- MockDocumentService: Simulates document storage, validation, and retrieval
- MockValidationService: Performs field validation and size checks
- TraceLoggerService: Adds trace ID and logs masked document info

Testing Strategy
----------------
- Upload sample documents with:
  - All valid fields → Success
  - >5MB file → size error
  - Missing metadata → validation failure
- Download:
  - Existing documentId → returns file
  - Non-existing ID → returns 404
  - Simulate expired or deleted documentId

README Deliverables
-------------------
- How to start the mock service
- Example curl/Postman requests
- Swagger UI link for testing
- Configuration and testing instructions
- Guidelines for simulating delays and errors

Extensibility Suggestions
-------------------------
- Add support for multipart/binary upload endpoint
- Simulate virus scan delays or file quarantine
- Add tagging and folder-level download
- Expand metadata to include file type, size, checksum
