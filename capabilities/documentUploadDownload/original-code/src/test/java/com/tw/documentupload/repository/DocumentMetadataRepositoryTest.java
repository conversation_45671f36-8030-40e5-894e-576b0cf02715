package com.tw.documentupload.repository;

import com.tw.documentupload.entity.DocumentMetadata;
import com.tw.documentupload.enums.DocumentStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for DocumentMetadataRepository.
 * 
 * Tests repository operations including custom queries, indexing,
 * and data integrity constraints.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@DataJpaTest
@ActiveProfiles("test")
@DisplayName("DocumentMetadataRepository Tests")
class DocumentMetadataRepositoryTest {
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Autowired
    private DocumentMetadataRepository documentMetadataRepository;
    
    private DocumentMetadata testDocument1;
    private DocumentMetadata testDocument2;
    private DocumentMetadata testDocument3;
    
    @BeforeEach
    void setUp() {
        // Create test documents
        testDocument1 = createTestDocument(
            "DOC20250616143025000001AB",
            "REF20250616143025000001ABC",
            "CUST001",
            "MLP123",
            "SanctionLetter"
        );
        
        testDocument2 = createTestDocument(
            "DOC20250616143025000002AB",
            "REF20250616143025000002ABC",
            "CUST001",
            "MLP124",
            "KYCDocument"
        );
        
        testDocument3 = createTestDocument(
            "DOC20250616143025000003AB",
            "REF20250616143025000003ABC",
            "CUST002",
            "MLP125",
            "IncomeProof"
        );
        
        // Persist test data
        entityManager.persistAndFlush(testDocument1);
        entityManager.persistAndFlush(testDocument2);
        entityManager.persistAndFlush(testDocument3);
    }
    
    @Test
    @DisplayName("Should find document by document ID")
    void shouldFindDocumentByDocumentId() {
        // When
        Optional<DocumentMetadata> found = documentMetadataRepository.findByDocumentId("DOC20250616143025000001AB");
        
        // Then
        assertTrue(found.isPresent());
        assertEquals(testDocument1.getDocumentId(), found.get().getDocumentId());
        assertEquals(testDocument1.getCustomerId(), found.get().getCustomerId());
    }
    
    @Test
    @DisplayName("Should find document by reference number")
    void shouldFindDocumentByReferenceNumber() {
        // When
        Optional<DocumentMetadata> found = documentMetadataRepository.findByReferenceNumber("REF20250616143025000001ABC");
        
        // Then
        assertTrue(found.isPresent());
        assertEquals(testDocument1.getReferenceNumber(), found.get().getReferenceNumber());
        assertEquals(testDocument1.getDocumentName(), found.get().getDocumentName());
    }
    
    @Test
    @DisplayName("Should find all documents for a customer")
    void shouldFindAllDocumentsForCustomer() {
        // When
        List<DocumentMetadata> documents = documentMetadataRepository.findByCustomerId("CUST001");
        
        // Then
        assertEquals(2, documents.size());
        assertTrue(documents.stream().allMatch(doc -> "CUST001".equals(doc.getCustomerId())));
    }
    
    @Test
    @DisplayName("Should find all documents for an application")
    void shouldFindAllDocumentsForApplication() {
        // When
        List<DocumentMetadata> documents = documentMetadataRepository.findByApplicationId("MLP123");
        
        // Then
        assertEquals(1, documents.size());
        assertEquals(testDocument1.getDocumentId(), documents.get(0).getDocumentId());
    }
    
    @Test
    @DisplayName("Should find documents by customer and application")
    void shouldFindDocumentsByCustomerAndApplication() {
        // When
        List<DocumentMetadata> documents = documentMetadataRepository.findByCustomerIdAndApplicationId("CUST001", "MLP123");
        
        // Then
        assertEquals(1, documents.size());
        assertEquals(testDocument1.getDocumentId(), documents.get(0).getDocumentId());
    }
    
    @Test
    @DisplayName("Should find documents by application and descriptor")
    void shouldFindDocumentsByApplicationAndDescriptor() {
        // When
        List<DocumentMetadata> documents = documentMetadataRepository.findByApplicationIdAndDescriptor("MLP123", "SanctionLetter");
        
        // Then
        assertEquals(1, documents.size());
        assertEquals(testDocument1.getDocumentId(), documents.get(0).getDocumentId());
    }
    
    @Test
    @DisplayName("Should find documents by status")
    void shouldFindDocumentsByStatus() {
        // Given - Update one document to ARCHIVED status
        testDocument2.setStatus(DocumentStatus.ARCHIVED);
        entityManager.persistAndFlush(testDocument2);
        
        // When
        List<DocumentMetadata> activeDocuments = documentMetadataRepository.findByStatus(DocumentStatus.ACTIVE);
        List<DocumentMetadata> archivedDocuments = documentMetadataRepository.findByStatus(DocumentStatus.ARCHIVED);
        
        // Then
        assertEquals(2, activeDocuments.size());
        assertEquals(1, archivedDocuments.size());
        assertEquals(testDocument2.getDocumentId(), archivedDocuments.get(0).getDocumentId());
    }
    
    @Test
    @DisplayName("Should find documents by upload timestamp range")
    void shouldFindDocumentsByUploadTimestampRange() {
        // Given
        ZonedDateTime startDate = ZonedDateTime.now().minusHours(1);
        ZonedDateTime endDate = ZonedDateTime.now().plusHours(1);
        
        // When
        List<DocumentMetadata> documents = documentMetadataRepository.findByUploadTimestampBetween(startDate, endDate);
        
        // Then
        assertEquals(3, documents.size());
    }
    
    @Test
    @DisplayName("Should find expired documents")
    void shouldFindExpiredDocuments() {
        // Given - Set one document as expired
        testDocument1.setRetentionUntilDate(LocalDate.now().minusDays(1));
        entityManager.persistAndFlush(testDocument1);
        
        // When
        List<DocumentMetadata> expiredDocuments = documentMetadataRepository.findExpiredDocuments(
            LocalDate.now(), DocumentStatus.ACTIVE);
        
        // Then
        assertEquals(1, expiredDocuments.size());
        assertEquals(testDocument1.getDocumentId(), expiredDocuments.get(0).getDocumentId());
    }
    
    @Test
    @DisplayName("Should find documents expiring soon")
    void shouldFindDocumentsExpiringSoon() {
        // Given - Set documents to expire in the future
        LocalDate futureDate = LocalDate.now().plusDays(30);
        testDocument1.setRetentionUntilDate(futureDate);
        testDocument2.setRetentionUntilDate(futureDate);
        entityManager.persistAndFlush(testDocument1);
        entityManager.persistAndFlush(testDocument2);
        
        // When
        List<DocumentMetadata> expiringDocuments = documentMetadataRepository.findDocumentsExpiringBefore(
            LocalDate.now().plusDays(60), DocumentStatus.ACTIVE);
        
        // Then
        assertEquals(2, expiringDocuments.size());
    }
    
    @Test
    @DisplayName("Should count documents by status")
    void shouldCountDocumentsByStatus() {
        // Given - Update one document to ARCHIVED status
        testDocument2.setStatus(DocumentStatus.ARCHIVED);
        entityManager.persistAndFlush(testDocument2);
        
        // When
        long activeCount = documentMetadataRepository.countByStatus(DocumentStatus.ACTIVE);
        long archivedCount = documentMetadataRepository.countByStatus(DocumentStatus.ARCHIVED);
        
        // Then
        assertEquals(2, activeCount);
        assertEquals(1, archivedCount);
    }
    
    @Test
    @DisplayName("Should count documents by customer")
    void shouldCountDocumentsByCustomer() {
        // When
        long cust001Count = documentMetadataRepository.countByCustomerId("CUST001");
        long cust002Count = documentMetadataRepository.countByCustomerId("CUST002");
        
        // Then
        assertEquals(2, cust001Count);
        assertEquals(1, cust002Count);
    }
    
    @Test
    @DisplayName("Should check if document exists by document ID")
    void shouldCheckIfDocumentExistsByDocumentId() {
        // When & Then
        assertTrue(documentMetadataRepository.existsByDocumentId("DOC20250616143025000001AB"));
        assertFalse(documentMetadataRepository.existsByDocumentId("DOC20250616143025000999ZZ"));
    }
    
    @Test
    @DisplayName("Should check if document exists by reference number")
    void shouldCheckIfDocumentExistsByReferenceNumber() {
        // When & Then
        assertTrue(documentMetadataRepository.existsByReferenceNumber("REF20250616143025000001ABC"));
        assertFalse(documentMetadataRepository.existsByReferenceNumber("REF20250616143025000999ZZZ"));
    }
    
    @Test
    @DisplayName("Should update access information")
    void shouldUpdateAccessInformation() {
        // Given
        String documentId = testDocument1.getDocumentId();
        ZonedDateTime accessTime = ZonedDateTime.now();
        
        // When
        int updatedRows = documentMetadataRepository.updateAccessInfo(documentId, 5, accessTime);
        entityManager.flush();
        entityManager.clear();
        
        // Then
        assertEquals(1, updatedRows);
        
        Optional<DocumentMetadata> updated = documentMetadataRepository.findByDocumentId(documentId);
        assertTrue(updated.isPresent());
        assertEquals(5, updated.get().getAccessCount());
        assertNotNull(updated.get().getLastAccessedTimestamp());
    }
    
    @Test
    @DisplayName("Should update document status")
    void shouldUpdateDocumentStatus() {
        // Given
        String documentId = testDocument1.getDocumentId();
        
        // When
        int updatedRows = documentMetadataRepository.updateStatus(documentId, DocumentStatus.ARCHIVED, "SYSTEM");
        entityManager.flush();
        entityManager.clear();
        
        // Then
        assertEquals(1, updatedRows);
        
        Optional<DocumentMetadata> updated = documentMetadataRepository.findByDocumentId(documentId);
        assertTrue(updated.isPresent());
        assertEquals(DocumentStatus.ARCHIVED, updated.get().getStatus());
        assertEquals("SYSTEM", updated.get().getUpdatedBy());
        assertNotNull(updated.get().getUpdatedAt());
    }
    
    @Test
    @DisplayName("Should find highly accessed documents")
    void shouldFindHighlyAccessedDocuments() {
        // Given - Set high access count for one document
        testDocument1.setAccessCount(100);
        entityManager.persistAndFlush(testDocument1);
        
        // When
        List<DocumentMetadata> highlyAccessed = documentMetadataRepository.findHighlyAccessedDocuments(50);
        
        // Then
        assertEquals(1, highlyAccessed.size());
        assertEquals(testDocument1.getDocumentId(), highlyAccessed.get(0).getDocumentId());
    }
    
    @Test
    @DisplayName("Should find unused documents")
    void shouldFindUnusedDocuments() {
        // Given - Set old last accessed time for one document
        testDocument1.setLastAccessedTimestamp(ZonedDateTime.now().minusDays(30));
        entityManager.persistAndFlush(testDocument1);
        
        // When
        List<DocumentMetadata> unusedDocuments = documentMetadataRepository.findUnusedDocuments(
            ZonedDateTime.now().minusDays(7));
        
        // Then
        assertEquals(3, unusedDocuments.size()); // All documents have null or old last accessed time
    }
    
    /**
     * Creates a test document metadata entity.
     * 
     * @param documentId document identifier
     * @param referenceNumber reference number
     * @param customerId customer identifier
     * @param applicationId application identifier
     * @param descriptor document descriptor
     * @return document metadata entity
     */
    private DocumentMetadata createTestDocument(String documentId, String referenceNumber,
                                              String customerId, String applicationId, String descriptor) {
        DocumentMetadata document = new DocumentMetadata(
            documentId,
            referenceNumber,
            "Test_Document.pdf",
            "test/folder/",
            1024L,
            customerId,
            applicationId,
            "TEST_PRODUCT",
            "ETB",
            descriptor,
            "15/06/2025"
        );
        
        document.setField1("TestField1");
        document.setField2("TestField2");
        document.setRetentionUntilDate(LocalDate.now().plusDays(2555));
        
        return document;
    }
}
