package com.tw.documentupload.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;



import java.util.Base64;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ValidationUtils class.
 *
 * Tests validation logic for document uploads including base64 validation,
 * size checks, format validation, and business rule validation.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@DisplayName("ValidationUtils Tests")
class ValidationUtilsTest {

    private ValidationUtils validationUtils;

    @BeforeEach
    void setUp() {
        validationUtils = new ValidationUtils();
    }

    @Test
    @DisplayName("Should validate valid base64 payload")
    void shouldValidateValidBase64Payload() {
        // Given
        String validBase64 = Base64.getEncoder().encodeToString("Test document content".getBytes());

        // When & Then
        assertTrue(validationUtils.isValidBase64Payload(validBase64));
    }

    @Test
    @DisplayName("Should reject invalid base64 payload")
    void shouldRejectInvalidBase64Payload() {
        // Given
        String invalidBase64 = "This is not base64!@#$%";

        // When & Then
        assertFalse(validationUtils.isValidBase64Payload(invalidBase64));
    }

    @Test
    @DisplayName("Should reject null or empty base64 payload")
    void shouldRejectNullOrEmptyBase64Payload() {
        // When & Then
        assertFalse(validationUtils.isValidBase64Payload(null));
        assertFalse(validationUtils.isValidBase64Payload(""));
        assertFalse(validationUtils.isValidBase64Payload("   "));
    }

    @Test
    @DisplayName("Should validate payload within size limit")
    void shouldValidatePayloadWithinSizeLimit() {
        // Given - Create a small document (1KB)
        byte[] smallDocument = new byte[1024];
        String base64Payload = Base64.getEncoder().encodeToString(smallDocument);

        // When & Then
        assertTrue(validationUtils.isWithinSizeLimit(base64Payload));
    }

    @Test
    @DisplayName("Should reject payload exceeding size limit")
    void shouldRejectPayloadExceedingSizeLimit() {
        // Given - Create a large document (6MB)
        byte[] largeDocument = new byte[6 * 1024 * 1024];
        String base64Payload = Base64.getEncoder().encodeToString(largeDocument);

        // When & Then
        assertFalse(validationUtils.isWithinSizeLimit(base64Payload));
    }

    @Test
    @DisplayName("Should calculate correct decoded size")
    void shouldCalculateCorrectDecodedSize() {
        // Given
        String content = "Test document content";
        String base64Payload = Base64.getEncoder().encodeToString(content.getBytes());

        // When
        long decodedSize = validationUtils.getDecodedSize(base64Payload);

        // Then
        assertEquals(content.getBytes().length, decodedSize);
    }

    @Test
    @DisplayName("Should validate correct date format")
    void shouldValidateCorrectDateFormat() {
        // Given
        String validDate = "15/06/2025";

        // When & Then
        assertTrue(validationUtils.isValidDateFormat(validDate));
    }

    @Test
    @DisplayName("Should reject invalid date formats")
    void shouldRejectInvalidDateFormats() {
        // Given
        String[] invalidDates = {
            "2025-06-15",    // Wrong format
            "15/6/2025",     // Single digit month
            "15/06/25",      // Two digit year
            "32/06/2025",    // Invalid day
            "15/13/2025",    // Invalid month
            "15/06/1800",    // Year too old
            "15/06/2200",    // Year too far in future
            null,            // Null
            "",              // Empty
            "invalid"        // Not a date
        };

        // When & Then
        for (String invalidDate : invalidDates) {
            assertFalse(validationUtils.isValidDateFormat(invalidDate),
                       "Should reject date: " + invalidDate);
        }
    }

    @Test
    @DisplayName("Should validate correct application ID format")
    void shouldValidateCorrectApplicationIdFormat() {
        // Given
        String[] validApplicationIds = {
            "MLP123",
            "APP456789",
            "LOAN123456",
            "AB123"
        };

        // When & Then
        for (String validId : validApplicationIds) {
            assertTrue(validationUtils.isValidApplicationId(validId),
                      "Should accept application ID: " + validId);
        }
    }

    @Test
    @DisplayName("Should reject invalid application ID formats")
    void shouldRejectInvalidApplicationIdFormats() {
        // Given
        String[] invalidApplicationIds = {
            "123",           // No letters
            "ABC",           // No numbers
            "A1",            // Too short
            "ABCDEFG1234567", // Too long
            "abc123",        // Lowercase
            "AB-123",        // Special characters
            null,            // Null
            ""               // Empty
        };

        // When & Then
        for (String invalidId : invalidApplicationIds) {
            assertFalse(validationUtils.isValidApplicationId(invalidId),
                       "Should reject application ID: " + invalidId);
        }
    }

    @Test
    @DisplayName("Should validate correct customer ID format")
    void shouldValidateCorrectCustomerIdFormat() {
        // Given
        String[] validCustomerIds = {
            "CUST001",
            "CUSTOMER123456789AB",
            "C123",
            "ABC123DEF456"
        };

        // When & Then
        for (String validId : validCustomerIds) {
            assertTrue(validationUtils.isValidCustomerId(validId),
                      "Should accept customer ID: " + validId);
        }
    }

    @Test
    @DisplayName("Should validate correct folder path format")
    void shouldValidateCorrectFolderPathFormat() {
        // Given
        String[] validFolderPaths = {
            "maximus/application/MLP123/",
            "documents/customer/CUST001/",
            "loan-docs/2025/",
            "archive/old-docs/"
        };

        // When & Then
        for (String validPath : validFolderPaths) {
            assertTrue(validationUtils.isValidFolderPath(validPath),
                      "Should accept folder path: " + validPath);
        }
    }

    @Test
    @DisplayName("Should validate correct document name format")
    void shouldValidateCorrectDocumentNameFormat() {
        // Given
        String[] validDocumentNames = {
            "Sanction_Letter.pdf",
            "KYC_Document.jpg",
            "Application-Form.docx",
            "bank_statement.png",
            "income.proof.doc"
        };

        // When & Then
        for (String validName : validDocumentNames) {
            assertTrue(validationUtils.isValidDocumentName(validName),
                      "Should accept document name: " + validName);
        }
    }

    @Test
    @DisplayName("Should reject unsupported file extensions")
    void shouldRejectUnsupportedFileExtensions() {
        // Given
        String[] unsupportedNames = {
            "document.exe",
            "script.bat",
            "archive.zip",
            "video.mp4",
            "audio.mp3"
        };

        // When & Then
        for (String unsupportedName : unsupportedNames) {
            assertFalse(validationUtils.isValidDocumentName(unsupportedName),
                       "Should reject document name: " + unsupportedName);
        }
    }

    @Test
    @DisplayName("Should check supported extensions correctly")
    void shouldCheckSupportedExtensionsCorrectly() {
        // Given
        String[] supportedExtensions = {"pdf", "jpg", "jpeg", "png", "doc", "docx"};
        String[] unsupportedExtensions = {"exe", "bat", "zip", "mp4", "mp3"};

        // When & Then
        for (String supported : supportedExtensions) {
            assertTrue(validationUtils.isSupportedExtension(supported),
                      "Should support extension: " + supported);
        }

        for (String unsupported : unsupportedExtensions) {
            assertFalse(validationUtils.isSupportedExtension(unsupported),
                       "Should not support extension: " + unsupported);
        }
    }

    @Test
    @DisplayName("Should extract file extension correctly")
    void shouldExtractFileExtensionCorrectly() {
        // Given & When & Then
        assertEquals("pdf", validationUtils.getFileExtension("document.pdf"));
        assertEquals("jpg", validationUtils.getFileExtension("image.jpg"));
        assertEquals("docx", validationUtils.getFileExtension("file.name.docx"));
        assertNull(validationUtils.getFileExtension("noextension"));
        assertNull(validationUtils.getFileExtension(""));
        assertNull(validationUtils.getFileExtension(null));
        assertNull(validationUtils.getFileExtension("file."));
    }

    @Test
    @DisplayName("Should validate string length correctly")
    void shouldValidateStringLengthCorrectly() {
        // Given
        String shortString = "short";
        String longString = "a".repeat(100);

        // When & Then
        assertTrue(validationUtils.isValidStringLength(shortString, 50, "test"));
        assertFalse(validationUtils.isValidStringLength(longString, 50, "test"));
        assertFalse(validationUtils.isValidStringLength(null, 50, "test"));
    }

    @Test
    @DisplayName("Should validate not null or empty correctly")
    void shouldValidateNotNullOrEmptyCorrectly() {
        // When & Then
        assertTrue(validationUtils.isNotNullOrEmpty("valid", "test"));
        assertFalse(validationUtils.isNotNullOrEmpty(null, "test"));
        assertFalse(validationUtils.isNotNullOrEmpty("", "test"));
        assertFalse(validationUtils.isNotNullOrEmpty("   ", "test"));
    }

    @Test
    @DisplayName("Should sanitize string correctly")
    void shouldSanitizeStringCorrectly() {
        // Given
        String inputWithControlChars = "test\u0001\u0002string\t\n\r";
        String inputWithMultipleSpaces = "test   multiple    spaces";

        // When
        String sanitized1 = validationUtils.sanitizeString(inputWithControlChars);
        String sanitized2 = validationUtils.sanitizeString(inputWithMultipleSpaces);

        // Then
        assertEquals("teststring", sanitized1);
        assertEquals("test multiple spaces", sanitized2);
        assertNull(validationUtils.sanitizeString(null));
    }

    @Test
    @DisplayName("Should return correct max file size")
    void shouldReturnCorrectMaxFileSize() {
        // When & Then
        assertEquals(5 * 1024 * 1024, validationUtils.getMaxFileSizeBytes());
        assertEquals(5.0, validationUtils.getMaxFileSizeMB());
    }

    @Test
    @DisplayName("Should return supported extensions array")
    void shouldReturnSupportedExtensionsArray() {
        // When
        String[] extensions = validationUtils.getSupportedExtensions();

        // Then
        assertNotNull(extensions);
        assertTrue(extensions.length > 0);
        // Verify it's a copy (defensive programming)
        extensions[0] = "modified";
        assertNotEquals("modified", validationUtils.getSupportedExtensions()[0]);
    }
}
