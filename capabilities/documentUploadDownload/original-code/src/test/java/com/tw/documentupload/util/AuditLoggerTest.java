package com.tw.documentupload.util;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for AuditLogger class.
 *
 * Tests audit logging functionality including PII masking, MDC context,
 * and structured logging for compliance and debugging purposes.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@DisplayName("AuditLogger Tests")
class AuditLoggerTest {

    private AuditLogger auditLogger;
    private ListAppender<ILoggingEvent> listAppender;
    private Logger logger;

    @BeforeEach
    void setUp() {
        auditLogger = new AuditLogger();

        // Set up log capture
        logger = (Logger) LoggerFactory.getLogger(AuditLogger.class);
        listAppender = new ListAppender<>();
        listAppender.start();
        logger.addAppender(listAppender);
        logger.setLevel(Level.DEBUG);

        // Clear MDC
        MDC.clear();
    }

    @Test
    @DisplayName("Should log request initiation with proper format")
    void shouldLogRequestInitiationWithProperFormat() {
        // Given
        String operation = "DOCUMENT_UPLOAD";
        String traceId = "TRC123456";
        String referenceNumber = "REF123456789";
        Map<String, Object> additionalInfo = Map.of("documentName", "test.pdf", "size", 1024);

        // When
        auditLogger.logRequestInitiation(operation, traceId, referenceNumber, additionalInfo);

        // Then
        List<ILoggingEvent> logEvents = listAppender.list;
        assertEquals(1, logEvents.size());

        ILoggingEvent logEvent = logEvents.get(0);
        assertEquals(Level.INFO, logEvent.getLevel());
        assertTrue(logEvent.getFormattedMessage().contains("REQUEST_INITIATED"));
        assertTrue(logEvent.getFormattedMessage().contains(operation));
        assertTrue(logEvent.getFormattedMessage().contains(traceId));
        assertTrue(logEvent.getFormattedMessage().contains(referenceNumber));

        // Check MDC context
        assertEquals(traceId, logEvent.getMDCPropertyMap().get("traceId"));
        assertEquals(referenceNumber, logEvent.getMDCPropertyMap().get("referenceNumber"));
        assertEquals(operation, logEvent.getMDCPropertyMap().get("operation"));
    }

    @Test
    @DisplayName("Should log success with result information")
    void shouldLogSuccessWithResultInformation() {
        // Given
        String operation = "DOCUMENT_UPLOAD";
        String traceId = "TRC123456";
        String referenceNumber = "REF123456789";
        Object result = Map.of("documentId", "DOC123", "status", "SUCCESS");

        // When
        auditLogger.logSuccess(operation, traceId, referenceNumber, result);

        // Then
        List<ILoggingEvent> logEvents = listAppender.list;
        assertEquals(1, logEvents.size());

        ILoggingEvent logEvent = logEvents.get(0);
        assertEquals(Level.INFO, logEvent.getLevel());
        assertTrue(logEvent.getFormattedMessage().contains("OPERATION_SUCCESS"));
        assertTrue(logEvent.getFormattedMessage().contains(operation));
        assertTrue(logEvent.getFormattedMessage().contains(traceId));
        assertTrue(logEvent.getFormattedMessage().contains(referenceNumber));
    }

    @Test
    @DisplayName("Should log failure with error details")
    void shouldLogFailureWithErrorDetails() {
        // Given
        String operation = "DOCUMENT_UPLOAD";
        String traceId = "TRC123456";
        String referenceNumber = "REF123456789";
        String errorCode = "ERR001";
        String errorMessage = "Invalid payload";
        Exception exception = new RuntimeException("Test exception");

        // When
        auditLogger.logFailure(operation, traceId, referenceNumber, errorCode, errorMessage, exception);

        // Then
        List<ILoggingEvent> logEvents = listAppender.list;
        assertEquals(1, logEvents.size());

        ILoggingEvent logEvent = logEvents.get(0);
        assertEquals(Level.ERROR, logEvent.getLevel());
        assertTrue(logEvent.getFormattedMessage().contains("OPERATION_FAILURE"));
        assertTrue(logEvent.getFormattedMessage().contains(operation));
        assertTrue(logEvent.getFormattedMessage().contains(traceId));
        assertTrue(logEvent.getFormattedMessage().contains(errorCode));
        assertTrue(logEvent.getFormattedMessage().contains(errorMessage));
        assertNotNull(logEvent.getThrowableProxy());
    }

    @Test
    @DisplayName("Should log retry attempts with attempt information")
    void shouldLogRetryAttemptsWithAttemptInformation() {
        // Given
        String operation = "DOCUMENT_UPLOAD";
        String traceId = "TRC123456";
        String referenceNumber = "REF123456789";
        int attemptNumber = 2;
        int maxAttempts = 3;
        String reason = "Network timeout";

        // When
        auditLogger.logRetryAttempt(operation, traceId, referenceNumber, attemptNumber, maxAttempts, reason);

        // Then
        List<ILoggingEvent> logEvents = listAppender.list;
        assertEquals(1, logEvents.size());

        ILoggingEvent logEvent = logEvents.get(0);
        assertEquals(Level.WARN, logEvent.getLevel());
        assertTrue(logEvent.getFormattedMessage().contains("RETRY_ATTEMPT"));
        assertTrue(logEvent.getFormattedMessage().contains("2/3"));
        assertTrue(logEvent.getFormattedMessage().contains(reason));
    }

    @Test
    @DisplayName("Should log validation errors with masked request data")
    void shouldLogValidationErrorsWithMaskedRequestData() {
        // Given
        String operation = "DOCUMENT_UPLOAD";
        String traceId = "TRC123456";
        String referenceNumber = "REF123456789";
        String validationErrors = "Field 'name' is required";
        Object requestData = Map.of("customerId", "CUST12345", "payload", "base64encodeddata");

        // When
        auditLogger.logValidationError(operation, traceId, referenceNumber, validationErrors, requestData);

        // Then
        List<ILoggingEvent> logEvents = listAppender.list;
        assertEquals(1, logEvents.size());

        ILoggingEvent logEvent = logEvents.get(0);
        assertEquals(Level.WARN, logEvent.getLevel());
        assertTrue(logEvent.getFormattedMessage().contains("VALIDATION_ERROR"));
        assertTrue(logEvent.getFormattedMessage().contains(validationErrors));
        // Check that sensitive data is masked
        assertTrue(logEvent.getFormattedMessage().contains("****"));
    }

    @Test
    @DisplayName("Should log document access events")
    void shouldLogDocumentAccessEvents() {
        // Given
        String documentId = "DOC123456";
        String traceId = "TRC123456";
        String accessType = "DOWNLOAD";
        String userId = "USER12345";
        String ipAddress = "*************";

        // When
        auditLogger.logDocumentAccess(documentId, traceId, accessType, userId, ipAddress);

        // Then
        List<ILoggingEvent> logEvents = listAppender.list;
        assertEquals(1, logEvents.size());

        ILoggingEvent logEvent = logEvents.get(0);
        assertEquals(Level.INFO, logEvent.getLevel());
        assertTrue(logEvent.getFormattedMessage().contains("DOCUMENT_ACCESS"));
        assertTrue(logEvent.getFormattedMessage().contains(documentId));
        assertTrue(logEvent.getFormattedMessage().contains(accessType));
        // Check that user ID and IP are masked
        assertFalse(logEvent.getFormattedMessage().contains("USER12345"));
        assertFalse(logEvent.getFormattedMessage().contains("*************"));
    }

    @Test
    @DisplayName("Should log performance metrics")
    void shouldLogPerformanceMetrics() {
        // Given
        String operation = "DOCUMENT_UPLOAD";
        String traceId = "TRC123456";
        long durationMs = 1500;
        Map<String, Object> additionalMetrics = Map.of("fileSize", 1024, "validationTime", 100);

        // When
        auditLogger.logPerformanceMetrics(operation, traceId, durationMs, additionalMetrics);

        // Then
        List<ILoggingEvent> logEvents = listAppender.list;
        assertEquals(1, logEvents.size());

        ILoggingEvent logEvent = logEvents.get(0);
        assertEquals(Level.INFO, logEvent.getLevel());
        assertTrue(logEvent.getFormattedMessage().contains("PERFORMANCE_METRICS"));
        assertTrue(logEvent.getFormattedMessage().contains("1500"));
        assertTrue(logEvent.getFormattedMessage().contains("fileSize"));
    }

    @Test
    @DisplayName("Should mask sensitive data in logs")
    void shouldMaskSensitiveDataInLogs() {
        // Given
        String operation = "TEST_OPERATION";
        String traceId = "TRC123456";
        String referenceNumber = "REF123456789";

        // Create request with sensitive data
        Map<String, Object> sensitiveData = new HashMap<>();
        sensitiveData.put("customerId", "CUST12345");
        sensitiveData.put("payload", "VGhpcyBpcyBhIHRlc3QgcGF5bG9hZA==");
        sensitiveData.put("password", "secretpassword");
        sensitiveData.put("token", "bearer_token_12345");

        // When
        auditLogger.logRequestInitiation(operation, traceId, referenceNumber, sensitiveData);

        // Then
        List<ILoggingEvent> logEvents = listAppender.list;
        assertEquals(1, logEvents.size());

        String logMessage = logEvents.get(0).getFormattedMessage();

        // Verify sensitive data is masked
        assertFalse(logMessage.contains("CUST12345"));
        assertFalse(logMessage.contains("VGhpcyBpcyBhIHRlc3QgcGF5bG9hZA=="));
        assertFalse(logMessage.contains("secretpassword"));
        assertFalse(logMessage.contains("bearer_token_12345"));

        // Verify masking patterns are present
        assertTrue(logMessage.contains("****") || logMessage.contains("[PAYLOAD_MASKED]"));
    }

    @Test
    @DisplayName("Should handle null values gracefully")
    void shouldHandleNullValuesGracefully() {
        // Given
        String operation = "TEST_OPERATION";
        String traceId = "TRC123456";

        // When - Test with null values
        assertDoesNotThrow(() -> {
            auditLogger.logRequestInitiation(operation, traceId, null, null);
            auditLogger.logSuccess(operation, traceId, null, null);
            auditLogger.logFailure(operation, traceId, null, "ERR001", "Error", null);
            auditLogger.logValidationError(operation, traceId, null, "Validation error", null);
        });

        // Then
        List<ILoggingEvent> logEvents = listAppender.list;
        assertEquals(4, logEvents.size());

        // Verify all logs were created without exceptions
        for (ILoggingEvent event : logEvents) {
            assertNotNull(event.getFormattedMessage());
            assertTrue(event.getFormattedMessage().contains(operation));
            assertTrue(event.getFormattedMessage().contains(traceId));
        }
    }

    @Test
    @DisplayName("Should clear MDC context")
    void shouldClearMDCContext() {
        // Given - Set some MDC values
        auditLogger.logRequestInitiation("TEST_OP", "TRC123", "REF123", null);
        assertFalse(MDC.getCopyOfContextMap().isEmpty());

        // When
        auditLogger.clearMDCContext();

        // Then
        assertTrue(MDC.getCopyOfContextMap() == null || MDC.getCopyOfContextMap().isEmpty());
    }

    @Test
    @DisplayName("Should mask IP addresses correctly")
    void shouldMaskIpAddressesCorrectly() {
        // Given
        String documentId = "DOC123456";
        String traceId = "TRC123456";
        String accessType = "DOWNLOAD";
        String userId = "USER12345";

        // Test different IP address formats
        String[] ipAddresses = {
            "*************",    // IPv4
            "********",         // IPv4
            "2001:db8::1",      // IPv6
            "localhost"         // Hostname
        };

        // When & Then
        for (String ipAddress : ipAddresses) {
            auditLogger.logDocumentAccess(documentId, traceId, accessType, userId, ipAddress);
        }

        List<ILoggingEvent> logEvents = listAppender.list;
        assertEquals(ipAddresses.length, logEvents.size());

        // Verify IP addresses are masked
        for (int i = 0; i < logEvents.size(); i++) {
            String logMessage = logEvents.get(i).getFormattedMessage();
            assertFalse(logMessage.contains(ipAddresses[i]),
                       "IP address should be masked: " + ipAddresses[i]);
        }
    }
}
