package com.tw.documentupload.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tw.documentupload.dto.DocumentDto;
import com.tw.documentupload.dto.DocumentMetadataDto;
import com.tw.documentupload.dto.DocumentUploadRequestDto;
import com.tw.documentupload.dto.DocumentUploadResponseDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.util.Base64;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for DocumentController.
 *
 * Tests the complete flow of document upload and download operations
 * including validation, error handling, and response formatting.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("DocumentController Integration Tests")
class DocumentControllerIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    @DisplayName("Should upload document successfully with valid request")
    void shouldUploadDocumentSuccessfullyWithValidRequest() throws Exception {
        // Given
        DocumentUploadRequestDto request = createValidUploadRequest();
        String requestJson = objectMapper.writeValueAsString(request);

        // When & Then
        MvcResult result = mockMvc.perform(post("/document/MLP123")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").value("Success"))
                .andExpect(jsonPath("$.documentId").exists())
                .andExpect(jsonPath("$.referenceNumber").exists())
                .andExpect(jsonPath("$.timestamp").exists())
                .andReturn();

        // Verify response
        String responseJson = result.getResponse().getContentAsString();
        DocumentUploadResponseDto response = objectMapper.readValue(responseJson, DocumentUploadResponseDto.class);

        assertNotNull(response.getDocumentId());
        assertTrue(response.getDocumentId().startsWith("DOC"));
        assertNotNull(response.getReferenceNumber());
        assertTrue(response.getReferenceNumber().startsWith("REF"));
        assertEquals("Success", response.getStatus());
        assertTrue(response.isSuccess());
    }

    @Test
    @DisplayName("Should reject upload with mismatched application ID")
    void shouldRejectUploadWithMismatchedApplicationId() throws Exception {
        // Given
        DocumentUploadRequestDto request = createValidUploadRequest();
        String requestJson = objectMapper.writeValueAsString(request);

        // When & Then
        mockMvc.perform(post("/document/DIFFERENT123")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").value("Failure"))
                .andExpect(jsonPath("$.error.code").value("ERR010"))
                .andExpect(jsonPath("$.error.message").exists());
    }

    @Test
    @DisplayName("Should reject upload with missing mandatory fields")
    void shouldRejectUploadWithMissingMandatoryFields() throws Exception {
        // Given
        DocumentUploadRequestDto request = createValidUploadRequest();
        request.getDocument().getMetadata().setCustomerId(null); // Remove mandatory field
        String requestJson = objectMapper.writeValueAsString(request);

        // When & Then
        mockMvc.perform(post("/document/MLP123")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").value("Failure"))
                .andExpect(jsonPath("$.error.code").value("ERR003"));
    }

    @Test
    @DisplayName("Should reject upload with oversized payload")
    void shouldRejectUploadWithOversizedPayload() throws Exception {
        // Given
        DocumentUploadRequestDto request = createValidUploadRequest();
        // Create a large payload (6MB)
        byte[] largePayload = new byte[6 * 1024 * 1024];
        String base64Payload = Base64.getEncoder().encodeToString(largePayload);
        request.getDocument().setPayload(base64Payload);
        String requestJson = objectMapper.writeValueAsString(request);

        // When & Then
        mockMvc.perform(post("/document/MLP123")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").value("Failure"))
                .andExpect(jsonPath("$.error.code").value("ERR002"));
    }

    @Test
    @DisplayName("Should reject upload with invalid base64 payload")
    void shouldRejectUploadWithInvalidBase64Payload() throws Exception {
        // Given
        DocumentUploadRequestDto request = createValidUploadRequest();
        request.getDocument().setPayload("This is not valid base64!@#$%");
        String requestJson = objectMapper.writeValueAsString(request);

        // When & Then
        mockMvc.perform(post("/document/MLP123")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").value("Failure"))
                .andExpect(jsonPath("$.error.code").value("ERR001"));
    }

    @Test
    @DisplayName("Should download document successfully with valid document ID")
    void shouldDownloadDocumentSuccessfullyWithValidDocumentId() throws Exception {
        // Given - First upload a document
        DocumentUploadRequestDto uploadRequest = createValidUploadRequest();
        String uploadRequestJson = objectMapper.writeValueAsString(uploadRequest);

        MvcResult uploadResult = mockMvc.perform(post("/document/MLP123")
                .contentType(MediaType.APPLICATION_JSON)
                .content(uploadRequestJson))
                .andExpect(status().isOk())
                .andReturn();

        String uploadResponseJson = uploadResult.getResponse().getContentAsString();
        DocumentUploadResponseDto uploadResponse = objectMapper.readValue(uploadResponseJson, DocumentUploadResponseDto.class);
        String documentId = uploadResponse.getDocumentId();

        // When & Then - Download the document
        mockMvc.perform(get("/document/" + documentId))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.documentId").value(documentId))
                .andExpect(jsonPath("$.payload").exists())
                .andExpect(jsonPath("$.name").value("Sanction_Letter.pdf"))
                .andExpect(jsonPath("$.metadata").exists())
                .andExpect(jsonPath("$.metadata.customerId").value("CUST001"))
                .andExpect(jsonPath("$.metadata.applicationId").value("MLP123"));
    }

    @Test
    @DisplayName("Should return 404 for non-existent document ID")
    void shouldReturn404ForNonExistentDocumentId() throws Exception {
        // Given
        String nonExistentDocumentId = "DOC20250616143025000999ZZ";

        // When & Then
        mockMvc.perform(get("/document/" + nonExistentDocumentId))
                .andExpect(status().isNotFound());
    }

    @Test
    @DisplayName("Should return 404 for invalid document ID format")
    void shouldReturn404ForInvalidDocumentIdFormat() throws Exception {
        // Given
        String invalidDocumentId = "INVALID_ID";

        // When & Then
        mockMvc.perform(get("/document/" + invalidDocumentId))
                .andExpect(status().isNotFound());
    }

    @Test
    @DisplayName("Should return health check information")
    void shouldReturnHealthCheckInformation() throws Exception {
        // When & Then
        mockMvc.perform(get("/document/health"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").value("UP"))
                .andExpect(jsonPath("$.service").value("document-upload-service"))
                .andExpect(jsonPath("$.timestamp").exists())
                .andExpect(jsonPath("$.version").value("1.0.0"));
    }

    @Test
    @DisplayName("Should return service information")
    void shouldReturnServiceInformation() throws Exception {
        // When & Then
        mockMvc.perform(get("/document/info"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.serviceName").value("Document Upload Service"))
                .andExpect(jsonPath("$.version").value("1.0.0"))
                .andExpect(jsonPath("$.maxFileSizeMB").value(5))
                .andExpect(jsonPath("$.supportedFormats").isArray())
                .andExpect(jsonPath("$.retentionPeriodDays").value(2555))
                .andExpect(jsonPath("$.apiVersion").value("v1"));
    }

    @Test
    @DisplayName("Should handle malformed JSON request")
    void shouldHandleMalformedJsonRequest() throws Exception {
        // Given
        String malformedJson = "{ invalid json }";

        // When & Then
        mockMvc.perform(post("/document/MLP123")
                .contentType(MediaType.APPLICATION_JSON)
                .content(malformedJson))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("Should handle empty request body")
    void shouldHandleEmptyRequestBody() throws Exception {
        // When & Then
        mockMvc.perform(post("/document/MLP123")
                .contentType(MediaType.APPLICATION_JSON)
                .content(""))
                .andExpect(status().isBadRequest());
    }

    /**
     * Creates a valid document upload request for testing.
     *
     * @return valid upload request
     */
    private DocumentUploadRequestDto createValidUploadRequest() {
        // Create metadata
        DocumentMetadataDto metadata = new DocumentMetadataDto(
            "CUST001",
            "MLP123",
            "FOUR_WHEELER_ETB_PA",
            "ETB",
            "SanctionLetter",
            "15/06/2025",
            "AdditionalInfo1",
            "AdditionalInfo2"
        );

        // Create document with base64 payload
        String content = "This is a test document content for PDF simulation";
        String base64Payload = Base64.getEncoder().encodeToString(content.getBytes());

        DocumentDto document = new DocumentDto(
            "Sanction_Letter.pdf",
            "maximus/application/MLP123/",
            base64Payload,
            metadata
        );

        return new DocumentUploadRequestDto(document);
    }
}
