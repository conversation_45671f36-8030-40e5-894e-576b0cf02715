package com.tw.documentupload.service;

import com.tw.documentupload.dto.*;
import com.tw.documentupload.dto.external.*;
import com.tw.documentupload.entity.DocumentMetadata;
import com.tw.documentupload.enums.DocumentStatus;
import com.tw.documentupload.repository.DocumentMetadataRepository;
import com.tw.documentupload.service.ExternalDocumentServiceClient;
import com.tw.documentupload.service.ExternalDocumentMapper;
import com.tw.documentupload.util.AuditLogger;
import com.tw.documentupload.util.DocumentIdGenerator;
import com.tw.documentupload.util.ValidationUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Base64;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for DocumentService.
 *
 * Tests the core business logic for document upload and download operations
 * including validation, error handling, and audit logging.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DocumentService Tests")
class DocumentServiceTest {

    @Mock
    private DocumentMetadataRepository documentMetadataRepository;

    @Mock
    private DocumentIdGenerator documentIdGenerator;

    @Mock
    private ValidationUtils validationUtils;

    @Mock
    private AuditLogger auditLogger;

    @Mock
    private DocumentStorageService documentStorageService;

    @Mock
    private ExternalDocumentServiceClient externalDocumentServiceClient;

    @Mock
    private ExternalDocumentMapper externalDocumentMapper;

    private DocumentService documentService;

    @BeforeEach
    void setUp() {
        documentService = new DocumentService(
            documentMetadataRepository,
            documentIdGenerator,
            validationUtils,
            auditLogger,
            documentStorageService,
            externalDocumentServiceClient,
            externalDocumentMapper
        );

        // Set test configuration
        ReflectionTestUtils.setField(documentService, "retentionDays", 2555);
        ReflectionTestUtils.setField(documentService, "maskSensitiveData", true);
    }

    @Test
    @DisplayName("Should upload document successfully with valid request")
    void shouldUploadDocumentSuccessfullyWithValidRequest() {
        // Given
        DocumentUploadRequestDto request = createValidUploadRequest();
        String traceId = "TRC123456";
        String documentId = "DOC20250616143025000123AB";
        String referenceNumber = "REF20250616143025000123ABC";

        // Mock dependencies
        when(documentIdGenerator.generateReferenceNumber()).thenReturn(referenceNumber);
        when(validationUtils.isValidBase64Payload(anyString())).thenReturn(true);
        when(validationUtils.isWithinSizeLimit(anyString())).thenReturn(true);
        when(validationUtils.isValidDocumentName(anyString())).thenReturn(true);
        when(validationUtils.isValidFolderPath(anyString())).thenReturn(true);
        when(validationUtils.isValidDateFormat(anyString())).thenReturn(true);
        when(validationUtils.isValidApplicationId(anyString())).thenReturn(true);
        when(validationUtils.getDecodedSize(anyString())).thenReturn(1024L);

        // Mock external service calls
        ExternalDocumentUploadRequestDto externalRequest = new ExternalDocumentUploadRequestDto();
        ExternalDocumentUploadResponseDto externalResponse = new ExternalDocumentUploadResponseDto(true, documentId, "Success", null, null);
        DocumentUploadResponseDto mappedResponse = DocumentUploadResponseDto.success(documentId, referenceNumber);

        when(externalDocumentMapper.mapToExternalUploadRequest(any())).thenReturn(externalRequest);
        when(externalDocumentServiceClient.uploadDocument(any(), anyString())).thenReturn(externalResponse);
        when(externalDocumentMapper.mapToInternalUploadResponse(any(), anyString())).thenReturn(mappedResponse);
        when(documentMetadataRepository.save(any(DocumentMetadata.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // When
        DocumentUploadResponseDto response = documentService.uploadDocument(request, traceId);

        // Then
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals("Success", response.getStatus());
        assertEquals(documentId, response.getDocumentId());
        assertEquals(referenceNumber, response.getReferenceNumber());

        // Verify interactions
        verify(auditLogger).logRequestInitiation(eq("DOCUMENT_UPLOAD"), eq(traceId), eq(referenceNumber), any());
        verify(auditLogger).logSuccess(eq("DOCUMENT_UPLOAD"), eq(traceId), eq(referenceNumber), any());
        verify(externalDocumentMapper).mapToExternalUploadRequest(request);
        verify(externalDocumentServiceClient).uploadDocument(externalRequest, traceId);
        verify(externalDocumentMapper).mapToInternalUploadResponse(externalResponse, referenceNumber);
        verify(documentMetadataRepository).save(any(DocumentMetadata.class));
    }

    @Test
    @DisplayName("Should reject upload with invalid payload")
    void shouldRejectUploadWithInvalidPayload() {
        // Given
        DocumentUploadRequestDto request = createValidUploadRequest();
        String traceId = "TRC123456";
        String referenceNumber = "REF20250616143025000123ABC";

        // Mock dependencies
        when(documentIdGenerator.generateReferenceNumber()).thenReturn(referenceNumber);
        when(validationUtils.isValidBase64Payload(anyString())).thenReturn(false);

        // When
        DocumentUploadResponseDto response = documentService.uploadDocument(request, traceId);

        // Then
        assertNotNull(response);
        assertTrue(response.isFailure());
        assertEquals("Failure", response.getStatus());
        assertNotNull(response.getError());
        assertEquals("ERR001", response.getError().getCode());

        // Verify interactions
        verify(auditLogger).logRequestInitiation(eq("DOCUMENT_UPLOAD"), eq(traceId), eq(referenceNumber), any());
        verify(auditLogger).logValidationError(eq("DOCUMENT_UPLOAD"), eq(traceId), eq(referenceNumber), anyString(), eq(request));
        verify(documentMetadataRepository, never()).save(any(DocumentMetadata.class));
    }

    @Test
    @DisplayName("Should reject upload with oversized payload")
    void shouldRejectUploadWithOversizedPayload() {
        // Given
        DocumentUploadRequestDto request = createValidUploadRequest();
        String traceId = "TRC123456";
        String referenceNumber = "REF20250616143025000123ABC";

        // Mock dependencies
        when(documentIdGenerator.generateReferenceNumber()).thenReturn(referenceNumber);
        when(validationUtils.isValidBase64Payload(anyString())).thenReturn(true);
        when(validationUtils.isWithinSizeLimit(anyString())).thenReturn(false);

        // When
        DocumentUploadResponseDto response = documentService.uploadDocument(request, traceId);

        // Then
        assertNotNull(response);
        assertTrue(response.isFailure());
        assertEquals("Failure", response.getStatus());
        assertNotNull(response.getError());
        assertEquals("ERR002", response.getError().getCode());

        // Verify interactions
        verify(auditLogger).logValidationError(eq("DOCUMENT_UPLOAD"), eq(traceId), eq(referenceNumber), anyString(), eq(request));
    }

    @Test
    @DisplayName("Should download document successfully with valid document ID")
    void shouldDownloadDocumentSuccessfullyWithValidDocumentId() {
        // Given
        String documentId = "DOC20250616143025000123AB";
        String traceId = "TRC123456";
        DocumentMetadata metadata = createDocumentMetadata();
        String payload = "base64encodedpayload";

        // Mock external service response
        ExternalDocumentMetadataDto externalMetadata = new ExternalDocumentMetadataDto(
            metadata.getCustomerId(),
            metadata.getApplicationId(),
            metadata.getProductCode(),
            metadata.getLoanCategory(),
            metadata.getDescriptor(),
            metadata.getCreatedDate(),
            metadata.getField1(),
            metadata.getField2()
        );
        ExternalDocumentDownloadResponseDto externalResponse = new ExternalDocumentDownloadResponseDto(
            documentId, payload, metadata.getDocumentName(), externalMetadata, java.time.ZonedDateTime.now()
        );

        // Mock internal response
        DocumentMetadataDto internalMetadata = new DocumentMetadataDto(
            metadata.getCustomerId(),
            metadata.getApplicationId(),
            metadata.getProductCode(),
            metadata.getLoanCategory(),
            metadata.getDescriptor(),
            metadata.getCreatedDate(),
            metadata.getField1(),
            metadata.getField2()
        );
        DocumentDownloadResponseDto mappedResponse = new DocumentDownloadResponseDto(
            documentId, payload, metadata.getDocumentName(), internalMetadata,
            metadata.getReferenceNumber(), metadata.getFileSizeBytes()
        );

        // Mock dependencies
        when(documentMetadataRepository.findByDocumentId(documentId)).thenReturn(Optional.of(metadata));
        when(externalDocumentServiceClient.downloadDocument(documentId, traceId)).thenReturn(externalResponse);
        when(externalDocumentMapper.mapToInternalDownloadResponse(externalResponse, metadata.getReferenceNumber(), metadata.getFileSizeBytes())).thenReturn(mappedResponse);
        when(documentMetadataRepository.updateAccessInfo(anyString(), anyInt(), any())).thenReturn(1);

        // When
        DocumentDownloadResponseDto response = documentService.downloadDocument(documentId, traceId);

        // Then
        assertNotNull(response);
        assertEquals(documentId, response.getDocumentId());
        assertEquals(payload, response.getPayload());
        assertEquals(metadata.getDocumentName(), response.getName());
        assertNotNull(response.getMetadata());
        assertEquals(metadata.getCustomerId(), response.getMetadata().getCustomerId());
        assertEquals(metadata.getApplicationId(), response.getMetadata().getApplicationId());

        // Verify interactions
        verify(auditLogger).logRequestInitiation(eq("DOCUMENT_DOWNLOAD"), eq(traceId), isNull(), any());
        verify(auditLogger).logSuccess(eq("DOCUMENT_DOWNLOAD"), eq(traceId), eq(metadata.getReferenceNumber()), any());
        verify(documentMetadataRepository).updateAccessInfo(eq(documentId), eq(1), any());
        verify(externalDocumentServiceClient).downloadDocument(documentId, traceId);
        verify(externalDocumentMapper).mapToInternalDownloadResponse(externalResponse, metadata.getReferenceNumber(), metadata.getFileSizeBytes());
    }

    @Test
    @DisplayName("Should return null for non-existent document")
    void shouldReturnNullForNonExistentDocument() {
        // Given
        String documentId = "DOC20250616143025000999ZZ";
        String traceId = "TRC123456";

        // Mock dependencies
        when(documentMetadataRepository.findByDocumentId(documentId)).thenReturn(Optional.empty());

        // When
        DocumentDownloadResponseDto response = documentService.downloadDocument(documentId, traceId);

        // Then
        assertNull(response);

        // Verify interactions
        verify(auditLogger).logRequestInitiation(eq("DOCUMENT_DOWNLOAD"), eq(traceId), isNull(), any());
        verify(auditLogger).logFailure(eq("DOCUMENT_DOWNLOAD"), eq(traceId), isNull(), eq("ERR101"), anyString(), isNull());
    }

    @Test
    @DisplayName("Should return null for document with non-retrievable status")
    void shouldReturnNullForDocumentWithNonRetrievableStatus() {
        // Given
        String documentId = "DOC20250616143025000123AB";
        String traceId = "TRC123456";
        DocumentMetadata metadata = createDocumentMetadata();
        metadata.setStatus(DocumentStatus.DELETED);

        // Mock dependencies
        when(documentMetadataRepository.findByDocumentId(documentId)).thenReturn(Optional.of(metadata));

        // When
        DocumentDownloadResponseDto response = documentService.downloadDocument(documentId, traceId);

        // Then
        assertNull(response);

        // Verify interactions
        verify(auditLogger).logFailure(eq("DOCUMENT_DOWNLOAD"), eq(traceId), eq(metadata.getReferenceNumber()), eq("ERR101"), anyString(), isNull());
    }

    @Test
    @DisplayName("Should handle external service failure during upload")
    void shouldHandleExternalServiceFailureDuringUpload() {
        // Given
        DocumentUploadRequestDto request = createValidUploadRequest();
        String traceId = "TRC123456";
        String referenceNumber = "REF20250616143025000123ABC";

        // Mock dependencies for validation
        when(documentIdGenerator.generateReferenceNumber()).thenReturn(referenceNumber);
        when(validationUtils.isValidBase64Payload(anyString())).thenReturn(true);
        when(validationUtils.isWithinSizeLimit(anyString())).thenReturn(true);
        when(validationUtils.isValidDocumentName(anyString())).thenReturn(true);
        when(validationUtils.isValidFolderPath(anyString())).thenReturn(true);
        when(validationUtils.isValidDateFormat(anyString())).thenReturn(true);
        when(validationUtils.isValidApplicationId(anyString())).thenReturn(true);

        // Mock external service calls
        ExternalDocumentUploadRequestDto externalRequest = new ExternalDocumentUploadRequestDto();
        ExternalErrorDto externalError = new ExternalErrorDto("ERR107", "Document upload failed");
        ExternalDocumentUploadResponseDto externalResponse = new ExternalDocumentUploadResponseDto(false, null, "Failure", java.time.ZonedDateTime.now(), externalError);
        DocumentUploadResponseDto mappedResponse = DocumentUploadResponseDto.failure("ERR107", "Document upload failed");

        when(externalDocumentMapper.mapToExternalUploadRequest(any())).thenReturn(externalRequest);
        when(externalDocumentServiceClient.uploadDocument(any(), anyString())).thenReturn(externalResponse);
        when(externalDocumentMapper.mapToInternalUploadResponse(any(), anyString())).thenReturn(mappedResponse);

        // When
        DocumentUploadResponseDto response = documentService.uploadDocument(request, traceId);

        // Then
        assertNotNull(response);
        assertTrue(response.isFailure());
        assertEquals("Failure", response.getStatus());
        assertNotNull(response.getError());
        assertEquals("ERR107", response.getError().getCode());

        // Verify interactions
        verify(auditLogger).logFailure(eq("DOCUMENT_UPLOAD"), eq(traceId), eq(referenceNumber), eq("ERR107"), anyString(), isNull());
        verify(documentMetadataRepository, never()).save(any(DocumentMetadata.class));
        verify(externalDocumentMapper).mapToExternalUploadRequest(request);
        verify(externalDocumentServiceClient).uploadDocument(externalRequest, traceId);
        verify(externalDocumentMapper).mapToInternalUploadResponse(externalResponse, referenceNumber);
    }

    /**
     * Creates a valid document upload request for testing.
     *
     * @return valid upload request
     */
    private DocumentUploadRequestDto createValidUploadRequest() {
        DocumentMetadataDto metadata = new DocumentMetadataDto(
            "CUST001",
            "MLP123",
            "FOUR_WHEELER_ETB_PA",
            "ETB",
            "SanctionLetter",
            "15/06/2025",
            "AdditionalInfo1",
            "AdditionalInfo2"
        );

        String content = "This is a test document content";
        String base64Payload = Base64.getEncoder().encodeToString(content.getBytes());

        DocumentDto document = new DocumentDto(
            "Sanction_Letter.pdf",
            "maximus/application/MLP123/",
            base64Payload,
            metadata
        );

        return new DocumentUploadRequestDto(document);
    }

    /**
     * Creates a document metadata entity for testing.
     *
     * @return document metadata
     */
    private DocumentMetadata createDocumentMetadata() {
        DocumentMetadata metadata = new DocumentMetadata(
            "DOC20250616143025000123AB",
            "REF20250616143025000123ABC",
            "Sanction_Letter.pdf",
            "maximus/application/MLP123/",
            1024L,
            "CUST001",
            "MLP123",
            "FOUR_WHEELER_ETB_PA",
            "ETB",
            "SanctionLetter",
            "15/06/2025"
        );
        metadata.setStatus(DocumentStatus.ACTIVE);
        return metadata;
    }
}
