spring:

  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
    driver-class-name: org.h2.Driver

  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect

  flyway:
    enabled: false

logging:
  level:
    com.tw.documentupload: DEBUG
    org.springframework.web: INFO
    org.hibernate: INFO

app:
  document:
    max-size-mb: 5
    allowed-extensions: pdf,jpg,jpeg,png,doc,docx
    retention-days: 2555

  security:
    mask-sensitive-data: true
    log-request-response: true

  storage:
    base-path: ./test-storage
    create-directories: true
