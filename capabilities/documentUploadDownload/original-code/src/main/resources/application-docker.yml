server:
  port: 8080
  servlet:
    context-path: /api/document-upload-download

spring:
  application:
    name: document-upload-service
  
  datasource:
    url: ${SPRING_DATASOURCE_URL:***********************************************}
    username: ${SPRING_DATASOURCE_USERNAME:postgres}
    password: ${SPRING_DATASOURCE_PASSWORD:postgres}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
        use_sql_comments: false
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
  
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 5MB

# Logging configuration for Docker
logging:
  level:
    com.tw.documentupload: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: /app/logs/document-upload-service.log

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  info:
    env:
      enabled: true



# Application specific configuration
app:
  document:
    max-size-mb: 5
    allowed-extensions: pdf,jpg,jpeg,png,doc,docx
    retention-days: 2555
  
  security:
    mask-sensitive-data: true
    log-request-response: false  # Reduced logging in Docker
  
  retry:
    max-attempts: 3
    delay-ms: 1000
    backoff-multiplier: 2.0
  
  storage:
    base-path: /app/document-storage
    create-directories: true
