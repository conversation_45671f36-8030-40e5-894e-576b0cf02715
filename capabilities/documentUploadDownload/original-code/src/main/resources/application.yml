server:
  port: ${SERVER_PORT:8116}
  servlet:
    context-path: /api/document-upload-download

spring:
  application:
    name: document-upload-service

  datasource:
    url: ************************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true

  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 5MB

# Logging configuration
logging:
  level:
    com.tw.documentupload: INFO
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: logs/document-upload-service.log

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  info:
    env:
      enabled: true



# Application specific configuration
app:
  document:
    max-size-mb: 5
    allowed-extensions: pdf,jpg,jpeg,png,doc,docx
    retention-days: 2555  # 7 years

  security:
    mask-sensitive-data: true
    log-request-response: true

  retry:
    max-attempts: 3
    delay-ms: 1000
    backoff-multiplier: 2.0

# SpringDoc OpenAPI configuration
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    try-it-out-enabled: true
  show-actuator: false

# External services configuration
external-services:
  document-service:
    url: http://localhost:8083/api/document-upload-download/internal/documents  # Mock service for development
    timeout: 30s
    retry:
      max-attempts: 3
      delay-ms: 1000

---
# Test profile
spring:
  config:
    activate:
      on-profile: test

  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
    driver-class-name: org.h2.Driver

  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true

  flyway:
    enabled: false

logging:
  level:
    com.tw.documentupload: DEBUG
    org.springframework.web: INFO

# External services for test profile
external-services:
  document-service:
    url: http://localhost:8083/api/document-upload-download/internal/documents  # Mock service for testing
    timeout: 10s
    retry:
      max-attempts: 2
      delay-ms: 500

---
# Development profile
spring:
  config:
    activate:
      on-profile: dev

  datasource:
    url: jdbc:h2:mem:devdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
    driver-class-name: org.h2.Driver

  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true

  flyway:
    enabled: false

  h2:
    console:
      enabled: true
      path: /h2-console

logging:
  level:
    com.tw.documentupload: DEBUG
    org.springframework.web: DEBUG

---
# Production profile
spring:
  config:
    activate:
      on-profile: prod

# External services for production
external-services:
  document-service:
    url: https://api.thirdparty.com/documents  # Real third-party service
    timeout: 30s
    retry:
      max-attempts: 3
      delay-ms: 1000

logging:
  level:
    com.tw.documentupload: INFO
    org.springframework.web: WARN
