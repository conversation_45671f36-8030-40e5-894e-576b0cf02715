-- Create document_metadata table for storing document information and metadata
CREATE TABLE document_metadata (
    id BIGSERIAL PRIMARY KEY,
    document_id VARCHAR(50) NOT NULL UNIQUE,
    reference_number VARCHAR(50) NOT NULL UNIQUE,
    
    -- Document basic information
    document_name VARCHAR(50) NOT NULL,
    folder_path VARCHAR(255) NOT NULL,
    file_size_bytes BIGINT NOT NULL,
    
    -- Metadata fields
    customer_id VARCHAR(50) NOT NULL,
    application_id VARCHAR(50) NOT NULL,
    product_code VARCHAR(50) NOT NULL,
    loan_category VARCHAR(50) NOT NULL,
    descriptor VARCHAR(50) NOT NULL,
    created_date VARCHAR(50) NOT NULL,
    field1 VARCHAR(50),
    field2 VARCHAR(50),
    
    -- Audit fields
    upload_timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_accessed_timestamp TIMESTAMP WITH TIME ZONE,
    access_count INTEGER NOT NULL DEFAULT 0,
    
    -- Status and tracking
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    retention_until_date DATE,
    
    -- System audit fields
    created_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100),
    updated_at TIMESTAMP WITH TIME ZONE,
    version INTEGER NOT NULL DEFAULT 1
);

-- Create indexes for performance
CREATE INDEX idx_document_metadata_document_id ON document_metadata(document_id);
CREATE INDEX idx_document_metadata_reference_number ON document_metadata(reference_number);
CREATE INDEX idx_document_metadata_customer_id ON document_metadata(customer_id);
CREATE INDEX idx_document_metadata_application_id ON document_metadata(application_id);
CREATE INDEX idx_document_metadata_upload_timestamp ON document_metadata(upload_timestamp);
CREATE INDEX idx_document_metadata_status ON document_metadata(status);
CREATE INDEX idx_document_metadata_retention_until_date ON document_metadata(retention_until_date);

-- Create composite indexes for common query patterns
CREATE INDEX idx_document_metadata_customer_application ON document_metadata(customer_id, application_id);
CREATE INDEX idx_document_metadata_application_descriptor ON document_metadata(application_id, descriptor);

-- Add comments for documentation
COMMENT ON TABLE document_metadata IS 'Stores metadata and tracking information for uploaded documents';
COMMENT ON COLUMN document_metadata.document_id IS 'Unique identifier for the document (DOC prefix)';
COMMENT ON COLUMN document_metadata.reference_number IS 'Unique reference number for audit tracking';
COMMENT ON COLUMN document_metadata.document_name IS 'Original name of the uploaded document';
COMMENT ON COLUMN document_metadata.folder_path IS 'Storage folder path for the document';
COMMENT ON COLUMN document_metadata.file_size_bytes IS 'Size of the document in bytes';
COMMENT ON COLUMN document_metadata.customer_id IS 'Unique customer identifier';
COMMENT ON COLUMN document_metadata.application_id IS 'Loan application identifier';
COMMENT ON COLUMN document_metadata.product_code IS 'Product code (e.g., FOUR_WHEELER_ETB_PA)';
COMMENT ON COLUMN document_metadata.loan_category IS 'Loan category (e.g., ETB, NTB)';
COMMENT ON COLUMN document_metadata.descriptor IS 'Document type descriptor (e.g., SanctionLetter, KYC)';
COMMENT ON COLUMN document_metadata.created_date IS 'Document creation date in DD/MM/YYYY format';
COMMENT ON COLUMN document_metadata.field1 IS 'Additional metadata field for future use';
COMMENT ON COLUMN document_metadata.field2 IS 'Additional metadata field for future use';
COMMENT ON COLUMN document_metadata.upload_timestamp IS 'Timestamp when document was uploaded';
COMMENT ON COLUMN document_metadata.last_accessed_timestamp IS 'Timestamp when document was last accessed';
COMMENT ON COLUMN document_metadata.access_count IS 'Number of times document has been accessed';
COMMENT ON COLUMN document_metadata.status IS 'Document status (ACTIVE, ARCHIVED, DELETED)';
COMMENT ON COLUMN document_metadata.retention_until_date IS 'Date until which document should be retained';
COMMENT ON COLUMN document_metadata.version IS 'Version number for optimistic locking';
