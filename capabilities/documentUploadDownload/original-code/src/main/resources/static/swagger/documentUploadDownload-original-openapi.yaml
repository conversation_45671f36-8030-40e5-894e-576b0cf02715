openapi: 3.0.3
info:
  title: Document Upload Download Original API
  description: |
    A comprehensive document management API that enables secure, structured, and metadata-rich document uploads and retrievals for loan processing ecosystems.

    This API supports:
    - Structured document uploads with comprehensive metadata
    - Document retrieval using document IDs
    - Size constraints (≤5MB) and format validation
    - Detailed audit logging and traceability
    - Support for various document formats (PDF, JPG, JPEG, PNG, DOC, DOCX)
    - Health check endpoints for service monitoring
    - Service information endpoints for configuration details

    The API serves as a core enabler for digital document management across customer journeys in lending applications.
  version: 1.0.0
  contact:
    name: TW Development Team
    email: <EMAIL>
  license:
    name: Proprietary
    url: https://tw.com/license

servers:
  - url: http://localhost:8116/api/document-upload-download
    description: Original service server

paths:
  /document/{applicationId}:
    post:
      summary: Upload a document
      description: |
        Uploads a document with comprehensive metadata for a specific loan application.
        
        The document payload should be base64 encoded. The API validates:
        - Document size (max 5MB)
        - File format (PDF, JPG, JPEG, PNG, DOC, DOCX)
        - Required metadata fields
        - Application ID format
        
        Returns a unique document ID for future retrieval operations.
      operationId: uploadDocument
      tags:
        - Document Management
      parameters:
        - name: applicationId
          in: path
          required: true
          description: The loan application identifier
          schema:
            type: string
            pattern: '^[A-Z0-9]{3,20}$'
            example: "MLP123"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentUploadRequest'
            examples:
              sanctionLetter:
                summary: Sanction Letter Upload
                value:
                  document:
                    name: "Sanction_Letter.pdf"
                    folder: "maximus/application/MLP123/"
                    payload: "JVBERi0xLjQKJcOkw7zDtsO..."
                    metadata:
                      customerId: "CUST001"
                      applicationId: "MLP123"
                      productCode: "FOUR_WHEELER_ETB_PA"
                      loanCategory: "ETB"
                      descriptor: "SanctionLetter"
                      createdDate: "05/06/2025"
                      field1: "AdditionalInfo1"
                      field2: "AdditionalInfo2"
      responses:
        '200':
          description: Document uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentUploadResponse'
              examples:
                success:
                  summary: Successful upload
                  value:
                    documentId: "DOC123456"
                    status: "Success"
                    timestamp: "2025-06-16T10:00:00Z"
                    referenceNumber: "REF789012"
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentUploadResponse'
              examples:
                validationError:
                  summary: Validation error
                  value:
                    status: "Failure"
                    timestamp: "2025-06-16T10:00:00Z"
                    error:
                      code: "VALIDATION_ERROR"
                      message: "Document validation failed"
                      description: "Document name is mandatory"
                      traceId: "trace-123"
        '413':
          description: Payload too large
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentUploadResponse'
              examples:
                fileTooLarge:
                  summary: File size exceeded
                  value:
                    status: "Failure"
                    timestamp: "2025-06-16T10:00:00Z"
                    error:
                      code: "FILE_SIZE_EXCEEDED"
                      message: "Document size exceeds maximum allowed limit"
                      description: "Maximum file size is 5MB"
                      traceId: "trace-456"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentUploadResponse'
              examples:
                serverError:
                  summary: Server error
                  value:
                    status: "Failure"
                    timestamp: "2025-06-16T10:00:00Z"
                    error:
                      code: "INTERNAL_ERROR"
                      message: "Internal server error occurred"
                      description: "Unable to process document upload"
                      traceId: "trace-789"

  /document/{documentId}:
    get:
      summary: Download a document
      description: |
        Retrieves a previously uploaded document using its unique document ID.
        
        Returns the document payload as base64 encoded string along with
        the original metadata and file information.
      operationId: downloadDocument
      tags:
        - Document Management
      parameters:
        - name: documentId
          in: path
          required: true
          description: The unique document identifier returned from upload
          schema:
            type: string
            pattern: '^DOC[A-Z0-9]{6,20}$'
            example: "DOC123456"
      responses:
        '200':
          description: Document retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentDownloadResponse'
              examples:
                success:
                  summary: Successful download
                  value:
                    documentId: "DOC123456"
                    payload: "JVBERi0xLjQKJcOkw7zDtsO..."
                    name: "Sanction_Letter.pdf"
                    timestamp: "2025-06-16T10:00:00Z"
                    referenceNumber: "REF789012"
                    fileSizeBytes: 1048576
                    metadata:
                      customerId: "CUST001"
                      applicationId: "MLP123"
                      productCode: "FOUR_WHEELER_ETB_PA"
                      loanCategory: "ETB"
                      descriptor: "SanctionLetter"
                      createdDate: "05/06/2025"
                      field1: "AdditionalInfo1"
                      field2: "AdditionalInfo2"
        '404':
          description: Document not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                notFound:
                  summary: Document not found
                  value:
                    error:
                      code: "DOCUMENT_NOT_FOUND"
                      message: "Document not found"
                      description: "No document found with the provided ID"
                      traceId: "trace-404"
                    timestamp: "2025-06-16T10:00:00Z"
        '410':
          description: Document expired
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                expired:
                  summary: Document expired
                  value:
                    error:
                      code: "DOCUMENT_EXPIRED"
                      message: "Document has expired"
                      description: "Document retention period has exceeded"
                      traceId: "trace-410"
                    timestamp: "2025-06-16T10:00:00Z"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /document/info:
    get:
      summary: Get service information
      description: |
        Returns service configuration and operational information including
        supported file formats, size limits, and API version details.
      operationId: getServiceInfo
      tags:
        - Service Information
      responses:
        '200':
          description: Service information retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceInfo'
              examples:
                serviceInfo:
                  summary: Service information
                  value:
                    serviceName: "Document Upload Service"
                    version: "1.0.0"
                    maxFileSizeMB: 5
                    supportedFormats: ["pdf", "jpg", "jpeg", "png", "doc", "docx"]
                    retentionPeriodDays: 2555
                    apiVersion: "v1"

  /document/health:
    get:
      summary: Document service health check
      description: Returns the health status of the document service
      operationId: documentServiceHealthCheck
      tags:
        - Health Check
      responses:
        '200':
          description: Document service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentServiceHealthStatus'
              examples:
                healthy:
                  summary: Healthy document service
                  value:
                    status: "UP"
                    service: "document-upload-service"
                    timestamp: 1687776000000
                    version: "1.0.0"

  /v1/document-upload-download/original/health:
    get:
      summary: Health check endpoint
      description: Returns the health status of the document upload download service
      operationId: healthCheck
      tags:
        - Health Check
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatus'
              examples:
                healthy:
                  summary: Healthy service
                  value:
                    status: "UP"
                    service: "Document Upload Download Original"
                    timestamp: "2025-06-16T10:00:00"
                    version: "1.0.0"

components:
  schemas:
    DocumentUploadRequest:
      type: object
      required:
        - document
      properties:
        document:
          $ref: '#/components/schemas/Document'
      example:
        document:
          name: "Sanction_Letter.pdf"
          folder: "maximus/application/MLP123/"
          payload: "JVBERi0xLjQKJcOkw7zDtsO..."
          metadata:
            customerId: "CUST001"
            applicationId: "MLP123"
            productCode: "FOUR_WHEELER_ETB_PA"
            loanCategory: "ETB"
            descriptor: "SanctionLetter"
            createdDate: "05/06/2025"
            field1: "AdditionalInfo1"
            field2: "AdditionalInfo2"

    Document:
      type: object
      required:
        - name
        - folder
        - payload
        - metadata
      properties:
        name:
          type: string
          maxLength: 50
          description: The document file name
          example: "Sanction_Letter.pdf"
        folder:
          type: string
          maxLength: 255
          description: The folder path where the document should be stored
          example: "maximus/application/MLP123/"
        payload:
          type: string
          format: byte
          description: Base64 encoded document content
          example: "JVBERi0xLjQKJcOkw7zDtsO..."
        metadata:
          $ref: '#/components/schemas/DocumentMetadata'

    DocumentMetadata:
      type: object
      required:
        - customerId
        - applicationId
        - productCode
        - loanCategory
        - descriptor
        - createdDate
      properties:
        customerId:
          type: string
          maxLength: 50
          description: Unique customer identifier
          example: "CUST001"
        applicationId:
          type: string
          maxLength: 50
          description: Loan application identifier
          example: "MLP123"
        productCode:
          type: string
          maxLength: 50
          description: Product code for the loan
          example: "FOUR_WHEELER_ETB_PA"
        loanCategory:
          type: string
          maxLength: 50
          description: Category of the loan
          example: "ETB"
        descriptor:
          type: string
          maxLength: 50
          description: Document type descriptor
          example: "SanctionLetter"
        createdDate:
          type: string
          pattern: '^\\d{2}/\\d{2}/\\d{4}$'
          maxLength: 50
          description: Document creation date in DD/MM/YYYY format
          example: "05/06/2025"
        field1:
          type: string
          maxLength: 50
          description: Additional metadata field 1
          example: "AdditionalInfo1"
        field2:
          type: string
          maxLength: 50
          description: Additional metadata field 2
          example: "AdditionalInfo2"

    DocumentUploadResponse:
      type: object
      properties:
        documentId:
          type: string
          description: Unique identifier for the uploaded document
          example: "DOC123456"
        status:
          type: string
          enum: [Success, Failure]
          description: Upload operation status
          example: "Success"
        timestamp:
          type: string
          format: date-time
          description: Response timestamp in ISO 8601 format
          example: "2025-06-16T10:00:00Z"
        referenceNumber:
          type: string
          description: Reference number for tracking purposes
          example: "REF789012"
        error:
          $ref: '#/components/schemas/Error'

    DocumentDownloadResponse:
      type: object
      required:
        - documentId
        - payload
        - name
        - metadata
        - timestamp
      properties:
        documentId:
          type: string
          description: Unique document identifier
          example: "DOC123456"
        payload:
          type: string
          format: byte
          description: Base64 encoded document content
          example: "JVBERi0xLjQKJcOkw7zDtsO..."
        name:
          type: string
          description: Original document file name
          example: "Sanction_Letter.pdf"
        metadata:
          $ref: '#/components/schemas/DocumentMetadata'
        timestamp:
          type: string
          format: date-time
          description: Response timestamp in ISO 8601 format
          example: "2025-06-16T10:00:00Z"
        referenceNumber:
          type: string
          description: Reference number for tracking purposes
          example: "REF789012"
        fileSizeBytes:
          type: integer
          format: int64
          description: File size in bytes
          example: 1048576

    Error:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: string
          description: Error code identifier
          example: "VALIDATION_ERROR"
        message:
          type: string
          description: Human-readable error message
          example: "Document validation failed"
        description:
          type: string
          description: Detailed error description
          example: "Document name is mandatory"
        timestamp:
          type: string
          format: date-time
          description: Error timestamp in ISO 8601 format
          example: "2025-06-16T10:00:00Z"
        traceId:
          type: string
          description: Trace identifier for debugging
          example: "trace-123"

    ErrorResponse:
      type: object
      required:
        - error
        - timestamp
      properties:
        error:
          $ref: '#/components/schemas/Error'
        timestamp:
          type: string
          format: date-time
          description: Response timestamp in ISO 8601 format
          example: "2025-06-16T10:00:00Z"

    ServiceInfo:
      type: object
      required:
        - serviceName
        - version
        - maxFileSizeMB
        - supportedFormats
        - retentionPeriodDays
        - apiVersion
      properties:
        serviceName:
          type: string
          description: Name of the service
          example: "Document Upload Service"
        version:
          type: string
          description: Service version
          example: "1.0.0"
        maxFileSizeMB:
          type: integer
          description: Maximum file size allowed in MB
          example: 5
        supportedFormats:
          type: array
          items:
            type: string
          description: List of supported file formats
          example: ["pdf", "jpg", "jpeg", "png", "doc", "docx"]
        retentionPeriodDays:
          type: integer
          description: Document retention period in days
          example: 2555
        apiVersion:
          type: string
          description: API version
          example: "v1"

    HealthStatus:
      type: object
      required:
        - status
        - service
        - timestamp
        - version
      properties:
        status:
          type: string
          enum: [UP, DOWN]
          description: Service health status
          example: "UP"
        service:
          type: string
          description: Service name
          example: "Document Upload Download Original"
        timestamp:
          type: string
          format: date-time
          description: Health check timestamp
          example: "2025-06-16T10:00:00"
        version:
          type: string
          description: Service version
          example: "1.0.0"

    DocumentServiceHealthStatus:
      type: object
      required:
        - status
        - service
        - timestamp
        - version
      properties:
        status:
          type: string
          enum: [UP, DOWN]
          description: Document service health status
          example: "UP"
        service:
          type: string
          description: Document service name
          example: "document-upload-service"
        timestamp:
          type: integer
          format: int64
          description: Health check timestamp (Unix timestamp)
          example: 1687776000000
        version:
          type: string
          description: Document service version
          example: "1.0.0"

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for API authentication

  parameters:
    ApplicationId:
      name: applicationId
      in: path
      required: true
      description: The loan application identifier
      schema:
        type: string
        pattern: '^[A-Z0-9]{3,20}$'
        example: "MLP123"

    DocumentId:
      name: documentId
      in: path
      required: true
      description: The unique document identifier
      schema:
        type: string
        pattern: '^DOC[A-Z0-9]{6,20}$'
        example: "DOC123456"

  responses:
    BadRequest:
      description: Bad request - validation errors
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DocumentUploadResponse'

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

tags:
  - name: Document Management
    description: Operations for uploading and downloading documents
  - name: Service Information
    description: Service configuration and status information
  - name: Health Check
    description: Health monitoring endpoints
