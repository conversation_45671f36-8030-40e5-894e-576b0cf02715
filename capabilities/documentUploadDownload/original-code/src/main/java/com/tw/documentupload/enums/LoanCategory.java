package com.tw.documentupload.enums;

/**
 * Enumeration representing different loan categories supported by the system.
 * 
 * This enum defines the various types of loan categories that can be associated
 * with document uploads, supporting business logic and validation rules.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public enum LoanCategory {
    
    /**
     * Existing to Bank (ETB) - Customers who already have a relationship with the bank.
     */
    ETB("ETB", "Existing to Bank", "Customers with existing bank relationship"),
    
    /**
     * New to Bank (NTB) - First-time customers with no prior bank relationship.
     */
    NTB("NTB", "New to Bank", "First-time customers"),
    
    /**
     * High Value Customer (HVC) - Premium customers with high-value transactions.
     */
    HVC("HVC", "High Value Customer", "Premium customers with high-value transactions"),
    
    /**
     * Corporate Banking (CORP) - Corporate and business customers.
     */
    CORP("CORP", "Corporate", "Corporate and business customers"),
    
    /**
     * Small and Medium Enterprise (SME) - Small and medium business customers.
     */
    SME("SME", "Small Medium Enterprise", "Small and medium business customers"),
    
    /**
     * Priority Banking (PB) - Priority banking customers.
     */
    PB("PB", "Priority Banking", "Priority banking customers"),
    
    /**
     * Wealth Management (WM) - Wealth management customers.
     */
    WM("WM", "Wealth Management", "Wealth management customers");
    
    private final String code;
    private final String displayName;
    private final String description;
    
    /**
     * Constructor for LoanCategory enum.
     * 
     * @param code the category code
     * @param displayName human-readable name for the category
     * @param description detailed description of the category
     */
    LoanCategory(String code, String displayName, String description) {
        this.code = code;
        this.displayName = displayName;
        this.description = description;
    }
    
    /**
     * Gets the category code.
     * 
     * @return the category code
     */
    public String getCode() {
        return code;
    }
    
    /**
     * Gets the human-readable display name for the category.
     * 
     * @return the display name
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * Gets the detailed description of the category.
     * 
     * @return the description
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Finds a LoanCategory by its code.
     * 
     * @param code the category code to search for
     * @return the matching LoanCategory, or null if not found
     */
    public static LoanCategory fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (LoanCategory category : values()) {
            if (category.getCode().equalsIgnoreCase(code)) {
                return category;
            }
        }
        return null;
    }
    
    /**
     * Checks if the given code represents a valid loan category.
     * 
     * @param code the code to validate
     * @return true if the code is valid, false otherwise
     */
    public static boolean isValidCode(String code) {
        return fromCode(code) != null;
    }
}
