package com.tw.documentupload.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * OpenAPI configuration for Document Upload Download Original API.
 *
 * This configuration provides the OpenAPI specification for the original service.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-23
 */
@Configuration
public class OpenApiConfig implements WebMvcConfigurer {

    @Value("${server.port:8116}")
    private String serverPort;

    @Value("${server.servlet.context-path:/api/document-upload-download}")
    private String contextPath;

    /**
     * Configure resource handlers to serve static OpenAPI YAML files.
     *
     * @param registry the resource handler registry
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/swagger/**")
                .addResourceLocations("classpath:/static/swagger/");
    }

    /**
     * Configures the OpenAPI specification for the Document Upload Download service.
     *
     * @return OpenAPI configuration
     */
    @Bean
    public OpenAPI documentUploadDownloadOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Document Upload Download Original API")
                        .description("""
                                A comprehensive document management API that enables secure, structured, and metadata-rich document uploads and retrievals for loan processing ecosystems.

                                This API supports:
                                - Structured document uploads with comprehensive metadata
                                - Document retrieval using document IDs
                                - Size constraints (≤5MB) and format validation
                                - Detailed audit logging and traceability
                                - Support for various document formats (PDF, JPG, JPEG, PNG, DOC, DOCX)
                                - Health check endpoints for service monitoring
                                - Service information endpoints for configuration details

                                The API serves as a core enabler for digital document management across customer journeys in lending applications.
                                """)
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("TW Development Team")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("Proprietary")
                                .url("https://tw.com/license")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + contextPath)
                                .description("Original service server")
                ));
    }
}
