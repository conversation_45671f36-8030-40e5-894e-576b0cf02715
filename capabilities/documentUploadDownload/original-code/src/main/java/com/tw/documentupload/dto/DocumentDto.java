package com.tw.documentupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.Objects;

/**
 * Data Transfer Object for document information in upload requests.
 * 
 * This DTO represents the document data including the file payload,
 * name, folder path, and associated metadata for upload operations.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class DocumentDto {
    
    @NotBlank(message = "Document name is mandatory")
    @Size(max = 50, message = "Document name must not exceed 50 characters")
    @JsonProperty("name")
    private String name;
    
    @NotBlank(message = "Folder path is mandatory")
    @Size(max = 255, message = "Folder path must not exceed 255 characters")
    @JsonProperty("folder")
    private String folder;
    
    @NotBlank(message = "Document payload is mandatory")
    @JsonProperty("payload")
    private String payload;
    
    @Valid
    @NotNull(message = "Document metadata is mandatory")
    @JsonProperty("metadata")
    private DocumentMetadataDto metadata;
    
    /**
     * Default constructor.
     */
    public DocumentDto() {
    }
    
    /**
     * Constructor with all fields.
     * 
     * @param name document name
     * @param folder folder path for storage
     * @param payload base64 encoded document payload
     * @param metadata document metadata
     */
    public DocumentDto(String name, String folder, String payload, DocumentMetadataDto metadata) {
        this.name = name;
        this.folder = folder;
        this.payload = payload;
        this.metadata = metadata;
    }
    
    // Getters and Setters
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getFolder() {
        return folder;
    }
    
    public void setFolder(String folder) {
        this.folder = folder;
    }
    
    public String getPayload() {
        return payload;
    }
    
    public void setPayload(String payload) {
        this.payload = payload;
    }
    
    public DocumentMetadataDto getMetadata() {
        return metadata;
    }
    
    public void setMetadata(DocumentMetadataDto metadata) {
        this.metadata = metadata;
    }
    
    /**
     * Gets the estimated size of the payload in bytes.
     * This is an approximation based on base64 encoding.
     * 
     * @return estimated payload size in bytes
     */
    public long getEstimatedPayloadSizeBytes() {
        if (payload == null || payload.isEmpty()) {
            return 0;
        }
        // Base64 encoding increases size by approximately 33%
        // So to get original size: (base64Length * 3) / 4
        return (long) ((payload.length() * 3.0) / 4.0);
    }
    
    /**
     * Checks if the document payload appears to be valid base64.
     * This is a basic validation and doesn't guarantee the payload is valid.
     * 
     * @return true if payload appears to be base64, false otherwise
     */
    public boolean isValidBase64Format() {
        if (payload == null || payload.isEmpty()) {
            return false;
        }
        
        // Basic base64 pattern check
        return payload.matches("^[A-Za-z0-9+/]*={0,2}$") && payload.length() % 4 == 0;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DocumentDto that = (DocumentDto) o;
        return Objects.equals(name, that.name) &&
               Objects.equals(folder, that.folder) &&
               Objects.equals(payload, that.payload) &&
               Objects.equals(metadata, that.metadata);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(name, folder, payload, metadata);
    }
    
    @Override
    public String toString() {
        return "DocumentDto{" +
                "name='" + name + '\'' +
                ", folder='" + folder + '\'' +
                ", payloadSize=" + (payload != null ? payload.length() : 0) + " chars" +
                ", estimatedSizeBytes=" + getEstimatedPayloadSizeBytes() +
                ", metadata=" + metadata +
                '}';
    }
}
