package com.tw.documentupload.service;

import com.tw.documentupload.dto.*;
import com.tw.documentupload.dto.external.*;
import org.springframework.stereotype.Service;

/**
 * Mapper service for converting between internal and external DTOs.
 * 
 * This service handles the mapping between the internal document DTOs
 * used by the original service and the external DTOs used by the
 * external document service (mock or third-party).
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Service
public class ExternalDocumentMapper {
    
    /**
     * Maps internal upload request to external upload request.
     * 
     * @param internalRequest the internal upload request
     * @return external upload request
     */
    public ExternalDocumentUploadRequestDto mapToExternalUploadRequest(DocumentUploadRequestDto internalRequest) {
        DocumentDto document = internalRequest.getDocument();
        DocumentMetadataDto metadata = document.getMetadata();
        
        ExternalDocumentMetadataDto externalMetadata = new ExternalDocumentMetadataDto(
            metadata.getCustomerId(),
            metadata.getApplicationId(),
            metadata.getProductCode(),
            metadata.getLoanCategory(),
            metadata.getDescriptor(),
            metadata.getCreatedDate(),
            metadata.getField1(),
            metadata.getField2()
        );
        
        ExternalDocumentDto externalDocument = new ExternalDocumentDto(
            document.getName(),
            document.getFolder(),
            document.getPayload(),
            externalMetadata
        );
        
        return new ExternalDocumentUploadRequestDto(externalDocument);
    }
    
    /**
     * Maps external upload response to internal upload response.
     * 
     * @param externalResponse the external upload response
     * @param referenceNumber the internal reference number
     * @return internal upload response
     */
    public DocumentUploadResponseDto mapToInternalUploadResponse(ExternalDocumentUploadResponseDto externalResponse, 
                                                               String referenceNumber) {
        if (externalResponse.isSuccess()) {
            return DocumentUploadResponseDto.success(externalResponse.getDocumentId(), referenceNumber);
        } else {
            ExternalErrorDto externalError = externalResponse.getError();
            if (externalError != null) {
                return DocumentUploadResponseDto.failure(
                    externalError.getCode(),
                    externalError.getMessage()
                );
            } else {
                return DocumentUploadResponseDto.failure(
                    "ERR107",
                    "External service returned failure without error details"
                );
            }
        }
    }
    
    /**
     * Maps external download response to internal download response.
     * 
     * @param externalResponse the external download response
     * @param referenceNumber the internal reference number
     * @param fileSizeBytes the file size in bytes
     * @return internal download response
     */
    public DocumentDownloadResponseDto mapToInternalDownloadResponse(ExternalDocumentDownloadResponseDto externalResponse,
                                                                   String referenceNumber, long fileSizeBytes) {
        ExternalDocumentMetadataDto externalMetadata = externalResponse.getMetadata();
        
        DocumentMetadataDto internalMetadata = new DocumentMetadataDto(
            externalMetadata.getCustomerId(),
            externalMetadata.getApplicationId(),
            externalMetadata.getProductCode(),
            externalMetadata.getLoanCategory(),
            externalMetadata.getDescriptor(),
            externalMetadata.getCreatedDate(),
            externalMetadata.getField1(),
            externalMetadata.getField2()
        );
        
        return new DocumentDownloadResponseDto(
            externalResponse.getDocumentId(),
            externalResponse.getPayload(),
            externalResponse.getName(),
            internalMetadata,
            referenceNumber,
            fileSizeBytes
        );
    }
}
