package com.tw.documentupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * Data Transfer Object for document download responses.
 * 
 * This DTO represents the response structure for document download operations,
 * including the document payload and associated metadata.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class DocumentDownloadResponseDto {
    
    @JsonProperty("documentId")
    private String documentId;
    
    @JsonProperty("payload")
    private String payload;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("metadata")
    private DocumentMetadataDto metadata;
    
    @JsonProperty("timestamp")
    private ZonedDateTime timestamp;
    
    @JsonProperty("referenceNumber")
    private String referenceNumber;
    
    @JsonProperty("fileSizeBytes")
    private Long fileSizeBytes;
    
    /**
     * Default constructor.
     */
    public DocumentDownloadResponseDto() {
        this.timestamp = ZonedDateTime.now();
    }
    
    /**
     * Constructor with essential fields.
     * 
     * @param documentId the document identifier
     * @param payload the base64 encoded document payload
     * @param name the document name
     * @param metadata the document metadata
     */
    public DocumentDownloadResponseDto(String documentId, String payload, String name, 
                                     DocumentMetadataDto metadata) {
        this();
        this.documentId = documentId;
        this.payload = payload;
        this.name = name;
        this.metadata = metadata;
    }
    
    /**
     * Constructor with all fields.
     * 
     * @param documentId the document identifier
     * @param payload the base64 encoded document payload
     * @param name the document name
     * @param metadata the document metadata
     * @param referenceNumber the reference number for tracking
     * @param fileSizeBytes the file size in bytes
     */
    public DocumentDownloadResponseDto(String documentId, String payload, String name, 
                                     DocumentMetadataDto metadata, String referenceNumber, 
                                     Long fileSizeBytes) {
        this(documentId, payload, name, metadata);
        this.referenceNumber = referenceNumber;
        this.fileSizeBytes = fileSizeBytes;
    }
    
    // Getters and Setters
    
    public String getDocumentId() {
        return documentId;
    }
    
    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }
    
    public String getPayload() {
        return payload;
    }
    
    public void setPayload(String payload) {
        this.payload = payload;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public DocumentMetadataDto getMetadata() {
        return metadata;
    }
    
    public void setMetadata(DocumentMetadataDto metadata) {
        this.metadata = metadata;
    }
    
    public ZonedDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(ZonedDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getReferenceNumber() {
        return referenceNumber;
    }
    
    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }
    
    public Long getFileSizeBytes() {
        return fileSizeBytes;
    }
    
    public void setFileSizeBytes(Long fileSizeBytes) {
        this.fileSizeBytes = fileSizeBytes;
    }
    
    /**
     * Gets the estimated size of the payload in bytes.
     * This is an approximation based on base64 encoding.
     * 
     * @return estimated payload size in bytes
     */
    public long getEstimatedPayloadSizeBytes() {
        if (payload == null || payload.isEmpty()) {
            return 0;
        }
        // Base64 encoding increases size by approximately 33%
        // So to get original size: (base64Length * 3) / 4
        return (long) ((payload.length() * 3.0) / 4.0);
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DocumentDownloadResponseDto that = (DocumentDownloadResponseDto) o;
        return Objects.equals(documentId, that.documentId) &&
               Objects.equals(payload, that.payload) &&
               Objects.equals(name, that.name) &&
               Objects.equals(metadata, that.metadata) &&
               Objects.equals(timestamp, that.timestamp) &&
               Objects.equals(referenceNumber, that.referenceNumber) &&
               Objects.equals(fileSizeBytes, that.fileSizeBytes);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(documentId, payload, name, metadata, timestamp, 
                          referenceNumber, fileSizeBytes);
    }
    
    @Override
    public String toString() {
        return "DocumentDownloadResponseDto{" +
                "documentId='" + documentId + '\'' +
                ", name='" + name + '\'' +
                ", payloadSize=" + (payload != null ? payload.length() : 0) + " chars" +
                ", estimatedSizeBytes=" + getEstimatedPayloadSizeBytes() +
                ", metadata=" + metadata +
                ", timestamp=" + timestamp +
                ", referenceNumber='" + referenceNumber + '\'' +
                ", fileSizeBytes=" + fileSizeBytes +
                '}';
    }
}
