package com.tw.documentupload.dto.external;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.ZonedDateTime;

/**
 * DTO for external document service download response.
 * Maps to the mock service API format.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class ExternalDocumentDownloadResponseDto {

    @JsonProperty("documentId")
    private String documentId;

    @JsonProperty("payload")
    private String payload;

    @JsonProperty("name")
    private String name;

    @JsonProperty("metadata")
    private ExternalDocumentMetadataDto metadata;

    @JsonProperty("timestamp")
    private ZonedDateTime timestamp;

    public ExternalDocumentDownloadResponseDto() {}

    public ExternalDocumentDownloadResponseDto(String documentId, String payload, String name,
                                             ExternalDocumentMetadataDto metadata, ZonedDateTime timestamp) {
        this.documentId = documentId;
        this.payload = payload;
        this.name = name;
        this.metadata = metadata;
        this.timestamp = timestamp;
    }

    public String getDocumentId() {
        return documentId;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getPayload() {
        return payload;
    }

    public void setPayload(String payload) {
        this.payload = payload;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ExternalDocumentMetadataDto getMetadata() {
        return metadata;
    }

    public void setMetadata(ExternalDocumentMetadataDto metadata) {
        this.metadata = metadata;
    }

    public ZonedDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(ZonedDateTime timestamp) {
        this.timestamp = timestamp;
    }
}
