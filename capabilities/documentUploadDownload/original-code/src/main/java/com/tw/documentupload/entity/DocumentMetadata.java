package com.tw.documentupload.entity;

import com.tw.documentupload.enums.DocumentStatus;
import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * Entity class representing document metadata stored in the database.
 * 
 * This entity stores comprehensive metadata about uploaded documents including
 * business information, audit trails, and retention policies. It follows
 * immutability principles for key data and includes explicit accessors and mutators.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Entity
@Table(name = "document_metadata", indexes = {
    @Index(name = "idx_document_metadata_document_id", columnList = "documentId"),
    @Index(name = "idx_document_metadata_reference_number", columnList = "referenceNumber"),
    @Index(name = "idx_document_metadata_customer_id", columnList = "customerId"),
    @Index(name = "idx_document_metadata_application_id", columnList = "applicationId"),
    @Index(name = "idx_document_metadata_upload_timestamp", columnList = "uploadTimestamp"),
    @Index(name = "idx_document_metadata_status", columnList = "status"),
    @Index(name = "idx_document_metadata_customer_application", columnList = "customerId, applicationId"),
    @Index(name = "idx_document_metadata_application_descriptor", columnList = "applicationId, descriptor")
})
public class DocumentMetadata {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "document_id", nullable = false, unique = true, length = 50)
    private String documentId;
    
    @Column(name = "reference_number", nullable = false, unique = true, length = 50)
    private String referenceNumber;
    
    // Document basic information
    @Column(name = "document_name", nullable = false, length = 50)
    private String documentName;
    
    @Column(name = "folder_path", nullable = false, length = 255)
    private String folderPath;
    
    @Column(name = "file_size_bytes", nullable = false)
    private Long fileSizeBytes;
    
    // Metadata fields
    @Column(name = "customer_id", nullable = false, length = 50)
    private String customerId;
    
    @Column(name = "application_id", nullable = false, length = 50)
    private String applicationId;
    
    @Column(name = "product_code", nullable = false, length = 50)
    private String productCode;
    
    @Column(name = "loan_category", nullable = false, length = 50)
    private String loanCategory;
    
    @Column(name = "descriptor", nullable = false, length = 50)
    private String descriptor;
    
    @Column(name = "created_date", nullable = false, length = 50)
    private String createdDate;
    
    @Column(name = "field1", length = 50)
    private String field1;
    
    @Column(name = "field2", length = 50)
    private String field2;
    
    // Audit fields
    @Column(name = "upload_timestamp", nullable = false)
    private ZonedDateTime uploadTimestamp;
    
    @Column(name = "last_accessed_timestamp")
    private ZonedDateTime lastAccessedTimestamp;
    
    @Column(name = "access_count", nullable = false)
    private Integer accessCount = 0;
    
    // Status and tracking
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private DocumentStatus status = DocumentStatus.ACTIVE;
    
    @Column(name = "retention_until_date")
    private LocalDate retentionUntilDate;
    
    // System audit fields
    @Column(name = "created_by", nullable = false, length = 100)
    private String createdBy = "SYSTEM";
    
    @Column(name = "created_at", nullable = false)
    private ZonedDateTime createdAt;
    
    @Column(name = "updated_by", length = 100)
    private String updatedBy;
    
    @Column(name = "updated_at")
    private ZonedDateTime updatedAt;
    
    @Version
    @Column(name = "version", nullable = false)
    private Integer version = 1;
    
    /**
     * Default constructor for JPA.
     */
    public DocumentMetadata() {
        this.uploadTimestamp = ZonedDateTime.now();
        this.createdAt = ZonedDateTime.now();
    }
    
    /**
     * Constructor with essential fields.
     * 
     * @param documentId unique document identifier
     * @param referenceNumber unique reference number for tracking
     * @param documentName name of the document
     * @param folderPath storage folder path
     * @param fileSizeBytes size of the document in bytes
     * @param customerId customer identifier
     * @param applicationId application identifier
     * @param productCode product code
     * @param loanCategory loan category
     * @param descriptor document descriptor
     * @param createdDate document creation date
     */
    public DocumentMetadata(String documentId, String referenceNumber, String documentName,
                          String folderPath, Long fileSizeBytes, String customerId,
                          String applicationId, String productCode, String loanCategory,
                          String descriptor, String createdDate) {
        this();
        this.documentId = documentId;
        this.referenceNumber = referenceNumber;
        this.documentName = documentName;
        this.folderPath = folderPath;
        this.fileSizeBytes = fileSizeBytes;
        this.customerId = customerId;
        this.applicationId = applicationId;
        this.productCode = productCode;
        this.loanCategory = loanCategory;
        this.descriptor = descriptor;
        this.createdDate = createdDate;
    }
    
    // Getters and Setters
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getDocumentId() {
        return documentId;
    }
    
    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }
    
    public String getReferenceNumber() {
        return referenceNumber;
    }
    
    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }
    
    public String getDocumentName() {
        return documentName;
    }
    
    public void setDocumentName(String documentName) {
        this.documentName = documentName;
    }
    
    public String getFolderPath() {
        return folderPath;
    }
    
    public void setFolderPath(String folderPath) {
        this.folderPath = folderPath;
    }
    
    public Long getFileSizeBytes() {
        return fileSizeBytes;
    }
    
    public void setFileSizeBytes(Long fileSizeBytes) {
        this.fileSizeBytes = fileSizeBytes;
    }
    
    public String getCustomerId() {
        return customerId;
    }
    
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    
    public String getApplicationId() {
        return applicationId;
    }
    
    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }
    
    public String getProductCode() {
        return productCode;
    }
    
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }
    
    public String getLoanCategory() {
        return loanCategory;
    }
    
    public void setLoanCategory(String loanCategory) {
        this.loanCategory = loanCategory;
    }
    
    public String getDescriptor() {
        return descriptor;
    }
    
    public void setDescriptor(String descriptor) {
        this.descriptor = descriptor;
    }
    
    public String getCreatedDate() {
        return createdDate;
    }
    
    public void setCreatedDate(String createdDate) {
        this.createdDate = createdDate;
    }
    
    public String getField1() {
        return field1;
    }
    
    public void setField1(String field1) {
        this.field1 = field1;
    }
    
    public String getField2() {
        return field2;
    }
    
    public void setField2(String field2) {
        this.field2 = field2;
    }
    
    public ZonedDateTime getUploadTimestamp() {
        return uploadTimestamp;
    }
    
    public void setUploadTimestamp(ZonedDateTime uploadTimestamp) {
        this.uploadTimestamp = uploadTimestamp;
    }
    
    public ZonedDateTime getLastAccessedTimestamp() {
        return lastAccessedTimestamp;
    }
    
    public void setLastAccessedTimestamp(ZonedDateTime lastAccessedTimestamp) {
        this.lastAccessedTimestamp = lastAccessedTimestamp;
    }
    
    public Integer getAccessCount() {
        return accessCount;
    }
    
    public void setAccessCount(Integer accessCount) {
        this.accessCount = accessCount;
    }
    
    public DocumentStatus getStatus() {
        return status;
    }
    
    public void setStatus(DocumentStatus status) {
        this.status = status;
    }
    
    public LocalDate getRetentionUntilDate() {
        return retentionUntilDate;
    }
    
    public void setRetentionUntilDate(LocalDate retentionUntilDate) {
        this.retentionUntilDate = retentionUntilDate;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    
    public ZonedDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(ZonedDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public String getUpdatedBy() {
        return updatedBy;
    }
    
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    
    public ZonedDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(ZonedDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Integer getVersion() {
        return version;
    }
    
    public void setVersion(Integer version) {
        this.version = version;
    }
    
    /**
     * Increments the access count and updates the last accessed timestamp.
     */
    public void incrementAccessCount() {
        this.accessCount++;
        this.lastAccessedTimestamp = ZonedDateTime.now();
    }
    
    /**
     * Updates the audit fields for modification tracking.
     * 
     * @param updatedBy the user or system making the update
     */
    public void updateAuditFields(String updatedBy) {
        this.updatedBy = updatedBy;
        this.updatedAt = ZonedDateTime.now();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DocumentMetadata that = (DocumentMetadata) o;
        return Objects.equals(documentId, that.documentId) &&
               Objects.equals(referenceNumber, that.referenceNumber);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(documentId, referenceNumber);
    }
    
    @Override
    public String toString() {
        return "DocumentMetadata{" +
                "id=" + id +
                ", documentId='" + documentId + '\'' +
                ", referenceNumber='" + referenceNumber + '\'' +
                ", documentName='" + documentName + '\'' +
                ", customerId='" + maskSensitiveData(customerId) + '\'' +
                ", applicationId='" + applicationId + '\'' +
                ", status=" + status +
                ", uploadTimestamp=" + uploadTimestamp +
                '}';
    }
    
    /**
     * Masks sensitive data for logging purposes.
     * 
     * @param data the data to mask
     * @return masked data
     */
    private String maskSensitiveData(String data) {
        if (data == null || data.length() <= 4) {
            return "****";
        }
        return data.substring(0, 2) + "****" + data.substring(data.length() - 2);
    }
}
