package com.tw.documentupload.service;

import com.tw.documentupload.util.AuditLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.Base64;
import java.util.Map;

/**
 * Service class for document storage operations.
 * 
 * This service handles the physical storage and retrieval of document payloads,
 * supporting both file system and future cloud storage implementations.
 * It includes proper error handling, audit logging, and security measures.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Service
public class DocumentStorageService {
    
    private static final Logger logger = LoggerFactory.getLogger(DocumentStorageService.class);
    
    private final AuditLogger auditLogger;
    
    @Value("${app.storage.base-path:./document-storage}")
    private String basePath;
    
    @Value("${app.storage.create-directories:true}")
    private boolean createDirectories;
    
    @Value("${app.security.mask-sensitive-data:true}")
    private boolean maskSensitiveData;
    
    @Autowired
    public DocumentStorageService(AuditLogger auditLogger) {
        this.auditLogger = auditLogger;
    }
    
    /**
     * Stores a document payload to the file system.
     * 
     * @param documentId the unique document identifier
     * @param base64Payload the base64 encoded document payload
     * @param folderPath the folder path for organization
     * @return true if storage was successful, false otherwise
     */
    public boolean storeDocument(String documentId, String base64Payload, String folderPath) {
        String traceId = generateTraceId();
        
        try {
            // Log storage initiation
            auditLogger.logRequestInitiation("DOCUMENT_STORAGE", traceId, null, 
                                            Map.of("documentId", documentId, "folderPath", folderPath));
            
            // Decode base64 payload
            byte[] documentBytes = Base64.getDecoder().decode(base64Payload);
            
            // Create storage path
            Path storagePath = createStoragePath(documentId, folderPath);
            
            // Ensure directory exists
            if (createDirectories) {
                Files.createDirectories(storagePath.getParent());
            }
            
            // Write document to file
            Files.write(storagePath, documentBytes, StandardOpenOption.CREATE, StandardOpenOption.WRITE);
            
            // Verify file was written correctly
            if (!Files.exists(storagePath) || Files.size(storagePath) != documentBytes.length) {
                auditLogger.logFailure("DOCUMENT_STORAGE", traceId, null, 
                                     "STORAGE_VERIFICATION_FAILED", "File verification failed after write", null);
                return false;
            }
            
            // Log successful storage
            auditLogger.logSuccess("DOCUMENT_STORAGE", traceId, null, 
                                  Map.of("documentId", documentId, "filePath", storagePath.toString(), 
                                        "fileSize", documentBytes.length));
            
            return true;
            
        } catch (IllegalArgumentException e) {
            auditLogger.logFailure("DOCUMENT_STORAGE", traceId, null, 
                                 "INVALID_BASE64", "Invalid base64 payload", e);
            return false;
        } catch (IOException e) {
            auditLogger.logFailure("DOCUMENT_STORAGE", traceId, null, 
                                 "IO_ERROR", "File system error during storage", e);
            return false;
        } catch (Exception e) {
            auditLogger.logFailure("DOCUMENT_STORAGE", traceId, null, 
                                 "STORAGE_ERROR", "Unexpected error during storage", e);
            return false;
        }
    }
    
    /**
     * Retrieves a document payload from storage.
     * 
     * @param documentId the unique document identifier
     * @param folderPath the folder path where the document is stored
     * @return base64 encoded document payload, or null if not found
     */
    public String retrieveDocument(String documentId, String folderPath) {
        String traceId = generateTraceId();
        
        try {
            // Log retrieval initiation
            auditLogger.logRequestInitiation("DOCUMENT_RETRIEVAL", traceId, null, 
                                            Map.of("documentId", documentId, "folderPath", folderPath));
            
            // Create storage path
            Path storagePath = createStoragePath(documentId, folderPath);
            
            // Check if file exists
            if (!Files.exists(storagePath)) {
                auditLogger.logFailure("DOCUMENT_RETRIEVAL", traceId, null, 
                                     "FILE_NOT_FOUND", "Document file not found", null);
                return null;
            }
            
            // Read document bytes
            byte[] documentBytes = Files.readAllBytes(storagePath);
            
            // Encode to base64
            String base64Payload = Base64.getEncoder().encodeToString(documentBytes);
            
            // Log successful retrieval
            auditLogger.logSuccess("DOCUMENT_RETRIEVAL", traceId, null, 
                                  Map.of("documentId", documentId, "filePath", storagePath.toString(), 
                                        "fileSize", documentBytes.length));
            
            return base64Payload;
            
        } catch (IOException e) {
            auditLogger.logFailure("DOCUMENT_RETRIEVAL", traceId, null, 
                                 "IO_ERROR", "File system error during retrieval", e);
            return null;
        } catch (Exception e) {
            auditLogger.logFailure("DOCUMENT_RETRIEVAL", traceId, null, 
                                 "RETRIEVAL_ERROR", "Unexpected error during retrieval", e);
            return null;
        }
    }
    
    /**
     * Deletes a document from storage.
     * 
     * @param documentId the unique document identifier
     * @param folderPath the folder path where the document is stored
     * @return true if deletion was successful, false otherwise
     */
    public boolean deleteDocument(String documentId, String folderPath) {
        String traceId = generateTraceId();
        
        try {
            // Log deletion initiation
            auditLogger.logRequestInitiation("DOCUMENT_DELETION", traceId, null, 
                                            Map.of("documentId", documentId, "folderPath", folderPath));
            
            // Create storage path
            Path storagePath = createStoragePath(documentId, folderPath);
            
            // Check if file exists
            if (!Files.exists(storagePath)) {
                auditLogger.logFailure("DOCUMENT_DELETION", traceId, null, 
                                     "FILE_NOT_FOUND", "Document file not found for deletion", null);
                return false;
            }
            
            // Delete the file
            boolean deleted = Files.deleteIfExists(storagePath);
            
            if (deleted) {
                // Log successful deletion
                auditLogger.logSuccess("DOCUMENT_DELETION", traceId, null, 
                                      Map.of("documentId", documentId, "filePath", storagePath.toString()));
            } else {
                auditLogger.logFailure("DOCUMENT_DELETION", traceId, null, 
                                     "DELETION_FAILED", "File deletion failed", null);
            }
            
            return deleted;
            
        } catch (IOException e) {
            auditLogger.logFailure("DOCUMENT_DELETION", traceId, null, 
                                 "IO_ERROR", "File system error during deletion", e);
            return false;
        } catch (Exception e) {
            auditLogger.logFailure("DOCUMENT_DELETION", traceId, null, 
                                 "DELETION_ERROR", "Unexpected error during deletion", e);
            return false;
        }
    }
    
    /**
     * Checks if a document exists in storage.
     * 
     * @param documentId the unique document identifier
     * @param folderPath the folder path where the document should be stored
     * @return true if the document exists, false otherwise
     */
    public boolean documentExists(String documentId, String folderPath) {
        try {
            Path storagePath = createStoragePath(documentId, folderPath);
            return Files.exists(storagePath) && Files.isRegularFile(storagePath);
        } catch (Exception e) {
            logger.warn("Error checking document existence for documentId: {}", documentId, e);
            return false;
        }
    }
    
    /**
     * Gets the size of a stored document in bytes.
     * 
     * @param documentId the unique document identifier
     * @param folderPath the folder path where the document is stored
     * @return file size in bytes, or -1 if file doesn't exist or error occurs
     */
    public long getDocumentSize(String documentId, String folderPath) {
        try {
            Path storagePath = createStoragePath(documentId, folderPath);
            if (Files.exists(storagePath)) {
                return Files.size(storagePath);
            }
            return -1;
        } catch (Exception e) {
            logger.warn("Error getting document size for documentId: {}", documentId, e);
            return -1;
        }
    }
    
    /**
     * Creates the storage path for a document.
     * 
     * @param documentId the unique document identifier
     * @param folderPath the folder path for organization
     * @return the complete storage path
     */
    private Path createStoragePath(String documentId, String folderPath) {
        // Sanitize folder path to prevent directory traversal attacks
        String sanitizedFolderPath = sanitizeFolderPath(folderPath);
        
        // Create the full path: basePath/folderPath/documentId
        return Paths.get(basePath, sanitizedFolderPath, documentId);
    }
    
    /**
     * Sanitizes the folder path to prevent security issues.
     * 
     * @param folderPath the original folder path
     * @return sanitized folder path
     */
    private String sanitizeFolderPath(String folderPath) {
        if (folderPath == null) {
            return "default";
        }
        
        // Remove any path traversal attempts and normalize
        String sanitized = folderPath.replaceAll("\\.\\.", "")
                                   .replaceAll("//+", "/")
                                   .replaceAll("^/+", "")
                                   .replaceAll("/+$", "");
        
        // If empty after sanitization, use default
        if (sanitized.isEmpty()) {
            return "default";
        }
        
        return sanitized;
    }
    
    /**
     * Generates a simple trace ID for internal operations.
     * 
     * @return trace ID
     */
    private String generateTraceId() {
        return "STG" + System.currentTimeMillis() + String.format("%03d", (int)(Math.random() * 1000));
    }
    
    /**
     * Initializes the storage directory structure.
     * This method should be called during application startup.
     */
    public void initializeStorage() {
        try {
            Path baseStoragePath = Paths.get(basePath);
            if (!Files.exists(baseStoragePath)) {
                Files.createDirectories(baseStoragePath);
                logger.info("Created base storage directory: {}", baseStoragePath.toAbsolutePath());
            }
        } catch (IOException e) {
            logger.error("Failed to initialize storage directory: {}", basePath, e);
            throw new RuntimeException("Storage initialization failed", e);
        }
    }
    
    /**
     * Gets storage statistics for monitoring purposes.
     * 
     * @return map containing storage statistics
     */
    public Map<String, Object> getStorageStatistics() {
        try {
            Path baseStoragePath = Paths.get(basePath);
            long totalSpace = Files.getFileStore(baseStoragePath).getTotalSpace();
            long usableSpace = Files.getFileStore(baseStoragePath).getUsableSpace();
            long usedSpace = totalSpace - usableSpace;
            
            return Map.of(
                "totalSpaceBytes", totalSpace,
                "usedSpaceBytes", usedSpace,
                "usableSpaceBytes", usableSpace,
                "usagePercentage", (double) usedSpace / totalSpace * 100
            );
        } catch (Exception e) {
            logger.warn("Error getting storage statistics", e);
            return Map.of("error", "Unable to retrieve storage statistics");
        }
    }
}
