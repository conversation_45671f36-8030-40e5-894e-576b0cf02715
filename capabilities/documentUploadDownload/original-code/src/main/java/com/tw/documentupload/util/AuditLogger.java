package com.tw.documentupload.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * Utility class for audit-ready logging with masked PII data.
 * 
 * This class provides standardized logging methods that ensure
 * sensitive data is properly masked while maintaining audit
 * traceability through unique identifiers and structured logging.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Component
public class AuditLogger {
    
    private static final Logger logger = LoggerFactory.getLogger(AuditLogger.class);
    private static final String TRACE_ID_KEY = "traceId";
    private static final String REFERENCE_NUMBER_KEY = "referenceNumber";
    private static final String OPERATION_KEY = "operation";
    private static final String TIMESTAMP_KEY = "timestamp";
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
    
    /**
     * Logs the initiation of a request operation.
     * 
     * @param operation the operation being performed
     * @param traceId the trace identifier
     * @param referenceNumber the reference number
     * @param additionalInfo additional information to log
     */
    public void logRequestInitiation(String operation, String traceId, String referenceNumber, 
                                   Map<String, Object> additionalInfo) {
        setMDCContext(traceId, referenceNumber, operation);
        
        StringBuilder logMessage = new StringBuilder()
            .append("REQUEST_INITIATED - Operation: ").append(operation)
            .append(", TraceId: ").append(traceId)
            .append(", ReferenceNumber: ").append(referenceNumber)
            .append(", Timestamp: ").append(getCurrentTimestamp());
        
        if (additionalInfo != null && !additionalInfo.isEmpty()) {
            logMessage.append(", AdditionalInfo: ").append(maskSensitiveData(additionalInfo));
        }
        
        logger.info(logMessage.toString());
    }
    
    /**
     * Logs successful completion of an operation.
     * 
     * @param operation the operation that completed
     * @param traceId the trace identifier
     * @param referenceNumber the reference number
     * @param result the operation result (will be masked if sensitive)
     */
    public void logSuccess(String operation, String traceId, String referenceNumber, Object result) {
        setMDCContext(traceId, referenceNumber, operation);
        
        String logMessage = String.format(
            "OPERATION_SUCCESS - Operation: %s, TraceId: %s, ReferenceNumber: %s, " +
            "Result: %s, Timestamp: %s",
            operation, traceId, referenceNumber, maskSensitiveData(result), getCurrentTimestamp()
        );
        
        logger.info(logMessage);
    }
    
    /**
     * Logs failure of an operation with error details.
     * 
     * @param operation the operation that failed
     * @param traceId the trace identifier
     * @param referenceNumber the reference number
     * @param errorCode the error code
     * @param errorMessage the error message
     * @param exception the exception (optional)
     */
    public void logFailure(String operation, String traceId, String referenceNumber, 
                          String errorCode, String errorMessage, Throwable exception) {
        setMDCContext(traceId, referenceNumber, operation);
        
        String logMessage = String.format(
            "OPERATION_FAILURE - Operation: %s, TraceId: %s, ReferenceNumber: %s, " +
            "ErrorCode: %s, ErrorMessage: %s, Timestamp: %s",
            operation, traceId, referenceNumber, errorCode, errorMessage, getCurrentTimestamp()
        );
        
        if (exception != null) {
            logger.error(logMessage, exception);
        } else {
            logger.error(logMessage);
        }
    }
    
    /**
     * Logs retry attempts for failed operations.
     * 
     * @param operation the operation being retried
     * @param traceId the trace identifier
     * @param referenceNumber the reference number
     * @param attemptNumber the current attempt number
     * @param maxAttempts the maximum number of attempts
     * @param reason the reason for retry
     */
    public void logRetryAttempt(String operation, String traceId, String referenceNumber, 
                               int attemptNumber, int maxAttempts, String reason) {
        setMDCContext(traceId, referenceNumber, operation);
        
        String logMessage = String.format(
            "RETRY_ATTEMPT - Operation: %s, TraceId: %s, ReferenceNumber: %s, " +
            "Attempt: %d/%d, Reason: %s, Timestamp: %s",
            operation, traceId, referenceNumber, attemptNumber, maxAttempts, reason, getCurrentTimestamp()
        );
        
        logger.warn(logMessage);
    }
    
    /**
     * Logs validation errors with masked request data.
     * 
     * @param operation the operation being validated
     * @param traceId the trace identifier
     * @param referenceNumber the reference number
     * @param validationErrors the validation error details
     * @param requestData the request data (will be masked)
     */
    public void logValidationError(String operation, String traceId, String referenceNumber, 
                                  String validationErrors, Object requestData) {
        setMDCContext(traceId, referenceNumber, operation);
        
        String logMessage = String.format(
            "VALIDATION_ERROR - Operation: %s, TraceId: %s, ReferenceNumber: %s, " +
            "ValidationErrors: %s, RequestData: %s, Timestamp: %s",
            operation, traceId, referenceNumber, validationErrors, 
            maskSensitiveData(requestData), getCurrentTimestamp()
        );
        
        logger.warn(logMessage);
    }
    
    /**
     * Logs document access events for audit purposes.
     * 
     * @param documentId the document identifier
     * @param traceId the trace identifier
     * @param accessType the type of access (UPLOAD, DOWNLOAD, VIEW)
     * @param userId the user identifier (will be masked)
     * @param ipAddress the client IP address
     */
    public void logDocumentAccess(String documentId, String traceId, String accessType, 
                                 String userId, String ipAddress) {
        setMDCContext(traceId, null, "DOCUMENT_ACCESS");
        
        String logMessage = String.format(
            "DOCUMENT_ACCESS - DocumentId: %s, TraceId: %s, AccessType: %s, " +
            "UserId: %s, IPAddress: %s, Timestamp: %s",
            documentId, traceId, accessType, maskUserId(userId), 
            maskIpAddress(ipAddress), getCurrentTimestamp()
        );
        
        logger.info(logMessage);
    }
    
    /**
     * Logs performance metrics for operations.
     * 
     * @param operation the operation name
     * @param traceId the trace identifier
     * @param durationMs the operation duration in milliseconds
     * @param additionalMetrics additional performance metrics
     */
    public void logPerformanceMetrics(String operation, String traceId, long durationMs, 
                                    Map<String, Object> additionalMetrics) {
        setMDCContext(traceId, null, operation);
        
        StringBuilder logMessage = new StringBuilder()
            .append("PERFORMANCE_METRICS - Operation: ").append(operation)
            .append(", TraceId: ").append(traceId)
            .append(", DurationMs: ").append(durationMs)
            .append(", Timestamp: ").append(getCurrentTimestamp());
        
        if (additionalMetrics != null && !additionalMetrics.isEmpty()) {
            logMessage.append(", Metrics: ").append(additionalMetrics);
        }
        
        logger.info(logMessage.toString());
    }
    
    /**
     * Sets the MDC (Mapped Diagnostic Context) for structured logging.
     * 
     * @param traceId the trace identifier
     * @param referenceNumber the reference number
     * @param operation the operation name
     */
    private void setMDCContext(String traceId, String referenceNumber, String operation) {
        MDC.put(TRACE_ID_KEY, traceId);
        MDC.put(TIMESTAMP_KEY, getCurrentTimestamp());
        
        if (referenceNumber != null) {
            MDC.put(REFERENCE_NUMBER_KEY, referenceNumber);
        }
        
        if (operation != null) {
            MDC.put(OPERATION_KEY, operation);
        }
    }
    
    /**
     * Clears the MDC context.
     */
    public void clearMDCContext() {
        MDC.clear();
    }
    
    /**
     * Masks sensitive data in objects for logging.
     * 
     * @param data the data to mask
     * @return masked data representation
     */
    private String maskSensitiveData(Object data) {
        if (data == null) {
            return "null";
        }
        
        String dataString = data.toString();
        
        // Mask common sensitive patterns
        dataString = dataString.replaceAll("(?i)(customerId[\"'\\s]*[:=][\"'\\s]*)([A-Z0-9]{4,})", "$1****");
        dataString = dataString.replaceAll("(?i)(payload[\"'\\s]*[:=][\"'\\s]*)([A-Za-z0-9+/]{20,})", "$1[PAYLOAD_MASKED]");
        dataString = dataString.replaceAll("(?i)(password[\"'\\s]*[:=][\"'\\s]*)([^,\\s}]+)", "$1****");
        dataString = dataString.replaceAll("(?i)(token[\"'\\s]*[:=][\"'\\s]*)([^,\\s}]+)", "$1****");
        
        return dataString;
    }
    
    /**
     * Masks user ID for logging.
     * 
     * @param userId the user ID to mask
     * @return masked user ID
     */
    private String maskUserId(String userId) {
        if (userId == null || userId.length() <= 4) {
            return "****";
        }
        return userId.substring(0, 2) + "****" + userId.substring(userId.length() - 2);
    }
    
    /**
     * Masks IP address for logging.
     * 
     * @param ipAddress the IP address to mask
     * @return masked IP address
     */
    private String maskIpAddress(String ipAddress) {
        if (ipAddress == null) {
            return "****";
        }
        
        // For IPv4, mask the last octet
        if (ipAddress.matches("\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}")) {
            int lastDotIndex = ipAddress.lastIndexOf('.');
            return ipAddress.substring(0, lastDotIndex) + ".***";
        }
        
        // For other formats, mask the end
        if (ipAddress.length() > 8) {
            return ipAddress.substring(0, 4) + "****";
        }
        
        return "****";
    }
    
    /**
     * Gets the current timestamp in ISO format.
     * 
     * @return formatted timestamp
     */
    private String getCurrentTimestamp() {
        return ZonedDateTime.now().format(TIMESTAMP_FORMAT);
    }
}
