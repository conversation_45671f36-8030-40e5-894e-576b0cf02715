package com.tw.documentupload;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main application class for the Document Upload Service.
 * 
 * This Spring Boot application provides secure document upload and retrieval
 * capabilities for the loan processing ecosystem, supporting both base64-encoded
 * and binary document streams with comprehensive metadata management.
 * 
 * Key Features:
 * - Secure document upload with size validation (≤5MB)
 * - Metadata-rich document storage and retrieval
 * - Audit-ready logging with masked PII
 * - Graceful error handling with standardized messages
 * - Data privacy and retention compliance
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@SpringBootApplication
@EnableTransactionManagement
public class DocumentUploadServiceApplication {

    /**
     * Main method to start the Document Upload Service application.
     * 
     * @param args command line arguments
     */
    public static void main(String[] args) {
        SpringApplication.run(DocumentUploadServiceApplication.class, args);
    }
}
