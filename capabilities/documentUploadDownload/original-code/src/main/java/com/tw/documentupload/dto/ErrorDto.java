package com.tw.documentupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * Data Transfer Object for error information in API responses.
 * 
 * This DTO represents error details including error code, message,
 * and additional context information for debugging and audit purposes.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class ErrorDto {
    
    @JsonProperty("code")
    private String code;
    
    @JsonProperty("message")
    private String message;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("timestamp")
    private ZonedDateTime timestamp;
    
    @JsonProperty("traceId")
    private String traceId;
    
    /**
     * Default constructor.
     */
    public ErrorDto() {
        this.timestamp = ZonedDateTime.now();
    }
    
    /**
     * Constructor with code and message.
     * 
     * @param code error code
     * @param message error message
     */
    public ErrorDto(String code, String message) {
        this();
        this.code = code;
        this.message = message;
    }
    
    /**
     * Constructor with code, message, and description.
     * 
     * @param code error code
     * @param message error message
     * @param description detailed error description
     */
    public ErrorDto(String code, String message, String description) {
        this(code, message);
        this.description = description;
    }
    
    /**
     * Constructor with all fields.
     * 
     * @param code error code
     * @param message error message
     * @param description detailed error description
     * @param traceId trace identifier for debugging
     */
    public ErrorDto(String code, String message, String description, String traceId) {
        this(code, message, description);
        this.traceId = traceId;
    }
    
    // Getters and Setters
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public ZonedDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(ZonedDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getTraceId() {
        return traceId;
    }
    
    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ErrorDto errorDto = (ErrorDto) o;
        return Objects.equals(code, errorDto.code) &&
               Objects.equals(message, errorDto.message) &&
               Objects.equals(description, errorDto.description) &&
               Objects.equals(timestamp, errorDto.timestamp) &&
               Objects.equals(traceId, errorDto.traceId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(code, message, description, timestamp, traceId);
    }
    
    @Override
    public String toString() {
        return "ErrorDto{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", description='" + description + '\'' +
                ", timestamp=" + timestamp +
                ", traceId='" + traceId + '\'' +
                '}';
    }
}
