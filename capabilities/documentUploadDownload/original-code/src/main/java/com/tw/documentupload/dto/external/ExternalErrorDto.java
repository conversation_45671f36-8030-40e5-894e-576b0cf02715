package com.tw.documentupload.dto.external;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * DTO for external document service error response.
 * Maps to the mock service API format.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class ExternalErrorDto {
    
    @JsonProperty("code")
    private String code;
    
    @JsonProperty("message")
    private String message;
    
    public ExternalErrorDto() {}
    
    public ExternalErrorDto(String code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
}
