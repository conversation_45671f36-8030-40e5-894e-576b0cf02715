package com.tw.documentupload.dto.external;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.ZonedDateTime;

/**
 * DTO for external document service upload response.
 * Maps to the mock service API format.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class ExternalDocumentUploadResponseDto {

    @JsonProperty("success")
    private boolean success;

    @JsonProperty("documentId")
    private String documentId;

    @JsonProperty("status")
    private String status;

    @JsonProperty("timestamp")
    private ZonedDateTime timestamp;

    @JsonProperty("error")
    private ExternalErrorDto error;

    public ExternalDocumentUploadResponseDto() {}

    public ExternalDocumentUploadResponseDto(boolean success, String documentId, String status,
                                           ZonedDateTime timestamp, ExternalErrorDto error) {
        this.success = success;
        this.documentId = documentId;
        this.status = status;
        this.timestamp = timestamp;
        this.error = error;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getDocumentId() {
        return documentId;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public ZonedDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(ZonedDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public ExternalErrorDto getError() {
        return error;
    }

    public void setError(ExternalErrorDto error) {
        this.error = error;
    }
}
