package com.tw.documentupload.dto.external;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * DTO for external document service upload request.
 * Maps to the mock service API format.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class ExternalDocumentUploadRequestDto {
    
    @NotNull
    @Valid
    @JsonProperty("document")
    private ExternalDocumentDto document;
    
    public ExternalDocumentUploadRequestDto() {}
    
    public ExternalDocumentUploadRequestDto(ExternalDocumentDto document) {
        this.document = document;
    }
    
    public ExternalDocumentDto getDocument() {
        return document;
    }
    
    public void setDocument(ExternalDocumentDto document) {
        this.document = document;
    }
}
