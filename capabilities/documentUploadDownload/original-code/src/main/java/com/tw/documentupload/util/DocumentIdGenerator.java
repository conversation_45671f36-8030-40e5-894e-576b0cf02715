package com.tw.documentupload.util;

import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Utility class for generating unique document and reference identifiers.
 * 
 * This class provides methods to generate unique, traceable identifiers
 * for documents and reference numbers following specific patterns and
 * ensuring uniqueness across the system.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Component
public class DocumentIdGenerator {
    
    private static final String DOCUMENT_ID_PREFIX = "DOC";
    private static final String REFERENCE_NUMBER_PREFIX = "REF";
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final SecureRandom SECURE_RANDOM = new SecureRandom();
    private static final AtomicLong SEQUENCE_COUNTER = new AtomicLong(1);
    
    // Character set for random components (excluding ambiguous characters)
    private static final String ALPHANUMERIC_CHARS = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789";
    
    /**
     * Generates a unique document ID.
     * 
     * Format: DOC + YYYYMMDDHHMMSS + 4-digit sequence + 2 random chars
     * Example: DOC20250616143025000123AB
     * 
     * @return unique document ID
     */
    public String generateDocumentId() {
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
        String sequence = String.format("%06d", getNextSequence());
        String randomSuffix = generateRandomString(2);
        
        return DOCUMENT_ID_PREFIX + timestamp + sequence + randomSuffix;
    }
    
    /**
     * Generates a unique reference number for audit tracking.
     * 
     * Format: REF + YYYYMMDDHHMMSS + 6-digit sequence + 3 random chars
     * Example: REF20250616143025000123ABC
     * 
     * @return unique reference number
     */
    public String generateReferenceNumber() {
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
        String sequence = String.format("%06d", getNextSequence());
        String randomSuffix = generateRandomString(3);
        
        return REFERENCE_NUMBER_PREFIX + timestamp + sequence + randomSuffix;
    }
    
    /**
     * Generates a trace ID for request tracking.
     * 
     * Format: TRC + YYYYMMDDHHMMSS + 4 random chars
     * Example: TRC20250616143025ABCD
     * 
     * @return unique trace ID
     */
    public String generateTraceId() {
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
        String randomSuffix = generateRandomString(4);
        
        return "TRC" + timestamp + randomSuffix;
    }
    
    /**
     * Validates if a document ID follows the expected format.
     * 
     * @param documentId the document ID to validate
     * @return true if valid format, false otherwise
     */
    public boolean isValidDocumentIdFormat(String documentId) {
        if (documentId == null || documentId.length() != 25) {
            return false;
        }
        
        return documentId.startsWith(DOCUMENT_ID_PREFIX) &&
               documentId.substring(3, 17).matches("\\d{14}") &&
               documentId.substring(17, 23).matches("\\d{6}") &&
               documentId.substring(23).matches("[A-Z0-9]{2}");
    }
    
    /**
     * Validates if a reference number follows the expected format.
     * 
     * @param referenceNumber the reference number to validate
     * @return true if valid format, false otherwise
     */
    public boolean isValidReferenceNumberFormat(String referenceNumber) {
        if (referenceNumber == null || referenceNumber.length() != 26) {
            return false;
        }
        
        return referenceNumber.startsWith(REFERENCE_NUMBER_PREFIX) &&
               referenceNumber.substring(3, 17).matches("\\d{14}") &&
               referenceNumber.substring(17, 23).matches("\\d{6}") &&
               referenceNumber.substring(23).matches("[A-Z0-9]{3}");
    }
    
    /**
     * Extracts the timestamp from a document ID.
     * 
     * @param documentId the document ID
     * @return timestamp string in YYYYMMDDHHMMSS format, or null if invalid
     */
    public String extractTimestampFromDocumentId(String documentId) {
        if (!isValidDocumentIdFormat(documentId)) {
            return null;
        }
        return documentId.substring(3, 17);
    }
    
    /**
     * Extracts the timestamp from a reference number.
     * 
     * @param referenceNumber the reference number
     * @return timestamp string in YYYYMMDDHHMMSS format, or null if invalid
     */
    public String extractTimestampFromReferenceNumber(String referenceNumber) {
        if (!isValidReferenceNumberFormat(referenceNumber)) {
            return null;
        }
        return referenceNumber.substring(3, 17);
    }
    
    /**
     * Gets the next sequence number in a thread-safe manner.
     * 
     * @return next sequence number
     */
    private long getNextSequence() {
        long sequence = SEQUENCE_COUNTER.getAndIncrement();
        // Reset counter if it exceeds 6 digits to prevent overflow
        if (sequence >= 999999) {
            SEQUENCE_COUNTER.set(1);
            return 1;
        }
        return sequence;
    }
    
    /**
     * Generates a random string of specified length using alphanumeric characters.
     * 
     * @param length the length of the random string
     * @return random string
     */
    private String generateRandomString(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int randomIndex = SECURE_RANDOM.nextInt(ALPHANUMERIC_CHARS.length());
            sb.append(ALPHANUMERIC_CHARS.charAt(randomIndex));
        }
        return sb.toString();
    }
    
    /**
     * Generates a short document ID for testing purposes.
     * This method should only be used in test environments.
     * 
     * @return short document ID
     */
    public String generateShortDocumentId() {
        String randomSuffix = generateRandomString(6);
        return DOCUMENT_ID_PREFIX + randomSuffix;
    }
    
    /**
     * Generates a short reference number for testing purposes.
     * This method should only be used in test environments.
     * 
     * @return short reference number
     */
    public String generateShortReferenceNumber() {
        String randomSuffix = generateRandomString(7);
        return REFERENCE_NUMBER_PREFIX + randomSuffix;
    }
}
