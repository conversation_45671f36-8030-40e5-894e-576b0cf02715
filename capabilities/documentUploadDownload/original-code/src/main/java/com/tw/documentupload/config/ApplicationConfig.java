package com.tw.documentupload.config;

import com.tw.documentupload.service.DocumentStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;

import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.time.Duration;

/**
 * Main application configuration class.
 *
 * This configuration class sets up application-wide settings including
 * CORS configuration, storage initialization, and other cross-cutting concerns.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Configuration
public class ApplicationConfig implements WebMvcConfigurer {

    @Autowired
    private DocumentStorageService documentStorageService;

    @Value("${external-services.document-service.timeout:30s}")
    private Duration externalServiceTimeout;

    /**
     * Configures CORS settings for the application.
     *
     * @param registry the CORS registry
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/document-upload-download/**")
            .allowedOriginPatterns("*")
            .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
            .allowedHeaders("*")
            .allowCredentials(true)
            .maxAge(3600);
    }

    /**
     * RestTemplate bean for external service calls.
     *
     * @return configured RestTemplate
     */
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(clientHttpRequestFactory());
        return restTemplate;
    }

    /**
     * HTTP request factory with timeout configuration.
     *
     * @return configured request factory
     */
    @Bean
    public ClientHttpRequestFactory clientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout((int) externalServiceTimeout.toMillis());
        factory.setReadTimeout((int) externalServiceTimeout.toMillis());
        return factory;
    }

    /**
     * Initializes the application on startup.
     *
     * @return CommandLineRunner for startup tasks
     */
    @Bean
    public CommandLineRunner initializeApplication() {
        return args -> {
            // Initialize document storage
            documentStorageService.initializeStorage();
        };
    }
}
