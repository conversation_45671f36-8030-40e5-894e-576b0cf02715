package com.tw.documentupload.service;

import com.tw.documentupload.dto.external.*;
import com.tw.documentupload.enums.ErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;

import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * Client service for external document service integration.
 *
 * This service handles communication with external document services
 * (mock service for testing, real third-party service for production).
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Service
public class ExternalDocumentServiceClient {

    private static final Logger logger = LoggerFactory.getLogger(ExternalDocumentServiceClient.class);

    private final RestTemplate restTemplate;

    @Value("${external-services.document-service.url}")
    private String documentServiceUrl;

    @Value("${external-services.document-service.timeout:30s}")
    private Duration timeout;

    public ExternalDocumentServiceClient(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    /**
     * Uploads a document to the external service.
     *
     * @param request the upload request
     * @param traceId the trace identifier
     * @return upload response
     * @throws ExternalServiceException if the external service call fails
     */
    public ExternalDocumentUploadResponseDto uploadDocument(ExternalDocumentUploadRequestDto request, String traceId) {
        try {
            logger.info("Calling external document service for upload. TraceId: {}", traceId);

            HttpHeaders headers = createHeaders(traceId);
            HttpEntity<ExternalDocumentUploadRequestDto> entity = new HttpEntity<>(request, headers);

            String uploadUrl = documentServiceUrl + "/upload";

            ResponseEntity<ExternalDocumentUploadResponseDto> response = restTemplate.exchange(
                uploadUrl,
                HttpMethod.POST,
                entity,
                ExternalDocumentUploadResponseDto.class
            );

            ExternalDocumentUploadResponseDto responseBody = response.getBody();

            if (responseBody != null && responseBody.isSuccess()) {
                logger.info("External document upload successful. DocumentId: {}, TraceId: {}",
                          responseBody.getDocumentId(), traceId);
                return responseBody;
            } else {
                logger.error("External document upload failed. Response: {}, TraceId: {}", responseBody, traceId);
                throw new ExternalServiceException(
                    ErrorCode.ERR107.getCode(),
                    "External document service returned failure response",
                    responseBody != null ? responseBody.getError() : null
                );
            }

        } catch (HttpClientErrorException e) {
            logger.error("Client error calling external document service. Status: {}, TraceId: {}",
                        e.getStatusCode(), traceId, e);
            throw new ExternalServiceException(
                ErrorCode.ERR107.getCode(),
                "External document service client error: " + e.getMessage(),
                null
            );
        } catch (HttpServerErrorException e) {
            logger.error("Server error calling external document service. Status: {}, TraceId: {}",
                        e.getStatusCode(), traceId, e);
            throw new ExternalServiceException(
                ErrorCode.ERR107.getCode(),
                "External document service server error: " + e.getMessage(),
                null
            );
        } catch (ResourceAccessException e) {
            logger.error("Network error calling external document service. TraceId: {}", traceId, e);
            throw new ExternalServiceException(
                ErrorCode.ERR107.getCode(),
                "External document service network error: " + e.getMessage(),
                null
            );
        } catch (Exception e) {
            logger.error("Unexpected error calling external document service. TraceId: {}", traceId, e);
            throw new ExternalServiceException(
                ErrorCode.ERR203.getCode(),
                "Unexpected error calling external document service: " + e.getMessage(),
                null
            );
        }
    }

    /**
     * Downloads a document from the external service.
     *
     * @param documentId the document identifier
     * @param traceId the trace identifier
     * @return download response or null if not found
     * @throws ExternalServiceException if the external service call fails
     */
    public ExternalDocumentDownloadResponseDto downloadDocument(String documentId, String traceId) {
        try {
            logger.info("Calling external document service for download. DocumentId: {}, TraceId: {}",
                       documentId, traceId);

            HttpHeaders headers = createHeaders(traceId);
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            String downloadUrl = documentServiceUrl + "/" + documentId;

            ResponseEntity<ExternalDocumentDownloadResponseDto> response = restTemplate.exchange(
                downloadUrl,
                HttpMethod.GET,
                entity,
                ExternalDocumentDownloadResponseDto.class
            );

            ExternalDocumentDownloadResponseDto responseBody = response.getBody();

            logger.info("External document download successful. DocumentId: {}, TraceId: {}",
                       documentId, traceId);
            return responseBody;

        } catch (HttpClientErrorException.NotFound e) {
            logger.warn("Document not found in external service. DocumentId: {}, TraceId: {}",
                       documentId, traceId);
            return null; // Document not found
        } catch (HttpClientErrorException e) {
            logger.error("Client error calling external document service for download. Status: {}, DocumentId: {}, TraceId: {}",
                        e.getStatusCode(), documentId, traceId, e);
            throw new ExternalServiceException(
                ErrorCode.ERR108.getCode(),
                "External document service client error: " + e.getMessage(),
                null
            );
        } catch (HttpServerErrorException e) {
            logger.error("Server error calling external document service for download. Status: {}, DocumentId: {}, TraceId: {}",
                        e.getStatusCode(), documentId, traceId, e);
            throw new ExternalServiceException(
                ErrorCode.ERR108.getCode(),
                "External document service server error: " + e.getMessage(),
                null
            );
        } catch (ResourceAccessException e) {
            logger.error("Network error calling external document service for download. DocumentId: {}, TraceId: {}",
                        documentId, traceId, e);
            throw new ExternalServiceException(
                ErrorCode.ERR108.getCode(),
                "External document service network error: " + e.getMessage(),
                null
            );
        } catch (Exception e) {
            logger.error("Unexpected error calling external document service for download. DocumentId: {}, TraceId: {}",
                        documentId, traceId, e);
            throw new ExternalServiceException(
                ErrorCode.ERR203.getCode(),
                "Unexpected error calling external document service: " + e.getMessage(),
                null
            );
        }
    }

    /**
     * Creates HTTP headers for external service calls.
     *
     * @param traceId the trace identifier
     * @return HTTP headers
     */
    private HttpHeaders createHeaders(String traceId) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-Trace-Id", traceId);
        headers.set("User-Agent", "document-upload-service/1.0.0");
        return headers;
    }

    /**
     * Exception for external service errors.
     */
    public static class ExternalServiceException extends RuntimeException {
        private final String errorCode;
        private final ExternalErrorDto externalError;

        public ExternalServiceException(String errorCode, String message, ExternalErrorDto externalError) {
            super(message);
            this.errorCode = errorCode;
            this.externalError = externalError;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public ExternalErrorDto getExternalError() {
            return externalError;
        }
    }
}
