package com.tw.documentupload.util;

import org.springframework.stereotype.Component;

import java.util.Base64;
import java.util.regex.Pattern;

/**
 * Utility class for various validation operations.
 * 
 * This class provides validation methods for document uploads,
 * including base64 validation, size checks, and format validation
 * to ensure data integrity and compliance with business rules.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Component
public class ValidationUtils {
    
    // Maximum file size in bytes (5MB)
    private static final long MAX_FILE_SIZE_BYTES = 5 * 1024 * 1024;
    
    // Date pattern for DD/MM/YYYY format
    private static final Pattern DATE_PATTERN = Pattern.compile("^\\d{2}/\\d{2}/\\d{4}$");
    
    // Application ID pattern (e.g., MLP123, APP456)
    private static final Pattern APPLICATION_ID_PATTERN = Pattern.compile("^[A-Z]{2,4}\\d{3,6}$");
    
    // Customer ID pattern (alphanumeric, 4-20 characters)
    private static final Pattern CUSTOMER_ID_PATTERN = Pattern.compile("^[A-Z0-9]{4,20}$");
    
    // Folder path pattern (allows alphanumeric, forward slashes, underscores, hyphens)
    private static final Pattern FOLDER_PATH_PATTERN = Pattern.compile("^[a-zA-Z0-9/_-]+/$");
    
    // Document name pattern (allows alphanumeric, dots, underscores, hyphens)
    private static final Pattern DOCUMENT_NAME_PATTERN = Pattern.compile("^[a-zA-Z0-9._-]+\\.[a-zA-Z]{2,5}$");
    
    // Supported file extensions
    private static final String[] SUPPORTED_EXTENSIONS = {
        "pdf", "jpg", "jpeg", "png", "doc", "docx", "txt", "csv", "xls", "xlsx"
    };
    
    /**
     * Validates if the base64 payload is valid and within size limits.
     * 
     * @param base64Payload the base64 encoded payload
     * @return true if valid, false otherwise
     */
    public boolean isValidBase64Payload(String base64Payload) {
        if (base64Payload == null || base64Payload.trim().isEmpty()) {
            return false;
        }
        
        try {
            // Check if it's valid base64
            byte[] decodedBytes = Base64.getDecoder().decode(base64Payload);
            
            // Check size limit
            return decodedBytes.length <= MAX_FILE_SIZE_BYTES;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * Validates if the base64 payload size is within limits.
     * 
     * @param base64Payload the base64 encoded payload
     * @return true if within size limits, false otherwise
     */
    public boolean isWithinSizeLimit(String base64Payload) {
        if (base64Payload == null) {
            return false;
        }
        
        try {
            byte[] decodedBytes = Base64.getDecoder().decode(base64Payload);
            return decodedBytes.length <= MAX_FILE_SIZE_BYTES;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * Gets the decoded size of a base64 payload in bytes.
     * 
     * @param base64Payload the base64 encoded payload
     * @return size in bytes, or -1 if invalid
     */
    public long getDecodedSize(String base64Payload) {
        if (base64Payload == null) {
            return -1;
        }
        
        try {
            byte[] decodedBytes = Base64.getDecoder().decode(base64Payload);
            return decodedBytes.length;
        } catch (IllegalArgumentException e) {
            return -1;
        }
    }
    
    /**
     * Validates if the date string is in DD/MM/YYYY format.
     * 
     * @param dateString the date string to validate
     * @return true if valid format, false otherwise
     */
    public boolean isValidDateFormat(String dateString) {
        if (dateString == null) {
            return false;
        }
        
        if (!DATE_PATTERN.matcher(dateString).matches()) {
            return false;
        }
        
        // Additional validation for valid date ranges
        String[] parts = dateString.split("/");
        int day = Integer.parseInt(parts[0]);
        int month = Integer.parseInt(parts[1]);
        int year = Integer.parseInt(parts[2]);
        
        return day >= 1 && day <= 31 && 
               month >= 1 && month <= 12 && 
               year >= 1900 && year <= 2100;
    }
    
    /**
     * Validates if the application ID follows the expected pattern.
     * 
     * @param applicationId the application ID to validate
     * @return true if valid format, false otherwise
     */
    public boolean isValidApplicationId(String applicationId) {
        return applicationId != null && APPLICATION_ID_PATTERN.matcher(applicationId).matches();
    }
    
    /**
     * Validates if the customer ID follows the expected pattern.
     * 
     * @param customerId the customer ID to validate
     * @return true if valid format, false otherwise
     */
    public boolean isValidCustomerId(String customerId) {
        return customerId != null && CUSTOMER_ID_PATTERN.matcher(customerId).matches();
    }
    
    /**
     * Validates if the folder path follows the expected pattern.
     * 
     * @param folderPath the folder path to validate
     * @return true if valid format, false otherwise
     */
    public boolean isValidFolderPath(String folderPath) {
        return folderPath != null && FOLDER_PATH_PATTERN.matcher(folderPath).matches();
    }
    
    /**
     * Validates if the document name follows the expected pattern and has a supported extension.
     * 
     * @param documentName the document name to validate
     * @return true if valid format and supported extension, false otherwise
     */
    public boolean isValidDocumentName(String documentName) {
        if (documentName == null || !DOCUMENT_NAME_PATTERN.matcher(documentName).matches()) {
            return false;
        }
        
        String extension = getFileExtension(documentName);
        return isSupportedExtension(extension);
    }
    
    /**
     * Checks if the file extension is supported.
     * 
     * @param extension the file extension (without dot)
     * @return true if supported, false otherwise
     */
    public boolean isSupportedExtension(String extension) {
        if (extension == null) {
            return false;
        }
        
        String lowerExtension = extension.toLowerCase();
        for (String supportedExt : SUPPORTED_EXTENSIONS) {
            if (supportedExt.equals(lowerExtension)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Extracts the file extension from a filename.
     * 
     * @param filename the filename
     * @return file extension without dot, or null if no extension
     */
    public String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return null;
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return null;
        }
        
        return filename.substring(lastDotIndex + 1);
    }
    
    /**
     * Validates if a string field meets length requirements.
     * 
     * @param value the string value
     * @param maxLength the maximum allowed length
     * @param fieldName the field name for error reporting
     * @return true if valid, false otherwise
     */
    public boolean isValidStringLength(String value, int maxLength, String fieldName) {
        return value != null && value.length() <= maxLength;
    }
    
    /**
     * Validates if a mandatory string field is not null or empty.
     * 
     * @param value the string value
     * @param fieldName the field name for error reporting
     * @return true if valid, false otherwise
     */
    public boolean isNotNullOrEmpty(String value, String fieldName) {
        return value != null && !value.trim().isEmpty();
    }
    
    /**
     * Sanitizes a string by removing potentially harmful characters.
     * 
     * @param input the input string
     * @return sanitized string
     */
    public String sanitizeString(String input) {
        if (input == null) {
            return null;
        }
        
        // Remove control characters and normalize whitespace
        return input.replaceAll("[\\p{Cntrl}&&[^\r\n\t]]", "")
                   .replaceAll("\\s+", " ")
                   .trim();
    }
    
    /**
     * Gets the maximum allowed file size in bytes.
     * 
     * @return maximum file size in bytes
     */
    public long getMaxFileSizeBytes() {
        return MAX_FILE_SIZE_BYTES;
    }
    
    /**
     * Gets the maximum allowed file size in MB.
     * 
     * @return maximum file size in MB
     */
    public double getMaxFileSizeMB() {
        return MAX_FILE_SIZE_BYTES / (1024.0 * 1024.0);
    }
    
    /**
     * Gets the list of supported file extensions.
     * 
     * @return array of supported extensions
     */
    public String[] getSupportedExtensions() {
        return SUPPORTED_EXTENSIONS.clone();
    }
}
