package com.tw.documentupload.enums;

/**
 * Enumeration of standardized error codes used throughout the Document Upload Service.
 * 
 * These error codes provide consistent error identification and handling across
 * all service operations, supporting audit requirements and debugging.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public enum ErrorCode {
    
    // Validation Errors (ERR001-ERR099)
    ERR001("ERR001", "Invalid payload", "The provided document payload is invalid or corrupted"),
    ERR002("ERR002", "Document size exceeds limit", "Document size exceeds the maximum allowed limit of 5MB"),
    ERR003("ERR003", "Missing mandatory field", "One or more mandatory fields are missing from the request"),
    ERR004("ERR004", "Field too long", "One or more fields exceed the maximum allowed length of 50 characters"),
    ERR005("ERR005", "Invalid date format", "Date field must be in DD/MM/YYYY format"),
    ERR006("ERR006", "Invalid file type", "File type is not supported"),
    ERR007("ERR007", "Empty payload", "Document payload cannot be empty"),
    ERR008("ERR008", "Invalid base64 encoding", "Document payload is not valid base64 encoded data"),
    ERR009("ERR009", "Invalid folder path", "Folder path contains invalid characters or format"),
    ERR010("ERR010", "Invalid application ID format", "Application ID does not match expected format"),
    
    // Document Operation Errors (ERR101-ERR199)
    ERR101("ERR101", "Document not found", "Document with the specified ID does not exist"),
    ERR102("ERR102", "Document already exists", "A document with the same identifier already exists"),
    ERR103("ERR103", "Document access denied", "Access to the document is not permitted"),
    ERR104("ERR104", "Document expired", "Document has exceeded its retention period"),
    ERR105("ERR105", "Document quarantined", "Document is under security review and not accessible"),
    ERR106("ERR106", "Document corrupted", "Document data appears to be corrupted"),
    ERR107("ERR107", "Document upload failed", "Failed to upload document to storage"),
    ERR108("ERR108", "Document retrieval failed", "Failed to retrieve document from storage"),
    ERR109("ERR109", "Document deletion failed", "Failed to delete document from storage"),
    ERR110("ERR110", "Document metadata update failed", "Failed to update document metadata"),
    
    // System Errors (ERR201-ERR299)
    ERR201("ERR201", "Database connection error", "Unable to connect to the database"),
    ERR202("ERR202", "Storage service unavailable", "Document storage service is currently unavailable"),
    ERR203("ERR203", "Internal server error", "An unexpected internal error occurred"),
    ERR204("ERR204", "Service timeout", "Operation timed out while processing the request"),
    ERR205("ERR205", "Configuration error", "System configuration error detected"),
    ERR206("ERR206", "Resource exhausted", "System resources are temporarily exhausted"),
    ERR207("ERR207", "Transaction failed", "Database transaction failed"),
    ERR208("ERR208", "Concurrent modification", "Document was modified by another process"),
    ERR209("ERR209", "Rate limit exceeded", "Too many requests, please try again later"),
    ERR210("ERR210", "Maintenance mode", "Service is temporarily unavailable for maintenance"),
    
    // Security Errors (ERR301-ERR399)
    ERR301("ERR301", "Authentication required", "Authentication is required to access this resource"),
    ERR302("ERR302", "Authorization failed", "Insufficient permissions to perform this operation"),
    ERR303("ERR303", "Invalid credentials", "The provided credentials are invalid"),
    ERR304("ERR304", "Session expired", "User session has expired"),
    ERR305("ERR305", "Security scan failed", "Document failed security scanning"),
    ERR306("ERR306", "Suspicious activity detected", "Suspicious activity detected, request blocked"),
    
    // Business Logic Errors (ERR401-ERR499)
    ERR401("ERR401", "Invalid loan category", "The specified loan category is not valid"),
    ERR402("ERR402", "Invalid product code", "The specified product code is not valid"),
    ERR403("ERR403", "Customer not found", "Customer with the specified ID does not exist"),
    ERR404("ERR404", "Application not found", "Loan application with the specified ID does not exist"),
    ERR405("ERR405", "Document type not allowed", "Document type is not allowed for this application"),
    ERR406("ERR406", "Duplicate document", "Document of this type already exists for the application"),
    ERR407("ERR407", "Application status invalid", "Cannot upload documents for applications in current status"),
    ERR408("ERR408", "Retention period exceeded", "Document retention period has been exceeded"),
    ERR409("ERR409", "Business rule violation", "Operation violates business rules");
    
    private final String code;
    private final String message;
    private final String description;
    
    /**
     * Constructor for ErrorCode enum.
     * 
     * @param code the error code identifier
     * @param message short error message
     * @param description detailed description of the error
     */
    ErrorCode(String code, String message, String description) {
        this.code = code;
        this.message = message;
        this.description = description;
    }
    
    /**
     * Gets the error code identifier.
     * 
     * @return the error code
     */
    public String getCode() {
        return code;
    }
    
    /**
     * Gets the short error message.
     * 
     * @return the error message
     */
    public String getMessage() {
        return message;
    }
    
    /**
     * Gets the detailed error description.
     * 
     * @return the error description
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Finds an ErrorCode by its code identifier.
     * 
     * @param code the error code to search for
     * @return the matching ErrorCode, or null if not found
     */
    public static ErrorCode fromCode(String code) {
        for (ErrorCode errorCode : values()) {
            if (errorCode.getCode().equals(code)) {
                return errorCode;
            }
        }
        return null;
    }
}
