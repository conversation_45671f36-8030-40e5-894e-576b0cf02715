package com.tw.documentupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * Data Transfer Object for document upload responses.
 * 
 * This DTO represents the response structure for document upload operations,
 * including success information with document ID or error details.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class DocumentUploadResponseDto {
    
    @JsonProperty("documentId")
    private String documentId;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("timestamp")
    private ZonedDateTime timestamp;
    
    @JsonProperty("error")
    private ErrorDto error;
    
    @JsonProperty("referenceNumber")
    private String referenceNumber;
    
    /**
     * Default constructor.
     */
    public DocumentUploadResponseDto() {
        this.timestamp = ZonedDateTime.now();
    }
    
    /**
     * Constructor for successful upload response.
     * 
     * @param documentId the generated document ID
     * @param referenceNumber the reference number for tracking
     */
    public DocumentUploadResponseDto(String documentId, String referenceNumber) {
        this();
        this.documentId = documentId;
        this.referenceNumber = referenceNumber;
        this.status = "Success";
    }
    
    /**
     * Constructor for error response.
     * 
     * @param error the error information
     */
    public DocumentUploadResponseDto(ErrorDto error) {
        this();
        this.error = error;
        this.status = "Failure";
    }
    
    /**
     * Static factory method for creating a successful response.
     * 
     * @param documentId the generated document ID
     * @param referenceNumber the reference number for tracking
     * @return successful upload response
     */
    public static DocumentUploadResponseDto success(String documentId, String referenceNumber) {
        return new DocumentUploadResponseDto(documentId, referenceNumber);
    }
    
    /**
     * Static factory method for creating an error response.
     * 
     * @param error the error information
     * @return error upload response
     */
    public static DocumentUploadResponseDto failure(ErrorDto error) {
        return new DocumentUploadResponseDto(error);
    }
    
    /**
     * Static factory method for creating an error response with code and message.
     * 
     * @param errorCode the error code
     * @param errorMessage the error message
     * @return error upload response
     */
    public static DocumentUploadResponseDto failure(String errorCode, String errorMessage) {
        return new DocumentUploadResponseDto(new ErrorDto(errorCode, errorMessage));
    }
    
    /**
     * Static factory method for creating an error response with code, message, and description.
     * 
     * @param errorCode the error code
     * @param errorMessage the error message
     * @param errorDescription the detailed error description
     * @return error upload response
     */
    public static DocumentUploadResponseDto failure(String errorCode, String errorMessage, String errorDescription) {
        return new DocumentUploadResponseDto(new ErrorDto(errorCode, errorMessage, errorDescription));
    }
    
    // Getters and Setters
    
    public String getDocumentId() {
        return documentId;
    }
    
    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public ZonedDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(ZonedDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public ErrorDto getError() {
        return error;
    }
    
    public void setError(ErrorDto error) {
        this.error = error;
    }
    
    public String getReferenceNumber() {
        return referenceNumber;
    }
    
    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }
    
    /**
     * Checks if the response indicates a successful operation.
     * 
     * @return true if successful, false otherwise
     */
    public boolean isSuccess() {
        return "Success".equals(status) && documentId != null && error == null;
    }
    
    /**
     * Checks if the response indicates a failed operation.
     * 
     * @return true if failed, false otherwise
     */
    public boolean isFailure() {
        return "Failure".equals(status) || error != null;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DocumentUploadResponseDto that = (DocumentUploadResponseDto) o;
        return Objects.equals(documentId, that.documentId) &&
               Objects.equals(status, that.status) &&
               Objects.equals(timestamp, that.timestamp) &&
               Objects.equals(error, that.error) &&
               Objects.equals(referenceNumber, that.referenceNumber);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(documentId, status, timestamp, error, referenceNumber);
    }
    
    @Override
    public String toString() {
        return "DocumentUploadResponseDto{" +
                "documentId='" + documentId + '\'' +
                ", status='" + status + '\'' +
                ", timestamp=" + timestamp +
                ", error=" + error +
                ", referenceNumber='" + referenceNumber + '\'' +
                '}';
    }
}
