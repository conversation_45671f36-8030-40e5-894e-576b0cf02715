package com.tw.documentupload.dto.external;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * DTO for external document service metadata.
 * Maps to the mock service API format.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class ExternalDocumentMetadataDto {
    
    @NotBlank
    @Size(max = 50)
    @JsonProperty("customerId")
    private String customerId;
    
    @NotBlank
    @Size(max = 50)
    @JsonProperty("applicationId")
    private String applicationId;
    
    @NotBlank
    @Size(max = 50)
    @JsonProperty("productCode")
    private String productCode;
    
    @NotBlank
    @Size(max = 50)
    @JsonProperty("loanCategory")
    private String loanCategory;
    
    @NotBlank
    @Size(max = 50)
    @JsonProperty("descriptor")
    private String descriptor;
    
    @NotBlank
    @Pattern(regexp = "^\\d{2}/\\d{2}/\\d{4}$")
    @Size(max = 50)
    @JsonProperty("createdDate")
    private String createdDate;
    
    @Size(max = 50)
    @JsonProperty("field1")
    private String field1;
    
    @Size(max = 50)
    @JsonProperty("field2")
    private String field2;
    
    public ExternalDocumentMetadataDto() {}
    
    public ExternalDocumentMetadataDto(String customerId, String applicationId, String productCode,
                                     String loanCategory, String descriptor, String createdDate,
                                     String field1, String field2) {
        this.customerId = customerId;
        this.applicationId = applicationId;
        this.productCode = productCode;
        this.loanCategory = loanCategory;
        this.descriptor = descriptor;
        this.createdDate = createdDate;
        this.field1 = field1;
        this.field2 = field2;
    }
    
    public String getCustomerId() {
        return customerId;
    }
    
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    
    public String getApplicationId() {
        return applicationId;
    }
    
    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }
    
    public String getProductCode() {
        return productCode;
    }
    
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }
    
    public String getLoanCategory() {
        return loanCategory;
    }
    
    public void setLoanCategory(String loanCategory) {
        this.loanCategory = loanCategory;
    }
    
    public String getDescriptor() {
        return descriptor;
    }
    
    public void setDescriptor(String descriptor) {
        this.descriptor = descriptor;
    }
    
    public String getCreatedDate() {
        return createdDate;
    }
    
    public void setCreatedDate(String createdDate) {
        this.createdDate = createdDate;
    }
    
    public String getField1() {
        return field1;
    }
    
    public void setField1(String field1) {
        this.field1 = field1;
    }
    
    public String getField2() {
        return field2;
    }
    
    public void setField2(String field2) {
        this.field2 = field2;
    }
}
