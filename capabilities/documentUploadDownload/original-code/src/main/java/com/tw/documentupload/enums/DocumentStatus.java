package com.tw.documentupload.enums;

/**
 * Enumeration representing the various states of a document in the system.
 * 
 * This enum is used to track the lifecycle of documents from upload to deletion,
 * supporting data retention policies and audit requirements.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public enum DocumentStatus {
    
    /**
     * Document is active and available for retrieval.
     * This is the default status for newly uploaded documents.
     */
    ACTIVE("Active", "Document is available for retrieval"),
    
    /**
     * Document has been archived but is still accessible.
     * Used for documents that are past their primary usage period
     * but still within retention requirements.
     */
    ARCHIVED("Archived", "Document is archived but accessible"),
    
    /**
     * Document has been marked for deletion.
     * The document may still exist physically but is not accessible
     * through normal retrieval operations.
     */
    DELETED("Deleted", "Document is marked for deletion"),
    
    /**
     * Document is temporarily quarantined due to security concerns.
     * Used when documents fail security scans or are flagged for review.
     */
    QUARANTINED("Quarantined", "Document is under security review"),
    
    /**
     * Document has expired based on retention policies.
     * The document is no longer accessible and scheduled for permanent removal.
     */
    EXPIRED("Expired", "Document has exceeded retention period");
    
    private final String displayName;
    private final String description;
    
    /**
     * Constructor for DocumentStatus enum.
     * 
     * @param displayName human-readable name for the status
     * @param description detailed description of what the status means
     */
    DocumentStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }
    
    /**
     * Gets the human-readable display name for the status.
     * 
     * @return the display name
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * Gets the detailed description of the status.
     * 
     * @return the description
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Checks if the document status allows retrieval operations.
     * 
     * @return true if the document can be retrieved, false otherwise
     */
    public boolean isRetrievable() {
        return this == ACTIVE || this == ARCHIVED;
    }
    
    /**
     * Checks if the document status indicates the document is no longer available.
     * 
     * @return true if the document is not available, false otherwise
     */
    public boolean isUnavailable() {
        return this == DELETED || this == EXPIRED || this == QUARANTINED;
    }
}
