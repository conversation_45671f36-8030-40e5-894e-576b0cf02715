package com.tw.documentupload.controller;

import com.tw.documentupload.dto.DocumentDownloadResponseDto;
import com.tw.documentupload.dto.DocumentUploadRequestDto;
import com.tw.documentupload.dto.DocumentUploadResponseDto;
import com.tw.documentupload.dto.ErrorDto;
import com.tw.documentupload.enums.ErrorCode;
import com.tw.documentupload.service.DocumentService;
import com.tw.documentupload.util.AuditLogger;
import com.tw.documentupload.util.DocumentIdGenerator;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * REST Controller for document upload and download operations.
 * 
 * This controller provides RESTful endpoints for document management
 * including upload, download, and retrieval operations with comprehensive
 * error handling, validation, and audit logging.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@RestController
@RequestMapping("/document")
public class DocumentController {
    
    private final DocumentService documentService;
    private final DocumentIdGenerator documentIdGenerator;
    private final AuditLogger auditLogger;
    
    @Autowired
    public DocumentController(DocumentService documentService,
                            DocumentIdGenerator documentIdGenerator,
                            AuditLogger auditLogger) {
        this.documentService = documentService;
        this.documentIdGenerator = documentIdGenerator;
        this.auditLogger = auditLogger;
    }
    
    /**
     * Uploads a document with metadata.
     * 
     * @param applicationId the application identifier from path
     * @param request the document upload request
     * @param bindingResult validation results
     * @param httpRequest the HTTP request for audit logging
     * @return upload response with document ID or error details
     */
    @PostMapping("/{applicationId}")
    public ResponseEntity<DocumentUploadResponseDto> uploadDocument(
            @PathVariable String applicationId,
            @Valid @RequestBody DocumentUploadRequestDto request,
            
            BindingResult bindingResult,
            HttpServletRequest httpRequest) {
        
        String traceId = documentIdGenerator.generateTraceId();
        
        try {
            // Log document access for audit
            auditLogger.logDocumentAccess(null, traceId, "UPLOAD", 
                                        request.getCustomerId(), getClientIpAddress(httpRequest));
            
            // Validate path parameter matches request
            if (!applicationId.equals(request.getApplicationId())) {
                ErrorDto error = new ErrorDto(ErrorCode.ERR010.getCode(), 
                                            "Application ID in path does not match request data", 
                                            ErrorCode.ERR010.getDescription(), traceId);
                return ResponseEntity.badRequest()
                    .body(DocumentUploadResponseDto.failure(error));
            }
            
            // Check for validation errors
            if (bindingResult.hasErrors()) {
                String validationErrors = bindingResult.getFieldErrors().stream()
                    .map(error -> error.getField() + ": " + error.getDefaultMessage())
                    .collect(Collectors.joining(", "));
                
                auditLogger.logValidationError("DOCUMENT_UPLOAD", traceId, null, 
                                              validationErrors, request);
                
                ErrorDto error = new ErrorDto(ErrorCode.ERR003.getCode(), 
                                            "Validation failed: " + validationErrors, 
                                            ErrorCode.ERR003.getDescription(), traceId);
                return ResponseEntity.badRequest()
                    .body(DocumentUploadResponseDto.failure(error));
            }
            
            // Process upload
            DocumentUploadResponseDto response = documentService.uploadDocument(request, traceId);
            
            if (response.isSuccess()) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            auditLogger.logFailure("DOCUMENT_UPLOAD", traceId, null, 
                                 ErrorCode.ERR203.getCode(), ErrorCode.ERR203.getMessage(), e);
            
            ErrorDto error = new ErrorDto(ErrorCode.ERR203.getCode(), 
                                        ErrorCode.ERR203.getMessage(), 
                                        ErrorCode.ERR203.getDescription(), traceId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(DocumentUploadResponseDto.failure(error));
        } finally {
            auditLogger.clearMDCContext();
        }
    }
    
    /**
     * Downloads a document by its ID.
     * 
     * @param documentId the document identifier
     * @param httpRequest the HTTP request for audit logging
     * @return document data or error response
     */
    @GetMapping("/{documentId}")
    public ResponseEntity<?> downloadDocument(
            @PathVariable String documentId,
            
            HttpServletRequest httpRequest) {
        
        String traceId = documentIdGenerator.generateTraceId();
        
        try {
            // Log document access for audit
            auditLogger.logDocumentAccess(documentId, traceId, "DOWNLOAD", 
                                        "SYSTEM", getClientIpAddress(httpRequest));
            
            // Validate document ID format
            if (!documentIdGenerator.isValidDocumentIdFormat(documentId)) {
                auditLogger.logValidationError("DOCUMENT_DOWNLOAD", traceId, null, 
                                              "Invalid document ID format", documentId);
                return ResponseEntity.notFound().build();
            }
            
            // Process download
            DocumentDownloadResponseDto response = documentService.downloadDocument(documentId, traceId);
            
            if (response != null) {
                return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(response);
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            auditLogger.logFailure("DOCUMENT_DOWNLOAD", traceId, null, 
                                 ErrorCode.ERR203.getCode(), ErrorCode.ERR203.getMessage(), e);
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Internal server error occurred while processing the request");
        } finally {
            auditLogger.clearMDCContext();
        }
    }
    
    /**
     * Health check endpoint for the document service.
     * 
     * @return health status
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "document-upload-service");
        health.put("timestamp", System.currentTimeMillis());
        health.put("version", "1.0.0");
        
        return ResponseEntity.ok(health);
    }
    
    /**
     * Gets service information and statistics.
     * 
     * @return service information
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getServiceInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("serviceName", "Document Upload Service");
        info.put("version", "1.0.0");
        info.put("maxFileSizeMB", 5);
        info.put("supportedFormats", new String[]{"pdf", "jpg", "jpeg", "png", "doc", "docx"});
        info.put("retentionPeriodDays", 2555);
        info.put("apiVersion", "v1");
        
        return ResponseEntity.ok(info);
    }
    
    /**
     * Handles validation exceptions and returns appropriate error responses.
     * 
     * @param ex the validation exception
     * @return error response
     */
    @ExceptionHandler(org.springframework.web.bind.MethodArgumentNotValidException.class)
    public ResponseEntity<DocumentUploadResponseDto> handleValidationException(
            org.springframework.web.bind.MethodArgumentNotValidException ex) {
        
        String traceId = documentIdGenerator.generateTraceId();
        String validationErrors = ex.getBindingResult().getFieldErrors().stream()
            .map(error -> error.getField() + ": " + error.getDefaultMessage())
            .collect(Collectors.joining(", "));
        
        auditLogger.logValidationError("REQUEST_VALIDATION", traceId, null, 
                                      validationErrors, ex.getParameter());
        
        ErrorDto error = new ErrorDto(ErrorCode.ERR003.getCode(), 
                                    "Request validation failed", 
                                    validationErrors, traceId);
        
        return ResponseEntity.badRequest()
            .body(DocumentUploadResponseDto.failure(error));
    }
    
    /**
     * Handles general exceptions and returns appropriate error responses.
     * 
     * @param ex the exception
     * @return error response
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<DocumentUploadResponseDto> handleGeneralException(Exception ex) {
        String traceId = documentIdGenerator.generateTraceId();
        
        auditLogger.logFailure("GENERAL_ERROR", traceId, null, 
                             ErrorCode.ERR203.getCode(), ErrorCode.ERR203.getMessage(), ex);
        
        ErrorDto error = new ErrorDto(ErrorCode.ERR203.getCode(), 
                                    ErrorCode.ERR203.getMessage(), 
                                    "An unexpected error occurred", traceId);
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(DocumentUploadResponseDto.failure(error));
    }
    
    /**
     * Extracts the client IP address from the HTTP request.
     * 
     * @param request the HTTP request
     * @return client IP address
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
