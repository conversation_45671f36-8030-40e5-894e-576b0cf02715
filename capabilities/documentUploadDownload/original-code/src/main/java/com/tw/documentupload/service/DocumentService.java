package com.tw.documentupload.service;

import com.tw.documentupload.dto.*;
import com.tw.documentupload.dto.external.*;
import com.tw.documentupload.entity.DocumentMetadata;
import com.tw.documentupload.enums.DocumentStatus;
import com.tw.documentupload.enums.ErrorCode;
import com.tw.documentupload.repository.DocumentMetadataRepository;
import com.tw.documentupload.service.ExternalDocumentServiceClient.ExternalServiceException;
import com.tw.documentupload.util.AuditLogger;
import com.tw.documentupload.util.DocumentIdGenerator;
import com.tw.documentupload.util.ValidationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Service class for document upload and download operations.
 *
 * This service handles the core business logic for document management,
 * including validation, storage, retrieval, and audit logging with
 * proper error handling and retry mechanisms.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Service
@Transactional
public class DocumentService {

    private final DocumentMetadataRepository documentMetadataRepository;
    private final DocumentIdGenerator documentIdGenerator;
    private final ValidationUtils validationUtils;
    private final AuditLogger auditLogger;
    private final DocumentStorageService documentStorageService;
    private final ExternalDocumentServiceClient externalDocumentServiceClient;
    private final ExternalDocumentMapper externalDocumentMapper;

    @Value("${app.document.retention-days:2555}")
    private int retentionDays;

    @Value("${app.security.mask-sensitive-data:true}")
    private boolean maskSensitiveData;

    @Autowired
    public DocumentService(DocumentMetadataRepository documentMetadataRepository,
                          DocumentIdGenerator documentIdGenerator,
                          ValidationUtils validationUtils,
                          AuditLogger auditLogger,
                          DocumentStorageService documentStorageService,
                          ExternalDocumentServiceClient externalDocumentServiceClient,
                          ExternalDocumentMapper externalDocumentMapper) {
        this.documentMetadataRepository = documentMetadataRepository;
        this.documentIdGenerator = documentIdGenerator;
        this.validationUtils = validationUtils;
        this.auditLogger = auditLogger;
        this.documentStorageService = documentStorageService;
        this.externalDocumentServiceClient = externalDocumentServiceClient;
        this.externalDocumentMapper = externalDocumentMapper;
    }

    /**
     * Uploads a document with comprehensive validation and audit logging.
     *
     * @param request the document upload request
     * @param traceId the trace identifier for tracking
     * @return upload response with document ID or error details
     */
    public DocumentUploadResponseDto uploadDocument(DocumentUploadRequestDto request, String traceId) {
        String referenceNumber = documentIdGenerator.generateReferenceNumber();

        try {
            // Log request initiation
            Map<String, Object> requestInfo = createRequestInfo(request);
            auditLogger.logRequestInitiation("DOCUMENT_UPLOAD", traceId, referenceNumber, requestInfo);

            // Validate request
            ValidationResult validationResult = validateUploadRequest(request, traceId, referenceNumber);
            if (!validationResult.isValid()) {
                return DocumentUploadResponseDto.failure(validationResult.getErrorCode(),
                                                       validationResult.getErrorMessage(),
                                                       validationResult.getErrorDescription());
            }

            // Call external document service
            ExternalDocumentUploadRequestDto externalRequest = externalDocumentMapper.mapToExternalUploadRequest(request);

            try {
                ExternalDocumentUploadResponseDto externalResponse = externalDocumentServiceClient.uploadDocument(externalRequest, traceId);

                // Map external response back to internal response
                DocumentUploadResponseDto response = externalDocumentMapper.mapToInternalUploadResponse(externalResponse, referenceNumber);

                if (response.isSuccess()) {
                    // Save metadata to our database for tracking
                    DocumentMetadata metadata = createDocumentMetadata(request, response.getDocumentId(), referenceNumber);
                    documentMetadataRepository.save(metadata);

                    // Log successful upload
                    auditLogger.logSuccess("DOCUMENT_UPLOAD", traceId, referenceNumber,
                                          Map.of("documentId", response.getDocumentId(), "size", metadata.getFileSizeBytes()));
                } else {
                    // Log external service failure
                    auditLogger.logFailure("DOCUMENT_UPLOAD", traceId, referenceNumber,
                                         response.getError().getCode(), response.getError().getMessage(), null);
                }

                return response;

            } catch (ExternalServiceException e) {
                auditLogger.logFailure("DOCUMENT_UPLOAD", traceId, referenceNumber,
                                     e.getErrorCode(), e.getMessage(), e);
                return DocumentUploadResponseDto.failure(e.getErrorCode(), e.getMessage());
            }

        } catch (Exception e) {
            auditLogger.logFailure("DOCUMENT_UPLOAD", traceId, referenceNumber,
                                 ErrorCode.ERR203.getCode(), ErrorCode.ERR203.getMessage(), e);
            return DocumentUploadResponseDto.failure(ErrorCode.ERR203.getCode(),
                                                   ErrorCode.ERR203.getMessage());
        }
    }

    /**
     * Downloads a document by its ID with access tracking.
     *
     * @param documentId the document identifier
     * @param traceId the trace identifier for tracking
     * @return download response with document data or error details
     */
    @Transactional
    public DocumentDownloadResponseDto downloadDocument(String documentId, String traceId) {
        try {
            // Log request initiation
            auditLogger.logRequestInitiation("DOCUMENT_DOWNLOAD", traceId, null,
                                            Map.of("documentId", documentId));

            // Find document metadata
            Optional<DocumentMetadata> metadataOpt = documentMetadataRepository.findByDocumentId(documentId);
            if (metadataOpt.isEmpty()) {
                auditLogger.logFailure("DOCUMENT_DOWNLOAD", traceId, null,
                                     ErrorCode.ERR101.getCode(), ErrorCode.ERR101.getMessage(), null);
                return null; // This will result in 404 response
            }

            DocumentMetadata metadata = metadataOpt.get();

            // Check document status
            if (!metadata.getStatus().isRetrievable()) {
                ErrorCode errorCode = getErrorCodeForStatus(metadata.getStatus());
                auditLogger.logFailure("DOCUMENT_DOWNLOAD", traceId, metadata.getReferenceNumber(),
                                     errorCode.getCode(), errorCode.getMessage(), null);
                return null; // This will result in 404 response
            }

            // Call external document service to retrieve payload
            try {
                ExternalDocumentDownloadResponseDto externalResponse = externalDocumentServiceClient.downloadDocument(documentId, traceId);

                if (externalResponse == null) {
                    auditLogger.logFailure("DOCUMENT_DOWNLOAD", traceId, metadata.getReferenceNumber(),
                                         ErrorCode.ERR108.getCode(), ErrorCode.ERR108.getMessage(), null);
                    return null; // This will result in 404 response
                }

                // Update access tracking
                updateAccessTracking(metadata);

                // Map external response to internal response
                DocumentDownloadResponseDto response = externalDocumentMapper.mapToInternalDownloadResponse(
                    externalResponse, metadata.getReferenceNumber(), metadata.getFileSizeBytes()
                );

                // Log successful download
                auditLogger.logSuccess("DOCUMENT_DOWNLOAD", traceId, metadata.getReferenceNumber(),
                                      Map.of("documentId", documentId, "accessCount", metadata.getAccessCount() + 1));

                return response;

            } catch (ExternalServiceException e) {
                auditLogger.logFailure("DOCUMENT_DOWNLOAD", traceId, metadata.getReferenceNumber(),
                                     e.getErrorCode(), e.getMessage(), e);
                return null; // This will result in 500 response
            }

        } catch (Exception e) {
            auditLogger.logFailure("DOCUMENT_DOWNLOAD", traceId, null,
                                 ErrorCode.ERR203.getCode(), ErrorCode.ERR203.getMessage(), e);
            return null; // This will result in 500 response
        }
    }

    /**
     * Validates the document upload request.
     *
     * @param request the upload request
     * @param traceId the trace identifier
     * @param referenceNumber the reference number
     * @return validation result
     */
    private ValidationResult validateUploadRequest(DocumentUploadRequestDto request,
                                                  String traceId, String referenceNumber) {
        DocumentDto document = request.getDocument();
        DocumentMetadataDto metadata = document.getMetadata();

        // Validate payload
        if (!validationUtils.isValidBase64Payload(document.getPayload())) {
            String error = "Invalid or oversized payload";
            auditLogger.logValidationError("DOCUMENT_UPLOAD", traceId, referenceNumber, error, request);
            return ValidationResult.invalid(ErrorCode.ERR001.getCode(), ErrorCode.ERR001.getMessage(),
                                          ErrorCode.ERR001.getDescription());
        }

        // Validate size
        if (!validationUtils.isWithinSizeLimit(document.getPayload())) {
            String error = "Document size exceeds 5MB limit";
            auditLogger.logValidationError("DOCUMENT_UPLOAD", traceId, referenceNumber, error, request);
            return ValidationResult.invalid(ErrorCode.ERR002.getCode(), ErrorCode.ERR002.getMessage(),
                                          ErrorCode.ERR002.getDescription());
        }

        // Validate document name
        if (!validationUtils.isValidDocumentName(document.getName())) {
            String error = "Invalid document name or unsupported file type";
            auditLogger.logValidationError("DOCUMENT_UPLOAD", traceId, referenceNumber, error, request);
            return ValidationResult.invalid(ErrorCode.ERR006.getCode(), ErrorCode.ERR006.getMessage(),
                                          ErrorCode.ERR006.getDescription());
        }

        // Validate folder path
        if (!validationUtils.isValidFolderPath(document.getFolder())) {
            String error = "Invalid folder path format";
            auditLogger.logValidationError("DOCUMENT_UPLOAD", traceId, referenceNumber, error, request);
            return ValidationResult.invalid(ErrorCode.ERR009.getCode(), ErrorCode.ERR009.getMessage(),
                                          ErrorCode.ERR009.getDescription());
        }

        // Validate date format
        if (!validationUtils.isValidDateFormat(metadata.getCreatedDate())) {
            String error = "Invalid date format, expected DD/MM/YYYY";
            auditLogger.logValidationError("DOCUMENT_UPLOAD", traceId, referenceNumber, error, request);
            return ValidationResult.invalid(ErrorCode.ERR005.getCode(), ErrorCode.ERR005.getMessage(),
                                          ErrorCode.ERR005.getDescription());
        }

        // Validate application ID format
        if (!validationUtils.isValidApplicationId(metadata.getApplicationId())) {
            String error = "Invalid application ID format";
            auditLogger.logValidationError("DOCUMENT_UPLOAD", traceId, referenceNumber, error, request);
            return ValidationResult.invalid(ErrorCode.ERR010.getCode(), ErrorCode.ERR010.getMessage(),
                                          ErrorCode.ERR010.getDescription());
        }

        return ValidationResult.valid();
    }

    /**
     * Generates a unique document ID, ensuring no duplicates.
     *
     * @return unique document ID
     */
    private String generateUniqueDocumentId() {
        String documentId;
        int attempts = 0;
        do {
            documentId = documentIdGenerator.generateDocumentId();
            attempts++;
        } while (documentMetadataRepository.existsByDocumentId(documentId) && attempts < 10);

        if (attempts >= 10) {
            throw new RuntimeException("Unable to generate unique document ID after 10 attempts");
        }

        return documentId;
    }

    /**
     * Creates document metadata entity from the upload request.
     *
     * @param request the upload request
     * @param documentId the generated document ID
     * @param referenceNumber the reference number
     * @return document metadata entity
     */
    private DocumentMetadata createDocumentMetadata(DocumentUploadRequestDto request,
                                                   String documentId, String referenceNumber) {
        DocumentDto document = request.getDocument();
        DocumentMetadataDto metadata = document.getMetadata();

        long fileSizeBytes = validationUtils.getDecodedSize(document.getPayload());
        LocalDate retentionUntilDate = LocalDate.now().plusDays(retentionDays);

        DocumentMetadata entity = new DocumentMetadata(
            documentId, referenceNumber, document.getName(), document.getFolder(),
            fileSizeBytes, metadata.getCustomerId(), metadata.getApplicationId(),
            metadata.getProductCode(), metadata.getLoanCategory(), metadata.getDescriptor(),
            metadata.getCreatedDate()
        );

        entity.setField1(metadata.getField1());
        entity.setField2(metadata.getField2());
        entity.setRetentionUntilDate(retentionUntilDate);

        return entity;
    }

    /**
     * Updates access tracking for a document.
     *
     * @param metadata the document metadata
     */
    private void updateAccessTracking(DocumentMetadata metadata) {
        metadata.incrementAccessCount();
        documentMetadataRepository.updateAccessInfo(
            metadata.getDocumentId(),
            metadata.getAccessCount(),
            metadata.getLastAccessedTimestamp()
        );
    }

    /**
     * Converts entity to DTO.
     *
     * @param metadata the document metadata entity
     * @return metadata DTO
     */
    private DocumentMetadataDto convertToMetadataDto(DocumentMetadata metadata) {
        return new DocumentMetadataDto(
            metadata.getCustomerId(), metadata.getApplicationId(), metadata.getProductCode(),
            metadata.getLoanCategory(), metadata.getDescriptor(), metadata.getCreatedDate(),
            metadata.getField1(), metadata.getField2()
        );
    }

    /**
     * Creates request information map for logging.
     *
     * @param request the upload request
     * @return request information map
     */
    private Map<String, Object> createRequestInfo(DocumentUploadRequestDto request) {
        Map<String, Object> info = new HashMap<>();
        info.put("documentName", request.getDocumentName());
        info.put("applicationId", request.getApplicationId());
        info.put("estimatedSize", request.getEstimatedPayloadSizeBytes());
        return info;
    }

    /**
     * Gets the appropriate error code for a document status.
     *
     * @param status the document status
     * @return corresponding error code
     */
    private ErrorCode getErrorCodeForStatus(DocumentStatus status) {
        return switch (status) {
            case EXPIRED -> ErrorCode.ERR104;
            case QUARANTINED -> ErrorCode.ERR105;
            case DELETED -> ErrorCode.ERR101;
            default -> ErrorCode.ERR101;
        };
    }

    /**
     * Inner class for validation results.
     */
    private static class ValidationResult {
        private final boolean valid;
        private final String errorCode;
        private final String errorMessage;
        private final String errorDescription;

        private ValidationResult(boolean valid, String errorCode, String errorMessage, String errorDescription) {
            this.valid = valid;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
            this.errorDescription = errorDescription;
        }

        public static ValidationResult valid() {
            return new ValidationResult(true, null, null, null);
        }

        public static ValidationResult invalid(String errorCode, String errorMessage, String errorDescription) {
            return new ValidationResult(false, errorCode, errorMessage, errorDescription);
        }

        public boolean isValid() { return valid; }
        public String getErrorCode() { return errorCode; }
        public String getErrorMessage() { return errorMessage; }
        public String getErrorDescription() { return errorDescription; }
    }
}
