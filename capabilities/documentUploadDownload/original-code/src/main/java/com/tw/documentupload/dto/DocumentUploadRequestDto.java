package com.tw.documentupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.Objects;

/**
 * Data Transfer Object for document upload requests.
 * 
 * This DTO represents the complete request structure for uploading a document,
 * containing the document information and metadata as specified in the API contract.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class DocumentUploadRequestDto {
    
    @Valid
    @NotNull(message = "Document is mandatory")
    @JsonProperty("document")
    private DocumentDto document;
    
    /**
     * Default constructor.
     */
    public DocumentUploadRequestDto() {
    }
    
    /**
     * Constructor with document.
     * 
     * @param document the document to upload
     */
    public DocumentUploadRequestDto(DocumentDto document) {
        this.document = document;
    }
    
    // Getters and Setters
    
    public DocumentDto getDocument() {
        return document;
    }
    
    public void setDocument(DocumentDto document) {
        this.document = document;
    }
    
    /**
     * Convenience method to get the application ID from the document metadata.
     * 
     * @return application ID if available, null otherwise
     */
    public String getApplicationId() {
        return document != null && document.getMetadata() != null 
            ? document.getMetadata().getApplicationId() 
            : null;
    }
    
    /**
     * Convenience method to get the customer ID from the document metadata.
     * 
     * @return customer ID if available, null otherwise
     */
    public String getCustomerId() {
        return document != null && document.getMetadata() != null 
            ? document.getMetadata().getCustomerId() 
            : null;
    }
    
    /**
     * Convenience method to get the document name.
     * 
     * @return document name if available, null otherwise
     */
    public String getDocumentName() {
        return document != null ? document.getName() : null;
    }
    
    /**
     * Convenience method to get the estimated payload size.
     * 
     * @return estimated payload size in bytes
     */
    public long getEstimatedPayloadSizeBytes() {
        return document != null ? document.getEstimatedPayloadSizeBytes() : 0;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DocumentUploadRequestDto that = (DocumentUploadRequestDto) o;
        return Objects.equals(document, that.document);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(document);
    }
    
    @Override
    public String toString() {
        return "DocumentUploadRequestDto{" +
                "document=" + document +
                '}';
    }
}
