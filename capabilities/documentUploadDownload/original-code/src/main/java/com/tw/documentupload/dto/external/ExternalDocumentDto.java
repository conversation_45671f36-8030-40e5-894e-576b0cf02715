package com.tw.documentupload.dto.external;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * DTO for external document service document data.
 * Maps to the mock service API format.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class ExternalDocumentDto {
    
    @NotBlank
    @Size(max = 50)
    @JsonProperty("name")
    private String name;
    
    @NotBlank
    @Size(max = 255)
    @JsonProperty("folder")
    private String folder;
    
    @NotBlank
    @JsonProperty("payload")
    private String payload;
    
    @NotNull
    @Valid
    @JsonProperty("metadata")
    private ExternalDocumentMetadataDto metadata;
    
    public ExternalDocumentDto() {}
    
    public ExternalDocumentDto(String name, String folder, String payload, ExternalDocumentMetadataDto metadata) {
        this.name = name;
        this.folder = folder;
        this.payload = payload;
        this.metadata = metadata;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getFolder() {
        return folder;
    }
    
    public void setFolder(String folder) {
        this.folder = folder;
    }
    
    public String getPayload() {
        return payload;
    }
    
    public void setPayload(String payload) {
        this.payload = payload;
    }
    
    public ExternalDocumentMetadataDto getMetadata() {
        return metadata;
    }
    
    public void setMetadata(ExternalDocumentMetadataDto metadata) {
        this.metadata = metadata;
    }
}
