openapi: 3.0.3
info:
  title: Document Upload Download Mock API
  description: |
    A mock service for the Document Upload Download API designed for development and QA environments.

    This mock service simulates the behavior of the document upload and download API with:
    - Configurable response delays and failure rates
    - Realistic validation and error scenarios
    - In-memory document storage for testing
    - Admin endpoints for configuration and monitoring
    - Comprehensive logging and tracing

    The mock service is designed to test document submission and retrieval workflows,
    metadata validation, and response handling without relying on the production system.
  version: 1.0.0
  contact:
    name: TW Development Team
    email: <EMAIL>
  license:
    name: Proprietary
    url: https://tw.com/license

servers:
  - url: http://localhost:8083/api/document-upload-download/internal
    description: Mock service server

paths:
  /documents/upload:
    post:
      summary: Mock document upload
      description: |
        Mock endpoint for document upload that simulates the behavior of the real API.
        
        Features:
        - Configurable latency simulation
        - Validation error scenarios
        - File size limit testing
        - Success/failure rate configuration
        - Comprehensive logging with trace IDs
        
        The mock validates document structure and metadata but stores documents in memory.
      operationId: mockUploadDocument
      tags:
        - Mock Document Operations
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MockDocumentUploadRequest'
            examples:
              sanctionLetter:
                summary: Mock Sanction Letter Upload
                value:
                  document:
                    name: "Sanction_Letter.pdf"
                    folder: "maximus/application/MLP123/"
                    payload: "JVBERi0xLjQKJcOkw7zDtsO..."
                    metadata:
                      customerId: "CUST001"
                      applicationId: "MLP123"
                      productCode: "FOUR_WHEELER_ETB_PA"
                      loanCategory: "ETB"
                      descriptor: "SanctionLetter"
                      createdDate: "05/06/2025"
                      field1: "AdditionalInfo1"
                      field2: "AdditionalInfo2"
      responses:
        '200':
          description: Document uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockDocumentUploadResponse'
              examples:
                success:
                  summary: Successful mock upload
                  value:
                    documentId: "MOCKDOC123456"
                    status: "Success"
                    timestamp: "2025-06-16T10:00:00Z"
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockDocumentUploadResponse'
              examples:
                validationError:
                  summary: Mock validation error
                  value:
                    status: "Failure"
                    timestamp: "2025-06-16T10:00:00Z"
                    error:
                      code: "VALIDATION_ERROR"
                      message: "Document validation failed"
                      description: "Document name is mandatory"
                      traceId: "mock-trace-123"
        '413':
          description: Payload too large
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockDocumentUploadResponse'
        '500':
          description: Internal server error (simulated)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockDocumentUploadResponse'

  /documents/{documentId}:
    get:
      summary: Mock document download
      description: |
        Mock endpoint for document download that retrieves documents from in-memory storage.
        
        Features:
        - Simulates document expiration scenarios
        - Configurable response delays
        - Realistic error responses
        - Document not found scenarios
      operationId: mockDownloadDocument
      tags:
        - Mock Document Operations
      parameters:
        - name: documentId
          in: path
          required: true
          description: The mock document identifier
          schema:
            type: string
            pattern: '^(MOCKDOC|DOC)[A-Z0-9]{6,20}$'
            example: "MOCKDOC123456"
      responses:
        '200':
          description: Document retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockDocumentDownloadResponse'
              examples:
                success:
                  summary: Successful mock download
                  value:
                    documentId: "MOCKDOC123456"
                    payload: "JVBERi0xLjQKJcOkw7zDtsO..."
                    name: "Sanction_Letter.pdf"
                    timestamp: "2025-06-16T10:00:00Z"
                    metadata:
                      customerId: "CUST001"
                      applicationId: "MLP123"
                      productCode: "FOUR_WHEELER_ETB_PA"
                      loanCategory: "ETB"
                      descriptor: "SanctionLetter"
                      createdDate: "05/06/2025"
                      field1: "AdditionalInfo1"
                      field2: "AdditionalInfo2"
        '404':
          description: Document not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockErrorResponse'
        '410':
          description: Document expired (simulated)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockErrorResponse'
        '500':
          description: Internal server error (simulated)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockErrorResponse'

  /documents/health:
    get:
      summary: Mock service health check
      description: Returns the health status of the mock document service
      operationId: mockHealthCheck
      tags:
        - Mock Service Management
      responses:
        '200':
          description: Mock service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockHealthStatus'
              examples:
                healthy:
                  summary: Healthy mock service
                  value:
                    status: "UP"
                    service: "document-upload-mock-service"
                    documentsStored: 42
                    timestamp: 1687776000000

  /documents/admin/config:
    post:
      summary: Configure mock simulation parameters
      description: |
        Admin endpoint to configure mock service behavior including
        response latency and failure rates for testing different scenarios.
      operationId: configureMockSimulation
      tags:
        - Mock Service Management
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MockConfigurationRequest'
            examples:
              configExample:
                summary: Configuration example
                value:
                  latencyMs: 500
                  failureRate: 0.1
      responses:
        '200':
          description: Configuration updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockConfigurationResponse'
              examples:
                configResponse:
                  summary: Configuration response
                  value:
                    currentLatencyMs: 500
                    currentFailureRate: 0.1
                    message: "Configuration updated successfully"

  /documents/admin/logs:
    get:
      summary: View mock service logs
      description: |
        Admin endpoint to retrieve upload and download operation logs
        for monitoring and debugging purposes.
      operationId: getMockLogs
      tags:
        - Mock Service Management
      responses:
        '200':
          description: Logs retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockLogsResponse'

  /documents/admin/reset:
    post:
      summary: Reset mock service state
      description: |
        Admin endpoint to clear in-memory document registry and logs,
        resetting the mock service to initial state.
      operationId: resetMockService
      tags:
        - Mock Service Management
      responses:
        '200':
          description: Mock service reset successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockResetResponse'
              examples:
                resetResponse:
                  summary: Reset response
                  value:
                    message: "Mock service reset successfully"
                    documentsCleared: 42
                    logsCleared: 156
                    timestamp: "2025-06-16T10:00:00Z"

  /v1/document-upload-download/mock/health:
    get:
      summary: Mock service health check endpoint
      description: Returns the health status of the mock document upload download service
      operationId: mockServiceHealthCheck
      tags:
        - Mock Service Management
      responses:
        '200':
          description: Mock service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockServiceHealthStatus'
              examples:
                healthy:
                  summary: Healthy mock service
                  value:
                    status: "UP"
                    service: "Document Upload Download Mock"
                    timestamp: "2025-06-16T10:00:00"
                    version: "1.0.0"

components:
  schemas:
    MockDocumentUploadRequest:
      type: object
      required:
        - document
      properties:
        document:
          $ref: '#/components/schemas/MockDocument'
      example:
        document:
          name: "Sanction_Letter.pdf"
          folder: "maximus/application/MLP123/"
          payload: "JVBERi0xLjQKJcOkw7zDtsO..."
          metadata:
            customerId: "CUST001"
            applicationId: "MLP123"
            productCode: "FOUR_WHEELER_ETB_PA"
            loanCategory: "ETB"
            descriptor: "SanctionLetter"
            createdDate: "05/06/2025"
            field1: "AdditionalInfo1"
            field2: "AdditionalInfo2"

    MockDocument:
      type: object
      required:
        - name
        - folder
        - payload
        - metadata
      properties:
        name:
          type: string
          maxLength: 50
          description: The document file name
          example: "Sanction_Letter.pdf"
        folder:
          type: string
          maxLength: 255
          description: The folder path where the document should be stored
          example: "maximus/application/MLP123/"
        payload:
          type: string
          format: byte
          description: Base64 encoded document content
          example: "JVBERi0xLjQKJcOkw7zDtsO..."
        metadata:
          $ref: '#/components/schemas/MockDocumentMetadata'

    MockDocumentMetadata:
      type: object
      required:
        - customerId
        - applicationId
        - productCode
        - loanCategory
        - descriptor
        - createdDate
      properties:
        customerId:
          type: string
          maxLength: 50
          description: Unique customer identifier
          example: "CUST001"
        applicationId:
          type: string
          maxLength: 50
          description: Loan application identifier
          example: "MLP123"
        productCode:
          type: string
          maxLength: 50
          description: Product code for the loan
          example: "FOUR_WHEELER_ETB_PA"
        loanCategory:
          type: string
          maxLength: 50
          description: Category of the loan
          example: "ETB"
        descriptor:
          type: string
          maxLength: 50
          description: Document type descriptor
          example: "SanctionLetter"
        createdDate:
          type: string
          pattern: '^\\d{2}/\\d{2}/\\d{4}$'
          maxLength: 50
          description: Document creation date in DD/MM/YYYY format
          example: "05/06/2025"
        field1:
          type: string
          maxLength: 50
          description: Additional metadata field 1
          example: "AdditionalInfo1"
        field2:
          type: string
          maxLength: 50
          description: Additional metadata field 2
          example: "AdditionalInfo2"

    MockDocumentUploadResponse:
      type: object
      properties:
        documentId:
          type: string
          description: Unique identifier for the uploaded document (mock)
          example: "MOCKDOC123456"
        status:
          type: string
          enum: [Success, Failure]
          description: Upload operation status
          example: "Success"
        timestamp:
          type: string
          format: date-time
          description: Response timestamp in ISO 8601 format
          example: "2025-06-16T10:00:00Z"
        error:
          $ref: '#/components/schemas/MockError'

    MockDocumentDownloadResponse:
      type: object
      required:
        - documentId
        - payload
        - name
        - metadata
        - timestamp
      properties:
        documentId:
          type: string
          description: Unique document identifier (mock)
          example: "MOCKDOC123456"
        payload:
          type: string
          format: byte
          description: Base64 encoded document content
          example: "JVBERi0xLjQKJcOkw7zDtsO..."
        name:
          type: string
          description: Original document file name
          example: "Sanction_Letter.pdf"
        metadata:
          $ref: '#/components/schemas/MockDocumentMetadata'
        timestamp:
          type: string
          format: date-time
          description: Response timestamp in ISO 8601 format
          example: "2025-06-16T10:00:00Z"

    MockError:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: string
          description: Mock error code identifier
          example: "VALIDATION_ERROR"
        message:
          type: string
          description: Human-readable error message
          example: "Document validation failed"
        description:
          type: string
          description: Detailed error description
          example: "Document name is mandatory"
        timestamp:
          type: string
          format: date-time
          description: Error timestamp in ISO 8601 format
          example: "2025-06-16T10:00:00Z"
        traceId:
          type: string
          description: Mock trace identifier for debugging
          example: "mock-trace-123"

    MockErrorResponse:
      type: object
      required:
        - error
        - timestamp
      properties:
        error:
          $ref: '#/components/schemas/MockError'
        timestamp:
          type: string
          format: date-time
          description: Response timestamp in ISO 8601 format
          example: "2025-06-16T10:00:00Z"

    MockHealthStatus:
      type: object
      required:
        - status
        - service
        - documentsStored
        - timestamp
      properties:
        status:
          type: string
          enum: [UP, DOWN]
          description: Mock service health status
          example: "UP"
        service:
          type: string
          description: Mock service name
          example: "document-upload-mock-service"
        documentsStored:
          type: integer
          description: Number of documents currently stored in memory
          example: 42
        timestamp:
          type: integer
          format: int64
          description: Health check timestamp (Unix timestamp)
          example: 1687776000000

    MockConfigurationRequest:
      type: object
      properties:
        latencyMs:
          type: integer
          minimum: 0
          maximum: 10000
          description: Simulated response latency in milliseconds
          example: 500
        failureRate:
          type: number
          format: double
          minimum: 0.0
          maximum: 1.0
          description: Simulated failure rate (0.0 to 1.0)
          example: 0.1

    MockConfigurationResponse:
      type: object
      required:
        - currentLatencyMs
        - currentFailureRate
        - message
      properties:
        currentLatencyMs:
          type: integer
          description: Current configured latency in milliseconds
          example: 500
        currentFailureRate:
          type: number
          format: double
          description: Current configured failure rate
          example: 0.1
        message:
          type: string
          description: Configuration update message
          example: "Configuration updated successfully"

    MockLogsResponse:
      type: object
      required:
        - logs
        - totalCount
        - timestamp
      properties:
        logs:
          type: array
          items:
            $ref: '#/components/schemas/MockLogEntry'
          description: List of log entries
        totalCount:
          type: integer
          description: Total number of log entries
          example: 156
        timestamp:
          type: string
          format: date-time
          description: Response timestamp
          example: "2025-06-16T10:00:00Z"

    MockLogEntry:
      type: object
      required:
        - timestamp
        - operation
        - documentId
        - status
        - traceId
      properties:
        timestamp:
          type: string
          format: date-time
          description: Log entry timestamp
          example: "2025-06-16T10:00:00Z"
        operation:
          type: string
          enum: [UPLOAD, DOWNLOAD]
          description: Operation type
          example: "UPLOAD"
        documentId:
          type: string
          description: Document identifier
          example: "MOCKDOC123456"
        status:
          type: string
          enum: [SUCCESS, FAILURE]
          description: Operation status
          example: "SUCCESS"
        traceId:
          type: string
          description: Trace identifier
          example: "mock-trace-123"
        details:
          type: string
          description: Additional operation details
          example: "Document uploaded successfully"

    MockResetResponse:
      type: object
      required:
        - message
        - documentsCleared
        - logsCleared
        - timestamp
      properties:
        message:
          type: string
          description: Reset operation message
          example: "Mock service reset successfully"
        documentsCleared:
          type: integer
          description: Number of documents cleared from memory
          example: 42
        logsCleared:
          type: integer
          description: Number of log entries cleared
          example: 156
        timestamp:
          type: string
          format: date-time
          description: Reset timestamp
          example: "2025-06-16T10:00:00Z"

    MockServiceHealthStatus:
      type: object
      required:
        - status
        - service
        - timestamp
        - version
      properties:
        status:
          type: string
          enum: [UP, DOWN]
          description: Mock service health status
          example: "UP"
        service:
          type: string
          description: Mock service name
          example: "Document Upload Download Mock"
        timestamp:
          type: string
          format: date-time
          description: Health check timestamp
          example: "2025-06-16T10:00:00"
        version:
          type: string
          description: Mock service version
          example: "1.0.0"

  parameters:
    MockDocumentId:
      name: documentId
      in: path
      required: true
      description: The mock document identifier
      schema:
        type: string
        pattern: '^(MOCKDOC|DOC)[A-Z0-9]{6,20}$'
        example: "MOCKDOC123456"

  responses:
    MockBadRequest:
      description: Bad request - validation errors
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/MockDocumentUploadResponse'

    MockNotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/MockErrorResponse'

    MockInternalServerError:
      description: Internal server error (simulated)
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/MockErrorResponse'

tags:
  - name: Mock Document Operations
    description: Mock operations for uploading and downloading documents
  - name: Mock Service Management
    description: Admin endpoints for mock service configuration and monitoring
