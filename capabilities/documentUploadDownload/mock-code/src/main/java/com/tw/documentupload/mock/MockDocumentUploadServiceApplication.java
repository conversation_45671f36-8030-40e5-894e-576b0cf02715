package com.tw.documentupload.mock;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Main application class for the Document Upload Mock Service.
 * 
 * This Spring Boot application provides a mock implementation of the document
 * upload and download API for development and QA testing environments.
 * It simulates realistic API behavior including validation, errors, and delays.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@SpringBootApplication
@EnableScheduling
public class MockDocumentUploadServiceApplication {

    /**
     * Main method to start the Mock Document Upload Service application.
     * 
     * @param args command line arguments
     */
    public static void main(String[] args) {
        SpringApplication.run(MockDocumentUploadServiceApplication.class, args);
    }
}
