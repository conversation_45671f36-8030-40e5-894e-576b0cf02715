package com.tw.documentupload.mock.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Mock DTO for error information.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class MockErrorDto {
    
    @JsonProperty("code")
    private String code;
    
    @JsonProperty("message")
    private String message;
    
    public MockErrorDto() {}
    
    public MockErrorDto(String code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public String getCode() { return code; }
    public void setCode(String code) { this.code = code; }
    
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
}
