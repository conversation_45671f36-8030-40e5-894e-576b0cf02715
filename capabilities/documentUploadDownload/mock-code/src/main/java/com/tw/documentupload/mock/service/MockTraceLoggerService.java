package com.tw.documentupload.mock.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * Mock trace logger service for audit and debugging.
 * 
 * Provides logging capabilities for the mock service with trace ID generation
 * and log management for testing and debugging purposes.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Service
public class MockTraceLoggerService {
    
    private static final Logger logger = LoggerFactory.getLogger(MockTraceLoggerService.class);
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
    
    private final ConcurrentLinkedQueue<MockLogEntry> logEntries = new ConcurrentLinkedQueue<>();
    private static final int MAX_LOG_ENTRIES = 1000;
    
    /**
     * Generates a unique trace ID.
     * 
     * @return trace ID
     */
    public String generateTraceId() {
        return "TRC-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
    
    /**
     * Logs a request with masked sensitive data.
     * 
     * @param operation the operation name
     * @param traceId the trace identifier
     * @param request the request object
     */
    public void logRequest(String operation, String traceId, Object request) {
        String timestamp = getCurrentTimestamp();
        String maskedRequest = maskSensitiveData(request.toString());
        
        MockLogEntry logEntry = new MockLogEntry(
            timestamp, traceId, operation, "REQUEST", maskedRequest
        );
        
        addLogEntry(logEntry);
        
        logger.info("MOCK_REQUEST - Operation: {}, TraceId: {}, Request: {}", 
                   operation, traceId, maskedRequest);
    }
    
    /**
     * Logs a response.
     * 
     * @param operation the operation name
     * @param traceId the trace identifier
     * @param response the response object
     */
    public void logResponse(String operation, String traceId, Object response) {
        String timestamp = getCurrentTimestamp();
        String responseStr = response != null ? response.toString() : "null";
        
        MockLogEntry logEntry = new MockLogEntry(
            timestamp, traceId, operation, "RESPONSE", responseStr
        );
        
        addLogEntry(logEntry);
        
        logger.info("MOCK_RESPONSE - Operation: {}, TraceId: {}, Response: {}", 
                   operation, traceId, responseStr);
    }
    
    /**
     * Gets all log entries.
     * 
     * @return list of log entries
     */
    public List<MockLogEntry> getAllLogs() {
        return new ArrayList<>(logEntries);
    }
    
    /**
     * Gets the number of log entries.
     * 
     * @return log count
     */
    public int getLogCount() {
        return logEntries.size();
    }
    
    /**
     * Clears all log entries.
     */
    public void clearAllLogs() {
        logEntries.clear();
        logger.info("All mock logs cleared");
    }
    
    /**
     * Adds a log entry with size management.
     * 
     * @param logEntry the log entry to add
     */
    private void addLogEntry(MockLogEntry logEntry) {
        logEntries.offer(logEntry);
        
        // Remove oldest entries if we exceed the maximum
        while (logEntries.size() > MAX_LOG_ENTRIES) {
            logEntries.poll();
        }
    }
    
    /**
     * Masks sensitive data in log messages.
     * 
     * @param data the data to mask
     * @return masked data
     */
    private String maskSensitiveData(String data) {
        if (data == null) {
            return "null";
        }
        
        // Mask common sensitive patterns
        String masked = data;
        masked = masked.replaceAll("(?i)(customerId[\"'\\s]*[:=][\"'\\s]*)([A-Z0-9]{4,})", "$1****");
        masked = masked.replaceAll("(?i)(payload[\"'\\s]*[:=][\"'\\s]*)([A-Za-z0-9+/]{20,})", "$1[PAYLOAD_MASKED]");
        
        return masked;
    }
    
    /**
     * Gets the current timestamp in ISO format.
     * 
     * @return formatted timestamp
     */
    private String getCurrentTimestamp() {
        return ZonedDateTime.now().format(TIMESTAMP_FORMAT);
    }
    
    /**
     * Inner class for log entries.
     */
    public static class MockLogEntry {
        private final String timestamp;
        private final String traceId;
        private final String operation;
        private final String type;
        private final String message;
        
        public MockLogEntry(String timestamp, String traceId, String operation, String type, String message) {
            this.timestamp = timestamp;
            this.traceId = traceId;
            this.operation = operation;
            this.type = type;
            this.message = message;
        }
        
        public String getTimestamp() { return timestamp; }
        public String getTraceId() { return traceId; }
        public String getOperation() { return operation; }
        public String getType() { return type; }
        public String getMessage() { return message; }
        
        @Override
        public String toString() {
            return String.format("[%s] %s %s-%s: %s", timestamp, traceId, operation, type, message);
        }
    }
}
