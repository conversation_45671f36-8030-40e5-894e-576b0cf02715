package com.tw.documentupload.mock.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.ZonedDateTime;

/**
 * Mock DTO for document download responses.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class MockDocumentDownloadResponseDto {
    
    @JsonProperty("documentId")
    private String documentId;
    
    @JsonProperty("payload")
    private String payload;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("metadata")
    private MockDocumentMetadataDto metadata;
    
    @JsonProperty("timestamp")
    private ZonedDateTime timestamp;
    
    public MockDocumentDownloadResponseDto() {
        this.timestamp = ZonedDateTime.now();
    }
    
    public MockDocumentDownloadResponseDto(String documentId, String payload, String name, 
                                          MockDocumentMetadataDto metadata) {
        this();
        this.documentId = documentId;
        this.payload = payload;
        this.name = name;
        this.metadata = metadata;
    }
    
    // Getters and Setters
    public String getDocumentId() { return documentId; }
    public void setDocumentId(String documentId) { this.documentId = documentId; }
    
    public String getPayload() { return payload; }
    public void setPayload(String payload) { this.payload = payload; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public MockDocumentMetadataDto getMetadata() { return metadata; }
    public void setMetadata(MockDocumentMetadataDto metadata) { this.metadata = metadata; }
    
    public ZonedDateTime getTimestamp() { return timestamp; }
    public void setTimestamp(ZonedDateTime timestamp) { this.timestamp = timestamp; }
}
