package com.tw.documentupload.mock.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.ZonedDateTime;

/**
 * Mock DTO for document upload responses.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class MockDocumentUploadResponseDto {
    
    @JsonProperty("documentId")
    private String documentId;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("timestamp")
    private ZonedDateTime timestamp;
    
    @JsonProperty("error")
    private MockErrorDto error;
    
    public MockDocumentUploadResponseDto() {
        this.timestamp = ZonedDateTime.now();
    }
    
    public MockDocumentUploadResponseDto(String documentId) {
        this();
        this.documentId = documentId;
        this.status = "Success";
    }
    
    public MockDocumentUploadResponseDto(MockErrorDto error) {
        this();
        this.error = error;
        this.status = "Failure";
    }
    
    public static MockDocumentUploadResponseDto success(String documentId) {
        return new MockDocumentUploadResponseDto(documentId);
    }
    
    public static MockDocumentUploadResponseDto failure(MockErrorDto error) {
        return new MockDocumentUploadResponseDto(error);
    }
    
    public static MockDocumentUploadResponseDto failure(String errorCode, String errorMessage) {
        return new MockDocumentUploadResponseDto(new MockErrorDto(errorCode, errorMessage));
    }
    
    // Getters and Setters
    public String getDocumentId() { return documentId; }
    public void setDocumentId(String documentId) { this.documentId = documentId; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public ZonedDateTime getTimestamp() { return timestamp; }
    public void setTimestamp(ZonedDateTime timestamp) { this.timestamp = timestamp; }
    
    public MockErrorDto getError() { return error; }
    public void setError(MockErrorDto error) { this.error = error; }
    
    public boolean isSuccess() {
        return "Success".equals(status) && documentId != null && error == null;
    }
}
