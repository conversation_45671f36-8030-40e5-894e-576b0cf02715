package com.tw.documentupload.mock.controller;

import com.tw.documentupload.mock.dto.*;
import com.tw.documentupload.mock.service.MockDocumentService;
import com.tw.documentupload.mock.service.MockTraceLoggerService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Mock REST Controller for document operations.
 * 
 * Provides mock endpoints for testing document upload and download workflows
 * with configurable simulation behavior and comprehensive logging.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@RestController
@RequestMapping("/documents")
public class MockDocumentController {
    
    private static final Logger logger = LoggerFactory.getLogger(MockDocumentController.class);
    
    private final MockDocumentService mockDocumentService;
    private final MockTraceLoggerService traceLoggerService;
    
    @Value("${mock.simulation.latency-ms:100}")
    private long simulationLatencyMs;
    
    @Value("${mock.simulation.failure-rate:0.0}")
    private double simulationFailureRate;
    
    @Autowired
    public MockDocumentController(MockDocumentService mockDocumentService,
                                 MockTraceLoggerService traceLoggerService) {
        this.mockDocumentService = mockDocumentService;
        this.traceLoggerService = traceLoggerService;
    }
    
    /**
     * Mock document upload endpoint.
     * 
     * @param request the upload request
     * @param bindingResult validation results
     * @return upload response
     */
    @PostMapping("/upload")
    public ResponseEntity<MockDocumentUploadResponseDto> uploadDocument(
            @Valid @RequestBody MockDocumentUploadRequestDto request,
            BindingResult bindingResult) {
        
        String traceId = traceLoggerService.generateTraceId();
        
        try {
            // Add trace ID to response headers
            traceLoggerService.logRequest("UPLOAD", traceId, request);
            
            // Simulate latency
            simulateLatency();
            
            // Simulate random failures
            if (shouldSimulateFailure()) {
                MockDocumentUploadResponseDto failureResponse = 
                    MockDocumentUploadResponseDto.failure("SIM_FAILURE", "Simulated failure for testing");
                traceLoggerService.logResponse("UPLOAD", traceId, failureResponse);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(failureResponse);
            }
            
            // Check for validation errors
            if (bindingResult.hasErrors()) {
                String validationErrors = bindingResult.getFieldErrors().stream()
                    .map(error -> error.getField() + ": " + error.getDefaultMessage())
                    .collect(Collectors.joining(", "));
                
                MockDocumentUploadResponseDto errorResponse = 
                    MockDocumentUploadResponseDto.failure("MISSING_FIELD", validationErrors);
                traceLoggerService.logResponse("UPLOAD", traceId, errorResponse);
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            // Process upload
            MockDocumentUploadResponseDto response = mockDocumentService.uploadDocument(request);
            traceLoggerService.logResponse("UPLOAD", traceId, response);
            
            return ResponseEntity.ok()
                .header("X-Trace-Id", traceId)
                .body(response);
            
        } catch (Exception e) {
            logger.error("Error in mock upload endpoint", e);
            MockDocumentUploadResponseDto errorResponse = 
                MockDocumentUploadResponseDto.failure("ERR203", "Internal server error");
            traceLoggerService.logResponse("UPLOAD", traceId, errorResponse);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Mock document download endpoint.
     * 
     * @param documentId the document identifier
     * @return download response or 404
     */
    @GetMapping("/{documentId}")
    public ResponseEntity<?> downloadDocument(
            @PathVariable String documentId) {
        
        String traceId = traceLoggerService.generateTraceId();
        
        try {
            traceLoggerService.logRequest("DOWNLOAD", traceId, Map.of("documentId", documentId));
            
            // Simulate latency
            simulateLatency();
            
            // Simulate random failures
            if (shouldSimulateFailure()) {
                traceLoggerService.logResponse("DOWNLOAD", traceId, "Simulated failure");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Simulated failure for testing");
            }
            
            // Process download
            MockDocumentDownloadResponseDto response = mockDocumentService.downloadDocument(documentId);
            
            if (response != null) {
                traceLoggerService.logResponse("DOWNLOAD", traceId, response);
                return ResponseEntity.ok()
                    .header("X-Trace-Id", traceId)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(response);
            } else {
                traceLoggerService.logResponse("DOWNLOAD", traceId, "Document not found");
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            logger.error("Error in mock download endpoint", e);
            traceLoggerService.logResponse("DOWNLOAD", traceId, "Internal server error");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Internal server error occurred");
        }
    }
    
    /**
     * Admin endpoint to view logs.
     * 
     * @return log entries
     */
    @GetMapping("/admin/logs")
    public ResponseEntity<Map<String, Object>> getLogs() {
        Map<String, Object> logs = new HashMap<>();
        logs.put("logs", traceLoggerService.getAllLogs());
        logs.put("totalEntries", traceLoggerService.getLogCount());
        return ResponseEntity.ok(logs);
    }
    
    /**
     * Admin endpoint to configure simulation.
     * 
     * @param config simulation configuration
     * @return configuration result
     */
    @PostMapping("/admin/config")
    public ResponseEntity<Map<String, Object>> configureSimulation(@RequestBody Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();
        
        if (config.containsKey("latencyMs")) {
            simulationLatencyMs = ((Number) config.get("latencyMs")).longValue();
        }
        
        if (config.containsKey("failureRate")) {
            simulationFailureRate = ((Number) config.get("failureRate")).doubleValue();
        }
        
        result.put("currentLatencyMs", simulationLatencyMs);
        result.put("currentFailureRate", simulationFailureRate);
        result.put("message", "Configuration updated successfully");
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * Admin endpoint to reset the mock service.
     * 
     * @return reset result
     */
    @PostMapping("/admin/reset")
    public ResponseEntity<Map<String, Object>> resetMockService() {
        mockDocumentService.clearAllDocuments();
        traceLoggerService.clearAllLogs();
        
        Map<String, Object> result = new HashMap<>();
        result.put("message", "Mock service reset successfully");
        result.put("documentsCleared", true);
        result.put("logsCleared", true);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * Health check endpoint.
     * 
     * @return health status
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "document-upload-mock-service");
        health.put("documentsStored", mockDocumentService.getDocumentCount());
        health.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(health);
    }
    
    /**
     * Simulates processing latency.
     */
    private void simulateLatency() {
        if (simulationLatencyMs > 0) {
            try {
                Thread.sleep(simulationLatencyMs);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }
    
    /**
     * Determines if a failure should be simulated.
     * 
     * @return true if failure should be simulated
     */
    private boolean shouldSimulateFailure() {
        return simulationFailureRate > 0 && Math.random() < simulationFailureRate;
    }
}
