package com.tw.documentupload.mock.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * Mock DTO for document upload requests.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class MockDocumentUploadRequestDto {
    
    @Valid
    @NotNull(message = "Document is mandatory")
    @JsonProperty("document")
    private MockDocumentDto document;
    
    public MockDocumentUploadRequestDto() {}
    
    public MockDocumentUploadRequestDto(MockDocumentDto document) {
        this.document = document;
    }
    
    public MockDocumentDto getDocument() {
        return document;
    }
    
    public void setDocument(MockDocumentDto document) {
        this.document = document;
    }
}
