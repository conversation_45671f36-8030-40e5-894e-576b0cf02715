package com.tw.documentupload.mock.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * OpenAPI configuration for Document Upload Download Mock API.
 *
 * This configuration provides the OpenAPI specification for the mock service.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-23
 */
@Configuration
public class OpenApiConfig implements WebMvcConfigurer {

    @Value("${server.port:8083}")
    private String serverPort;

    /**
     * Configure resource handlers to serve static OpenAPI YAML files.
     *
     * @param registry the resource handler registry
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/swagger/**")
                .addResourceLocations("classpath:/static/swagger/");
    }

    /**
     * Configures the OpenAPI specification for the Mock Document Upload Download service.
     *
     * @return OpenAPI configuration
     */
    @Bean
    public OpenAPI documentUploadDownloadMockOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Document Upload Download Mock API")
                        .description("""
                                A mock service for the Document Upload Download API designed for development and QA environments.

                                This mock service simulates the behavior of the document upload and download API with:
                                - Configurable response delays and failure rates
                                - Realistic validation and error scenarios
                                - In-memory document storage for testing
                                - Admin endpoints for configuration and monitoring
                                - Comprehensive logging and tracing

                                The mock service is designed to test document submission and retrieval workflows,
                                metadata validation, and response handling without relying on the production system.
                                """)
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("TW Development Team")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("Proprietary")
                                .url("https://tw.com/license")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort)
                                .description("Mock service server")
                ));
    }
}
