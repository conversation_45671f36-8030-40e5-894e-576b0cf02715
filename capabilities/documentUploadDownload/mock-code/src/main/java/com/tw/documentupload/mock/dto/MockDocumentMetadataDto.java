package com.tw.documentupload.mock.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * Mock DTO for document metadata.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class MockDocumentMetadataDto {
    
    @NotBlank(message = "Customer ID is mandatory")
    @Size(max = 50, message = "Customer ID must not exceed 50 characters")
    @JsonProperty("customerId")
    private String customerId;
    
    @NotBlank(message = "Application ID is mandatory")
    @Size(max = 50, message = "Application ID must not exceed 50 characters")
    @JsonProperty("applicationId")
    private String applicationId;
    
    @NotBlank(message = "Product code is mandatory")
    @Size(max = 50, message = "Product code must not exceed 50 characters")
    @JsonProperty("productCode")
    private String productCode;
    
    @NotBlank(message = "Loan category is mandatory")
    @Size(max = 50, message = "Loan category must not exceed 50 characters")
    @JsonProperty("loanCategory")
    private String loanCategory;
    
    @NotBlank(message = "Descriptor is mandatory")
    @Size(max = 50, message = "Descriptor must not exceed 50 characters")
    @JsonProperty("descriptor")
    private String descriptor;
    
    @NotBlank(message = "Created date is mandatory")
    @Pattern(regexp = "^\\d{2}/\\d{2}/\\d{4}$", message = "Created date must be in DD/MM/YYYY format")
    @Size(max = 50, message = "Created date must not exceed 50 characters")
    @JsonProperty("createdDate")
    private String createdDate;
    
    @Size(max = 50, message = "Field1 must not exceed 50 characters")
    @JsonProperty("field1")
    private String field1;
    
    @Size(max = 50, message = "Field2 must not exceed 50 characters")
    @JsonProperty("field2")
    private String field2;
    
    public MockDocumentMetadataDto() {}
    
    public MockDocumentMetadataDto(String customerId, String applicationId, String productCode,
                                  String loanCategory, String descriptor, String createdDate) {
        this.customerId = customerId;
        this.applicationId = applicationId;
        this.productCode = productCode;
        this.loanCategory = loanCategory;
        this.descriptor = descriptor;
        this.createdDate = createdDate;
    }
    
    // Getters and Setters
    public String getCustomerId() { return customerId; }
    public void setCustomerId(String customerId) { this.customerId = customerId; }
    
    public String getApplicationId() { return applicationId; }
    public void setApplicationId(String applicationId) { this.applicationId = applicationId; }
    
    public String getProductCode() { return productCode; }
    public void setProductCode(String productCode) { this.productCode = productCode; }
    
    public String getLoanCategory() { return loanCategory; }
    public void setLoanCategory(String loanCategory) { this.loanCategory = loanCategory; }
    
    public String getDescriptor() { return descriptor; }
    public void setDescriptor(String descriptor) { this.descriptor = descriptor; }
    
    public String getCreatedDate() { return createdDate; }
    public void setCreatedDate(String createdDate) { this.createdDate = createdDate; }
    
    public String getField1() { return field1; }
    public void setField1(String field1) { this.field1 = field1; }
    
    public String getField2() { return field2; }
    public void setField2(String field2) { this.field2 = field2; }
}
