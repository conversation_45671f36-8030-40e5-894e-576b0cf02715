package com.tw.documentupload.mock.service;

import com.tw.documentupload.mock.dto.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Mock service for document operations.
 *
 * Simulates document storage, validation, and retrieval with configurable
 * behavior for testing different scenarios.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Service
public class MockDocumentService {

    private static final Logger logger = LoggerFactory.getLogger(MockDocumentService.class);

    private final Map<String, MockStoredDocument> documentRegistry = new ConcurrentHashMap<>();

    @Value("${mock.validation.max-file-size-mb:5}")
    private int maxFileSizeMB;

    @Value("${mock.validation.max-field-length:50}")
    private int maxFieldLength;

    @Value("${mock.simulation.expired-document-pattern:DOCEXPIRED.*}")
    private String expiredDocumentPattern;

    /**
     * Uploads a document with validation.
     *
     * @param request the upload request
     * @return upload response
     */
    public MockDocumentUploadResponseDto uploadDocument(MockDocumentUploadRequestDto request) {
        try {
            // Validate request
            MockValidationResult validation = validateUploadRequest(request);
            if (!validation.isValid()) {
                return MockDocumentUploadResponseDto.failure(validation.getErrorCode(), validation.getErrorMessage());
            }

            // Generate document ID
            String documentId = generateDocumentId();

            // Store document
            MockStoredDocument storedDocument = new MockStoredDocument(
                documentId,
                request.getDocument().getName(),
                request.getDocument().getFolder(),
                request.getDocument().getPayload(),
                request.getDocument().getMetadata()
            );

            documentRegistry.put(documentId, storedDocument);

            logger.info("Mock document uploaded successfully: documentId={}, name={}",
                       documentId, request.getDocument().getName());

            return MockDocumentUploadResponseDto.success(documentId);

        } catch (Exception e) {
            logger.error("Error uploading mock document", e);
            return MockDocumentUploadResponseDto.failure("ERR203", "Internal server error");
        }
    }

    /**
     * Downloads a document by ID.
     *
     * @param documentId the document identifier
     * @return download response or null if not found
     */
    public MockDocumentDownloadResponseDto downloadDocument(String documentId) {
        try {
            // Check for expired document simulation
            if (documentId.matches(expiredDocumentPattern)) {
                logger.info("Simulating expired document: {}", documentId);
                return null;
            }

            MockStoredDocument storedDocument = documentRegistry.get(documentId);
            if (storedDocument == null) {
                logger.info("Mock document not found: {}", documentId);
                return null;
            }

            logger.info("Mock document downloaded successfully: documentId={}", documentId);

            return new MockDocumentDownloadResponseDto(
                documentId,
                storedDocument.getPayload(),
                storedDocument.getName(),
                storedDocument.getMetadata()
            );

        } catch (Exception e) {
            logger.error("Error downloading mock document: {}", documentId, e);
            return null;
        }
    }

    /**
     * Gets the number of stored documents.
     *
     * @return document count
     */
    public int getDocumentCount() {
        return documentRegistry.size();
    }

    /**
     * Clears all stored documents.
     */
    public void clearAllDocuments() {
        documentRegistry.clear();
        logger.info("All mock documents cleared");
    }

    /**
     * Validates the upload request.
     *
     * @param request the upload request
     * @return validation result
     */
    private MockValidationResult validateUploadRequest(MockDocumentUploadRequestDto request) {
        MockDocumentDto document = request.getDocument();
        MockDocumentMetadataDto metadata = document.getMetadata();

        // Validate payload size
        if (!isValidPayloadSize(document.getPayload())) {
            return MockValidationResult.invalid("ERR001", "Document size exceeds " + maxFileSizeMB + "MB");
        }

        // Validate field lengths
        if (!isValidFieldLength(document.getName()) ||
            !isValidFieldLength(metadata.getCustomerId()) ||
            !isValidFieldLength(metadata.getApplicationId()) ||
            !isValidFieldLength(metadata.getProductCode()) ||
            !isValidFieldLength(metadata.getLoanCategory()) ||
            !isValidFieldLength(metadata.getDescriptor()) ||
            !isValidFieldLength(metadata.getCreatedDate()) ||
            !isValidFieldLength(metadata.getField1()) ||
            !isValidFieldLength(metadata.getField2())) {
            return MockValidationResult.invalid("FIELD_TOO_LONG", "One or more fields exceed maximum length");
        }

        // Validate base64 payload
        if (!isValidBase64(document.getPayload())) {
            return MockValidationResult.invalid("ERR001", "Invalid base64 payload");
        }

        return MockValidationResult.valid();
    }

    /**
     * Validates payload size.
     *
     * @param base64Payload the base64 payload
     * @return true if valid size
     */
    private boolean isValidPayloadSize(String base64Payload) {
        if (base64Payload == null) return false;

        try {
            byte[] decoded = Base64.getDecoder().decode(base64Payload);
            long sizeInMB = decoded.length / (1024 * 1024);
            return sizeInMB <= maxFileSizeMB;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Validates field length.
     *
     * @param field the field value
     * @return true if valid length
     */
    private boolean isValidFieldLength(String field) {
        return field == null || field.length() <= maxFieldLength;
    }

    /**
     * Validates base64 format.
     *
     * @param payload the payload to validate
     * @return true if valid base64
     */
    private boolean isValidBase64(String payload) {
        if (payload == null || payload.isEmpty()) return false;

        try {
            Base64.getDecoder().decode(payload);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Generates a mock document ID in the format expected by the main service.
     * Format: DOC + YYYYMMDDHHMMSS + 6-digit sequence + 2-char random = 25 chars total
     *
     * @return document ID
     */
    private String generateDocumentId() {
        // Get current timestamp in YYYYMMDDHHMMSS format
        String timestamp = java.time.LocalDateTime.now()
            .format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

        // Generate 6-digit sequence (using random for simplicity in mock)
        String sequence = String.format("%06d", (int)(Math.random() * 999999));

        // Generate 2-character random suffix
        String randomSuffix = UUID.randomUUID().toString().replace("-", "").substring(0, 2).toUpperCase();

        return "DOC" + timestamp + sequence + randomSuffix;
    }

    /**
     * Inner class for stored documents.
     */
    private static class MockStoredDocument {
        private final String documentId;
        private final String name;
        private final String folder;
        private final String payload;
        private final MockDocumentMetadataDto metadata;

        public MockStoredDocument(String documentId, String name, String folder,
                                 String payload, MockDocumentMetadataDto metadata) {
            this.documentId = documentId;
            this.name = name;
            this.folder = folder;
            this.payload = payload;
            this.metadata = metadata;
        }

        public String getDocumentId() { return documentId; }
        public String getName() { return name; }
        public String getFolder() { return folder; }
        public String getPayload() { return payload; }
        public MockDocumentMetadataDto getMetadata() { return metadata; }
    }

    /**
     * Inner class for validation results.
     */
    private static class MockValidationResult {
        private final boolean valid;
        private final String errorCode;
        private final String errorMessage;

        private MockValidationResult(boolean valid, String errorCode, String errorMessage) {
            this.valid = valid;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public static MockValidationResult valid() {
            return new MockValidationResult(true, null, null);
        }

        public static MockValidationResult invalid(String errorCode, String errorMessage) {
            return new MockValidationResult(false, errorCode, errorMessage);
        }

        public boolean isValid() { return valid; }
        public String getErrorCode() { return errorCode; }
        public String getErrorMessage() { return errorMessage; }
    }
}
