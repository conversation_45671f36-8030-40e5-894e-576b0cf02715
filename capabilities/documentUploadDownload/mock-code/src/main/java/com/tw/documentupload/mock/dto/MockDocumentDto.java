package com.tw.documentupload.mock.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * Mock DTO for document information.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class MockDocumentDto {
    
    @NotBlank(message = "Document name is mandatory")
    @Size(max = 50, message = "Document name must not exceed 50 characters")
    @JsonProperty("name")
    private String name;
    
    @NotBlank(message = "Folder path is mandatory")
    @Size(max = 255, message = "Folder path must not exceed 255 characters")
    @JsonProperty("folder")
    private String folder;
    
    @NotBlank(message = "Document payload is mandatory")
    @JsonProperty("payload")
    private String payload;
    
    @Valid
    @NotNull(message = "Document metadata is mandatory")
    @JsonProperty("metadata")
    private MockDocumentMetadataDto metadata;
    
    public MockDocumentDto() {}
    
    public MockDocumentDto(String name, String folder, String payload, MockDocumentMetadataDto metadata) {
        this.name = name;
        this.folder = folder;
        this.payload = payload;
        this.metadata = metadata;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getFolder() {
        return folder;
    }
    
    public void setFolder(String folder) {
        this.folder = folder;
    }
    
    public String getPayload() {
        return payload;
    }
    
    public void setPayload(String payload) {
        this.payload = payload;
    }
    
    public MockDocumentMetadataDto getMetadata() {
        return metadata;
    }
    
    public void setMetadata(MockDocumentMetadataDto metadata) {
        this.metadata = metadata;
    }
}
