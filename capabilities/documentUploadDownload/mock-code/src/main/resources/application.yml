server:
  port: 8083
  servlet:
    context-path: /api/document-upload-download/internal

spring:
  application:
    name: document-upload-mock-service

# Logging configuration
logging:
  level:
    com.tw.documentupload.mock: INFO
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always



# Mock service configuration
mock:
  simulation:
    enabled: true
    latency-ms: 100
    failure-rate: 0.0
    expired-document-pattern: "DOCEXPIRED.*"
  
  storage:
    max-documents: 10000
    cleanup-interval-minutes: 60
  
  validation:
    max-file-size-mb: 5
    max-field-length: 50
  
  auth:
    enabled: false
    username: admin
    password: password

# SpringDoc OpenAPI configuration
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    try-it-out-enabled: true
  show-actuator: false
