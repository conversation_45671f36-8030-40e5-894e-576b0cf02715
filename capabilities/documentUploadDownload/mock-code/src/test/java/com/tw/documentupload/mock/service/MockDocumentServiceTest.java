package com.tw.documentupload.mock.service;

import com.tw.documentupload.mock.dto.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Base64;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for MockDocumentService.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@DisplayName("MockDocumentService Tests")
class MockDocumentServiceTest {
    
    private MockDocumentService mockDocumentService;
    
    @BeforeEach
    void setUp() {
        mockDocumentService = new MockDocumentService();
        // Set test configuration
        ReflectionTestUtils.setField(mockDocumentService, "maxFileSizeMB", 5);
        ReflectionTestUtils.setField(mockDocumentService, "maxFieldLength", 50);
        ReflectionTestUtils.setField(mockDocumentService, "expiredDocumentPattern", "DOCEXPIRED.*");
    }
    
    @Test
    @DisplayName("Should upload document successfully with valid request")
    void shouldUploadDocumentSuccessfullyWithValidRequest() {
        // Given
        MockDocumentUploadRequestDto request = createValidUploadRequest();
        
        // When
        MockDocumentUploadResponseDto response = mockDocumentService.uploadDocument(request);
        
        // Then
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals("Success", response.getStatus());
        assertNotNull(response.getDocumentId());
        assertTrue(response.getDocumentId().startsWith("DOC"));
        assertNull(response.getError());
    }
    
    @Test
    @DisplayName("Should reject upload with oversized payload")
    void shouldRejectUploadWithOversizedPayload() {
        // Given
        MockDocumentUploadRequestDto request = createValidUploadRequest();
        // Create a large payload (6MB)
        byte[] largePayload = new byte[6 * 1024 * 1024];
        String base64Payload = Base64.getEncoder().encodeToString(largePayload);
        request.getDocument().setPayload(base64Payload);
        
        // When
        MockDocumentUploadResponseDto response = mockDocumentService.uploadDocument(request);
        
        // Then
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("Failure", response.getStatus());
        assertNotNull(response.getError());
        assertEquals("ERR001", response.getError().getCode());
    }
    
    @Test
    @DisplayName("Should reject upload with invalid base64 payload")
    void shouldRejectUploadWithInvalidBase64Payload() {
        // Given
        MockDocumentUploadRequestDto request = createValidUploadRequest();
        request.getDocument().setPayload("Invalid base64 payload!@#$%");
        
        // When
        MockDocumentUploadResponseDto response = mockDocumentService.uploadDocument(request);
        
        // Then
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("Failure", response.getStatus());
        assertNotNull(response.getError());
        assertEquals("ERR001", response.getError().getCode());
    }
    
    @Test
    @DisplayName("Should download document successfully after upload")
    void shouldDownloadDocumentSuccessfullyAfterUpload() {
        // Given - Upload a document first
        MockDocumentUploadRequestDto uploadRequest = createValidUploadRequest();
        MockDocumentUploadResponseDto uploadResponse = mockDocumentService.uploadDocument(uploadRequest);
        String documentId = uploadResponse.getDocumentId();
        
        // When
        MockDocumentDownloadResponseDto downloadResponse = mockDocumentService.downloadDocument(documentId);
        
        // Then
        assertNotNull(downloadResponse);
        assertEquals(documentId, downloadResponse.getDocumentId());
        assertEquals("Sanction_Letter.pdf", downloadResponse.getName());
        assertNotNull(downloadResponse.getPayload());
        assertNotNull(downloadResponse.getMetadata());
        assertEquals("CUST001", downloadResponse.getMetadata().getCustomerId());
        assertEquals("MLP123", downloadResponse.getMetadata().getApplicationId());
    }
    
    @Test
    @DisplayName("Should return null for non-existent document")
    void shouldReturnNullForNonExistentDocument() {
        // Given
        String nonExistentDocumentId = "DOCNONEXISTENT123";
        
        // When
        MockDocumentDownloadResponseDto response = mockDocumentService.downloadDocument(nonExistentDocumentId);
        
        // Then
        assertNull(response);
    }
    
    @Test
    @DisplayName("Should simulate expired document")
    void shouldSimulateExpiredDocument() {
        // Given
        String expiredDocumentId = "DOCEXPIRED123";
        
        // When
        MockDocumentDownloadResponseDto response = mockDocumentService.downloadDocument(expiredDocumentId);
        
        // Then
        assertNull(response);
    }
    
    @Test
    @DisplayName("Should track document count correctly")
    void shouldTrackDocumentCountCorrectly() {
        // Given
        assertEquals(0, mockDocumentService.getDocumentCount());
        
        // When - Upload multiple documents
        mockDocumentService.uploadDocument(createValidUploadRequest());
        mockDocumentService.uploadDocument(createValidUploadRequest());
        mockDocumentService.uploadDocument(createValidUploadRequest());
        
        // Then
        assertEquals(3, mockDocumentService.getDocumentCount());
    }
    
    @Test
    @DisplayName("Should clear all documents")
    void shouldClearAllDocuments() {
        // Given - Upload some documents
        mockDocumentService.uploadDocument(createValidUploadRequest());
        mockDocumentService.uploadDocument(createValidUploadRequest());
        assertEquals(2, mockDocumentService.getDocumentCount());
        
        // When
        mockDocumentService.clearAllDocuments();
        
        // Then
        assertEquals(0, mockDocumentService.getDocumentCount());
    }
    
    /**
     * Creates a valid mock upload request for testing.
     * 
     * @return valid upload request
     */
    private MockDocumentUploadRequestDto createValidUploadRequest() {
        // Create metadata
        MockDocumentMetadataDto metadata = new MockDocumentMetadataDto(
            "CUST001",
            "MLP123",
            "FOUR_WHEELER_ETB_PA",
            "ETB",
            "SanctionLetter",
            "15/06/2025"
        );
        metadata.setField1("AdditionalInfo1");
        metadata.setField2("AdditionalInfo2");
        
        // Create document with base64 payload
        String content = "This is a test document content for PDF simulation";
        String base64Payload = Base64.getEncoder().encodeToString(content.getBytes());
        
        MockDocumentDto document = new MockDocumentDto(
            "Sanction_Letter.pdf",
            "maximus/application/MLP123/",
            base64Payload,
            metadata
        );
        
        return new MockDocumentUploadRequestDto(document);
    }
}
