version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: bre-postgres
    environment:
      POSTGRES_DB: bre_db
      POSTGRES_USER: bre_user
      POSTGRES_PASSWORD: bre_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U bre_user -d bre_db"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
