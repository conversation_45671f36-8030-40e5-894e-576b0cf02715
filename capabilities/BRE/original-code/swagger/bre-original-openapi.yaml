openapi: 3.0.3
info:
  title: BRE Application API
  description: |
    Business Rule Engine for Loan Eligibility Evaluation
    
    This API provides comprehensive loan eligibility evaluation services using business rules engine.
    It processes applicant data, loan requirements, and additional verification data to determine
    loan approval decisions with detailed reasoning and fee calculations.
  version: 1.0.0
  contact:
    name: BRE Team
    email: <EMAIL>
  license:
    name: Internal Use
    url: https://loan.com/license



paths:
  /v1/loan-eligibility/evaluate:
    post:
      tags:
        - Loan Eligibility
      summary: Evaluate loan eligibility
      description: |
        Evaluates loan eligibility based on applicant data, loan requirements, and additional verification data.
        Returns comprehensive decision with reasoning, eligible amounts, and fee details.
      operationId: evaluateLoanEligibility
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BRERequestDTO'
            examples:
              salaried_applicant:
                summary: Salaried Applicant Example
                value:
                  header:
                    applicationId: "APP_2024_001"
                    productCode: "PL_STANDARD"
                    loanAmount: 500000
                    loanTenure: 36
                  applicants:
                    - personalInfo:
                        firstName: "<PERSON>"
                        lastName: "Doe"
                        dateOfBirth: "1990-05-15"
                        gender: "MALE"
                        mobileNumber: "9876543210"
                        emailId: "<EMAIL>"
                        panNumber: "**********"
                      profession: "SALARIED"
                      income: 750000
                      addresses:
                        - addressType: "CURRENT"
                          addressLine1: "123 Main Street"
                          city: "Mumbai"
                          state: "Maharashtra"
                          pincode: "400001"
                      cibilData:
                        cibilScore: 750
                        cibilReportDate: "2024-06-01"
                      nsdlData:
                        nsdlNameMatchPercentage: 95.5
                        panStatus: "VALID"
                  additionalData:
                    hunter: "NO_MATCH"
                    posidexDedupe: "NO_DUPLICATE"
                    cibilBureauData: "VERIFIED"
      responses:
        '200':
          description: Loan eligibility evaluation completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BREProcessingResponseDTO'
              examples:
                approved_loan:
                  summary: Approved Loan Example
                  value:
                    success: true
                    message: "Loan eligibility evaluation completed successfully"
                    breResponse:
                      applicationId: "APP_2024_001"
                      decision: "APPROVE"
                      eligibleFOIR: "40%"
                      eligibleAmount: 450000
                      decisionResult:
                        - ruleId: "CIBIL_SCORE_CHECK"
                          decisionResult: "APPROVE"
                          reasonText: "CIBIL score 750 meets minimum requirement of 650"
                        - ruleId: "INCOME_VERIFICATION"
                          decisionResult: "APPROVE"
                          reasonText: "Monthly income sufficient for requested loan amount"
                      applicant:
                        profileId: "PROF_001"
                        riskCategory: "LOW"
                        creditScore: 750
                        eligibilityStatus: "ELIGIBLE"
                      feeDetails:
                        processingFee: 5000
                        adminFee: 1000
                        feeStructure: "STANDARD"
                      timestamp: "2024-06-23T10:30:00Z"
                      traceId: "BRE_TRACE_123456"
                    traceId: "BRE_TRACE_123456"
                    timestamp: "2024-06-23T10:30:00Z"
        '400':
          description: Bad request - validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponseDTO'
              examples:
                validation_error:
                  summary: Validation Error Example
                  value:
                    errorCode: "VALIDATION_FAILED"
                    errorMessage: "Loan amount must be between 10,000 and 10,000,000"
                    traceId: "BRE_TRACE_123456"
                    timestamp: "2024-06-23T10:30:00Z"
                    path: "/v1/loan-eligibility/evaluate"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponseDTO'
              examples:
                server_error:
                  summary: Server Error Example
                  value:
                    errorCode: "INTERNAL_SERVER_ERROR"
                    errorMessage: "An unexpected error occurred during loan evaluation"
                    traceId: "BRE_TRACE_123456"
                    timestamp: "2024-06-23T10:30:00Z"
                    path: "/v1/loan-eligibility/evaluate"

  /v1/loan-eligibility/health:
    get:
      tags:
        - Health Check
      summary: Health check for loan eligibility service
      description: Returns the health status of the loan eligibility evaluation service
      operationId: loanEligibilityHealthCheck
      responses:
        '200':
          description: Service is healthy
          content:
            text/plain:
              schema:
                type: string
                example: "BRE Service is healthy"

  /v1/bre/original/health:
    get:
      tags:
        - Health Check
      summary: Detailed health check for BRE service
      description: Returns detailed health status information for the BRE service
      operationId: breHealthCheck
      responses:
        '200':
          description: Service is healthy with details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatusDTO'
              example:
                status: "UP"
                service: "BRE Original"
                timestamp: "2024-06-23T10:30:00Z"
                version: "1.0.0"

components:
  schemas:
    BRERequestDTO:
      type: object
      required:
        - header
        - applicants
        - additionalData
      properties:
        header:
          $ref: '#/components/schemas/HeaderDTO'
        applicants:
          type: array
          items:
            $ref: '#/components/schemas/ApplicantDTO'
          minItems: 1
          description: List of applicants (primary and co-applicants)
        additionalData:
          $ref: '#/components/schemas/AdditionalDataDTO'

    HeaderDTO:
      type: object
      required:
        - applicationId
        - productCode
        - loanAmount
        - loanTenure
      properties:
        applicationId:
          type: string
          maxLength: 100
          description: Unique application identifier
          example: "APP_2024_001"
        productCode:
          type: string
          maxLength: 50
          description: Product code for the loan type
          example: "PL_STANDARD"
        loanAmount:
          type: number
          format: decimal
          minimum: 10000
          maximum: 10000000
          description: Requested loan amount
          example: 500000
        loanTenure:
          type: integer
          minimum: 6
          maximum: 360
          description: Loan tenure in months
          example: 36

    ApplicantDTO:
      type: object
      required:
        - personalInfo
        - profession
        - income
        - addresses
        - cibilData
        - nsdlData
      properties:
        personalInfo:
          $ref: '#/components/schemas/PersonalInfoDTO'
        profession:
          $ref: '#/components/schemas/Profession'
        income:
          type: number
          format: decimal
          minimum: 0
          description: Annual income
          example: 750000
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/AddressDTO'
          minItems: 1
        cibilData:
          $ref: '#/components/schemas/CibilDataDTO'
        nsdlData:
          $ref: '#/components/schemas/NSDLDataDTO'

    PersonalInfoDTO:
      type: object
      required:
        - firstName
        - lastName
        - dateOfBirth
        - gender
        - mobileNumber
        - emailId
        - panNumber
      properties:
        firstName:
          type: string
          maxLength: 50
          description: First name
          example: "John"
        lastName:
          type: string
          maxLength: 50
          description: Last name
          example: "Doe"
        dateOfBirth:
          type: string
          format: date
          description: Date of birth in YYYY-MM-DD format
          example: "1990-05-15"
        gender:
          $ref: '#/components/schemas/Gender'
        mobileNumber:
          type: string
          pattern: '^[0-9]{10}$'
          description: 10-digit mobile number
          example: "9876543210"
        emailId:
          type: string
          format: email
          maxLength: 100
          description: Email address
          example: "<EMAIL>"
        panNumber:
          type: string
          pattern: '^[A-Z]{5}[0-9]{4}[A-Z]{1}$'
          description: PAN number in standard format
          example: "**********"

    AddressDTO:
      type: object
      required:
        - addressType
        - addressLine1
        - city
        - state
        - pincode
      properties:
        addressType:
          $ref: '#/components/schemas/AddressType'
        addressLine1:
          type: string
          maxLength: 200
          description: Address line 1
          example: "123 Main Street"
        addressLine2:
          type: string
          maxLength: 200
          description: Address line 2
          example: "Near City Center"
        city:
          type: string
          maxLength: 50
          description: City name
          example: "Mumbai"
        state:
          type: string
          maxLength: 50
          description: State name
          example: "Maharashtra"
        pincode:
          type: string
          pattern: '^[0-9]{6}$'
          description: 6-digit pincode
          example: "400001"

    CibilDataDTO:
      type: object
      required:
        - cibilScore
        - cibilReportDate
      properties:
        cibilScore:
          type: integer
          minimum: 300
          maximum: 900
          description: CIBIL credit score
          example: 750
        cibilReportDate:
          type: string
          format: date
          description: Date of CIBIL report generation
          example: "2024-06-01"

    NSDLDataDTO:
      type: object
      required:
        - nsdlNameMatchPercentage
        - panStatus
      properties:
        nsdlNameMatchPercentage:
          type: number
          format: decimal
          minimum: 0.0
          maximum: 100.0
          description: Name match percentage from NSDL verification
          example: 95.5
        panStatus:
          type: string
          enum: [VALID, INVALID, NOT_FOUND]
          description: PAN verification status
          example: "VALID"

    AdditionalDataDTO:
      type: object
      required:
        - hunter
        - posidexDedupe
        - cibilBureauData
      properties:
        hunter:
          type: string
          description: Hunter verification data
          example: "NO_MATCH"
        posidexDedupe:
          type: string
          description: Posidex deduplication data
          example: "NO_DUPLICATE"
        cibilBureauData:
          type: string
          description: CIBIL bureau verification data
          example: "VERIFIED"

    BREProcessingResponseDTO:
      type: object
      required:
        - success
        - message
        - traceId
        - timestamp
      properties:
        success:
          type: boolean
          description: Whether the processing was successful
          example: true
        message:
          type: string
          description: Processing result message
          example: "Loan eligibility evaluation completed successfully"
        breResponse:
          $ref: '#/components/schemas/BREResponseDTO'
        traceId:
          type: string
          description: Unique trace identifier
          example: "BRE_TRACE_123456"
        timestamp:
          type: string
          format: date-time
          description: Response timestamp
          example: "2024-06-23T10:30:00Z"

    BREResponseDTO:
      type: object
      required:
        - applicationId
        - decision
        - timestamp
        - traceId
      properties:
        applicationId:
          type: string
          description: Application identifier
          example: "APP_2024_001"
        decision:
          $ref: '#/components/schemas/DecisionType'
        eligibleFOIR:
          type: string
          description: Eligible Fixed Obligation to Income Ratio
          example: "40%"
        eligibleAmount:
          type: number
          format: decimal
          description: Eligible loan amount
          example: 450000
        decisionResult:
          type: array
          items:
            $ref: '#/components/schemas/DecisionResultDTO'
        applicant:
          $ref: '#/components/schemas/ApplicantProfileDTO'
        feeDetails:
          $ref: '#/components/schemas/FeeDetailsDTO'
        timestamp:
          type: string
          format: date-time
          description: Decision timestamp
          example: "2024-06-23T10:30:00Z"
        traceId:
          type: string
          description: Unique trace identifier
          example: "BRE_TRACE_123456"

    DecisionResultDTO:
      type: object
      required:
        - ruleId
        - decisionResult
      properties:
        ruleId:
          type: string
          description: Business rule identifier
          example: "CIBIL_SCORE_CHECK"
        decisionResult:
          $ref: '#/components/schemas/DecisionType'
        reasonText:
          type: string
          maxLength: 500
          description: Reason for the decision
          example: "CIBIL score 750 meets minimum requirement of 650"

    ApplicantProfileDTO:
      type: object
      properties:
        profileId:
          type: string
          description: Applicant profile identifier
          example: "PROF_001"
        riskCategory:
          type: string
          description: Risk category assessment
          example: "LOW"
        creditScore:
          type: number
          format: decimal
          description: Evaluated credit score
          example: 750
        eligibilityStatus:
          type: string
          description: Overall eligibility status
          example: "ELIGIBLE"

    FeeDetailsDTO:
      type: object
      properties:
        processingFee:
          type: number
          format: decimal
          description: Processing fee amount
          example: 5000
        adminFee:
          type: number
          format: decimal
          description: Administrative fee amount
          example: 1000
        feeStructure:
          type: string
          description: Fee structure type
          example: "STANDARD"

    ErrorResponseDTO:
      type: object
      required:
        - errorCode
        - errorMessage
        - traceId
        - timestamp
      properties:
        errorCode:
          type: string
          description: Error code identifier
          example: "VALIDATION_FAILED"
        errorMessage:
          type: string
          description: Human-readable error message
          example: "Loan amount must be between 10,000 and 10,000,000"
        traceId:
          type: string
          description: Unique trace identifier
          example: "BRE_TRACE_123456"
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
          example: "2024-06-23T10:30:00Z"
        path:
          type: string
          description: Request path that caused the error
          example: "/v1/loan-eligibility/evaluate"

    HealthStatusDTO:
      type: object
      required:
        - status
        - service
        - timestamp
        - version
      properties:
        status:
          type: string
          description: Service status
          example: "UP"
        service:
          type: string
          description: Service name
          example: "BRE Original"
        timestamp:
          type: string
          format: date-time
          description: Health check timestamp
          example: "2024-06-23T10:30:00Z"
        version:
          type: string
          description: Service version
          example: "1.0.0"

    # Enums
    DecisionType:
      type: string
      enum: [APPROVE, REJECT, REFER]
      description: Loan decision outcome
      example: "APPROVE"

    Gender:
      type: string
      enum: [MALE, FEMALE, OTHER]
      description: Gender options
      example: "MALE"

    Profession:
      type: string
      enum: [SALARIED, SELF_EMPLOYED, BUSINESS, PROFESSIONAL, RETIRED, STUDENT, HOUSEWIFE, UNEMPLOYED, OTHER]
      description: Profession categories
      example: "SALARIED"

    AddressType:
      type: string
      enum: [PERMANENT, CURRENT, OFFICE, CORRESPONDENCE]
      description: Address type categories
      example: "CURRENT"

tags:
  - name: Loan Eligibility
    description: Loan eligibility evaluation operations
  - name: Health Check
    description: Service health monitoring operations
