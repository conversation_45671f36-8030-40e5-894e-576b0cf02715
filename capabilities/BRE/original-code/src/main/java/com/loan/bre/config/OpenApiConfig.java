package com.loan.bre.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;

/**
 * OpenAPI configuration for BRE Application.
 */
@Configuration
public class OpenApiConfig {

    @Value("${server.port:8108}")
    private String serverPort;

    @Value("${server.servlet.context-path:/api/bre}")
    private String contextPath;

    @Bean
    public OpenAPI breOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("BRE Application API")
                        .description("Business Rule Engine for Loan Eligibility Evaluation")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("BRE Team")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("Internal Use")
                                .url("https://loan.com/license")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + contextPath)
                                .description("Local Development Server")
                ));
    }
}
