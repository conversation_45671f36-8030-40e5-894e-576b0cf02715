package com.loan.bre.repository;

import com.loan.bre.entity.BRERequestAudit;
import com.loan.bre.enums.RequestStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for BRE Request Audit operations.
 */
@Repository
public interface BRERequestAuditRepository extends JpaRepository<BRERequestAudit, Long> {

    /**
     * Find audit record by trace ID.
     */
    Optional<BRERequestAudit> findByTraceId(String traceId);

    /**
     * Find audit records by application ID.
     */
    List<BRERequestAudit> findByApplicationId(String applicationId);

    /**
     * Find audit records by request status.
     */
    List<BRERequestAudit> findByRequestStatus(RequestStatus requestStatus);

    /**
     * Find audit records by application ID and request status.
     */
    List<BRERequestAudit> findByApplicationIdAndRequestStatus(String applicationId, RequestStatus requestStatus);

    /**
     * Find audit records created within a date range.
     */
    @Query("SELECT r FROM BRERequestAudit r WHERE r.requestTimestamp BETWEEN :startDate AND :endDate ORDER BY r.requestTimestamp DESC")
    List<BRERequestAudit> findByRequestTimestampBetween(@Param("startDate") LocalDateTime startDate, 
                                                        @Param("endDate") LocalDateTime endDate);

    /**
     * Find audit records older than specified date for cleanup.
     */
    @Query("SELECT r FROM BRERequestAudit r WHERE r.createdAt < :cutoffDate")
    List<BRERequestAudit> findRecordsOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * Count records by request status.
     */
    long countByRequestStatus(RequestStatus requestStatus);

    /**
     * Find recent audit records for monitoring.
     */
    @Query("SELECT r FROM BRERequestAudit r ORDER BY r.requestTimestamp DESC LIMIT :limit")
    List<BRERequestAudit> findRecentRequests(@Param("limit") int limit);

    /**
     * Check if trace ID exists.
     */
    boolean existsByTraceId(String traceId);

    /**
     * Delete records older than specified date.
     */
    @Query("DELETE FROM BRERequestAudit r WHERE r.createdAt < :cutoffDate")
    void deleteRecordsOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);
}
