package com.loan.bre.repository;

import com.loan.bre.entity.BREErrorLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository interface for BRE Error Log operations.
 */
@Repository
public interface BREErrorLogRepository extends JpaRepository<BREErrorLog, Long> {

    /**
     * Find error logs by trace ID.
     */
    List<BREErrorLog> findByTraceId(String traceId);

    /**
     * Find error logs by application ID.
     */
    List<BREErrorLog> findByApplicationId(String applicationId);

    /**
     * Find error logs by error type.
     */
    List<BREErrorLog> findByErrorType(String errorType);

    /**
     * Find error logs by error code.
     */
    List<BREErrorLog> findByErrorCode(String errorCode);

    /**
     * Find error logs by trace ID and error type.
     */
    List<BREErrorLog> findByTraceIdAndErrorType(String traceId, String errorType);

    /**
     * Find error logs created within a date range.
     */
    @Query("SELECT e FROM BREErrorLog e WHERE e.errorTimestamp BETWEEN :startDate AND :endDate ORDER BY e.errorTimestamp DESC")
    List<BREErrorLog> findByErrorTimestampBetween(@Param("startDate") LocalDateTime startDate, 
                                                 @Param("endDate") LocalDateTime endDate);

    /**
     * Find error logs with retry attempts greater than specified value.
     */
    @Query("SELECT e FROM BREErrorLog e WHERE e.retryAttempt > :retryAttempt")
    List<BREErrorLog> findByRetryAttemptGreaterThan(@Param("retryAttempt") Integer retryAttempt);

    /**
     * Find error logs older than specified date for cleanup.
     */
    @Query("SELECT e FROM BREErrorLog e WHERE e.createdAt < :cutoffDate")
    List<BREErrorLog> findRecordsOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * Count error logs by error type.
     */
    long countByErrorType(String errorType);

    /**
     * Count error logs by error code.
     */
    long countByErrorCode(String errorCode);

    /**
     * Find recent error logs for monitoring.
     */
    @Query("SELECT e FROM BREErrorLog e ORDER BY e.errorTimestamp DESC LIMIT :limit")
    List<BREErrorLog> findRecentErrors(@Param("limit") int limit);

    /**
     * Find error logs by application ID ordered by timestamp.
     */
    @Query("SELECT e FROM BREErrorLog e WHERE e.applicationId = :applicationId ORDER BY e.errorTimestamp DESC")
    List<BREErrorLog> findByApplicationIdOrderByErrorTimestampDesc(@Param("applicationId") String applicationId);

    /**
     * Delete records older than specified date.
     */
    @Query("DELETE FROM BREErrorLog e WHERE e.createdAt < :cutoffDate")
    void deleteRecordsOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * Find error summary by error type within date range.
     */
    @Query("SELECT e.errorType, COUNT(e) FROM BREErrorLog e WHERE e.errorTimestamp BETWEEN :startDate AND :endDate GROUP BY e.errorType")
    List<Object[]> findErrorSummaryByType(@Param("startDate") LocalDateTime startDate, 
                                         @Param("endDate") LocalDateTime endDate);
}
