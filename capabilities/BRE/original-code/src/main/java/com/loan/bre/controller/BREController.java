package com.loan.bre.controller;

import com.loan.bre.dto.BREProcessingResponseDTO;
import com.loan.bre.dto.BRERequestDTO;
import com.loan.bre.service.BREService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for BRE operations.
 */
@RestController
@RequestMapping("/v1/loan-eligibility")
public class BREController {

    private static final Logger logger = LoggerFactory.getLogger(BREController.class);

    private final BREService breService;

    public BREController(BREService breService) {
        this.breService = breService;
    }

    @PostMapping("/evaluate")
    public ResponseEntity<BREProcessingResponseDTO> evaluateLoanEligibility(
            @Valid @RequestBody BRERequestDTO request) {
        
        logger.info("Received loan eligibility request for application: {}", 
            request.getHeader().getApplicationId());

        BREService.BREProcessingResult result = breService.processLoanEligibility(request);

        BREProcessingResponseDTO response = new BREProcessingResponseDTO(
            result.isSuccess(),
            result.getMessage(),
            result.getResponse(),
            result.getTraceId()
        );

        logger.info("Loan eligibility evaluation completed - Success: {}, TraceId: {}",
            result.isSuccess(), result.getTraceId());

        // Return appropriate HTTP status based on the result
        if (!result.isSuccess()) {
            // Check if it's a validation error
            if (result.getMessage() != null && result.getMessage().contains("validation failed")) {
                return ResponseEntity.badRequest().body(response);
            }
            // For other errors, return 500
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }

        return ResponseEntity.ok(response);
    }

    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("BRE Service is healthy");
    }
}
