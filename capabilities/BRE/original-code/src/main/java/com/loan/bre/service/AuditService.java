package com.loan.bre.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.loan.bre.config.AuditConfig;
import com.loan.bre.dto.BRERequestDTO;
import com.loan.bre.dto.BREResponseDTO;
import com.loan.bre.entity.BREErrorLog;
import com.loan.bre.entity.BRERequestAudit;
import com.loan.bre.entity.BREResponseAudit;
import com.loan.bre.enums.RequestStatus;
import com.loan.bre.repository.BREErrorLogRepository;
import com.loan.bre.repository.BRERequestAuditRepository;
import com.loan.bre.repository.BREResponseAuditRepository;
import com.loan.bre.util.PIIMaskingUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * Service for handling audit logging with PII masking.
 */
@Service
@Transactional
public class AuditService {

    private static final Logger logger = LoggerFactory.getLogger(AuditService.class);
    private static final String TRACE_ID_KEY = "traceId";

    private final BRERequestAuditRepository requestAuditRepository;
    private final BREResponseAuditRepository responseAuditRepository;
    private final BREErrorLogRepository errorLogRepository;
    private final AuditConfig auditConfig;
    private final ObjectMapper objectMapper;

    public AuditService(BRERequestAuditRepository requestAuditRepository,
                       BREResponseAuditRepository responseAuditRepository,
                       BREErrorLogRepository errorLogRepository,
                       AuditConfig auditConfig,
                       ObjectMapper objectMapper) {
        this.requestAuditRepository = requestAuditRepository;
        this.responseAuditRepository = responseAuditRepository;
        this.errorLogRepository = errorLogRepository;
        this.auditConfig = auditConfig;
        this.objectMapper = objectMapper;
    }

    /**
     * Log BRE request initiation.
     */
    public void logRequestInitiation(String traceId, BRERequestDTO request) {
        try {
            MDC.put(TRACE_ID_KEY, traceId);
            
            String requestJson = objectMapper.writeValueAsString(request);
            String maskedData = auditConfig.isMaskPii()
                ? PIIMaskingUtil.maskPIIDataInJson(requestJson)
                : requestJson;

            BRERequestAudit audit = new BRERequestAudit(
                traceId,
                request.getHeader().getApplicationId(),
                LocalDateTime.now(),
                maskedData,
                RequestStatus.INITIATED
            );

            requestAuditRepository.save(audit);
            
            logger.info("BRE request initiated - ApplicationId: {}, LoanAmount: {}, ProductCode: {}",
                request.getHeader().getApplicationId(),
                request.getHeader().getLoanAmount(),
                request.getHeader().getProductCode());
                
        } catch (Exception e) {
            logger.error("Failed to log request initiation: {}", e.getMessage(), e);
        } finally {
            MDC.remove(TRACE_ID_KEY);
        }
    }

    /**
     * Update request status.
     */
    public void updateRequestStatus(String traceId, RequestStatus status) {
        try {
            MDC.put(TRACE_ID_KEY, traceId);
            
            requestAuditRepository.findByTraceId(traceId)
                .ifPresentOrElse(
                    audit -> {
                        audit.setRequestStatus(status);
                        requestAuditRepository.save(audit);
                        logger.info("Request status updated to: {}", status);
                    },
                    () -> logger.warn("Request audit not found for trace ID: {}", traceId)
                );
                
        } catch (Exception e) {
            logger.error("Failed to update request status: {}", e.getMessage(), e);
        } finally {
            MDC.remove(TRACE_ID_KEY);
        }
    }

    /**
     * Log BRE response.
     */
    public void logResponse(String traceId, String applicationId, BREResponseDTO response, RequestStatus status) {
        try {
            MDC.put(TRACE_ID_KEY, traceId);
            
            String responseJson = objectMapper.writeValueAsString(response);
            String maskedData = auditConfig.isMaskPii()
                ? PIIMaskingUtil.maskPIIDataInJson(responseJson)
                : responseJson;

            BREResponseAudit audit = new BREResponseAudit(
                traceId,
                applicationId,
                LocalDateTime.now(),
                maskedData,
                status
            );

            if (response != null) {
                audit.setDecision(response.getDecision());
                audit.setEligibleFoir(response.getEligibleFOIR());
                audit.setEligibleAmount(response.getEligibleAmount());
            }

            responseAuditRepository.save(audit);
            
            logger.info("BRE response logged - Decision: {}, EligibleAmount: {}, Status: {}",
                response != null ? response.getDecision() : "N/A",
                response != null ? response.getEligibleAmount() : "N/A",
                status);
                
        } catch (Exception e) {
            logger.error("Failed to log response: {}", e.getMessage(), e);
        } finally {
            MDC.remove(TRACE_ID_KEY);
        }
    }

    /**
     * Log error with retry information.
     */
    public void logError(String traceId, String applicationId, String errorType, 
                        String errorCode, String errorMessage, String stackTrace, int retryAttempt) {
        try {
            MDC.put(TRACE_ID_KEY, traceId);
            
            BREErrorLog errorLog = new BREErrorLog(
                traceId,
                applicationId,
                LocalDateTime.now(),
                errorType,
                errorCode,
                errorMessage,
                stackTrace,
                retryAttempt
            );

            errorLogRepository.save(errorLog);
            
            // Also update response audit with error information
            responseAuditRepository.findByTraceId(traceId)
                .ifPresentOrElse(
                    audit -> {
                        audit.setErrorCode(errorCode);
                        audit.setErrorMessage(errorMessage);
                        audit.setRetryCount(retryAttempt);
                        audit.setResponseStatus(RequestStatus.FAILED);
                        responseAuditRepository.save(audit);
                    },
                    () -> {
                        // Create new response audit for error
                        BREResponseAudit audit = new BREResponseAudit(
                            traceId, applicationId, LocalDateTime.now(), null, RequestStatus.FAILED);
                        audit.setErrorCode(errorCode);
                        audit.setErrorMessage(errorMessage);
                        audit.setRetryCount(retryAttempt);
                        responseAuditRepository.save(audit);
                    }
                );
            
            logger.error("Error logged - Type: {}, Code: {}, Message: {}, RetryAttempt: {}",
                errorType, errorCode, errorMessage, retryAttempt);
                
        } catch (Exception e) {
            logger.error("Failed to log error: {}", e.getMessage(), e);
        } finally {
            MDC.remove(TRACE_ID_KEY);
        }
    }

    /**
     * Log successful completion.
     */
    public void logSuccess(String traceId, String applicationId, BREResponseDTO response) {
        logResponse(traceId, applicationId, response, RequestStatus.COMPLETED);
        updateRequestStatus(traceId, RequestStatus.COMPLETED);
    }

    /**
     * Log failure with fallback message.
     */
    public void logFailure(String traceId, String applicationId, String reason) {
        try {
            MDC.put(TRACE_ID_KEY, traceId);
            
            logError(traceId, applicationId, "PROCESSING_FAILURE", "BRE_FAILURE", reason, null, 0);
            updateRequestStatus(traceId, RequestStatus.FAILED);
            
            logger.error("BRE processing failed - Reason: {}", reason);
            
        } finally {
            MDC.remove(TRACE_ID_KEY);
        }
    }

    /**
     * Clean up old audit records based on retention policy.
     */
    @Transactional
    public void cleanupOldRecords() {
        try {
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(auditConfig.getRetentionDays());
            
            requestAuditRepository.deleteRecordsOlderThan(cutoffDate);
            responseAuditRepository.deleteRecordsOlderThan(cutoffDate);
            errorLogRepository.deleteRecordsOlderThan(cutoffDate);
            
            logger.info("Cleaned up audit records older than {} days", auditConfig.getRetentionDays());
            
        } catch (Exception e) {
            logger.error("Failed to cleanup old records: {}", e.getMessage(), e);
        }
    }
}
