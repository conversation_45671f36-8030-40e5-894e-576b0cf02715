package com.loan.bre.entity;

import jakarta.persistence.*;

import java.time.LocalDateTime;

/**
 * Entity representing BRE error log.
 */
@Entity
@Table(name = "bre_error_log")
public class BREErrorLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "trace_id", nullable = false, length = 50)
    private String traceId;

    @Column(name = "application_id", nullable = false, length = 100)
    private String applicationId;

    @Column(name = "error_timestamp", nullable = false)
    private LocalDateTime errorTimestamp;

    @Column(name = "error_type", nullable = false, length = 50)
    private String errorType;

    @Column(name = "error_code", length = 50)
    private String errorCode;

    @Column(name = "error_message", nullable = false, columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "stack_trace", columnDefinition = "TEXT")
    private String stackTrace;

    @Column(name = "retry_attempt")
    private Integer retryAttempt = 0;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    // Constructors
    public BREErrorLog() {
        this.createdAt = LocalDateTime.now();
    }

    public BREErrorLog(String traceId, String applicationId, LocalDateTime errorTimestamp,
                      String errorType, String errorCode, String errorMessage,
                      String stackTrace, Integer retryAttempt) {
        this();
        this.traceId = traceId;
        this.applicationId = applicationId;
        this.errorTimestamp = errorTimestamp;
        this.errorType = errorType;
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.stackTrace = stackTrace;
        this.retryAttempt = retryAttempt;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public LocalDateTime getErrorTimestamp() {
        return errorTimestamp;
    }

    public void setErrorTimestamp(LocalDateTime errorTimestamp) {
        this.errorTimestamp = errorTimestamp;
    }

    public String getErrorType() {
        return errorType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getStackTrace() {
        return stackTrace;
    }

    public void setStackTrace(String stackTrace) {
        this.stackTrace = stackTrace;
    }

    public Integer getRetryAttempt() {
        return retryAttempt;
    }

    public void setRetryAttempt(Integer retryAttempt) {
        this.retryAttempt = retryAttempt;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "BREErrorLog{" +
                "id=" + id +
                ", traceId='" + traceId + '\'' +
                ", applicationId='" + applicationId + '\'' +
                ", errorTimestamp=" + errorTimestamp +
                ", errorType='" + errorType + '\'' +
                ", errorCode='" + errorCode + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", retryAttempt=" + retryAttempt +
                ", createdAt=" + createdAt +
                '}';
    }
}
