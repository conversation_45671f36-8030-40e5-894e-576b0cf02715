package com.loan.bre.entity;

import com.loan.bre.enums.RequestStatus;
import jakarta.persistence.*;

import java.time.LocalDateTime;

/**
 * Entity representing BRE request audit log.
 */
@Entity
@Table(name = "bre_request_audit")
public class BRERequestAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "trace_id", nullable = false, unique = true, length = 50)
    private String traceId;

    @Column(name = "application_id", nullable = false, length = 100)
    private String applicationId;

    @Column(name = "request_timestamp", nullable = false)
    private LocalDateTime requestTimestamp;

    @Column(name = "masked_request_data", nullable = false, columnDefinition = "TEXT")
    private String maskedRequestData;

    @Enumerated(EnumType.STRING)
    @Column(name = "request_status", nullable = false, length = 20)
    private RequestStatus requestStatus;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Constructors
    public BRERequestAudit() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public BRERequestAudit(String traceId, String applicationId, LocalDateTime requestTimestamp,
                          String maskedRequestData, RequestStatus requestStatus) {
        this();
        this.traceId = traceId;
        this.applicationId = applicationId;
        this.requestTimestamp = requestTimestamp;
        this.maskedRequestData = maskedRequestData;
        this.requestStatus = requestStatus;
    }

    // Lifecycle callbacks
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public LocalDateTime getRequestTimestamp() {
        return requestTimestamp;
    }

    public void setRequestTimestamp(LocalDateTime requestTimestamp) {
        this.requestTimestamp = requestTimestamp;
    }

    public String getMaskedRequestData() {
        return maskedRequestData;
    }

    public void setMaskedRequestData(String maskedRequestData) {
        this.maskedRequestData = maskedRequestData;
    }

    public RequestStatus getRequestStatus() {
        return requestStatus;
    }

    public void setRequestStatus(RequestStatus requestStatus) {
        this.requestStatus = requestStatus;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "BRERequestAudit{" +
                "id=" + id +
                ", traceId='" + traceId + '\'' +
                ", applicationId='" + applicationId + '\'' +
                ", requestTimestamp=" + requestTimestamp +
                ", requestStatus=" + requestStatus +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
