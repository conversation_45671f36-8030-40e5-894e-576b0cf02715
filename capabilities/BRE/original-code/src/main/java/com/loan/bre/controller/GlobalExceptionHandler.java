package com.loan.bre.controller;

import com.loan.bre.dto.ErrorResponseDTO;
import com.loan.bre.exception.BREServiceException;
import com.loan.bre.exception.ValidationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import java.util.stream.Collectors;

/**
 * Global exception handler for standardized error responses.
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    private static final String TRACE_ID_KEY = "traceId";

    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ErrorResponseDTO> handleValidationException(ValidationException ex, WebRequest request) {
        String traceId = MDC.get(TRACE_ID_KEY);
        
        logger.error("Validation error: {}", ex.getMessage());
        
        ErrorResponseDTO errorResponse = new ErrorResponseDTO(
            "VALIDATION_ERROR",
            ex.getMessage(),
            traceId,
            request.getDescription(false)
        );
        
        return ResponseEntity.badRequest().body(errorResponse);
    }

    @ExceptionHandler(BREServiceException.class)
    public ResponseEntity<ErrorResponseDTO> handleBREServiceException(BREServiceException ex, WebRequest request) {
        String traceId = MDC.get(TRACE_ID_KEY);
        
        logger.error("BRE service error: {}", ex.getMessage(), ex);
        
        ErrorResponseDTO errorResponse = new ErrorResponseDTO(
            "BRE_SERVICE_ERROR",
            "We are unable to proceed with your loan application right now. Please try again later.",
            traceId,
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(errorResponse);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponseDTO> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, WebRequest request) {
        String traceId = MDC.get(TRACE_ID_KEY);
        
        String errorMessage = ex.getBindingResult()
            .getFieldErrors()
            .stream()
            .map(FieldError::getDefaultMessage)
            .collect(Collectors.joining("; "));
        
        logger.error("Request validation error: {}", errorMessage);
        
        ErrorResponseDTO errorResponse = new ErrorResponseDTO(
            "REQUEST_VALIDATION_ERROR",
            errorMessage,
            traceId,
            request.getDescription(false)
        );
        
        return ResponseEntity.badRequest().body(errorResponse);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ErrorResponseDTO> handleIllegalArgumentException(IllegalArgumentException ex, WebRequest request) {
        String traceId = MDC.get(TRACE_ID_KEY);
        
        logger.error("Illegal argument error: {}", ex.getMessage());
        
        ErrorResponseDTO errorResponse = new ErrorResponseDTO(
            "INVALID_ARGUMENT",
            ex.getMessage(),
            traceId,
            request.getDescription(false)
        );
        
        return ResponseEntity.badRequest().body(errorResponse);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponseDTO> handleGenericException(Exception ex, WebRequest request) {
        String traceId = MDC.get(TRACE_ID_KEY);
        
        logger.error("Unexpected error: {}", ex.getMessage(), ex);
        
        ErrorResponseDTO errorResponse = new ErrorResponseDTO(
            "INTERNAL_SERVER_ERROR",
            "An unexpected error occurred. Please try again later.",
            traceId,
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
}
