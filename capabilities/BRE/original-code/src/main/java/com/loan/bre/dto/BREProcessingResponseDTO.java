package com.loan.bre.dto;

import java.time.LocalDateTime;

/**
 * DTO for BRE processing response.
 */
public class BREProcessingResponseDTO {

    private boolean success;
    private String message;
    private BREResponseDTO breResponse;
    private String traceId;
    private LocalDateTime timestamp;

    // Constructors
    public BREProcessingResponseDTO() {
        this.timestamp = LocalDateTime.now();
    }

    public BREProcessingResponseDTO(boolean success, String message, BREResponseDTO breResponse, String traceId) {
        this();
        this.success = success;
        this.message = message;
        this.breResponse = breResponse;
        this.traceId = traceId;
    }

    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public BREResponseDTO getBreResponse() {
        return breResponse;
    }

    public void setBreResponse(BREResponseDTO breResponse) {
        this.breResponse = breResponse;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
}
