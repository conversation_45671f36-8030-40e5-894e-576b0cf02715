package com.loan.bre.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * Configuration properties for BRE service.
 */
@Configuration
@ConfigurationProperties(prefix = "bre.service")
public class BREServiceConfig {

    private String baseUrl = "http://localhost:8083/api/bre/internal/v1";
    private String evaluateEndpoint = "/evaluate";
    private Duration timeout = Duration.ofSeconds(30);
    private RetryConfig retry = new RetryConfig();

    // Getters and Setters
    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getEvaluateEndpoint() {
        return evaluateEndpoint;
    }

    public void setEvaluateEndpoint(String evaluateEndpoint) {
        this.evaluateEndpoint = evaluateEndpoint;
    }

    public Duration getTimeout() {
        return timeout;
    }

    public void setTimeout(Duration timeout) {
        this.timeout = timeout;
    }

    public RetryConfig getRetry() {
        return retry;
    }

    public void setRetry(RetryConfig retry) {
        this.retry = retry;
    }

    public String getFullEvaluateUrl() {
        return baseUrl + evaluateEndpoint;
    }

    /**
     * Retry configuration for BRE service calls.
     */
    public static class RetryConfig {
        private int maxAttempts = 3;
        private Duration delay = Duration.ofSeconds(2);
        private double multiplier = 2.0;

        public int getMaxAttempts() {
            return maxAttempts;
        }

        public void setMaxAttempts(int maxAttempts) {
            this.maxAttempts = maxAttempts;
        }

        public Duration getDelay() {
            return delay;
        }

        public void setDelay(Duration delay) {
            this.delay = delay;
        }

        public double getMultiplier() {
            return multiplier;
        }

        public void setMultiplier(double multiplier) {
            this.multiplier = multiplier;
        }
    }
}
