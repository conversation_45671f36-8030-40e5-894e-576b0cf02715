package com.loan.bre.repository;

import com.loan.bre.entity.BREResponseAudit;
import com.loan.bre.enums.DecisionType;
import com.loan.bre.enums.RequestStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for BRE Response Audit operations.
 */
@Repository
public interface BREResponseAuditRepository extends JpaRepository<BREResponseAudit, Long> {

    /**
     * Find audit record by trace ID.
     */
    Optional<BREResponseAudit> findByTraceId(String traceId);

    /**
     * Find audit records by application ID.
     */
    List<BREResponseAudit> findByApplicationId(String applicationId);

    /**
     * Find audit records by response status.
     */
    List<BREResponseAudit> findByResponseStatus(RequestStatus responseStatus);

    /**
     * Find audit records by decision type.
     */
    List<BREResponseAudit> findByDecision(DecisionType decision);

    /**
     * Find audit records by application ID and decision.
     */
    List<BREResponseAudit> findByApplicationIdAndDecision(String applicationId, DecisionType decision);

    /**
     * Find audit records with retry count greater than specified value.
     */
    @Query("SELECT r FROM BREResponseAudit r WHERE r.retryCount > :retryCount")
    List<BREResponseAudit> findByRetryCountGreaterThan(@Param("retryCount") Integer retryCount);

    /**
     * Find audit records created within a date range.
     */
    @Query("SELECT r FROM BREResponseAudit r WHERE r.responseTimestamp BETWEEN :startDate AND :endDate ORDER BY r.responseTimestamp DESC")
    List<BREResponseAudit> findByResponseTimestampBetween(@Param("startDate") LocalDateTime startDate, 
                                                         @Param("endDate") LocalDateTime endDate);

    /**
     * Find audit records older than specified date for cleanup.
     */
    @Query("SELECT r FROM BREResponseAudit r WHERE r.createdAt < :cutoffDate")
    List<BREResponseAudit> findRecordsOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * Count records by decision type.
     */
    long countByDecision(DecisionType decision);

    /**
     * Count records by response status.
     */
    long countByResponseStatus(RequestStatus responseStatus);

    /**
     * Find recent audit records for monitoring.
     */
    @Query("SELECT r FROM BREResponseAudit r ORDER BY r.responseTimestamp DESC LIMIT :limit")
    List<BREResponseAudit> findRecentResponses(@Param("limit") int limit);

    /**
     * Find failed responses for retry analysis.
     */
    @Query("SELECT r FROM BREResponseAudit r WHERE r.responseStatus = 'FAILED' AND r.retryCount < :maxRetries")
    List<BREResponseAudit> findFailedResponsesForRetry(@Param("maxRetries") Integer maxRetries);

    /**
     * Delete records older than specified date.
     */
    @Query("DELETE FROM BREResponseAudit r WHERE r.createdAt < :cutoffDate")
    void deleteRecordsOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);
}
