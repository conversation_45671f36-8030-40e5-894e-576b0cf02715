package com.loan.bre.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;

/**
 * Configuration for WebClient used for BRE service calls.
 */
@Configuration
public class WebClientConfig {

    private final BREServiceConfig breServiceConfig;

    public WebClientConfig(BREServiceConfig breServiceConfig) {
        this.breServiceConfig = breServiceConfig;
    }

    @Bean
    public WebClient breWebClient() {
        HttpClient httpClient = HttpClient.create()
                .responseTimeout(breServiceConfig.getTimeout())
                .option(io.netty.channel.ChannelOption.CONNECT_TIMEOUT_MILLIS, 
                       (int) breServiceConfig.getTimeout().toMillis());

        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .baseUrl(breServiceConfig.getBaseUrl())
                .defaultHeader("Content-Type", "application/json")
                .defaultHeader("Accept", "application/json")
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(1024 * 1024)) // 1MB buffer
                .build();
    }
}
