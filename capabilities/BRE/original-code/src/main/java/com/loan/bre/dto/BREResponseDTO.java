package com.loan.bre.dto;

import com.loan.bre.enums.DecisionType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO representing the BRE response payload.
 */
public class BREResponseDTO {

    @NotBlank(message = "Application ID is required")
    private String applicationId;

    @NotNull(message = "Decision is required")
    private DecisionType decision;

    private String eligibleFOIR;

    private BigDecimal eligibleAmount;

    @Valid
    private List<DecisionResultDTO> decisionResult;

    @Valid
    private ApplicantProfileDTO applicant;

    @Valid
    private FeeDetailsDTO feeDetails;

    @NotNull(message = "Timestamp is required")
    private LocalDateTime timestamp;

    @NotBlank(message = "Trace ID is required")
    private String traceId;

    // Constructors
    public BREResponseDTO() {}

    public BREResponseDTO(String applicationId, DecisionType decision, String eligibleFOIR, 
                         BigDecimal eligibleAmount, List<DecisionResultDTO> decisionResult,
                         ApplicantProfileDTO applicant, FeeDetailsDTO feeDetails,
                         LocalDateTime timestamp, String traceId) {
        this.applicationId = applicationId;
        this.decision = decision;
        this.eligibleFOIR = eligibleFOIR;
        this.eligibleAmount = eligibleAmount;
        this.decisionResult = decisionResult;
        this.applicant = applicant;
        this.feeDetails = feeDetails;
        this.timestamp = timestamp;
        this.traceId = traceId;
    }

    // Getters and Setters
    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public DecisionType getDecision() {
        return decision;
    }

    public void setDecision(DecisionType decision) {
        this.decision = decision;
    }

    public String getEligibleFOIR() {
        return eligibleFOIR;
    }

    public void setEligibleFOIR(String eligibleFOIR) {
        this.eligibleFOIR = eligibleFOIR;
    }

    public BigDecimal getEligibleAmount() {
        return eligibleAmount;
    }

    public void setEligibleAmount(BigDecimal eligibleAmount) {
        this.eligibleAmount = eligibleAmount;
    }

    public List<DecisionResultDTO> getDecisionResult() {
        return decisionResult;
    }

    public void setDecisionResult(List<DecisionResultDTO> decisionResult) {
        this.decisionResult = decisionResult;
    }

    public ApplicantProfileDTO getApplicant() {
        return applicant;
    }

    public void setApplicant(ApplicantProfileDTO applicant) {
        this.applicant = applicant;
    }

    public FeeDetailsDTO getFeeDetails() {
        return feeDetails;
    }

    public void setFeeDetails(FeeDetailsDTO feeDetails) {
        this.feeDetails = feeDetails;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    /**
     * Decision result with rule details.
     */
    public static class DecisionResultDTO {
        @NotBlank(message = "Rule ID is required")
        private String ruleId;

        @NotNull(message = "Decision result is required")
        private DecisionType decisionResult;

        @Size(max = 500, message = "Reason text must not exceed 500 characters")
        private String reasonText;

        // Constructors
        public DecisionResultDTO() {}

        public DecisionResultDTO(String ruleId, DecisionType decisionResult, String reasonText) {
            this.ruleId = ruleId;
            this.decisionResult = decisionResult;
            this.reasonText = reasonText;
        }

        // Getters and Setters
        public String getRuleId() {
            return ruleId;
        }

        public void setRuleId(String ruleId) {
            this.ruleId = ruleId;
        }

        public DecisionType getDecisionResult() {
            return decisionResult;
        }

        public void setDecisionResult(DecisionType decisionResult) {
            this.decisionResult = decisionResult;
        }

        public String getReasonText() {
            return reasonText;
        }

        public void setReasonText(String reasonText) {
            this.reasonText = reasonText;
        }
    }

    /**
     * Applicant profile data from BRE.
     */
    public static class ApplicantProfileDTO {
        private String profileId;
        private String riskCategory;
        private BigDecimal creditScore;
        private String eligibilityStatus;

        // Constructors
        public ApplicantProfileDTO() {}

        public ApplicantProfileDTO(String profileId, String riskCategory, BigDecimal creditScore, String eligibilityStatus) {
            this.profileId = profileId;
            this.riskCategory = riskCategory;
            this.creditScore = creditScore;
            this.eligibilityStatus = eligibilityStatus;
        }

        // Getters and Setters
        public String getProfileId() {
            return profileId;
        }

        public void setProfileId(String profileId) {
            this.profileId = profileId;
        }

        public String getRiskCategory() {
            return riskCategory;
        }

        public void setRiskCategory(String riskCategory) {
            this.riskCategory = riskCategory;
        }

        public BigDecimal getCreditScore() {
            return creditScore;
        }

        public void setCreditScore(BigDecimal creditScore) {
            this.creditScore = creditScore;
        }

        public String getEligibilityStatus() {
            return eligibilityStatus;
        }

        public void setEligibilityStatus(String eligibilityStatus) {
            this.eligibilityStatus = eligibilityStatus;
        }
    }

    /**
     * Fee details from BRE.
     */
    public static class FeeDetailsDTO {
        private BigDecimal processingFee;
        private BigDecimal adminFee;
        private String feeStructure;

        // Constructors
        public FeeDetailsDTO() {}

        public FeeDetailsDTO(BigDecimal processingFee, BigDecimal adminFee, String feeStructure) {
            this.processingFee = processingFee;
            this.adminFee = adminFee;
            this.feeStructure = feeStructure;
        }

        // Getters and Setters
        public BigDecimal getProcessingFee() {
            return processingFee;
        }

        public void setProcessingFee(BigDecimal processingFee) {
            this.processingFee = processingFee;
        }

        public BigDecimal getAdminFee() {
            return adminFee;
        }

        public void setAdminFee(BigDecimal adminFee) {
            this.adminFee = adminFee;
        }

        public String getFeeStructure() {
            return feeStructure;
        }

        public void setFeeStructure(String feeStructure) {
            this.feeStructure = feeStructure;
        }
    }
}
