package com.loan.bre.enums;

/**
 * Enum representing gender options.
 */
public enum Gender {
    MALE("Male"),
    FEM<PERSON><PERSON>("Female"),
    OTHER("Other");

    private final String value;

    Gender(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static Gender fromValue(String value) {
        for (Gender gender : Gender.values()) {
            if (gender.value.equalsIgnoreCase(value)) {
                return gender;
            }
        }
        throw new IllegalArgumentException("Unknown gender: " + value);
    }

    @Override
    public String toString() {
        return value;
    }
}
