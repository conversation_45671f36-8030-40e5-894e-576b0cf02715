package com.loan.bre.service;

import com.loan.bre.config.BREServiceConfig;
import com.loan.bre.dto.BRERequestDTO;
import com.loan.bre.dto.BREResponseDTO;
import com.loan.bre.enums.RequestStatus;
import com.loan.bre.exception.BREServiceException;
import com.loan.bre.exception.ValidationException;
import com.loan.bre.util.TraceIdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.util.retry.Retry;

/**
 * Main service for handling BRE requests and responses.
 */
@Service
public class BREService {

    private static final Logger logger = LoggerFactory.getLogger(BREService.class);
    private static final String TRACE_ID_KEY = "traceId";
    private static final String FALLBACK_MESSAGE = 
        "We are unable to proceed with your loan application right now. Please try again later.";

    private final WebClient breWebClient;
    private final ValidationService validationService;
    private final AuditService auditService;
    private final BREServiceConfig breServiceConfig;

    public BREService(WebClient breWebClient,
                     ValidationService validationService,
                     AuditService auditService,
                     BREServiceConfig breServiceConfig) {
        this.breWebClient = breWebClient;
        this.validationService = validationService;
        this.auditService = auditService;
        this.breServiceConfig = breServiceConfig;
    }

    /**
     * Process BRE request for loan eligibility decision.
     */
    public BREProcessingResult processLoanEligibility(BRERequestDTO request) {
        String traceId = TraceIdGenerator.generateTraceIdWithAppId(request.getHeader().getApplicationId());
        String applicationId = request.getHeader().getApplicationId();
        
        try {
            MDC.put(TRACE_ID_KEY, traceId);
            
            logger.info("Starting BRE processing for application: {}", applicationId);
            
            // Phase 1: Validate request
            ValidationService.ValidationResult validationResult = validationService.validateRequest(request);
            if (!validationResult.isValid()) {
                String errorMessage = "Request validation failed: " + validationResult.getErrorMessage();
                auditService.logError(traceId, applicationId, "VALIDATION_ERROR", 
                    "INVALID_REQUEST", errorMessage, null, 0);
                throw new ValidationException(errorMessage);
            }

            // Log request initiation
            auditService.logRequestInitiation(traceId, request);
            auditService.updateRequestStatus(traceId, RequestStatus.VALIDATED);

            // Phase 2: Call BRE service with retry logic
            BREResponseDTO response = callBREServiceWithRetry(traceId, applicationId, request);

            // Phase 3: Validate response
            ValidationService.ValidationResult responseValidation = validationService.validateResponse(response);
            if (!responseValidation.isValid()) {
                String errorMessage = "Response validation failed: " + responseValidation.getErrorMessage();
                auditService.logError(traceId, applicationId, "RESPONSE_VALIDATION_ERROR", 
                    "INVALID_RESPONSE", errorMessage, null, 0);
                auditService.logFailure(traceId, applicationId, errorMessage);
                return new BREProcessingResult(false, FALLBACK_MESSAGE, null, traceId);
            }

            // Phase 4: Process response based on decision type
            if (validationService.isSuccessfulResponse(response)) {
                auditService.logSuccess(traceId, applicationId, response);

                String message;
                switch (response.getDecision()) {
                    case APPROVE:
                        message = "Loan application approved";
                        logger.info("BRE processing completed successfully - Decision: APPROVE, Amount: {}",
                            response.getEligibleAmount());
                        break;
                    case REJECT:
                        message = "Loan application rejected";
                        logger.info("BRE processing completed successfully - Decision: REJECT");
                        break;
                    case REFER:
                        message = "Loan application requires manual review";
                        logger.info("BRE processing completed successfully - Decision: REFER");
                        break;
                    default:
                        message = "BRE evaluation completed";
                        logger.info("BRE processing completed successfully - Decision: {}", response.getDecision());
                        break;
                }

                return new BREProcessingResult(true, message, response, traceId);
            } else {
                String reason = String.format("Invalid response: Decision: %s, FOIR: %s, Amount: %s",
                    response.getDecision(), response.getEligibleFOIR(), response.getEligibleAmount());
                auditService.logFailure(traceId, applicationId, "Invalid BRE response: " + reason);
                logger.error("BRE processing failed with invalid response: {}", reason);
                return new BREProcessingResult(false, FALLBACK_MESSAGE, response, traceId);
            }

        } catch (ValidationException e) {
            logger.error("Validation error in BRE processing: {}", e.getMessage());
            return new BREProcessingResult(false, e.getMessage(), null, traceId);
        } catch (BREServiceException e) {
            logger.error("BRE service error: {}", e.getMessage());
            auditService.logFailure(traceId, applicationId, e.getMessage());
            return new BREProcessingResult(false, FALLBACK_MESSAGE, null, traceId);
        } catch (Exception e) {
            logger.error("Unexpected error in BRE processing: {}", e.getMessage(), e);
            auditService.logError(traceId, applicationId, "UNEXPECTED_ERROR", 
                "PROCESSING_ERROR", e.getMessage(), getStackTrace(e), 0);
            auditService.logFailure(traceId, applicationId, "Unexpected error: " + e.getMessage());
            return new BREProcessingResult(false, FALLBACK_MESSAGE, null, traceId);
        } finally {
            MDC.remove(TRACE_ID_KEY);
        }
    }

    /**
     * Call BRE service with retry logic.
     */
    private BREResponseDTO callBREServiceWithRetry(String traceId, String applicationId, BRERequestDTO request) {
        auditService.updateRequestStatus(traceId, RequestStatus.SENT_TO_BRE);
        
        return breWebClient
            .post()
            .uri(breServiceConfig.getEvaluateEndpoint())
            .header("X-Trace-ID", traceId)
            .bodyValue(request)
            .retrieve()
            .bodyToMono(BREResponseDTO.class)
            .retryWhen(Retry.fixedDelay(
                breServiceConfig.getRetry().getMaxAttempts(),
                breServiceConfig.getRetry().getDelay())
                .filter(this::isRetryableException)
                .doBeforeRetry(retrySignal -> {
                    int attempt = (int) retrySignal.totalRetries() + 1;
                    String retryTraceId = TraceIdGenerator.generateRetryTraceId(traceId, attempt);
                    logger.warn("Retrying BRE service call - Attempt: {}, Error: {}",
                        attempt, retrySignal.failure().getMessage());
                    auditService.logError(retryTraceId, applicationId, "RETRY_ATTEMPT",
                        "BRE_SERVICE_RETRY", "Retry attempt " + attempt,
                        getStackTrace(retrySignal.failure()), attempt);
                }))
            .onErrorMap(this::mapToServiceException)
            .block();
    }

    /**
     * Check if exception is retryable.
     */
    private boolean isRetryableException(Throwable throwable) {
        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException webClientException = (WebClientResponseException) throwable;
            HttpStatus status = HttpStatus.valueOf(webClientException.getStatusCode().value());
            
            // Retry on server errors and specific client errors
            return status.is5xxServerError() || 
                   status == HttpStatus.REQUEST_TIMEOUT ||
                   status == HttpStatus.TOO_MANY_REQUESTS;
        }
        
        // Retry on network-related exceptions
        return throwable instanceof java.net.ConnectException ||
               throwable instanceof java.net.SocketTimeoutException ||
               throwable instanceof java.io.IOException;
    }

    /**
     * Map exceptions to BREServiceException.
     */
    private Throwable mapToServiceException(Throwable throwable) {
        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException webClientException = (WebClientResponseException) throwable;
            String errorMessage = String.format("BRE service call failed - Status: %s, Body: %s",
                webClientException.getStatusCode(), webClientException.getResponseBodyAsString());
            return new BREServiceException(errorMessage, throwable);
        }
        
        return new BREServiceException("BRE service call failed: " + throwable.getMessage(), throwable);
    }

    /**
     * Get stack trace as string.
     */
    private String getStackTrace(Throwable throwable) {
        if (throwable == null) {
            return null;
        }
        
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }

    /**
     * Result of BRE processing.
     */
    public static class BREProcessingResult {
        private final boolean success;
        private final String message;
        private final BREResponseDTO response;
        private final String traceId;

        public BREProcessingResult(boolean success, String message, BREResponseDTO response, String traceId) {
            this.success = success;
            this.message = message;
            this.response = response;
            this.traceId = traceId;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }

        public BREResponseDTO getResponse() {
            return response;
        }

        public String getTraceId() {
            return traceId;
        }
    }
}
