package com.loan.bre.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for audit settings.
 */
@Configuration
@ConfigurationProperties(prefix = "bre.audit")
public class AuditConfig {

    private int retentionDays = 90;
    private boolean maskPii = true;

    // Getters and Setters
    public int getRetentionDays() {
        return retentionDays;
    }

    public void setRetentionDays(int retentionDays) {
        this.retentionDays = retentionDays;
    }

    public boolean isMaskPii() {
        return maskPii;
    }

    public void setMaskPii(boolean maskPii) {
        this.maskPii = maskPii;
    }
}
