package com.loan.bre.dto;

import com.loan.bre.enums.AddressType;
import com.loan.bre.enums.Gender;
import com.loan.bre.enums.Profession;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * DTO representing the complete BRE request payload.
 */
public class BRERequestDTO {

    @Valid
    @NotNull(message = "Header is required")
    private HeaderDTO header;

    @Valid
    @NotEmpty(message = "At least one applicant is required")
    private List<ApplicantDTO> applicants;

    @Valid
    @NotNull(message = "Additional data is required")
    private AdditionalDataDTO additionalData;

    // Constructors
    public BRERequestDTO() {}

    public BRERequestDTO(HeaderDTO header, List<ApplicantDTO> applicants, AdditionalDataDTO additionalData) {
        this.header = header;
        this.applicants = applicants;
        this.additionalData = additionalData;
    }

    // Getters and Setters
    public HeaderDTO getHeader() {
        return header;
    }

    public void setHeader(HeaderDTO header) {
        this.header = header;
    }

    public List<ApplicantDTO> getApplicants() {
        return applicants;
    }

    public void setApplicants(List<ApplicantDTO> applicants) {
        this.applicants = applicants;
    }

    public AdditionalDataDTO getAdditionalData() {
        return additionalData;
    }

    public void setAdditionalData(AdditionalDataDTO additionalData) {
        this.additionalData = additionalData;
    }

    /**
     * Header information for the BRE request.
     */
    public static class HeaderDTO {
        @NotBlank(message = "Application ID is required")
        @Size(max = 100, message = "Application ID must not exceed 100 characters")
        private String applicationId;

        @NotBlank(message = "Product code is required")
        @Size(max = 50, message = "Product code must not exceed 50 characters")
        private String productCode;

        @NotNull(message = "Loan amount is required")
        @DecimalMin(value = "10000", message = "Loan amount must be at least 10,000")
        @DecimalMax(value = "10000000", message = "Loan amount must not exceed 10,000,000")
        private BigDecimal loanAmount;

        @NotNull(message = "Loan tenure is required")
        @Min(value = 6, message = "Loan tenure must be at least 6 months")
        @Max(value = 360, message = "Loan tenure must not exceed 360 months")
        private Integer loanTenure;

        // Constructors
        public HeaderDTO() {}

        public HeaderDTO(String applicationId, String productCode, BigDecimal loanAmount, Integer loanTenure) {
            this.applicationId = applicationId;
            this.productCode = productCode;
            this.loanAmount = loanAmount;
            this.loanTenure = loanTenure;
        }

        // Getters and Setters
        public String getApplicationId() {
            return applicationId;
        }

        public void setApplicationId(String applicationId) {
            this.applicationId = applicationId;
        }

        public String getProductCode() {
            return productCode;
        }

        public void setProductCode(String productCode) {
            this.productCode = productCode;
        }

        public BigDecimal getLoanAmount() {
            return loanAmount;
        }

        public void setLoanAmount(BigDecimal loanAmount) {
            this.loanAmount = loanAmount;
        }

        public Integer getLoanTenure() {
            return loanTenure;
        }

        public void setLoanTenure(Integer loanTenure) {
            this.loanTenure = loanTenure;
        }
    }

    /**
     * Applicant information for the BRE request.
     */
    public static class ApplicantDTO {
        @NotBlank(message = "First name is required")
        @Size(max = 100, message = "First name must not exceed 100 characters")
        private String firstName;

        @NotBlank(message = "Last name is required")
        @Size(max = 100, message = "Last name must not exceed 100 characters")
        private String lastName;

        @NotNull(message = "Date of birth is required")
        @Past(message = "Date of birth must be in the past")
        private LocalDate dateOfBirth;

        @NotNull(message = "Gender is required")
        private Gender gender;

        @NotBlank(message = "Mobile number is required")
        @Pattern(regexp = "^[6-9]\\d{9}$", message = "Mobile number must be a valid 10-digit Indian mobile number")
        private String mobileNumber;

        @NotBlank(message = "PAN is required")
        @Pattern(regexp = "^[A-Z]{5}[0-9]{4}[A-Z]{1}$", message = "PAN must be in valid format (e.g., **********)")
        private String pan;

        @NotBlank(message = "Name on PAN is required")
        @Size(max = 200, message = "Name on PAN must not exceed 200 characters")
        private String nameOnPan;

        @NotNull(message = "Date of birth on PAN is required")
        private LocalDate dobOnPan;

        @Valid
        @NotNull(message = "Address is required")
        private AddressDTO address;

        @Valid
        @NotNull(message = "NSDL data is required")
        private NSDLDataDTO nsdlData;

        // Constructors
        public ApplicantDTO() {}

        // Getters and Setters
        public String getFirstName() {
            return firstName;
        }

        public void setFirstName(String firstName) {
            this.firstName = firstName;
        }

        public String getLastName() {
            return lastName;
        }

        public void setLastName(String lastName) {
            this.lastName = lastName;
        }

        public LocalDate getDateOfBirth() {
            return dateOfBirth;
        }

        public void setDateOfBirth(LocalDate dateOfBirth) {
            this.dateOfBirth = dateOfBirth;
        }

        public Gender getGender() {
            return gender;
        }

        public void setGender(Gender gender) {
            this.gender = gender;
        }

        public String getMobileNumber() {
            return mobileNumber;
        }

        public void setMobileNumber(String mobileNumber) {
            this.mobileNumber = mobileNumber;
        }

        public String getPan() {
            return pan;
        }

        public void setPan(String pan) {
            this.pan = pan;
        }

        public String getNameOnPan() {
            return nameOnPan;
        }

        public void setNameOnPan(String nameOnPan) {
            this.nameOnPan = nameOnPan;
        }

        public LocalDate getDobOnPan() {
            return dobOnPan;
        }

        public void setDobOnPan(LocalDate dobOnPan) {
            this.dobOnPan = dobOnPan;
        }

        public AddressDTO getAddress() {
            return address;
        }

        public void setAddress(AddressDTO address) {
            this.address = address;
        }

        public NSDLDataDTO getNsdlData() {
            return nsdlData;
        }

        public void setNsdlData(NSDLDataDTO nsdlData) {
            this.nsdlData = nsdlData;
        }
    }

    /**
     * Address information for the applicant.
     */
    public static class AddressDTO {
        @NotBlank(message = "Address line 1 is required")
        @Size(max = 200, message = "Address line 1 must not exceed 200 characters")
        private String address1;

        @Size(max = 200, message = "Address line 2 must not exceed 200 characters")
        private String address2;

        @NotBlank(message = "Zip code is required")
        @Pattern(regexp = "^[1-9][0-9]{5}$", message = "Zip code must be a valid 6-digit Indian postal code")
        private String zipCode;

        @NotBlank(message = "Country is required")
        @Size(max = 100, message = "Country must not exceed 100 characters")
        private String country;

        @NotNull(message = "Address type is required")
        private AddressType addressType;

        // Constructors
        public AddressDTO() {}

        public AddressDTO(String address1, String address2, String zipCode, String country, AddressType addressType) {
            this.address1 = address1;
            this.address2 = address2;
            this.zipCode = zipCode;
            this.country = country;
            this.addressType = addressType;
        }

        // Getters and Setters
        public String getAddress1() {
            return address1;
        }

        public void setAddress1(String address1) {
            this.address1 = address1;
        }

        public String getAddress2() {
            return address2;
        }

        public void setAddress2(String address2) {
            this.address2 = address2;
        }

        public String getZipCode() {
            return zipCode;
        }

        public void setZipCode(String zipCode) {
            this.zipCode = zipCode;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }

        public AddressType getAddressType() {
            return addressType;
        }

        public void setAddressType(AddressType addressType) {
            this.addressType = addressType;
        }
    }

    /**
     * NSDL data for PAN verification.
     */
    public static class NSDLDataDTO {
        @NotNull(message = "NSDL name match percentage is required")
        @DecimalMin(value = "0.0", message = "NSDL name match percentage must be at least 0")
        @DecimalMax(value = "100.0", message = "NSDL name match percentage must not exceed 100")
        private BigDecimal nsdlNameMatchPercentage;

        @NotBlank(message = "PAN status is required")
        @Pattern(regexp = "^(VALID|INVALID|NOT_FOUND)$", message = "PAN status must be VALID, INVALID, or NOT_FOUND")
        private String panStatus;

        // Constructors
        public NSDLDataDTO() {}

        public NSDLDataDTO(BigDecimal nsdlNameMatchPercentage, String panStatus) {
            this.nsdlNameMatchPercentage = nsdlNameMatchPercentage;
            this.panStatus = panStatus;
        }

        // Getters and Setters
        public BigDecimal getNsdlNameMatchPercentage() {
            return nsdlNameMatchPercentage;
        }

        public void setNsdlNameMatchPercentage(BigDecimal nsdlNameMatchPercentage) {
            this.nsdlNameMatchPercentage = nsdlNameMatchPercentage;
        }

        public String getPanStatus() {
            return panStatus;
        }

        public void setPanStatus(String panStatus) {
            this.panStatus = panStatus;
        }
    }

    /**
     * Additional data for BRE evaluation.
     */
    public static class AdditionalDataDTO {
        @NotBlank(message = "Hunter data is required")
        private String hunter;

        @NotBlank(message = "Posidex dedupe data is required")
        private String posidexDedupe;

        @NotBlank(message = "BQS match data is required")
        private String bqsMatch;

        @Size(max = 200, message = "Company name must not exceed 200 characters")
        private String companyName;

        @Size(max = 100, message = "Constitution must not exceed 100 characters")
        private String constitution;

        @Size(max = 100, message = "Education qualification must not exceed 100 characters")
        private String eduQualification;

        @NotNull(message = "Relative of employee flag is required")
        private Boolean relativeOfEmployee;

        @NotNull(message = "Profession is required")
        private Profession profession;

        @NotNull(message = "Number of years in profession is required")
        @Min(value = 0, message = "Years in profession must be at least 0")
        @Max(value = 50, message = "Years in profession must not exceed 50")
        private Integer noOfYrsInProfession;

        @NotNull(message = "Politically exposed flag is required")
        private Boolean politicallyExposed;

        @NotNull(message = "Aadhaar available flag is required")
        private Boolean aadhaarAvailable;

        @NotNull(message = "CIBIL score is required")
        @Min(value = 300, message = "CIBIL score must be at least 300")
        @Max(value = 900, message = "CIBIL score must not exceed 900")
        private Integer cibil;

        @NotNull(message = "Yearly income is required")
        @DecimalMin(value = "0", message = "Yearly income must be at least 0")
        private BigDecimal yearlyIncome;

        // Constructors
        public AdditionalDataDTO() {}

        // Getters and Setters
        public String getHunter() {
            return hunter;
        }

        public void setHunter(String hunter) {
            this.hunter = hunter;
        }

        public String getPosidexDedupe() {
            return posidexDedupe;
        }

        public void setPosidexDedupe(String posidexDedupe) {
            this.posidexDedupe = posidexDedupe;
        }

        public String getBqsMatch() {
            return bqsMatch;
        }

        public void setBqsMatch(String bqsMatch) {
            this.bqsMatch = bqsMatch;
        }

        public String getCompanyName() {
            return companyName;
        }

        public void setCompanyName(String companyName) {
            this.companyName = companyName;
        }

        public String getConstitution() {
            return constitution;
        }

        public void setConstitution(String constitution) {
            this.constitution = constitution;
        }

        public String getEduQualification() {
            return eduQualification;
        }

        public void setEduQualification(String eduQualification) {
            this.eduQualification = eduQualification;
        }

        public Boolean getRelativeOfEmployee() {
            return relativeOfEmployee;
        }

        public void setRelativeOfEmployee(Boolean relativeOfEmployee) {
            this.relativeOfEmployee = relativeOfEmployee;
        }

        public Profession getProfession() {
            return profession;
        }

        public void setProfession(Profession profession) {
            this.profession = profession;
        }

        public Integer getNoOfYrsInProfession() {
            return noOfYrsInProfession;
        }

        public void setNoOfYrsInProfession(Integer noOfYrsInProfession) {
            this.noOfYrsInProfession = noOfYrsInProfession;
        }

        public Boolean getPoliticallyExposed() {
            return politicallyExposed;
        }

        public void setPoliticallyExposed(Boolean politicallyExposed) {
            this.politicallyExposed = politicallyExposed;
        }

        public Boolean getAadhaarAvailable() {
            return aadhaarAvailable;
        }

        public void setAadhaarAvailable(Boolean aadhaarAvailable) {
            this.aadhaarAvailable = aadhaarAvailable;
        }

        public Integer getCibil() {
            return cibil;
        }

        public void setCibil(Integer cibil) {
            this.cibil = cibil;
        }

        public BigDecimal getYearlyIncome() {
            return yearlyIncome;
        }

        public void setYearlyIncome(BigDecimal yearlyIncome) {
            this.yearlyIncome = yearlyIncome;
        }
    }
}
