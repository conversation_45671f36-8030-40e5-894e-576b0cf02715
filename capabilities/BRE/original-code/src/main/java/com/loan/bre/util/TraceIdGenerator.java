package com.loan.bre.util;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * Utility class for generating unique trace IDs for request tracking.
 */
public class TraceIdGenerator {

    private static final SecureRandom SECURE_RANDOM = new SecureRandom();
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    /**
     * Generate a unique trace ID using UUID.
     * Format: BRE-{UUID}
     */
    public static String generateUUIDBasedTraceId() {
        return "BRE-" + UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }

    /**
     * Generate a unique trace ID with timestamp.
     * Format: BRE-{TIMESTAMP}-{RANDOM}
     */
    public static String generateTimestampBasedTraceId() {
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMATTER);
        String randomPart = generateRandomString(8);
        return "BRE-" + timestamp + "-" + randomPart;
    }

    /**
     * Generate a unique trace ID with application ID prefix.
     * Format: BRE-{APP_ID_SUFFIX}-{TIMESTAMP}-{RANDOM}
     */
    public static String generateTraceIdWithAppId(String applicationId) {
        if (applicationId == null || applicationId.trim().isEmpty()) {
            return generateTimestampBasedTraceId();
        }
        
        // Extract last 4 characters of application ID
        String appIdSuffix = applicationId.length() >= 4 
            ? applicationId.substring(applicationId.length() - 4).toUpperCase()
            : applicationId.toUpperCase();
        
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMATTER);
        String randomPart = generateRandomString(6);
        
        return "BRE-" + appIdSuffix + "-" + timestamp + "-" + randomPart;
    }

    /**
     * Generate a short trace ID for internal use.
     * Format: BRE-{SHORT_TIMESTAMP}-{RANDOM}
     */
    public static String generateShortTraceId() {
        String shortTimestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMddHHmm"));
        String randomPart = generateRandomString(6);
        return "BRE-" + shortTimestamp + "-" + randomPart;
    }

    /**
     * Generate a trace ID for retry operations.
     * Format: {ORIGINAL_TRACE_ID}-R{RETRY_COUNT}
     */
    public static String generateRetryTraceId(String originalTraceId, int retryCount) {
        if (originalTraceId == null || originalTraceId.trim().isEmpty()) {
            return generateTimestampBasedTraceId() + "-R" + retryCount;
        }
        
        // Remove existing retry suffix if present
        String baseTraceId = originalTraceId.replaceAll("-R\\d+$", "");
        return baseTraceId + "-R" + retryCount;
    }

    /**
     * Validate if a string is a valid trace ID format.
     */
    public static boolean isValidTraceId(String traceId) {
        if (traceId == null || traceId.trim().isEmpty()) {
            return false;
        }
        
        // Check if it starts with BRE- and has minimum length
        return traceId.startsWith("BRE-") && traceId.length() >= 10;
    }

    /**
     * Extract application ID suffix from trace ID if present.
     */
    public static String extractAppIdFromTraceId(String traceId) {
        if (!isValidTraceId(traceId)) {
            return null;
        }
        
        String[] parts = traceId.split("-");
        if (parts.length >= 3 && parts[1].length() <= 4) {
            // Likely contains app ID suffix
            return parts[1];
        }
        
        return null;
    }

    /**
     * Check if trace ID is a retry trace ID.
     */
    public static boolean isRetryTraceId(String traceId) {
        return traceId != null && traceId.matches(".*-R\\d+$");
    }

    /**
     * Extract retry count from retry trace ID.
     */
    public static int extractRetryCount(String traceId) {
        if (!isRetryTraceId(traceId)) {
            return 0;
        }
        
        try {
            String retryPart = traceId.substring(traceId.lastIndexOf("-R") + 2);
            return Integer.parseInt(retryPart);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * Get the base trace ID without retry suffix.
     */
    public static String getBaseTraceId(String traceId) {
        if (traceId == null) {
            return null;
        }
        
        return traceId.replaceAll("-R\\d+$", "");
    }

    /**
     * Generate a random string of specified length.
     */
    private static String generateRandomString(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(CHARACTERS.charAt(SECURE_RANDOM.nextInt(CHARACTERS.length())));
        }
        return sb.toString();
    }

    /**
     * Generate trace ID for testing purposes.
     * Format: TEST-BRE-{RANDOM}
     */
    public static String generateTestTraceId() {
        String randomPart = generateRandomString(12);
        return "TEST-BRE-" + randomPart;
    }
}
