package com.loan.bre.service;

import com.loan.bre.dto.BRERequestDTO;
import com.loan.bre.dto.BREResponseDTO;
import com.loan.bre.enums.DecisionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Period;
import java.util.ArrayList;
import java.util.List;

/**
 * Service for validating BRE requests and responses.
 */
@Service
public class ValidationService {

    private static final Logger logger = LoggerFactory.getLogger(ValidationService.class);

    /**
     * Validate BRE request data.
     */
    public ValidationResult validateRequest(BRERequestDTO request) {
        List<String> errors = new ArrayList<>();

        if (request == null) {
            errors.add("Request cannot be null");
            return new ValidationResult(false, errors);
        }

        // Validate header
        validateHeader(request.getHeader(), errors);

        // Validate applicants
        validateApplicants(request.getApplicants(), errors);

        // Validate additional data
        validateAdditionalData(request.getAdditionalData(), errors);

        // Business rule validations
        validateBusinessRules(request, errors);

        boolean isValid = errors.isEmpty();
        if (!isValid) {
            logger.warn("Request validation failed with {} errors", errors.size());
        }

        return new ValidationResult(isValid, errors);
    }

    /**
     * Validate BRE response data.
     */
    public ValidationResult validateResponse(BREResponseDTO response) {
        List<String> errors = new ArrayList<>();

        if (response == null) {
            errors.add("Response cannot be null");
            return new ValidationResult(false, errors);
        }

        // Validate mandatory fields
        if (response.getDecision() == null) {
            errors.add("Decision is required in response");
        }

        if (response.getApplicationId() == null || response.getApplicationId().trim().isEmpty()) {
            errors.add("Application ID is required in response");
        }

        if (response.getTraceId() == null || response.getTraceId().trim().isEmpty()) {
            errors.add("Trace ID is required in response");
        }

        // Validate decision-specific requirements
        if (response.getDecision() == DecisionType.APPROVE) {
            if (response.getEligibleFOIR() == null || response.getEligibleFOIR().trim().isEmpty()) {
                errors.add("Eligible FOIR is required for approved decisions");
            }

            if (response.getEligibleAmount() == null || response.getEligibleAmount().compareTo(BigDecimal.ZERO) <= 0) {
                errors.add("Eligible amount must be greater than zero for approved decisions");
            }
        }

        boolean isValid = errors.isEmpty();
        if (!isValid) {
            logger.warn("Response validation failed with {} errors", errors.size());
        }

        return new ValidationResult(isValid, errors);
    }

    /**
     * Check if response indicates success for loan processing.
     * All valid BRE decisions (APPROVE, REJECT, REFER) are considered successful API responses.
     */
    public boolean isSuccessfulResponse(BREResponseDTO response) {
        if (response == null) {
            return false;
        }

        // Any valid decision type indicates a successful BRE evaluation
        // The business outcome (approve/reject/refer) is separate from API success
        return response.getDecision() != null;
    }

    private void validateHeader(BRERequestDTO.HeaderDTO header, List<String> errors) {
        if (header == null) {
            errors.add("Header is required");
            return;
        }

        if (header.getApplicationId() == null || header.getApplicationId().trim().isEmpty()) {
            errors.add("Application ID is required");
        }

        if (header.getProductCode() == null || header.getProductCode().trim().isEmpty()) {
            errors.add("Product code is required");
        }

        if (header.getLoanAmount() == null) {
            errors.add("Loan amount is required");
        } else if (header.getLoanAmount().compareTo(BigDecimal.valueOf(10000)) < 0) {
            errors.add("Loan amount must be at least 10,000");
        } else if (header.getLoanAmount().compareTo(BigDecimal.valueOf(10000000)) > 0) {
            errors.add("Loan amount must not exceed 10,000,000");
        }

        if (header.getLoanTenure() == null) {
            errors.add("Loan tenure is required");
        } else if (header.getLoanTenure() < 6 || header.getLoanTenure() > 360) {
            errors.add("Loan tenure must be between 6 and 360 months");
        }
    }

    private void validateApplicants(List<BRERequestDTO.ApplicantDTO> applicants, List<String> errors) {
        if (applicants == null || applicants.isEmpty()) {
            errors.add("At least one applicant is required");
            return;
        }

        for (int i = 0; i < applicants.size(); i++) {
            BRERequestDTO.ApplicantDTO applicant = applicants.get(i);
            String prefix = "Applicant " + (i + 1) + ": ";

            validateApplicant(applicant, errors, prefix);
        }
    }

    private void validateApplicant(BRERequestDTO.ApplicantDTO applicant, List<String> errors, String prefix) {
        if (applicant == null) {
            errors.add(prefix + "Applicant data is required");
            return;
        }

        // Validate personal details
        if (applicant.getFirstName() == null || applicant.getFirstName().trim().isEmpty()) {
            errors.add(prefix + "First name is required");
        }

        if (applicant.getLastName() == null || applicant.getLastName().trim().isEmpty()) {
            errors.add(prefix + "Last name is required");
        }

        if (applicant.getDateOfBirth() == null) {
            errors.add(prefix + "Date of birth is required");
        } else {
            // Check age (must be at least 18 years old)
            int age = Period.between(applicant.getDateOfBirth(), LocalDate.now()).getYears();
            if (age < 18) {
                errors.add(prefix + "Applicant must be at least 18 years old");
            } else if (age > 100) {
                errors.add(prefix + "Invalid date of birth");
            }
        }

        if (applicant.getGender() == null) {
            errors.add(prefix + "Gender is required");
        }

        // Validate PAN
        if (applicant.getPan() == null || applicant.getPan().trim().isEmpty()) {
            errors.add(prefix + "PAN is required");
        } else if (!applicant.getPan().matches("^[A-Z]{5}[0-9]{4}[A-Z]{1}$")) {
            errors.add(prefix + "PAN format is invalid");
        }

        // Validate mobile number
        if (applicant.getMobileNumber() == null || applicant.getMobileNumber().trim().isEmpty()) {
            errors.add(prefix + "Mobile number is required");
        } else if (!applicant.getMobileNumber().matches("^[6-9]\\d{9}$")) {
            errors.add(prefix + "Mobile number format is invalid");
        }

        // Validate DOB consistency
        if (applicant.getDateOfBirth() != null && applicant.getDobOnPan() != null) {
            if (!applicant.getDateOfBirth().equals(applicant.getDobOnPan())) {
                errors.add(prefix + "Date of birth and DOB on PAN must match");
            }
        }

        // Validate NSDL data
        validateNSDLData(applicant.getNsdlData(), errors, prefix);

        // Validate address
        validateAddress(applicant.getAddress(), errors, prefix);
    }

    private void validateNSDLData(BRERequestDTO.NSDLDataDTO nsdlData, List<String> errors, String prefix) {
        if (nsdlData == null) {
            errors.add(prefix + "NSDL data is required");
            return;
        }

        if (nsdlData.getNsdlNameMatchPercentage() == null) {
            errors.add(prefix + "NSDL name match percentage is required");
        } else if (nsdlData.getNsdlNameMatchPercentage().compareTo(BigDecimal.ZERO) < 0 ||
                   nsdlData.getNsdlNameMatchPercentage().compareTo(BigDecimal.valueOf(100)) > 0) {
            errors.add(prefix + "NSDL name match percentage must be between 0 and 100");
        }

        if (nsdlData.getPanStatus() == null || nsdlData.getPanStatus().trim().isEmpty()) {
            errors.add(prefix + "PAN status is required");
        } else if (!nsdlData.getPanStatus().matches("^(VALID|INVALID|NOT_FOUND)$")) {
            errors.add(prefix + "PAN status must be VALID, INVALID, or NOT_FOUND");
        }
    }

    private void validateAddress(BRERequestDTO.AddressDTO address, List<String> errors, String prefix) {
        if (address == null) {
            errors.add(prefix + "Address is required");
            return;
        }

        if (address.getAddress1() == null || address.getAddress1().trim().isEmpty()) {
            errors.add(prefix + "Address line 1 is required");
        }

        if (address.getZipCode() == null || address.getZipCode().trim().isEmpty()) {
            errors.add(prefix + "Zip code is required");
        } else if (!address.getZipCode().matches("^[1-9][0-9]{5}$")) {
            errors.add(prefix + "Zip code format is invalid");
        }

        if (address.getCountry() == null || address.getCountry().trim().isEmpty()) {
            errors.add(prefix + "Country is required");
        }

        if (address.getAddressType() == null) {
            errors.add(prefix + "Address type is required");
        }
    }

    private void validateAdditionalData(BRERequestDTO.AdditionalDataDTO additionalData, List<String> errors) {
        if (additionalData == null) {
            errors.add("Additional data is required");
            return;
        }

        if (additionalData.getCibil() == null) {
            errors.add("CIBIL score is required");
        } else if (additionalData.getCibil() < 300 || additionalData.getCibil() > 900) {
            errors.add("CIBIL score must be between 300 and 900");
        }

        if (additionalData.getYearlyIncome() == null) {
            errors.add("Yearly income is required");
        } else if (additionalData.getYearlyIncome().compareTo(BigDecimal.ZERO) < 0) {
            errors.add("Yearly income must be non-negative");
        }

        if (additionalData.getProfession() == null) {
            errors.add("Profession is required");
        }

        if (additionalData.getNoOfYrsInProfession() == null) {
            errors.add("Number of years in profession is required");
        } else if (additionalData.getNoOfYrsInProfession() < 0 || additionalData.getNoOfYrsInProfession() > 50) {
            errors.add("Years in profession must be between 0 and 50");
        }
    }

    private void validateBusinessRules(BRERequestDTO request, List<String> errors) {
        // Business rule: NSDL name match should be reasonable for valid PAN
        for (BRERequestDTO.ApplicantDTO applicant : request.getApplicants()) {
            if (applicant.getNsdlData() != null && 
                "VALID".equals(applicant.getNsdlData().getPanStatus()) &&
                applicant.getNsdlData().getNsdlNameMatchPercentage() != null &&
                applicant.getNsdlData().getNsdlNameMatchPercentage().compareTo(BigDecimal.valueOf(50)) < 0) {
                errors.add("NSDL name match percentage is too low for valid PAN");
            }
        }

        // Business rule: Income should be reasonable for loan amount
        BigDecimal loanAmount = request.getHeader().getLoanAmount();
        BigDecimal yearlyIncome = request.getAdditionalData().getYearlyIncome();
        
        if (loanAmount != null && yearlyIncome != null && yearlyIncome.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal loanToIncomeRatio = loanAmount.divide(yearlyIncome, 2, BigDecimal.ROUND_HALF_UP);
            if (loanToIncomeRatio.compareTo(BigDecimal.valueOf(10)) > 0) {
                errors.add("Loan amount is too high compared to yearly income");
            }
        }
    }

    /**
     * Result of validation operation.
     */
    public static class ValidationResult {
        private final boolean valid;
        private final List<String> errors;

        public ValidationResult(boolean valid, List<String> errors) {
            this.valid = valid;
            this.errors = errors != null ? errors : new ArrayList<>();
        }

        public boolean isValid() {
            return valid;
        }

        public List<String> getErrors() {
            return errors;
        }

        public String getErrorMessage() {
            return String.join("; ", errors);
        }
    }
}
