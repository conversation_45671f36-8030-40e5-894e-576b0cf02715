package com.loan.bre.enums;

/**
 * Enum representing the status of BRE request processing.
 */
public enum RequestStatus {
    INITIATED("INITIATED"),
    VALIDATED("VALIDATED"),
    SENT_TO_BRE("SENT_TO_BRE"),
    COMPLETED("COMPLETED"),
    FAILED("FAILED"),
    RETRY("RETRY");

    private final String value;

    RequestStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static RequestStatus fromValue(String value) {
        for (RequestStatus status : RequestStatus.values()) {
            if (status.value.equalsIgnoreCase(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown request status: " + value);
    }

    @Override
    public String toString() {
        return value;
    }
}
