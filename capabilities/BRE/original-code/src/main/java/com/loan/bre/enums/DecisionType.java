package com.loan.bre.enums;

/**
 * Enum representing the possible decision outcomes from BRE evaluation.
 */
public enum DecisionType {
    APPROVE("Approve"),
    REJECT("Reject"),
    REFER("Refer");

    private final String value;

    DecisionType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static DecisionType fromValue(String value) {
        for (DecisionType decision : DecisionType.values()) {
            if (decision.value.equalsIgnoreCase(value)) {
                return decision;
            }
        }
        throw new IllegalArgumentException("Unknown decision type: " + value);
    }

    @Override
    public String toString() {
        return value;
    }
}
