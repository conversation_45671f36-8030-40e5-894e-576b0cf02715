package com.loan.bre.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Pattern;

/**
 * Utility class for masking Personally Identifiable Information (PII) in data.
 */
public class PIIMaskingUtil {

    private static final Logger logger = LoggerFactory.getLogger(PIIMaskingUtil.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    // PII field patterns
    private static final Set<String> PII_FIELDS = Set.of(
        "pan", "nameOnPan", "firstName", "lastName", "mobileNumber", 
        "dateOfBirth", "dobOnPan", "address1", "address2", "zipCode"
    );

    // Regex patterns for PII detection
    private static final Pattern PAN_PATTERN = Pattern.compile("[A-Z]{5}[0-9]{4}[A-Z]{1}");
    private static final Pattern MOBILE_PATTERN = Pattern.compile("[6-9]\\d{9}");
    private static final Pattern EMAIL_PATTERN = Pattern.compile("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}");

    /**
     * Mask PII data in a Map object.
     */
    public static Map<String, Object> maskPIIData(Map<String, Object> data) {
        if (data == null || data.isEmpty()) {
            return data;
        }

        try {
            Map<String, Object> maskedData = new HashMap<>();
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                
                if (value == null) {
                    maskedData.put(key, null);
                    continue;
                }

                if (isPIIField(key)) {
                    maskedData.put(key, maskValue(value.toString()));
                } else if (value instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> nestedMap = (Map<String, Object>) value;
                    maskedData.put(key, maskPIIData(nestedMap));
                } else if (value instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Object> list = (List<Object>) value;
                    maskedData.put(key, maskPIIDataInList(list));
                } else {
                    // Check if the value contains PII patterns
                    String stringValue = value.toString();
                    if (containsPIIPattern(stringValue)) {
                        maskedData.put(key, maskPIIPatterns(stringValue));
                    } else {
                        maskedData.put(key, value);
                    }
                }
            }
            return maskedData;
        } catch (Exception e) {
            logger.error("Error masking PII data: {}", e.getMessage(), e);
            return data; // Return original data if masking fails
        }
    }

    /**
     * Mask PII data in a JSON string.
     */
    public static String maskPIIDataInJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return jsonString;
        }

        try {
            JsonNode jsonNode = objectMapper.readTree(jsonString);
            JsonNode maskedNode = maskPIIInJsonNode(jsonNode);
            return objectMapper.writeValueAsString(maskedNode);
        } catch (Exception e) {
            logger.error("Error masking PII in JSON: {}", e.getMessage(), e);
            return jsonString; // Return original JSON if masking fails
        }
    }

    /**
     * Mask PII data in a list.
     */
    private static List<Object> maskPIIDataInList(List<Object> list) {
        List<Object> maskedList = new ArrayList<>();
        for (Object item : list) {
            if (item instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> map = (Map<String, Object>) item;
                maskedList.add(maskPIIData(map));
            } else if (item instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> nestedList = (List<Object>) item;
                maskedList.add(maskPIIDataInList(nestedList));
            } else if (item != null && containsPIIPattern(item.toString())) {
                maskedList.add(maskPIIPatterns(item.toString()));
            } else {
                maskedList.add(item);
            }
        }
        return maskedList;
    }

    /**
     * Mask PII data in a JSON node.
     */
    private static JsonNode maskPIIInJsonNode(JsonNode node) {
        if (node.isObject()) {
            ObjectNode objectNode = (ObjectNode) node;
            ObjectNode maskedNode = objectMapper.createObjectNode();
            
            objectNode.fields().forEachRemaining(entry -> {
                String fieldName = entry.getKey();
                JsonNode fieldValue = entry.getValue();
                
                if (isPIIField(fieldName)) {
                    maskedNode.put(fieldName, maskValue(fieldValue.asText()));
                } else if (fieldValue.isObject() || fieldValue.isArray()) {
                    maskedNode.set(fieldName, maskPIIInJsonNode(fieldValue));
                } else if (fieldValue.isTextual() && containsPIIPattern(fieldValue.asText())) {
                    maskedNode.put(fieldName, maskPIIPatterns(fieldValue.asText()));
                } else {
                    maskedNode.set(fieldName, fieldValue);
                }
            });
            
            return maskedNode;
        } else if (node.isArray()) {
            for (int i = 0; i < node.size(); i++) {
                JsonNode arrayItem = node.get(i);
                ((com.fasterxml.jackson.databind.node.ArrayNode) node).set(i, maskPIIInJsonNode(arrayItem));
            }
        }
        
        return node;
    }

    /**
     * Check if a field name indicates PII data.
     */
    private static boolean isPIIField(String fieldName) {
        if (fieldName == null) {
            return false;
        }
        
        String lowerFieldName = fieldName.toLowerCase();
        return PII_FIELDS.stream().anyMatch(piiField -> 
            lowerFieldName.contains(piiField.toLowerCase()) ||
            lowerFieldName.equals(piiField.toLowerCase())
        );
    }

    /**
     * Check if a string contains PII patterns.
     */
    private static boolean containsPIIPattern(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        
        return PAN_PATTERN.matcher(value).find() ||
               MOBILE_PATTERN.matcher(value).find() ||
               EMAIL_PATTERN.matcher(value).find();
    }

    /**
     * Mask PII patterns in a string.
     */
    private static String maskPIIPatterns(String value) {
        if (value == null) {
            return null;
        }
        
        String maskedValue = value;
        
        // Mask PAN numbers
        maskedValue = PAN_PATTERN.matcher(maskedValue).replaceAll(match -> 
            match.group().substring(0, 2) + "***" + match.group().substring(5, 7) + "**");
        
        // Mask mobile numbers
        maskedValue = MOBILE_PATTERN.matcher(maskedValue).replaceAll(match -> 
            "***" + match.group().substring(6));
        
        // Mask email addresses
        maskedValue = EMAIL_PATTERN.matcher(maskedValue).replaceAll(match -> {
            String email = match.group();
            int atIndex = email.indexOf('@');
            if (atIndex > 2) {
                return email.substring(0, 2) + "***" + email.substring(atIndex);
            }
            return "***" + email.substring(atIndex);
        });
        
        return maskedValue;
    }

    /**
     * Mask a value based on its type and content.
     */
    private static String maskValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return value;
        }
        
        // If it contains PII patterns, mask them
        if (containsPIIPattern(value)) {
            return maskPIIPatterns(value);
        }
        
        // For other PII fields, mask partially
        if (value.length() <= 2) {
            return "**";
        } else if (value.length() <= 4) {
            return value.substring(0, 1) + "***";
        } else {
            return value.substring(0, 2) + "***" + value.substring(value.length() - 2);
        }
    }
}
