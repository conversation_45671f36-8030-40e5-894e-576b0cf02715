package com.loan.bre.enums;

/**
 * Enum representing different professions.
 */
public enum Profession {
    SALARIED("Salaried"),
    SELF_EMPLOYED("Self Employed"),
    BUSINESS("Business"),
    PROFESSIONAL("Professional"),
    RETIRED("Retired"),
    STUDENT("Student"),
    HOUSEWIFE("Housewife"),
    UNEMPLOYED("Unemployed"),
    OTHER("Other");

    private final String value;

    Profession(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static Profession fromValue(String value) {
        for (Profession profession : Profession.values()) {
            if (profession.value.equalsIgnoreCase(value)) {
                return profession;
            }
        }
        throw new IllegalArgumentException("Unknown profession: " + value);
    }

    @Override
    public String toString() {
        return value;
    }
}
