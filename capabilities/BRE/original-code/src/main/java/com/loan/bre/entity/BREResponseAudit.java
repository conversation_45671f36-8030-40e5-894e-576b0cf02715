package com.loan.bre.entity;

import com.loan.bre.enums.DecisionType;
import com.loan.bre.enums.RequestStatus;
import jakarta.persistence.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Entity representing BRE response audit log.
 */
@Entity
@Table(name = "bre_response_audit")
public class BREResponseAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "trace_id", nullable = false, length = 50)
    private String traceId;

    @Column(name = "application_id", nullable = false, length = 100)
    private String applicationId;

    @Column(name = "response_timestamp", nullable = false)
    private LocalDateTime responseTimestamp;

    @Column(name = "masked_response_data", columnDefinition = "TEXT")
    private String maskedResponseData;

    @Enumerated(EnumType.STRING)
    @Column(name = "response_status", nullable = false, length = 20)
    private RequestStatus responseStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "decision", length = 20)
    private DecisionType decision;

    @Column(name = "eligible_foir", length = 10)
    private String eligibleFoir;

    @Column(name = "eligible_amount", precision = 15, scale = 2)
    private BigDecimal eligibleAmount;

    @Column(name = "error_code", length = 50)
    private String errorCode;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "retry_count")
    private Integer retryCount = 0;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Constructors
    public BREResponseAudit() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public BREResponseAudit(String traceId, String applicationId, LocalDateTime responseTimestamp,
                           String maskedResponseData, RequestStatus responseStatus) {
        this();
        this.traceId = traceId;
        this.applicationId = applicationId;
        this.responseTimestamp = responseTimestamp;
        this.maskedResponseData = maskedResponseData;
        this.responseStatus = responseStatus;
    }

    // Lifecycle callbacks
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public LocalDateTime getResponseTimestamp() {
        return responseTimestamp;
    }

    public void setResponseTimestamp(LocalDateTime responseTimestamp) {
        this.responseTimestamp = responseTimestamp;
    }

    public String getMaskedResponseData() {
        return maskedResponseData;
    }

    public void setMaskedResponseData(String maskedResponseData) {
        this.maskedResponseData = maskedResponseData;
    }

    public RequestStatus getResponseStatus() {
        return responseStatus;
    }

    public void setResponseStatus(RequestStatus responseStatus) {
        this.responseStatus = responseStatus;
    }

    public DecisionType getDecision() {
        return decision;
    }

    public void setDecision(DecisionType decision) {
        this.decision = decision;
    }

    public String getEligibleFoir() {
        return eligibleFoir;
    }

    public void setEligibleFoir(String eligibleFoir) {
        this.eligibleFoir = eligibleFoir;
    }

    public BigDecimal getEligibleAmount() {
        return eligibleAmount;
    }

    public void setEligibleAmount(BigDecimal eligibleAmount) {
        this.eligibleAmount = eligibleAmount;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "BREResponseAudit{" +
                "id=" + id +
                ", traceId='" + traceId + '\'' +
                ", applicationId='" + applicationId + '\'' +
                ", responseTimestamp=" + responseTimestamp +
                ", responseStatus=" + responseStatus +
                ", decision=" + decision +
                ", eligibleFoir='" + eligibleFoir + '\'' +
                ", eligibleAmount=" + eligibleAmount +
                ", retryCount=" + retryCount +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
