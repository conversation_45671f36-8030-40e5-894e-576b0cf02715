package com.loan.bre.enums;

/**
 * Enum representing different types of addresses.
 */
public enum AddressType {
    PERMANENT("Permanent"),
    CURRENT("Current"),
    OFFICE("Office"),
    CORRESPONDENCE("Correspondence");

    private final String value;

    AddressType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static AddressType fromValue(String value) {
        for (AddressType addressType : AddressType.values()) {
            if (addressType.value.equalsIgnoreCase(value)) {
                return addressType;
            }
        }
        throw new IllegalArgumentException("Unknown address type: " + value);
    }

    @Override
    public String toString() {
        return value;
    }
}
