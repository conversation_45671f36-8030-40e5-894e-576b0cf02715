package com.loan.bre;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main application class for Business Rule Engine (BRE) Application.
 * 
 * This application processes loan eligibility requests by:
 * 1. Validating incoming loan application data
 * 2. Calling external BRE service for decision making
 * 3. Processing and returning the decision response
 * 4. Maintaining audit logs with masked PII data
 */
@SpringBootApplication
@EnableAsync
@EnableTransactionManagement
@ConfigurationPropertiesScan
public class BREApplication {

    public static void main(String[] args) {
        SpringApplication.run(BREApplication.class, args);
    }
}
