server:
  port: ${SERVER_PORT:8108}
  servlet:
    context-path: /api/bre

spring:
  application:
    name: bre-application
  
  datasource:
    url: ***************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true

  jackson:
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false

# BRE Service Configuration
bre:
  service:
    base-url: http://localhost:8208/api/bre/internal/v1
    evaluate-endpoint: /evaluate
    timeout: 30s
    retry:
      max-attempts: 3
      delay: 2s
      multiplier: 2.0
  
  audit:
    retention-days: 90
    mask-pii: true

# Logging Configuration
logging:
  level:
    com.loan.bre: INFO
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: logs/bre-application.log

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized

# SpringDoc OpenAPI Configuration
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    try-it-out-enabled: true
    operations-sorter: method
    tags-sorter: alpha
    disable-swagger-default-url: true
  show-actuator: false
  use-management-port: false
  writer-with-default-pretty-printer: true


