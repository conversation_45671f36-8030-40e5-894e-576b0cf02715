-- Create audit tables for BRE application

-- Table to store BRE request audit logs
CREATE TABLE bre_request_audit (
    id BIGSERIAL PRIMARY KEY,
    trace_id VARCHAR(50) NOT NULL UNIQUE,
    application_id VARCHAR(100) NOT NULL,
    request_timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    masked_request_data TEXT NOT NULL,
    request_status VARCHAR(20) NOT NULL DEFAULT 'INITIATED',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Table to store BRE response audit logs
CREATE TABLE bre_response_audit (
    id BIGSERIAL PRIMARY KEY,
    trace_id VARCHAR(50) NOT NULL,
    application_id VARCHAR(100) NOT NULL,
    response_timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    masked_response_data TEXT,
    response_status VARCHAR(20) NOT NULL,
    decision VARCHAR(20),
    eligible_foir VARCHAR(10),
    eligible_amount DECIMAL(15,2),
    error_code VARCHAR(50),
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trace_id) REFERENCES bre_request_audit(trace_id)
);

-- Table to store processing errors and retries
CREATE TABLE bre_error_log (
    id BIGSERIAL PRIMARY KEY,
    trace_id VARCHAR(50) NOT NULL,
    application_id VARCHAR(100) NOT NULL,
    error_timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    error_type VARCHAR(50) NOT NULL,
    error_code VARCHAR(50),
    error_message TEXT NOT NULL,
    stack_trace TEXT,
    retry_attempt INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trace_id) REFERENCES bre_request_audit(trace_id)
);

-- Indexes for performance
CREATE INDEX idx_bre_request_audit_trace_id ON bre_request_audit(trace_id);
CREATE INDEX idx_bre_request_audit_application_id ON bre_request_audit(application_id);
CREATE INDEX idx_bre_request_audit_timestamp ON bre_request_audit(request_timestamp);

CREATE INDEX idx_bre_response_audit_trace_id ON bre_response_audit(trace_id);
CREATE INDEX idx_bre_response_audit_application_id ON bre_response_audit(application_id);
CREATE INDEX idx_bre_response_audit_timestamp ON bre_response_audit(response_timestamp);
CREATE INDEX idx_bre_response_audit_decision ON bre_response_audit(decision);

CREATE INDEX idx_bre_error_log_trace_id ON bre_error_log(trace_id);
CREATE INDEX idx_bre_error_log_application_id ON bre_error_log(application_id);
CREATE INDEX idx_bre_error_log_timestamp ON bre_error_log(error_timestamp);
CREATE INDEX idx_bre_error_log_error_type ON bre_error_log(error_type);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update updated_at
CREATE TRIGGER update_bre_request_audit_updated_at 
    BEFORE UPDATE ON bre_request_audit 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bre_response_audit_updated_at 
    BEFORE UPDATE ON bre_response_audit 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
