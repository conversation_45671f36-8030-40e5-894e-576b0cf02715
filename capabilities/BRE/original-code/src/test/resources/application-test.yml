server:
  port: 0

spring:
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    database-platform: org.hibernate.dialect.H2Dialect
  
  flyway:
    enabled: false

# BRE Service Configuration for testing
bre:
  service:
    base-url: http://localhost:8083/api/bre/internal/v1
    evaluate-endpoint: /evaluate
    timeout: 5s
    retry:
      max-attempts: 2
      delay: 1s
      multiplier: 1.5
  
  audit:
    retention-days: 30
    mask-pii: true

# Logging Configuration
logging:
  level:
    com.loan.bre: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: INFO
