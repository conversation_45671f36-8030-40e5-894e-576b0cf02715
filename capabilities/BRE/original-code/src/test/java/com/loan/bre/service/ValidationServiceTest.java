package com.loan.bre.service;

import com.loan.bre.dto.BRERequestDTO;
import com.loan.bre.dto.BREResponseDTO;
import com.loan.bre.enums.AddressType;
import com.loan.bre.enums.DecisionType;
import com.loan.bre.enums.Gender;
import com.loan.bre.enums.Profession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ValidationService.
 */
class ValidationServiceTest {

    private ValidationService validationService;

    @BeforeEach
    void setUp() {
        validationService = new ValidationService();
    }

    @Test
    void testValidateRequest_ValidRequest_ShouldPass() {
        // Given
        BRERequestDTO request = createValidRequest();

        // When
        ValidationService.ValidationResult result = validationService.validateRequest(request);

        // Then
        assertTrue(result.isValid());
        assertTrue(result.getErrors().isEmpty());
    }

    @Test
    void testValidateRequest_NullRequest_ShouldFail() {
        // When
        ValidationService.ValidationResult result = validationService.validateRequest(null);

        // Then
        assertFalse(result.isValid());
        assertEquals(1, result.getErrors().size());
        assertEquals("Request cannot be null", result.getErrors().get(0));
    }

    @Test
    void testValidateRequest_InvalidPAN_ShouldFail() {
        // Given
        BRERequestDTO request = createValidRequest();
        request.getApplicants().get(0).setPan("INVALID_PAN");

        // When
        ValidationService.ValidationResult result = validationService.validateRequest(request);

        // Then
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream()
            .anyMatch(error -> error.contains("PAN format is invalid")));
    }

    @Test
    void testValidateRequest_InvalidMobileNumber_ShouldFail() {
        // Given
        BRERequestDTO request = createValidRequest();
        request.getApplicants().get(0).setMobileNumber("1234567890"); // Invalid - doesn't start with 6-9

        // When
        ValidationService.ValidationResult result = validationService.validateRequest(request);

        // Then
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream()
            .anyMatch(error -> error.contains("Mobile number format is invalid")));
    }

    @Test
    void testValidateRequest_UnderageApplicant_ShouldFail() {
        // Given
        BRERequestDTO request = createValidRequest();
        request.getApplicants().get(0).setDateOfBirth(LocalDate.now().minusYears(17)); // 17 years old

        // When
        ValidationService.ValidationResult result = validationService.validateRequest(request);

        // Then
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream()
            .anyMatch(error -> error.contains("must be at least 18 years old")));
    }

    @Test
    void testValidateRequest_LowCIBIL_ShouldFail() {
        // Given
        BRERequestDTO request = createValidRequest();
        request.getAdditionalData().setCibil(250); // Below minimum

        // When
        ValidationService.ValidationResult result = validationService.validateRequest(request);

        // Then
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream()
            .anyMatch(error -> error.contains("CIBIL score must be between 300 and 900")));
    }

    @Test
    void testValidateResponse_ValidApprovalResponse_ShouldPass() {
        // Given
        BREResponseDTO response = createValidApprovalResponse();

        // When
        ValidationService.ValidationResult result = validationService.validateResponse(response);

        // Then
        assertTrue(result.isValid());
        assertTrue(result.getErrors().isEmpty());
    }

    @Test
    void testValidateResponse_ApprovalWithoutFOIR_ShouldFail() {
        // Given
        BREResponseDTO response = createValidApprovalResponse();
        response.setEligibleFOIR(null);

        // When
        ValidationService.ValidationResult result = validationService.validateResponse(response);

        // Then
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream()
            .anyMatch(error -> error.contains("Eligible FOIR is required for approved decisions")));
    }

    @Test
    void testIsSuccessfulResponse_ValidApproval_ShouldReturnTrue() {
        // Given
        BREResponseDTO response = createValidApprovalResponse();

        // When
        boolean isSuccessful = validationService.isSuccessfulResponse(response);

        // Then
        assertTrue(isSuccessful);
    }

    @Test
    void testIsSuccessfulResponse_RejectedDecision_ShouldReturnTrue() {
        // Given
        BREResponseDTO response = createValidApprovalResponse();
        response.setDecision(DecisionType.REJECT);

        // When
        boolean isSuccessful = validationService.isSuccessfulResponse(response);

        // Then
        // REJECT is a valid decision, so API call is considered successful
        assertTrue(isSuccessful);
    }

    private BRERequestDTO createValidRequest() {
        // Create header
        BRERequestDTO.HeaderDTO header = new BRERequestDTO.HeaderDTO(
            "APP123456",
            "PL001",
            BigDecimal.valueOf(500000),
            60
        );

        // Create address
        BRERequestDTO.AddressDTO address = new BRERequestDTO.AddressDTO(
            "123 Main Street",
            "Apartment 4B",
            "400001",
            "India",
            AddressType.PERMANENT
        );

        // Create NSDL data
        BRERequestDTO.NSDLDataDTO nsdlData = new BRERequestDTO.NSDLDataDTO(
            BigDecimal.valueOf(95.5),
            "VALID"
        );

        // Create applicant
        BRERequestDTO.ApplicantDTO applicant = new BRERequestDTO.ApplicantDTO();
        applicant.setFirstName("John");
        applicant.setLastName("Doe");
        applicant.setDateOfBirth(LocalDate.of(1990, 1, 1));
        applicant.setGender(Gender.MALE);
        applicant.setMobileNumber("9876543210");
        applicant.setPan("**********");
        applicant.setNameOnPan("John Doe");
        applicant.setDobOnPan(LocalDate.of(1990, 1, 1));
        applicant.setAddress(address);
        applicant.setNsdlData(nsdlData);

        // Create additional data
        BRERequestDTO.AdditionalDataDTO additionalData = new BRERequestDTO.AdditionalDataDTO();
        additionalData.setHunter("CLEAR");
        additionalData.setPosidexDedupe("NO_MATCH");
        additionalData.setBqsMatch("CLEAR");
        additionalData.setCompanyName("Tech Corp");
        additionalData.setConstitution("Private Limited");
        additionalData.setEduQualification("Graduate");
        additionalData.setRelativeOfEmployee(false);
        additionalData.setProfession(Profession.SALARIED);
        additionalData.setNoOfYrsInProfession(5);
        additionalData.setPoliticallyExposed(false);
        additionalData.setAadhaarAvailable(true);
        additionalData.setCibil(750);
        additionalData.setYearlyIncome(BigDecimal.valueOf(600000));

        return new BRERequestDTO(header, List.of(applicant), additionalData);
    }

    private BREResponseDTO createValidApprovalResponse() {
        BREResponseDTO response = new BREResponseDTO();
        response.setApplicationId("APP123456");
        response.setDecision(DecisionType.APPROVE);
        response.setEligibleFOIR("40%");
        response.setEligibleAmount(BigDecimal.valueOf(400000));
        response.setTimestamp(LocalDateTime.now());
        response.setTraceId("BRE-TEST-123");
        return response;
    }
}
