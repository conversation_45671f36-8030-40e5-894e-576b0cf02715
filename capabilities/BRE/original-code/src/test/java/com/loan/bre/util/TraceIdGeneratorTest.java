package com.loan.bre.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for TraceIdGenerator.
 */
class TraceIdGeneratorTest {

    @Test
    void testGenerateUUIDBasedTraceId_ShouldStartWithBRE() {
        // When
        String traceId = TraceIdGenerator.generateUUIDBasedTraceId();

        // Then
        assertNotNull(traceId);
        assertTrue(traceId.startsWith("BRE-"));
        assertTrue(traceId.length() > 10);
    }

    @Test
    void testGenerateTimestampBasedTraceId_ShouldContainTimestamp() {
        // When
        String traceId = TraceIdGenerator.generateTimestampBasedTraceId();

        // Then
        assertNotNull(traceId);
        assertTrue(traceId.startsWith("BRE-"));
        assertTrue(traceId.contains("-"));
        assertTrue(traceId.length() > 15);
    }

    @Test
    void testGenerateTraceIdWithAppId_WithValidAppId_ShouldContainAppId() {
        // Given
        String applicationId = "APP123456";

        // When
        String traceId = TraceIdGenerator.generateTraceIdWithAppId(applicationId);

        // Then
        assertNotNull(traceId);
        assertTrue(traceId.startsWith("BRE-"));
        assertTrue(traceId.contains("3456")); // Last 4 characters of app ID
    }

    @Test
    void testGenerateTraceIdWithAppId_WithNullAppId_ShouldGenerateDefault() {
        // When
        String traceId = TraceIdGenerator.generateTraceIdWithAppId(null);

        // Then
        assertNotNull(traceId);
        assertTrue(traceId.startsWith("BRE-"));
    }

    @Test
    void testGenerateShortTraceId_ShouldBeShorter() {
        // When
        String traceId = TraceIdGenerator.generateShortTraceId();

        // Then
        assertNotNull(traceId);
        assertTrue(traceId.startsWith("BRE-"));
        assertTrue(traceId.length() < 20); // Should be shorter than full timestamp version
    }

    @Test
    void testGenerateRetryTraceId_ShouldAppendRetryCount() {
        // Given
        String originalTraceId = "BRE-TEST-123";
        int retryCount = 2;

        // When
        String retryTraceId = TraceIdGenerator.generateRetryTraceId(originalTraceId, retryCount);

        // Then
        assertNotNull(retryTraceId);
        assertTrue(retryTraceId.startsWith("BRE-TEST-123"));
        assertTrue(retryTraceId.endsWith("-R2"));
    }

    @Test
    void testGenerateRetryTraceId_WithExistingRetry_ShouldReplaceRetryCount() {
        // Given
        String originalTraceId = "BRE-TEST-123-R1";
        int retryCount = 3;

        // When
        String retryTraceId = TraceIdGenerator.generateRetryTraceId(originalTraceId, retryCount);

        // Then
        assertNotNull(retryTraceId);
        assertTrue(retryTraceId.equals("BRE-TEST-123-R3"));
        assertFalse(retryTraceId.contains("-R1"));
    }

    @Test
    void testIsValidTraceId_WithValidTraceId_ShouldReturnTrue() {
        // Given
        String validTraceId = "BRE-TEST-123456";

        // When
        boolean isValid = TraceIdGenerator.isValidTraceId(validTraceId);

        // Then
        assertTrue(isValid);
    }

    @Test
    void testIsValidTraceId_WithInvalidTraceId_ShouldReturnFalse() {
        // Given
        String invalidTraceId = "INVALID-123";

        // When
        boolean isValid = TraceIdGenerator.isValidTraceId(invalidTraceId);

        // Then
        assertFalse(isValid);
    }

    @Test
    void testIsValidTraceId_WithNullTraceId_ShouldReturnFalse() {
        // When
        boolean isValid = TraceIdGenerator.isValidTraceId(null);

        // Then
        assertFalse(isValid);
    }

    @Test
    void testIsRetryTraceId_WithRetryTraceId_ShouldReturnTrue() {
        // Given
        String retryTraceId = "BRE-TEST-123-R2";

        // When
        boolean isRetry = TraceIdGenerator.isRetryTraceId(retryTraceId);

        // Then
        assertTrue(isRetry);
    }

    @Test
    void testIsRetryTraceId_WithNormalTraceId_ShouldReturnFalse() {
        // Given
        String normalTraceId = "BRE-TEST-123";

        // When
        boolean isRetry = TraceIdGenerator.isRetryTraceId(normalTraceId);

        // Then
        assertFalse(isRetry);
    }

    @Test
    void testExtractRetryCount_WithRetryTraceId_ShouldReturnCount() {
        // Given
        String retryTraceId = "BRE-TEST-123-R5";

        // When
        int retryCount = TraceIdGenerator.extractRetryCount(retryTraceId);

        // Then
        assertEquals(5, retryCount);
    }

    @Test
    void testExtractRetryCount_WithNormalTraceId_ShouldReturnZero() {
        // Given
        String normalTraceId = "BRE-TEST-123";

        // When
        int retryCount = TraceIdGenerator.extractRetryCount(normalTraceId);

        // Then
        assertEquals(0, retryCount);
    }

    @Test
    void testGetBaseTraceId_WithRetryTraceId_ShouldReturnBase() {
        // Given
        String retryTraceId = "BRE-TEST-123-R3";

        // When
        String baseTraceId = TraceIdGenerator.getBaseTraceId(retryTraceId);

        // Then
        assertEquals("BRE-TEST-123", baseTraceId);
    }

    @Test
    void testGetBaseTraceId_WithNormalTraceId_ShouldReturnSame() {
        // Given
        String normalTraceId = "BRE-TEST-123";

        // When
        String baseTraceId = TraceIdGenerator.getBaseTraceId(normalTraceId);

        // Then
        assertEquals("BRE-TEST-123", baseTraceId);
    }

    @Test
    void testGenerateTestTraceId_ShouldStartWithTEST() {
        // When
        String testTraceId = TraceIdGenerator.generateTestTraceId();

        // Then
        assertNotNull(testTraceId);
        assertTrue(testTraceId.startsWith("TEST-BRE-"));
    }

    @Test
    void testMultipleGenerations_ShouldBeUnique() {
        // When
        String traceId1 = TraceIdGenerator.generateUUIDBasedTraceId();
        String traceId2 = TraceIdGenerator.generateUUIDBasedTraceId();

        // Then
        assertNotEquals(traceId1, traceId2);
    }
}
