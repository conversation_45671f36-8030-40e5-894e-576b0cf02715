package com.loan.bre.util;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for PIIMaskingUtil.
 */
class PIIMaskingUtilTest {

    @Test
    void testMaskPIIData_WithPANField_ShouldMaskPAN() {
        // Given
        Map<String, Object> data = new HashMap<>();
        data.put("pan", "**********");
        data.put("name", "John Doe");

        // When
        Map<String, Object> maskedData = PIIMaskingUtil.maskPIIData(data);

        // Then
        assertNotNull(maskedData);
        String maskedPan = (String) maskedData.get("pan");
        assertTrue(maskedPan.contains("***"));
        assertFalse(maskedPan.equals("**********"));
        assertEquals("John Doe", maskedData.get("name")); // Non-PII field should remain unchanged
    }

    @Test
    void testMaskPIIData_WithMobileNumber_ShouldMaskMobile() {
        // Given
        Map<String, Object> data = new HashMap<>();
        data.put("mobileNumber", "9876543210");

        // When
        Map<String, Object> maskedData = PIIMaskingUtil.maskPIIData(data);

        // Then
        assertNotNull(maskedData);
        String maskedMobile = (String) maskedData.get("mobileNumber");
        assertTrue(maskedMobile.contains("***"));
        assertFalse(maskedMobile.equals("9876543210"));
    }

    @Test
    void testMaskPIIData_WithNestedData_ShouldMaskNestedPII() {
        // Given
        Map<String, Object> nestedData = new HashMap<>();
        nestedData.put("pan", "**********");
        nestedData.put("firstName", "John");

        Map<String, Object> data = new HashMap<>();
        data.put("applicant", nestedData);
        data.put("loanAmount", 500000);

        // When
        Map<String, Object> maskedData = PIIMaskingUtil.maskPIIData(data);

        // Then
        assertNotNull(maskedData);
        @SuppressWarnings("unchecked")
        Map<String, Object> maskedApplicant = (Map<String, Object>) maskedData.get("applicant");
        assertNotNull(maskedApplicant);
        
        String maskedPan = (String) maskedApplicant.get("pan");
        assertTrue(maskedPan.contains("***"));
        
        String maskedFirstName = (String) maskedApplicant.get("firstName");
        assertFalse(maskedFirstName.equals("John"));
        
        assertEquals(500000, maskedData.get("loanAmount")); // Non-PII should remain unchanged
    }

    @Test
    void testMaskPIIData_WithNullData_ShouldReturnNull() {
        // When
        Map<String, Object> maskedData = PIIMaskingUtil.maskPIIData(null);

        // Then
        assertNull(maskedData);
    }

    @Test
    void testMaskPIIData_WithEmptyData_ShouldReturnEmpty() {
        // Given
        Map<String, Object> data = new HashMap<>();

        // When
        Map<String, Object> maskedData = PIIMaskingUtil.maskPIIData(data);

        // Then
        assertNotNull(maskedData);
        assertTrue(maskedData.isEmpty());
    }

    @Test
    void testMaskPIIDataInJson_WithValidJson_ShouldMaskPII() {
        // Given
        String json = "{\"pan\":\"**********\",\"firstName\":\"John\",\"loanAmount\":500000}";

        // When
        String maskedJson = PIIMaskingUtil.maskPIIDataInJson(json);

        // Then
        assertNotNull(maskedJson);
        assertFalse(maskedJson.contains("**********"));
        assertTrue(maskedJson.contains("***"));
        assertTrue(maskedJson.contains("500000")); // Non-PII should remain
    }

    @Test
    void testMaskPIIDataInJson_WithInvalidJson_ShouldReturnOriginal() {
        // Given
        String invalidJson = "invalid json";

        // When
        String result = PIIMaskingUtil.maskPIIDataInJson(invalidJson);

        // Then
        assertEquals(invalidJson, result);
    }

    @Test
    void testMaskPIIDataInJson_WithNullJson_ShouldReturnNull() {
        // When
        String result = PIIMaskingUtil.maskPIIDataInJson(null);

        // Then
        assertNull(result);
    }

    @Test
    void testMaskPIIData_WithPIIPatterns_ShouldMaskPatterns() {
        // Given
        Map<String, Object> data = new HashMap<>();
        data.put("description", "PAN number ********** and mobile 9876543210");
        data.put("email", "<EMAIL>");

        // When
        Map<String, Object> maskedData = PIIMaskingUtil.maskPIIData(data);

        // Then
        assertNotNull(maskedData);
        String maskedDescription = (String) maskedData.get("description");
        assertFalse(maskedDescription.contains("**********"));
        assertFalse(maskedDescription.contains("9876543210"));
        assertTrue(maskedDescription.contains("***"));
        
        String maskedEmail = (String) maskedData.get("email");
        assertFalse(maskedEmail.contains("<EMAIL>"));
        assertTrue(maskedEmail.contains("@example.com"));
    }
}
