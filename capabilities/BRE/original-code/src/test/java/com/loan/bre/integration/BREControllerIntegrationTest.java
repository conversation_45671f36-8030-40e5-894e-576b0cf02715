package com.loan.bre.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.loan.bre.dto.BRERequestDTO;
import com.loan.bre.enums.AddressType;
import com.loan.bre.enums.Gender;
import com.loan.bre.enums.Profession;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for BRE Controller.
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@AutoConfigureMockMvc
@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.ANY)
class BREControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testHealthCheck_ShouldReturnOk() throws Exception {
        mockMvc.perform(get("/v1/loan-eligibility/health"))
                .andExpect(status().isOk())
                .andExpect(content().string("BRE Service is healthy"));
    }

    @Test
    void testEvaluateLoanEligibility_WithValidRequest_ShouldReturnResponse() throws Exception {
        // Given
        BRERequestDTO request = createValidRequest();
        String requestJson = objectMapper.writeValueAsString(request);

        // When & Then - Expecting 500 because external BRE service is not available in test
        mockMvc.perform(post("/v1/loan-eligibility/evaluate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("We are unable to proceed with your loan application right now. Please try again later."))
                .andExpect(jsonPath("$.traceId").exists())
                .andExpect(jsonPath("$.timestamp").exists());
    }

    @Test
    void testEvaluateLoanEligibility_WithInvalidRequest_ShouldReturnBadRequest() throws Exception {
        // Given - Request with missing required fields
        BRERequestDTO request = new BRERequestDTO();
        String requestJson = objectMapper.writeValueAsString(request);

        // When & Then
        mockMvc.perform(post("/v1/loan-eligibility/evaluate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorCode").value("REQUEST_VALIDATION_ERROR"))
                .andExpect(jsonPath("$.errorMessage").exists());
    }

    @Test
    void testEvaluateLoanEligibility_WithInvalidPAN_ShouldReturnBadRequest() throws Exception {
        // Given
        BRERequestDTO request = createValidRequest();
        request.getApplicants().get(0).setPan("INVALID_PAN");
        String requestJson = objectMapper.writeValueAsString(request);

        // When & Then
        mockMvc.perform(post("/v1/loan-eligibility/evaluate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorCode").value("REQUEST_VALIDATION_ERROR"))
                .andExpect(jsonPath("$.errorMessage").value(org.hamcrest.Matchers.containsString("PAN must be in valid format")));
    }

    @Test
    void testEvaluateLoanEligibility_WithInvalidMobile_ShouldReturnBadRequest() throws Exception {
        // Given
        BRERequestDTO request = createValidRequest();
        request.getApplicants().get(0).setMobileNumber("1234567890"); // Invalid mobile
        String requestJson = objectMapper.writeValueAsString(request);

        // When & Then
        mockMvc.perform(post("/v1/loan-eligibility/evaluate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorCode").value("REQUEST_VALIDATION_ERROR"))
                .andExpect(jsonPath("$.errorMessage").value(org.hamcrest.Matchers.containsString("Mobile number")));
    }

    @Test
    void testEvaluateLoanEligibility_WithLowCIBIL_ShouldReturnBadRequest() throws Exception {
        // Given
        BRERequestDTO request = createValidRequest();
        request.getAdditionalData().setCibil(250); // Below minimum
        String requestJson = objectMapper.writeValueAsString(request);

        // When & Then
        mockMvc.perform(post("/v1/loan-eligibility/evaluate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorCode").value("REQUEST_VALIDATION_ERROR"))
                .andExpect(jsonPath("$.errorMessage").value(org.hamcrest.Matchers.containsString("CIBIL score")));
    }

    @Test
    void testEvaluateLoanEligibility_WithUnderageApplicant_ShouldReturnBadRequest() throws Exception {
        // Given
        BRERequestDTO request = createValidRequest();
        request.getApplicants().get(0).setDateOfBirth(LocalDate.now().minusYears(17)); // 17 years old
        String requestJson = objectMapper.writeValueAsString(request);

        // When & Then
        mockMvc.perform(post("/v1/loan-eligibility/evaluate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("18 years old")));
    }

    private BRERequestDTO createValidRequest() {
        // Create header
        BRERequestDTO.HeaderDTO header = new BRERequestDTO.HeaderDTO(
            "APP123456",
            "PL001",
            BigDecimal.valueOf(500000),
            60
        );

        // Create address
        BRERequestDTO.AddressDTO address = new BRERequestDTO.AddressDTO(
            "123 Main Street",
            "Apartment 4B",
            "400001",
            "India",
            AddressType.PERMANENT
        );

        // Create NSDL data
        BRERequestDTO.NSDLDataDTO nsdlData = new BRERequestDTO.NSDLDataDTO(
            BigDecimal.valueOf(95.5),
            "VALID"
        );

        // Create applicant
        BRERequestDTO.ApplicantDTO applicant = new BRERequestDTO.ApplicantDTO();
        applicant.setFirstName("John");
        applicant.setLastName("Doe");
        applicant.setDateOfBirth(LocalDate.of(1990, 1, 1));
        applicant.setGender(Gender.MALE);
        applicant.setMobileNumber("9876543210");
        applicant.setPan("**********");
        applicant.setNameOnPan("John Doe");
        applicant.setDobOnPan(LocalDate.of(1990, 1, 1));
        applicant.setAddress(address);
        applicant.setNsdlData(nsdlData);

        // Create additional data
        BRERequestDTO.AdditionalDataDTO additionalData = new BRERequestDTO.AdditionalDataDTO();
        additionalData.setHunter("CLEAR");
        additionalData.setPosidexDedupe("NO_MATCH");
        additionalData.setBqsMatch("CLEAR");
        additionalData.setCompanyName("Tech Corp");
        additionalData.setConstitution("Private Limited");
        additionalData.setEduQualification("Graduate");
        additionalData.setRelativeOfEmployee(false);
        additionalData.setProfession(Profession.SALARIED);
        additionalData.setNoOfYrsInProfession(5);
        additionalData.setPoliticallyExposed(false);
        additionalData.setAadhaarAvailable(true);
        additionalData.setCibil(750);
        additionalData.setYearlyIncome(BigDecimal.valueOf(600000));

        return new BRERequestDTO(header, List.of(applicant), additionalData);
    }
}
