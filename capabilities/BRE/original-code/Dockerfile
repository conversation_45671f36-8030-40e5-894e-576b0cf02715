# Multi-stage build for BRE Original Service
FROM maven:3.9.6-eclipse-temurin-17-alpine AS builder

# Set working directory
WORKDIR /app

# Copy pom.xml first for better layer caching
COPY pom.xml .

# Download dependencies
RUN mvn dependency:go-offline -B

# Copy source code
COPY src ./src

# Build the application
RUN mvn clean package -DskipTests -B

# ── Runtime stage with OpenTelemetry agent ─────────────────────────────
FROM eclipse-temurin:17-jre-alpine

# Install required packages
RUN apk add --no-cache \
    curl \
    bash \
    tzdata

# Set timezone
ENV TZ=Asia/Kolkata
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create application user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Create application directories
RUN mkdir -p /app/logs /otel && \
    chown -R appuser:appgroup /app /otel

# 📥 Download OpenTelemetry Java agent
ENV OTEL_AGENT_VERSION=2.17.1
RUN curl -sSL https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/download/v${OTEL_AGENT_VERSION}/opentelemetry-javaagent.jar \
    -o /otel/opentelemetry-javaagent.jar

# Set working directory
WORKDIR /app

# Copy the built JAR from builder stage
COPY --from=builder /app/target/*.jar app.jar

# Change ownership of the JAR file
RUN chown appuser:appgroup app.jar

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8108

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8108/api/bre/v1/bre/original/health || exit 1

# Environment variables
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseContainerSupport" \
    OTEL_AGENT_PATH="/otel/opentelemetry-javaagent.jar" \
    SPRING_PROFILES_ACTIVE=docker \
    SERVER_PORT=8108 \
    SPRING_DATASOURCE_URL=************************************** \
    SPRING_DATASOURCE_USERNAME=postgres \
    SPRING_DATASOURCE_PASSWORD=postgres \
    # OpenTelemetry
    OTEL_SERVICE_NAME=${OTEL_SERVICE_NAME:-bre} \
    OTEL_TRACES_EXPORTER=${OTEL_TRACES_EXPORTER:-otlp} \
    OTEL_METRICS_EXPORTER=${OTEL_METRICS_EXPORTER:-otlp} \
    OTEL_LOGS_EXPORTER=${OTEL_LOGS_EXPORTER:-otlp} \
    OTEL_EXPORTER_OTLP_ENDPOINT=${OTEL_EXPORTER_OTLP_ENDPOINT:-http://otel-collector.observability:4317} \
    OTEL_EXPORTER_OTLP_PROTOCOL=${OTEL_EXPORTER_OTLP_PROTOCOL:-grpc} \
    OTEL_RESOURCE_ATTRIBUTES=${OTEL_RESOURCE_ATTRIBUTES:-service.name=bre,service.version=1.0.0,deployment.environment=docker}

# Start the application with OpenTelemetry Java agent
ENTRYPOINT ["sh", "-c", "java -javaagent:$OTEL_AGENT_PATH $JAVA_OPTS -jar app.jar"]
