package com.loan.mockbre.service;

import com.loan.mockbre.enums.DecisionType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for AdminConfigurationService.
 */
class AdminConfigurationServiceTest {

    private AdminConfigurationService adminConfigService;

    @BeforeEach
    void setUp() {
        adminConfigService = new AdminConfigurationService();
    }

    @Test
    void testSetDecisionOverride_WithValidData_ShouldStore() {
        // Given
        String applicationId = "APP123";
        DecisionType decision = DecisionType.APPROVE;

        // When
        adminConfigService.setDecisionOverride(applicationId, decision);

        // Then
        DecisionType retrieved = adminConfigService.getDecisionOverride(applicationId);
        assertEquals(decision, retrieved);
    }

    @Test
    void testSetDecisionOverride_WithNullDecision_ShouldRemove() {
        // Given
        String applicationId = "APP123";
        adminConfigService.setDecisionOverride(applicationId, DecisionType.APPROVE);

        // When
        adminConfigService.setDecisionOverride(applicationId, null);

        // Then
        DecisionType retrieved = adminConfigService.getDecisionOverride(applicationId);
        assertNull(retrieved);
    }

    @Test
    void testSetDecisionOverride_WithNullApplicationId_ShouldThrowException() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            adminConfigService.setDecisionOverride(null, DecisionType.APPROVE);
        });
    }

    @Test
    void testSetDecisionOverride_WithEmptyApplicationId_ShouldThrowException() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            adminConfigService.setDecisionOverride("", DecisionType.APPROVE);
        });
    }

    @Test
    void testGetDecisionOverride_WithNonExistentId_ShouldReturnNull() {
        // When
        DecisionType result = adminConfigService.getDecisionOverride("NON_EXISTENT");

        // Then
        assertNull(result);
    }

    @Test
    void testSetGlobalDecisionOverride_ShouldStore() {
        // Given
        DecisionType decision = DecisionType.REJECT;

        // When
        adminConfigService.setGlobalDecisionOverride(decision);

        // Then
        DecisionType retrieved = adminConfigService.getGlobalDecisionOverride();
        assertEquals(decision, retrieved);
    }

    @Test
    void testSetGlobalDecisionOverride_WithNull_ShouldRemove() {
        // Given
        adminConfigService.setGlobalDecisionOverride(DecisionType.APPROVE);

        // When
        adminConfigService.setGlobalDecisionOverride(null);

        // Then
        DecisionType retrieved = adminConfigService.getGlobalDecisionOverride();
        assertNull(retrieved);
    }

    @Test
    void testSetConfig_WithValidData_ShouldStore() {
        // Given
        String key = "testKey";
        String value = "testValue";

        // When
        adminConfigService.setConfig(key, value);

        // Then
        Object retrieved = adminConfigService.getConfig(key);
        assertEquals(value, retrieved);
    }

    @Test
    void testSetConfig_WithNullValue_ShouldRemove() {
        // Given
        String key = "testKey";
        adminConfigService.setConfig(key, "testValue");

        // When
        adminConfigService.setConfig(key, null);

        // Then
        Object retrieved = adminConfigService.getConfig(key);
        assertNull(retrieved);
    }

    @Test
    void testSetConfig_WithNullKey_ShouldThrowException() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            adminConfigService.setConfig(null, "value");
        });
    }

    @Test
    void testSetConfig_WithEmptyKey_ShouldThrowException() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            adminConfigService.setConfig("", "value");
        });
    }

    @Test
    void testGetAllDecisionOverrides_ShouldReturnCopy() {
        // Given
        adminConfigService.setDecisionOverride("APP1", DecisionType.APPROVE);
        adminConfigService.setDecisionOverride("APP2", DecisionType.REJECT);

        // When
        Map<String, DecisionType> overrides = adminConfigService.getAllDecisionOverrides();

        // Then
        assertEquals(2, overrides.size());
        assertEquals(DecisionType.APPROVE, overrides.get("APP1"));
        assertEquals(DecisionType.REJECT, overrides.get("APP2"));

        // Verify it's a copy (modifications shouldn't affect original)
        assertThrows(UnsupportedOperationException.class, () -> {
            overrides.put("APP3", DecisionType.REFER);
        });
    }

    @Test
    void testGetAllConfig_ShouldReturnCopy() {
        // Given
        adminConfigService.setConfig("key1", "value1");
        adminConfigService.setConfig("key2", 123);

        // When
        Map<String, Object> config = adminConfigService.getAllConfig();

        // Then
        assertEquals(2, config.size());
        assertEquals("value1", config.get("key1"));
        assertEquals(123, config.get("key2"));

        // Verify it's a copy
        assertThrows(UnsupportedOperationException.class, () -> {
            config.put("key3", "value3");
        });
    }

    @Test
    void testClearAll_ShouldRemoveAllData() {
        // Given
        adminConfigService.setDecisionOverride("APP1", DecisionType.APPROVE);
        adminConfigService.setGlobalDecisionOverride(DecisionType.REJECT);
        adminConfigService.setConfig("key1", "value1");

        // When
        adminConfigService.clearAll();

        // Then
        assertNull(adminConfigService.getDecisionOverride("APP1"));
        assertNull(adminConfigService.getGlobalDecisionOverride());
        assertNull(adminConfigService.getConfig("key1"));
        assertTrue(adminConfigService.getAllDecisionOverrides().isEmpty());
        assertTrue(adminConfigService.getAllConfig().isEmpty());
    }

    @Test
    void testGetConfigSummary_ShouldReturnCorrectCounts() {
        // Given
        adminConfigService.setDecisionOverride("APP1", DecisionType.APPROVE);
        adminConfigService.setDecisionOverride("APP2", DecisionType.REJECT);
        adminConfigService.setGlobalDecisionOverride(DecisionType.REFER);
        adminConfigService.setConfig("key1", "value1");

        // When
        Map<String, Object> summary = adminConfigService.getConfigSummary();

        // Then
        assertEquals(2, summary.get("decisionOverridesCount"));
        assertEquals(2, summary.get("globalConfigCount")); // globalDecision + key1
        assertEquals(true, summary.get("hasGlobalDecisionOverride"));
    }

    @Test
    void testGetConfigSummary_WithEmptyState_ShouldReturnZeros() {
        // When
        Map<String, Object> summary = adminConfigService.getConfigSummary();

        // Then
        assertEquals(0, summary.get("decisionOverridesCount"));
        assertEquals(0, summary.get("globalConfigCount"));
        assertEquals(false, summary.get("hasGlobalDecisionOverride"));
    }

    @Test
    void testMultipleOperations_ShouldMaintainConsistency() {
        // Given & When
        adminConfigService.setDecisionOverride("APP1", DecisionType.APPROVE);
        adminConfigService.setDecisionOverride("APP2", DecisionType.REJECT);
        adminConfigService.setDecisionOverride("APP1", DecisionType.REFER); // Update existing
        adminConfigService.setDecisionOverride("APP3", DecisionType.APPROVE);
        adminConfigService.setDecisionOverride("APP2", null); // Remove

        // Then
        Map<String, DecisionType> overrides = adminConfigService.getAllDecisionOverrides();
        assertEquals(2, overrides.size());
        assertEquals(DecisionType.REFER, overrides.get("APP1"));
        assertEquals(DecisionType.APPROVE, overrides.get("APP3"));
        assertNull(overrides.get("APP2"));
    }
}
