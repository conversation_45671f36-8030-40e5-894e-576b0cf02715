server:
  port: 0

spring:
  application:
    name: mock-bre-service-test

# Mock BRE Configuration for testing
mock-bre:
  decision-rules:
    approve-cibil-threshold: 700
    approve-income-threshold: 200000
    reject-cibil-threshold: 650
    reject-income-threshold: 100000
  
  simulation:
    error-pan-prefix: "ERR"
    delay-dob: "1990-01-01"
    delay-seconds: 1  # Reduced for testing
  
  admin:
    max-request-history: 50

# Logging Configuration
logging:
  level:
    com.loan.mockbre: DEBUG
    org.springframework.web: INFO
