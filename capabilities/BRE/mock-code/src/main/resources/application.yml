server:
  port: ${SERVER_PORT:8208}
  servlet:
    context-path: /api/bre/internal

spring:
  application:
    name: mock-bre-service

  jackson:
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false

# Mock BRE Configuration
mock-bre:
  decision-rules:
    approve-cibil-threshold: 700
    approve-income-threshold: 200000
    reject-cibil-threshold: 650
    reject-income-threshold: 100000
  
  simulation:
    error-pan-prefix: "ERR"
    delay-dob: "1990-01-01"
    delay-seconds: 5
  
  admin:
    max-request-history: 100

# Logging Configuration
logging:
  level:
    com.loan.mockbre: INFO
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: logs/mock-bre-service.log

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# SpringDoc OpenAPI Configuration
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    try-it-out-enabled: true
    operations-sorter: method
    tags-sorter: alpha
    disable-swagger-default-url: true
  show-actuator: false
  use-management-port: false
  writer-with-default-pretty-printer: true


