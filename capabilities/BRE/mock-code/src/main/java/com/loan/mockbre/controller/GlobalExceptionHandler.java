package com.loan.mockbre.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Global exception handler for Mock BRE service.
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Map<String, Object>> handleValidationException(
            MethodArgumentNotValidException ex, WebRequest request) {
        
        String errorMessage = ex.getBindingResult()
            .getFieldErrors()
            .stream()
            .map(FieldError::getDefaultMessage)
            .collect(Collectors.joining("; "));
        
        logger.error("Validation error: {}", errorMessage);
        
        Map<String, Object> errorResponse = Map.of(
            "error", "VALIDATION_ERROR",
            "message", errorMessage,
            "timestamp", LocalDateTime.now(),
            "path", request.getDescription(false)
        );
        
        return ResponseEntity.badRequest().body(errorResponse);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Map<String, Object>> handleIllegalArgumentException(
            IllegalArgumentException ex, WebRequest request) {
        
        logger.error("Illegal argument error: {}", ex.getMessage());
        
        Map<String, Object> errorResponse = Map.of(
            "error", "INVALID_ARGUMENT",
            "message", ex.getMessage(),
            "timestamp", LocalDateTime.now(),
            "path", request.getDescription(false)
        );
        
        return ResponseEntity.badRequest().body(errorResponse);
    }

    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<Map<String, Object>> handleRuntimeException(
            RuntimeException ex, WebRequest request) {
        
        // Check if this is a simulated error
        if (ex.getMessage() != null && ex.getMessage().contains("Simulated")) {
            logger.warn("Simulated error triggered: {}", ex.getMessage());
            
            Map<String, Object> errorResponse = Map.of(
                "error", "SIMULATED_ERROR",
                "message", "Mock BRE service error simulation",
                "timestamp", LocalDateTime.now(),
                "path", request.getDescription(false)
            );
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
        
        logger.error("Runtime error: {}", ex.getMessage(), ex);
        
        Map<String, Object> errorResponse = Map.of(
            "error", "RUNTIME_ERROR",
            "message", ex.getMessage(),
            "timestamp", LocalDateTime.now(),
            "path", request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleGenericException(
            Exception ex, WebRequest request) {
        
        logger.error("Unexpected error: {}", ex.getMessage(), ex);
        
        Map<String, Object> errorResponse = Map.of(
            "error", "INTERNAL_SERVER_ERROR",
            "message", "An unexpected error occurred",
            "timestamp", LocalDateTime.now(),
            "path", request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
}
