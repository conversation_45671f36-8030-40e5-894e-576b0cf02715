package com.loan.mockbre.dto;

import com.loan.mockbre.enums.DecisionType;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO for Mock BRE response.
 */
public class MockBREResponseDTO {

    private String applicationId;
    private DecisionType decision;
    private String eligibleFOIR;
    private BigDecimal eligibleAmount;
    private List<DecisionResultDTO> decisionResult;
    private LocalDateTime timestamp;
    private String traceId;

    // Constructors
    public MockBREResponseDTO() {
        this.timestamp = LocalDateTime.now();
    }

    // Getters and Setters
    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public DecisionType getDecision() {
        return decision;
    }

    public void setDecision(DecisionType decision) {
        this.decision = decision;
    }

    public String getEligibleFOIR() {
        return eligibleFOIR;
    }

    public void setEligibleFOIR(String eligibleFOIR) {
        this.eligibleFOIR = eligibleFOIR;
    }

    public BigDecimal getEligibleAmount() {
        return eligibleAmount;
    }

    public void setEligibleAmount(BigDecimal eligibleAmount) {
        this.eligibleAmount = eligibleAmount;
    }

    public List<DecisionResultDTO> getDecisionResult() {
        return decisionResult;
    }

    public void setDecisionResult(List<DecisionResultDTO> decisionResult) {
        this.decisionResult = decisionResult;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public static class DecisionResultDTO {
        private String ruleId;
        private DecisionType decisionResult;
        private String reasonText;

        // Constructors
        public DecisionResultDTO() {}

        public DecisionResultDTO(String ruleId, DecisionType decisionResult, String reasonText) {
            this.ruleId = ruleId;
            this.decisionResult = decisionResult;
            this.reasonText = reasonText;
        }

        // Getters and Setters
        public String getRuleId() {
            return ruleId;
        }

        public void setRuleId(String ruleId) {
            this.ruleId = ruleId;
        }

        public DecisionType getDecisionResult() {
            return decisionResult;
        }

        public void setDecisionResult(DecisionType decisionResult) {
            this.decisionResult = decisionResult;
        }

        public String getReasonText() {
            return reasonText;
        }

        public void setReasonText(String reasonText) {
            this.reasonText = reasonText;
        }
    }
}
