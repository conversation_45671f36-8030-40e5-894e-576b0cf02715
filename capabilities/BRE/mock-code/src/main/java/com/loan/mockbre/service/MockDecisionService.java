package com.loan.mockbre.service;

import com.loan.mockbre.dto.MockBRERequestDTO;
import com.loan.mockbre.dto.MockBREResponseDTO;
import com.loan.mockbre.enums.DecisionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Service for simulating BRE decision logic.
 */
@Service
public class MockDecisionService {

    private static final Logger logger = LoggerFactory.getLogger(MockDecisionService.class);

    @Value("${mock-bre.decision-rules.approve-cibil-threshold:700}")
    private int approveCibilThreshold;

    @Value("${mock-bre.decision-rules.approve-income-threshold:200000}")
    private BigDecimal approveIncomeThreshold;

    @Value("${mock-bre.decision-rules.reject-cibil-threshold:650}")
    private int rejectCibilThreshold;

    @Value("${mock-bre.decision-rules.reject-income-threshold:100000}")
    private BigDecimal rejectIncomeThreshold;

    @Value("${mock-bre.simulation.error-pan-prefix:ERR}")
    private String errorPanPrefix;

    @Value("${mock-bre.simulation.delay-dob:1990-01-01}")
    private String delayDob;

    @Value("${mock-bre.simulation.delay-seconds:5}")
    private int delaySeconds;

    private final AdminConfigurationService adminConfigService;

    public MockDecisionService(AdminConfigurationService adminConfigService) {
        this.adminConfigService = adminConfigService;
    }

    /**
     * Evaluate loan eligibility based on mock rules.
     */
    public MockBREResponseDTO evaluateEligibility(MockBRERequestDTO request, String traceId) {
        String applicationId = request.getHeader().getApplicationId();
        
        try {
            MDC.put("traceId", traceId);
            
            logger.info("Starting mock BRE evaluation for application: {}", applicationId);

            // Check for admin overrides first
            DecisionType overrideDecision = adminConfigService.getDecisionOverride(applicationId);
            if (overrideDecision != null) {
                logger.info("Using admin override decision: {} for application: {}", overrideDecision, applicationId);
                return createResponse(request, overrideDecision, traceId, "Admin Override");
            }

            // Check for simulation scenarios
            checkSimulationScenarios(request);

            // Apply decision logic
            DecisionResult decisionResult = applyDecisionLogic(request);
            
            logger.info("Mock BRE evaluation completed - Decision: {}, Reason: {}", 
                decisionResult.decision, decisionResult.reason);

            return createResponse(request, decisionResult.decision, traceId, decisionResult.reason);

        } catch (Exception e) {
            logger.error("Error in mock BRE evaluation: {}", e.getMessage(), e);
            throw e;
        } finally {
            MDC.remove("traceId");
        }
    }

    /**
     * Check for simulation scenarios that trigger special behavior.
     */
    private void checkSimulationScenarios(MockBRERequestDTO request) {
        // Check for error simulation
        if (errorPanPrefix != null) {
            for (MockBRERequestDTO.ApplicantDTO applicant : request.getApplicants()) {
                if (applicant.getPan() != null && applicant.getPan().startsWith(errorPanPrefix)) {
                    logger.warn("Simulating HTTP 500 error for PAN starting with: {}", errorPanPrefix);
                    throw new RuntimeException("Simulated BRE service error");
                }
            }
        }

        // Check for delay simulation
        if (delayDob != null) {
            for (MockBRERequestDTO.ApplicantDTO applicant : request.getApplicants()) {
                if (applicant.getDateOfBirth() != null &&
                    applicant.getDateOfBirth().equals(LocalDate.parse(delayDob))) {
                    logger.info("Simulating delay of {} seconds for DOB: {}", delaySeconds, delayDob);
                    try {
                        Thread.sleep(delaySeconds * 1000L);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Delay simulation interrupted", e);
                    }
                }
            }
        }
    }

    /**
     * Apply mock decision logic based on CIBIL and income.
     */
    private DecisionResult applyDecisionLogic(MockBRERequestDTO request) {
        Integer cibil = request.getAdditionalData().getCibil();
        BigDecimal income = request.getAdditionalData().getYearlyIncome();

        if (cibil == null || income == null) {
            return new DecisionResult(DecisionType.REJECT, "Missing CIBIL or income data");
        }

        // Approve logic: CIBIL >= 700 and income >= 2L
        if (cibil >= approveCibilThreshold && income.compareTo(approveIncomeThreshold) >= 0) {
            return new DecisionResult(DecisionType.APPROVE, 
                String.format("Credit Score %d Above %d and Income %s Above %s", 
                    cibil, approveCibilThreshold, income, approveIncomeThreshold));
        }

        // Reject logic: CIBIL < 650 or income < 1L
        if (cibil < rejectCibilThreshold || income.compareTo(rejectIncomeThreshold) < 0) {
            return new DecisionResult(DecisionType.REJECT, 
                String.format("Credit Score %d Below %d or Income %s Below %s", 
                    cibil, rejectCibilThreshold, income, rejectIncomeThreshold));
        }

        // Refer for edge cases
        return new DecisionResult(DecisionType.REFER, 
            String.format("Credit Score %d and Income %s require manual review", cibil, income));
    }

    /**
     * Create mock BRE response.
     */
    private MockBREResponseDTO createResponse(MockBRERequestDTO request, DecisionType decision, 
                                            String traceId, String reason) {
        MockBREResponseDTO response = new MockBREResponseDTO();
        response.setApplicationId(request.getHeader().getApplicationId());
        response.setDecision(decision);
        response.setTraceId(traceId);

        // Set decision-specific fields
        if (decision == DecisionType.APPROVE) {
            response.setEligibleFOIR("40%");
            
            // Calculate eligible amount (80% of requested amount for simplicity)
            BigDecimal requestedAmount = request.getHeader().getLoanAmount();
            BigDecimal eligibleAmount = requestedAmount.multiply(BigDecimal.valueOf(0.8));
            response.setEligibleAmount(eligibleAmount);
        }

        // Create decision result
        MockBREResponseDTO.DecisionResultDTO decisionResult = 
            new MockBREResponseDTO.DecisionResultDTO(
                request.getHeader().getApplicationId(),
                decision,
                reason
            );
        
        response.setDecisionResult(List.of(decisionResult));

        return response;
    }

    /**
     * Internal class for decision result.
     */
    private static class DecisionResult {
        final DecisionType decision;
        final String reason;

        DecisionResult(DecisionType decision, String reason) {
            this.decision = decision;
            this.reason = reason;
        }
    }
}
