package com.loan.mockbre.controller;

import com.loan.mockbre.dto.MockBRERequestDTO;
import com.loan.mockbre.dto.MockBREResponseDTO;
import com.loan.mockbre.service.MockDecisionService;
import com.loan.mockbre.service.TraceService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

/**
 * Mock BRE Controller for loan eligibility evaluation.
 */
@RestController
@RequestMapping("/v1")
public class MockBREController {

    private static final Logger logger = LoggerFactory.getLogger(MockBREController.class);

    private final MockDecisionService mockDecisionService;
    private final TraceService traceService;

    public MockBREController(MockDecisionService mockDecisionService, TraceService traceService) {
        this.mockDecisionService = mockDecisionService;
        this.traceService = traceService;
    }

    @PostMapping("/evaluate")
    public ResponseEntity<MockBREResponseDTO> evaluateLoanEligibility(
            @RequestHeader(value = "X-Trace-ID", required = false) String traceId,
            @Valid @RequestBody MockBRERequestDTO request) {

        // Generate trace ID if not provided
        if (traceId == null || traceId.trim().isEmpty()) {
            traceId = "MOCK-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        }

        String applicationId = request.getHeader().getApplicationId();
        String endpoint = "/v1/evaluate";

        logger.info("Received mock BRE request - ApplicationId: {}, TraceId: {}", applicationId, traceId);

        try {
            // Log request
            traceService.logRequest(traceId, request, endpoint);

            // Process request
            MockBREResponseDTO response = mockDecisionService.evaluateEligibility(request, traceId);

            // Log response
            traceService.logResponse(traceId, response, endpoint);

            logger.info("Mock BRE evaluation completed - Decision: {}, TraceId: {}", 
                response.getDecision(), traceId);

            return ResponseEntity.ok()
                .header("X-Trace-ID", traceId)
                .body(response);

        } catch (Exception e) {
            // Log error
            traceService.logError(traceId, endpoint, e.getMessage(), e);
            
            logger.error("Mock BRE evaluation failed - TraceId: {}, Error: {}", traceId, e.getMessage(), e);
            
            // Re-throw to let global exception handler deal with it
            throw e;
        }
    }

    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("Mock BRE Service is healthy");
    }
}
