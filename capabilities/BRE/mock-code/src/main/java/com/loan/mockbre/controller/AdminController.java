package com.loan.mockbre.controller;

import com.loan.mockbre.enums.DecisionType;
import com.loan.mockbre.service.AdminConfigurationService;
import com.loan.mockbre.service.TraceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Admin controller for Mock BRE configuration and monitoring.
 */
@RestController
@RequestMapping("/v1/admin")
public class AdminController {

    private static final Logger logger = LoggerFactory.getLogger(AdminController.class);

    private final AdminConfigurationService adminConfigService;
    private final TraceService traceService;

    public AdminController(AdminConfigurationService adminConfigService, TraceService traceService) {
        this.adminConfigService = adminConfigService;
        this.traceService = traceService;
    }

    @GetMapping("/requests")
    public ResponseEntity<List<TraceService.RequestHistoryEntry>> getRecentRequests(
            @RequestParam(defaultValue = "50") int limit) {
        
        logger.info("Admin request: Get recent requests with limit: {}", limit);
        
        List<TraceService.RequestHistoryEntry> requests = traceService.getRecentRequests(limit);
        return ResponseEntity.ok(requests);
    }

    @GetMapping("/requests/{traceId}")
    public ResponseEntity<List<TraceService.RequestHistoryEntry>> getRequestsByTraceId(
            @PathVariable String traceId) {
        
        logger.info("Admin request: Get requests for trace ID: {}", traceId);
        
        List<TraceService.RequestHistoryEntry> requests = traceService.getRequestsByTraceId(traceId);
        return ResponseEntity.ok(requests);
    }

    @PostMapping("/config")
    public ResponseEntity<String> setConfig(@RequestBody Map<String, Object> config) {
        logger.info("Admin request: Set configuration: {}", config);
        
        try {
            // Handle decision overrides
            if (config.containsKey("applicationId") && config.containsKey("decision")) {
                String applicationId = (String) config.get("applicationId");
                String decisionStr = (String) config.get("decision");
                DecisionType decision = DecisionType.fromValue(decisionStr);
                
                adminConfigService.setDecisionOverride(applicationId, decision);
                return ResponseEntity.ok("Decision override set for application: " + applicationId);
            }
            
            // Handle global decision override
            if (config.containsKey("globalDecision")) {
                String decisionStr = (String) config.get("globalDecision");
                DecisionType decision = DecisionType.fromValue(decisionStr);
                
                adminConfigService.setGlobalDecisionOverride(decision);
                return ResponseEntity.ok("Global decision override set to: " + decision);
            }
            
            // Handle other configuration
            for (Map.Entry<String, Object> entry : config.entrySet()) {
                adminConfigService.setConfig(entry.getKey(), entry.getValue());
            }
            
            return ResponseEntity.ok("Configuration updated successfully");
            
        } catch (Exception e) {
            logger.error("Failed to set configuration: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body("Failed to set configuration: " + e.getMessage());
        }
    }

    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> getConfig() {
        logger.info("Admin request: Get configuration");
        
        Map<String, Object> config = Map.of(
            "decisionOverrides", adminConfigService.getAllDecisionOverrides(),
            "globalConfig", adminConfigService.getAllConfig(),
            "summary", adminConfigService.getConfigSummary()
        );
        
        return ResponseEntity.ok(config);
    }

    @PostMapping("/reset")
    public ResponseEntity<String> reset() {
        logger.info("Admin request: Reset service state");
        
        try {
            adminConfigService.clearAll();
            traceService.clearHistory();
            
            return ResponseEntity.ok("Service state reset successfully");
            
        } catch (Exception e) {
            logger.error("Failed to reset service state: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body("Failed to reset service state: " + e.getMessage());
        }
    }

    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getStats() {
        logger.info("Admin request: Get statistics");
        
        Map<String, Object> stats = Map.of(
            "historyStats", traceService.getHistoryStats(),
            "configSummary", adminConfigService.getConfigSummary()
        );
        
        return ResponseEntity.ok(stats);
    }

    @DeleteMapping("/config/{key}")
    public ResponseEntity<String> removeConfig(
            @PathVariable String key) {
        
        logger.info("Admin request: Remove configuration key: {}", key);
        
        try {
            adminConfigService.setConfig(key, null);
            return ResponseEntity.ok("Configuration key removed: " + key);
            
        } catch (Exception e) {
            logger.error("Failed to remove configuration key: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body("Failed to remove configuration: " + e.getMessage());
        }
    }

    @DeleteMapping("/overrides/{applicationId}")
    public ResponseEntity<String> removeDecisionOverride(
            @PathVariable String applicationId) {
        
        logger.info("Admin request: Remove decision override for application: {}", applicationId);
        
        try {
            adminConfigService.setDecisionOverride(applicationId, null);
            return ResponseEntity.ok("Decision override removed for application: " + applicationId);
            
        } catch (Exception e) {
            logger.error("Failed to remove decision override: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body("Failed to remove decision override: " + e.getMessage());
        }
    }
}
