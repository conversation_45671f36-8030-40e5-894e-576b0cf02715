package com.loan.mockbre.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;

/**
 * OpenAPI configuration for Mock BRE Service.
 */
@Configuration
public class OpenApiConfig {

    @Value("${server.port:8208}")
    private String serverPort;

    @Value("${server.servlet.context-path:/api/bre/internal}")
    private String contextPath;

    @Bean
    public OpenAPI mockBreOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Mock BRE Service API")
                        .description("Mock Business Rule Engine for Testing and Development")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Mock BRE Team")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("Internal Use")
                                .url("https://loan.com/license")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + contextPath)
                                .description("Local Development Server")
                ));
    }
}
