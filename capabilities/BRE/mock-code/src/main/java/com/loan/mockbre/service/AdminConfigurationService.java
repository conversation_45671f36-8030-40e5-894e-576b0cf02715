package com.loan.mockbre.service;

import com.loan.mockbre.enums.DecisionType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * Service for managing admin configuration and overrides.
 */
@Service
public class AdminConfigurationService {

    private static final Logger logger = LoggerFactory.getLogger(AdminConfigurationService.class);

    private final Map<String, DecisionType> decisionOverrides = new ConcurrentHashMap<>();
    private final Map<String, Object> globalConfig = new ConcurrentHashMap<>();

    /**
     * Set decision override for specific application ID.
     */
    public void setDecisionOverride(String applicationId, DecisionType decision) {
        if (applicationId == null || applicationId.trim().isEmpty()) {
            throw new IllegalArgumentException("Application ID cannot be null or empty");
        }
        
        if (decision == null) {
            decisionOverrides.remove(applicationId);
            logger.info("Removed decision override for application: {}", applicationId);
        } else {
            decisionOverrides.put(applicationId, decision);
            logger.info("Set decision override for application: {} to {}", applicationId, decision);
        }
    }

    /**
     * Get decision override for specific application ID.
     */
    public DecisionType getDecisionOverride(String applicationId) {
        return decisionOverrides.get(applicationId);
    }

    /**
     * Set global decision override (applies to all applications).
     */
    public void setGlobalDecisionOverride(DecisionType decision) {
        if (decision == null) {
            globalConfig.remove("globalDecision");
            logger.info("Removed global decision override");
        } else {
            globalConfig.put("globalDecision", decision);
            logger.info("Set global decision override to: {}", decision);
        }
    }

    /**
     * Get global decision override.
     */
    public DecisionType getGlobalDecisionOverride() {
        return (DecisionType) globalConfig.get("globalDecision");
    }

    /**
     * Set configuration value.
     */
    public void setConfig(String key, Object value) {
        if (key == null || key.trim().isEmpty()) {
            throw new IllegalArgumentException("Config key cannot be null or empty");
        }
        
        if (value == null) {
            globalConfig.remove(key);
            logger.info("Removed config: {}", key);
        } else {
            globalConfig.put(key, value);
            logger.info("Set config: {} = {}", key, value);
        }
    }

    /**
     * Get configuration value.
     */
    public Object getConfig(String key) {
        return globalConfig.get(key);
    }

    /**
     * Get all decision overrides.
     */
    public Map<String, DecisionType> getAllDecisionOverrides() {
        return Map.copyOf(decisionOverrides);
    }

    /**
     * Get all configuration.
     */
    public Map<String, Object> getAllConfig() {
        return Map.copyOf(globalConfig);
    }

    /**
     * Clear all overrides and configuration.
     */
    public void clearAll() {
        decisionOverrides.clear();
        globalConfig.clear();
        logger.info("Cleared all admin configuration and overrides");
    }

    /**
     * Get configuration summary.
     */
    public Map<String, Object> getConfigSummary() {
        Map<String, Object> summary = new ConcurrentHashMap<>();
        summary.put("decisionOverridesCount", decisionOverrides.size());
        summary.put("globalConfigCount", globalConfig.size());
        summary.put("hasGlobalDecisionOverride", globalConfig.containsKey("globalDecision"));
        return summary;
    }
}
