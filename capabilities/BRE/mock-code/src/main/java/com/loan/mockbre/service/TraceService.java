package com.loan.mockbre.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.regex.Pattern;

/**
 * Service for handling trace logging with PII masking.
 */
@Service
public class TraceService {

    private static final Logger logger = LoggerFactory.getLogger(TraceService.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${mock-bre.admin.max-request-history:100}")
    private int maxRequestHistory;

    // In-memory storage for request history
    private final Queue<RequestHistoryEntry> requestHistory = new ConcurrentLinkedQueue<>();

    // PII masking patterns
    private static final Pattern PAN_PATTERN = Pattern.compile("[A-Z]{5}[0-9]{4}[A-Z]{1}");
    private static final Pattern MOBILE_PATTERN = Pattern.compile("[6-9]\\d{9}");

    /**
     * Log request with PII masking.
     */
    public void logRequest(String traceId, Object request, String endpoint) {
        try {
            String maskedRequest = maskPIIInJson(objectMapper.writeValueAsString(request));
            
            RequestHistoryEntry entry = new RequestHistoryEntry(
                traceId,
                endpoint,
                maskedRequest,
                LocalDateTime.now(),
                "REQUEST"
            );
            
            addToHistory(entry);
            
            logger.info("Request logged - TraceId: {}, Endpoint: {}", traceId, endpoint);
            
        } catch (Exception e) {
            logger.error("Failed to log request: {}", e.getMessage(), e);
        }
    }

    /**
     * Log response with PII masking.
     */
    public void logResponse(String traceId, Object response, String endpoint) {
        try {
            String maskedResponse = maskPIIInJson(objectMapper.writeValueAsString(response));
            
            RequestHistoryEntry entry = new RequestHistoryEntry(
                traceId,
                endpoint,
                maskedResponse,
                LocalDateTime.now(),
                "RESPONSE"
            );
            
            addToHistory(entry);
            
            logger.info("Response logged - TraceId: {}", traceId);
            
        } catch (Exception e) {
            logger.error("Failed to log response: {}", e.getMessage(), e);
        }
    }

    /**
     * Log error with details.
     */
    public void logError(String traceId, String endpoint, String errorMessage, Exception exception) {
        try {
            Map<String, Object> errorDetails = new HashMap<>();
            errorDetails.put("errorMessage", errorMessage);
            errorDetails.put("exceptionType", exception != null ? exception.getClass().getSimpleName() : "Unknown");
            errorDetails.put("timestamp", LocalDateTime.now());
            
            RequestHistoryEntry entry = new RequestHistoryEntry(
                traceId,
                endpoint,
                objectMapper.writeValueAsString(errorDetails),
                LocalDateTime.now(),
                "ERROR"
            );
            
            addToHistory(entry);
            
            logger.error("Error logged - TraceId: {}, Error: {}", traceId, errorMessage, exception);
            
        } catch (Exception e) {
            logger.error("Failed to log error: {}", e.getMessage(), e);
        }
    }

    /**
     * Get recent request history.
     */
    public List<RequestHistoryEntry> getRecentRequests(int limit) {
        return requestHistory.stream()
            .sorted((a, b) -> b.timestamp.compareTo(a.timestamp))
            .limit(limit > 0 ? limit : maxRequestHistory)
            .toList();
    }

    /**
     * Get request history by trace ID.
     */
    public List<RequestHistoryEntry> getRequestsByTraceId(String traceId) {
        return requestHistory.stream()
            .filter(entry -> traceId.equals(entry.traceId))
            .sorted((a, b) -> a.timestamp.compareTo(b.timestamp))
            .toList();
    }

    /**
     * Clear request history.
     */
    public void clearHistory() {
        requestHistory.clear();
        logger.info("Request history cleared");
    }

    /**
     * Get history statistics.
     */
    public Map<String, Object> getHistoryStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalEntries", requestHistory.size());
        stats.put("maxCapacity", maxRequestHistory);
        
        Map<String, Long> typeCount = requestHistory.stream()
            .collect(java.util.stream.Collectors.groupingBy(
                entry -> entry.type,
                java.util.stream.Collectors.counting()
            ));
        stats.put("entriesByType", typeCount);
        
        return stats;
    }

    /**
     * Add entry to history with size management.
     */
    private void addToHistory(RequestHistoryEntry entry) {
        requestHistory.offer(entry);
        
        // Remove old entries if exceeding max size
        while (requestHistory.size() > maxRequestHistory) {
            requestHistory.poll();
        }
    }

    /**
     * Mask PII data in JSON string.
     */
    private String maskPIIInJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return jsonString;
        }
        
        String masked = jsonString;
        
        // Mask PAN numbers
        masked = PAN_PATTERN.matcher(masked).replaceAll(match -> 
            "\"" + match.group().substring(0, 2) + "***" + match.group().substring(5, 7) + "**\"");
        
        // Mask mobile numbers
        masked = MOBILE_PATTERN.matcher(masked).replaceAll(match -> 
            "\"***" + match.group().substring(6) + "\"");
        
        // Mask names (simple approach - mask values of firstName, lastName fields)
        masked = masked.replaceAll("(\"firstName\"\\s*:\\s*\")([^\"]+)(\")", "$1***$3");
        masked = masked.replaceAll("(\"lastName\"\\s*:\\s*\")([^\"]+)(\")", "$1***$3");
        
        return masked;
    }

    /**
     * Request history entry.
     */
    public static class RequestHistoryEntry {
        private final String traceId;
        private final String endpoint;
        private final String maskedData;
        private final LocalDateTime timestamp;
        private final String type;

        public RequestHistoryEntry(String traceId, String endpoint, String maskedData, 
                                 LocalDateTime timestamp, String type) {
            this.traceId = traceId;
            this.endpoint = endpoint;
            this.maskedData = maskedData;
            this.timestamp = timestamp;
            this.type = type;
        }

        // Getters
        public String getTraceId() { return traceId; }
        public String getEndpoint() { return endpoint; }
        public String getMaskedData() { return maskedData; }
        public LocalDateTime getTimestamp() { return timestamp; }
        public String getType() { return type; }
    }
}
