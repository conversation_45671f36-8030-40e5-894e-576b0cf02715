package com.loan.mockbre.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Simplified DTO for Mock BRE request.
 */
public class MockBRERequestDTO {

    @Valid
    @NotNull
    private HeaderDTO header;

    @Valid
    @NotNull
    private List<ApplicantDTO> applicants;

    @Valid
    @NotNull
    private AdditionalDataDTO additionalData;

    // Constructors
    public MockBRERequestDTO() {}

    // Getters and Setters
    public HeaderDTO getHeader() {
        return header;
    }

    public void setHeader(HeaderDTO header) {
        this.header = header;
    }

    public List<ApplicantDTO> getApplicants() {
        return applicants;
    }

    public void setApplicants(List<ApplicantDTO> applicants) {
        this.applicants = applicants;
    }

    public AdditionalDataDTO getAdditionalData() {
        return additionalData;
    }

    public void setAdditionalData(AdditionalDataDTO additionalData) {
        this.additionalData = additionalData;
    }

    public static class HeaderDTO {
        private String applicationId;
        private String productCode;
        private BigDecimal loanAmount;
        private Integer loanTenure;

        // Getters and Setters
        public String getApplicationId() {
            return applicationId;
        }

        public void setApplicationId(String applicationId) {
            this.applicationId = applicationId;
        }

        public String getProductCode() {
            return productCode;
        }

        public void setProductCode(String productCode) {
            this.productCode = productCode;
        }

        public BigDecimal getLoanAmount() {
            return loanAmount;
        }

        public void setLoanAmount(BigDecimal loanAmount) {
            this.loanAmount = loanAmount;
        }

        public Integer getLoanTenure() {
            return loanTenure;
        }

        public void setLoanTenure(Integer loanTenure) {
            this.loanTenure = loanTenure;
        }
    }

    public static class ApplicantDTO {
        private String firstName;
        private String lastName;
        private LocalDate dateOfBirth;
        private String pan;
        private String mobileNumber;

        // Getters and Setters
        public String getFirstName() {
            return firstName;
        }

        public void setFirstName(String firstName) {
            this.firstName = firstName;
        }

        public String getLastName() {
            return lastName;
        }

        public void setLastName(String lastName) {
            this.lastName = lastName;
        }

        public LocalDate getDateOfBirth() {
            return dateOfBirth;
        }

        public void setDateOfBirth(LocalDate dateOfBirth) {
            this.dateOfBirth = dateOfBirth;
        }

        public String getPan() {
            return pan;
        }

        public void setPan(String pan) {
            this.pan = pan;
        }

        public String getMobileNumber() {
            return mobileNumber;
        }

        public void setMobileNumber(String mobileNumber) {
            this.mobileNumber = mobileNumber;
        }
    }

    public static class AdditionalDataDTO {
        private Integer cibil;
        private BigDecimal yearlyIncome;

        // Getters and Setters
        public Integer getCibil() {
            return cibil;
        }

        public void setCibil(Integer cibil) {
            this.cibil = cibil;
        }

        public BigDecimal getYearlyIncome() {
            return yearlyIncome;
        }

        public void setYearlyIncome(BigDecimal yearlyIncome) {
            this.yearlyIncome = yearlyIncome;
        }
    }
}
