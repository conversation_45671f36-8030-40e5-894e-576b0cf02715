openapi: 3.0.3
info:
  title: Mock BRE Service API
  description: |
    Mock Business Rule Engine for Testing and Development
    
    This mock service simulates the Business Rule Engine (BRE) for loan eligibility evaluation.
    It provides configurable decision outcomes (Approve, Reject, Refer), FOIR calculations,
    and eligible loan amounts based on input conditions. Includes admin endpoints for
    configuration and request history management.
    
    **Simulation Logic:**
    - CIBIL ≥ 700 and income ≥ 200K → decision: "Approve"
    - CIBIL < 650 or income < 100K → decision: "Reject"
    - PAN starting with "ERR" → simulate HTTP 500 error
    - DOB "1990-01-01" → simulate 5s delay
    - Admin-configurable overrides supported
  version: 1.0.0
  contact:
    name: Mock BRE Team
    email: <EMAIL>
  license:
    name: Internal Use
    url: https://loan.com/license



paths:
  /v1/evaluate:
    post:
      tags:
        - Mock BRE Evaluation
      summary: Evaluate loan eligibility (Mock)
      description: |
        Mock evaluation of loan eligibility with configurable simulation logic.
        Supports error simulation, delay simulation, and admin overrides.
      operationId: evaluateLoanEligibilityMock
      parameters:
        - name: X-Trace-ID
          in: header
          description: Optional trace ID for request tracking
          required: false
          schema:
            type: string
            example: "MOCK-12345678"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MockBRERequestDTO'
            examples:
              approve_scenario:
                summary: Approve Scenario
                value:
                  header:
                    applicationId: "APP_MOCK_001"
                    productCode: "PL_STANDARD"
                    loanAmount: 300000
                    loanTenure: 24
                  applicants:
                    - personalInfo:
                        firstName: "Jane"
                        lastName: "Smith"
                        dateOfBirth: "1985-03-20"
                        gender: "FEMALE"
                        mobileNumber: "9876543210"
                        emailId: "<EMAIL>"
                        panNumber: "**********"
                      profession: "SALARIED"
                      income: 800000
                      cibilData:
                        cibilScore: 750
                        cibilReportDate: "2024-06-01"
                  additionalData:
                    hunter: "NO_MATCH"
                    posidexDedupe: "NO_DUPLICATE"
              reject_scenario:
                summary: Reject Scenario
                value:
                  header:
                    applicationId: "APP_MOCK_002"
                    productCode: "PL_STANDARD"
                    loanAmount: 500000
                    loanTenure: 36
                  applicants:
                    - personalInfo:
                        firstName: "Bob"
                        lastName: "Johnson"
                        dateOfBirth: "1992-08-10"
                        gender: "MALE"
                        mobileNumber: "9876543211"
                        emailId: "<EMAIL>"
                        panNumber: "**********"
                      profession: "SELF_EMPLOYED"
                      income: 80000
                      cibilData:
                        cibilScore: 600
                        cibilReportDate: "2024-06-01"
                  additionalData:
                    hunter: "NO_MATCH"
                    posidexDedupe: "NO_DUPLICATE"
              error_scenario:
                summary: Error Simulation Scenario
                value:
                  header:
                    applicationId: "APP_MOCK_003"
                    productCode: "PL_STANDARD"
                    loanAmount: 200000
                    loanTenure: 18
                  applicants:
                    - personalInfo:
                        firstName: "Error"
                        lastName: "Test"
                        dateOfBirth: "1988-12-05"
                        gender: "MALE"
                        mobileNumber: "9876543212"
                        emailId: "<EMAIL>"
                        panNumber: "ERR123456L"
                      profession: "SALARIED"
                      income: 600000
                      cibilData:
                        cibilScore: 720
                        cibilReportDate: "2024-06-01"
                  additionalData:
                    hunter: "NO_MATCH"
                    posidexDedupe: "NO_DUPLICATE"
      responses:
        '200':
          description: Mock loan eligibility evaluation completed successfully
          headers:
            X-Trace-ID:
              description: Trace ID for request tracking
              schema:
                type: string
                example: "MOCK-12345678"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockBREResponseDTO'
              examples:
                approved_response:
                  summary: Approved Response
                  value:
                    applicationId: "APP_MOCK_001"
                    decision: "APPROVE"
                    eligibleFOIR: "40%"
                    eligibleAmount: 240000
                    decisionResult:
                      - ruleId: "MOCK_CIBIL_CHECK"
                        decisionResult: "APPROVE"
                        reasonText: "CIBIL score 750 above threshold of 700"
                      - ruleId: "MOCK_INCOME_CHECK"
                        decisionResult: "APPROVE"
                        reasonText: "Income 800000 above threshold of 200000"
                    timestamp: "2024-06-23T10:30:00Z"
                    traceId: "MOCK-12345678"
                rejected_response:
                  summary: Rejected Response
                  value:
                    applicationId: "APP_MOCK_002"
                    decision: "REJECT"
                    eligibleFOIR: "0%"
                    eligibleAmount: 0
                    decisionResult:
                      - ruleId: "MOCK_CIBIL_CHECK"
                        decisionResult: "REJECT"
                        reasonText: "CIBIL score 600 below threshold of 650"
                      - ruleId: "MOCK_INCOME_CHECK"
                        decisionResult: "REJECT"
                        reasonText: "Income 80000 below threshold of 100000"
                    timestamp: "2024-06-23T10:30:00Z"
                    traceId: "MOCK-12345678"
        '400':
          description: Bad request - validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponseDTO'
        '500':
          description: Internal server error (simulated for testing)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponseDTO'
              example:
                errorCode: "MOCK_SIMULATION_ERROR"
                errorMessage: "Simulated error for PAN starting with ERR"
                traceId: "MOCK-12345678"
                timestamp: "2024-06-23T10:30:00Z"
                path: "/v1/evaluate"

  /v1/health:
    get:
      tags:
        - Health Check
      summary: Health check for mock BRE service
      description: Returns the health status of the mock BRE service
      operationId: mockBreHealthCheck
      responses:
        '200':
          description: Service is healthy
          content:
            text/plain:
              schema:
                type: string
                example: "Mock BRE Service is healthy"

  /v1/bre/mock/health:
    get:
      tags:
        - Health Check
      summary: Detailed health check for mock BRE service
      description: Returns detailed health status information for the mock BRE service
      operationId: mockBreDetailedHealthCheck
      responses:
        '200':
          description: Service is healthy with details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatusDTO'
              example:
                status: "UP"
                service: "BRE Mock"
                timestamp: "2024-06-23T10:30:00Z"
                version: "1.0.0"

  /v1/admin/requests:
    get:
      tags:
        - Admin Operations
      summary: Get recent request history
      description: |
        Retrieves recent request history with masked sensitive data.
        Supports filtering by limit and trace ID.
      operationId: getRecentRequests
      parameters:
        - name: limit
          in: query
          description: Maximum number of requests to return
          required: false
          schema:
            type: integer
            default: 50
            minimum: 1
            maximum: 1000
      responses:
        '200':
          description: Request history retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RequestHistoryEntryDTO'

  /v1/admin/requests/{traceId}:
    get:
      tags:
        - Admin Operations
      summary: Get requests by trace ID
      description: Retrieves all requests associated with a specific trace ID
      operationId: getRequestsByTraceId
      parameters:
        - name: traceId
          in: path
          description: Trace ID to search for
          required: true
          schema:
            type: string
            example: "MOCK-12345678"
      responses:
        '200':
          description: Requests retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RequestHistoryEntryDTO'

  /v1/admin/config:
    get:
      tags:
        - Admin Operations
      summary: Get current configuration
      description: Retrieves current mock service configuration including decision overrides
      operationId: getConfiguration
      responses:
        '200':
          description: Configuration retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigurationDTO'
    
    post:
      tags:
        - Admin Operations
      summary: Update configuration
      description: |
        Updates mock service configuration including decision overrides.
        Allows forcing specific decisions for testing scenarios.
      operationId: updateConfiguration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfigUpdateRequestDTO'
            examples:
              force_approve:
                summary: Force Approve for Application
                value:
                  type: "DECISION_OVERRIDE"
                  key: "APP_MOCK_001"
                  value: "APPROVE"
                  description: "Force approve for testing"
              global_config:
                summary: Update Global Configuration
                value:
                  type: "GLOBAL_CONFIG"
                  key: "DEFAULT_DECISION"
                  value: "REFER"
                  description: "Set default decision to REFER"
      responses:
        '200':
          description: Configuration updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigUpdateResponseDTO'

  /v1/admin/reset:
    post:
      tags:
        - Admin Operations
      summary: Reset service state
      description: |
        Clears all in-memory state including request history and configuration overrides.
        Useful for resetting the mock service to initial state.
      operationId: resetServiceState
      responses:
        '200':
          description: Service state reset successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResetResponseDTO'
              example:
                success: true
                message: "Service state reset successfully"
                timestamp: "2024-06-23T10:30:00Z"
                clearedItems:
                  - "Request history"
                  - "Configuration overrides"
                  - "Decision cache"

components:
  schemas:
    MockBRERequestDTO:
      type: object
      required:
        - header
        - applicants
        - additionalData
      properties:
        header:
          $ref: '#/components/schemas/HeaderDTO'
        applicants:
          type: array
          items:
            $ref: '#/components/schemas/ApplicantDTO'
          minItems: 1
          description: List of applicants (simplified for mock)
        additionalData:
          $ref: '#/components/schemas/AdditionalDataDTO'

    HeaderDTO:
      type: object
      properties:
        applicationId:
          type: string
          description: Application identifier
          example: "APP_MOCK_001"
        productCode:
          type: string
          description: Product code
          example: "PL_STANDARD"
        loanAmount:
          type: number
          format: decimal
          description: Requested loan amount
          example: 300000
        loanTenure:
          type: integer
          description: Loan tenure in months
          example: 24

    ApplicantDTO:
      type: object
      properties:
        personalInfo:
          $ref: '#/components/schemas/PersonalInfoDTO'
        profession:
          type: string
          description: Profession type
          example: "SALARIED"
        income:
          type: number
          format: decimal
          description: Annual income
          example: 800000
        cibilData:
          $ref: '#/components/schemas/CibilDataDTO'

    PersonalInfoDTO:
      type: object
      properties:
        firstName:
          type: string
          example: "Jane"
        lastName:
          type: string
          example: "Smith"
        dateOfBirth:
          type: string
          format: date
          example: "1985-03-20"
        gender:
          type: string
          example: "FEMALE"
        mobileNumber:
          type: string
          example: "9876543210"
        emailId:
          type: string
          format: email
          example: "<EMAIL>"
        panNumber:
          type: string
          example: "**********"

    CibilDataDTO:
      type: object
      properties:
        cibilScore:
          type: integer
          minimum: 300
          maximum: 900
          description: CIBIL credit score
          example: 750
        cibilReportDate:
          type: string
          format: date
          description: CIBIL report date
          example: "2024-06-01"

    AdditionalDataDTO:
      type: object
      properties:
        hunter:
          type: string
          description: Hunter verification data
          example: "NO_MATCH"
        posidexDedupe:
          type: string
          description: Posidex deduplication data
          example: "NO_DUPLICATE"

    MockBREResponseDTO:
      type: object
      required:
        - applicationId
        - decision
        - timestamp
        - traceId
      properties:
        applicationId:
          type: string
          description: Application identifier
          example: "APP_MOCK_001"
        decision:
          $ref: '#/components/schemas/DecisionType'
        eligibleFOIR:
          type: string
          description: Eligible Fixed Obligation to Income Ratio
          example: "40%"
        eligibleAmount:
          type: number
          format: decimal
          description: Eligible loan amount
          example: 240000
        decisionResult:
          type: array
          items:
            $ref: '#/components/schemas/DecisionResultDTO'
        timestamp:
          type: string
          format: date-time
          description: Response timestamp
          example: "2024-06-23T10:30:00Z"
        traceId:
          type: string
          description: Trace identifier
          example: "MOCK-12345678"

    DecisionResultDTO:
      type: object
      properties:
        ruleId:
          type: string
          description: Mock rule identifier
          example: "MOCK_CIBIL_CHECK"
        decisionResult:
          $ref: '#/components/schemas/DecisionType'
        reasonText:
          type: string
          description: Reason for the decision
          example: "CIBIL score 750 above threshold of 700"

    RequestHistoryEntryDTO:
      type: object
      properties:
        traceId:
          type: string
          example: "MOCK-12345678"
        timestamp:
          type: string
          format: date-time
          example: "2024-06-23T10:30:00Z"
        endpoint:
          type: string
          example: "/v1/evaluate"
        applicationId:
          type: string
          example: "APP_MOCK_001"
        maskedRequest:
          type: string
          description: Masked request data
        maskedResponse:
          type: string
          description: Masked response data
        processingTimeMs:
          type: integer
          description: Processing time in milliseconds
          example: 150

    ConfigurationDTO:
      type: object
      properties:
        decisionOverrides:
          type: object
          additionalProperties:
            type: string
          description: Application-specific decision overrides
        globalConfig:
          type: object
          additionalProperties:
            type: string
          description: Global configuration settings
        summary:
          type: object
          properties:
            totalOverrides:
              type: integer
            activeRules:
              type: array
              items:
                type: string

    ConfigUpdateRequestDTO:
      type: object
      required:
        - type
        - key
        - value
      properties:
        type:
          type: string
          enum: [DECISION_OVERRIDE, GLOBAL_CONFIG]
          description: Type of configuration update
        key:
          type: string
          description: Configuration key
          example: "APP_MOCK_001"
        value:
          type: string
          description: Configuration value
          example: "APPROVE"
        description:
          type: string
          description: Optional description
          example: "Force approve for testing"

    ConfigUpdateResponseDTO:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Configuration updated successfully"
        updatedKey:
          type: string
          example: "APP_MOCK_001"
        previousValue:
          type: string
          example: "REJECT"
        newValue:
          type: string
          example: "APPROVE"

    ResetResponseDTO:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Service state reset successfully"
        timestamp:
          type: string
          format: date-time
          example: "2024-06-23T10:30:00Z"
        clearedItems:
          type: array
          items:
            type: string
          example: ["Request history", "Configuration overrides"]

    ErrorResponseDTO:
      type: object
      required:
        - errorCode
        - errorMessage
        - traceId
        - timestamp
      properties:
        errorCode:
          type: string
          description: Error code identifier
          example: "MOCK_SIMULATION_ERROR"
        errorMessage:
          type: string
          description: Human-readable error message
          example: "Simulated error for testing purposes"
        traceId:
          type: string
          description: Trace identifier
          example: "MOCK-12345678"
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
          example: "2024-06-23T10:30:00Z"
        path:
          type: string
          description: Request path that caused the error
          example: "/v1/evaluate"

    HealthStatusDTO:
      type: object
      required:
        - status
        - service
        - timestamp
        - version
      properties:
        status:
          type: string
          description: Service status
          example: "UP"
        service:
          type: string
          description: Service name
          example: "BRE Mock"
        timestamp:
          type: string
          format: date-time
          description: Health check timestamp
          example: "2024-06-23T10:30:00Z"
        version:
          type: string
          description: Service version
          example: "1.0.0"

    # Enums
    DecisionType:
      type: string
      enum: [APPROVE, REJECT, REFER]
      description: Mock loan decision outcome
      example: "APPROVE"

tags:
  - name: Mock BRE Evaluation
    description: Mock loan eligibility evaluation operations
  - name: Health Check
    description: Service health monitoring operations
  - name: Admin Operations
    description: Administrative operations for mock service management
