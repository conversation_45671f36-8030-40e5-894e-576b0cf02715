Business Flow

Phase 1: Initiate the BRE Request

    Trigger:
        - Starts when the loan application system sends a request for loan eligibility decision.

    Mandatory Inputs:
        - <PERSON><PERSON>er personal details (FNAME, LNAME, DOB, SEX, MOBILE_NO, PAN, NAME_ON_PAN, DOB_ON_PAN)
        - NSDL data (NSDLNAMEMATCHPERCENTAGE, PANSTATUS)
        - Address details (ADDRESS1, ADDRESS2, ZIP<PERSON><PERSON>, COUNTRY, ADDRESSTYPE)
        - Loan details (APPLICATIONID, PRODUCT_CODE, LOAN_AMOUNT, loanTenure)
        - Additional info (hunter, posidexdedupe, bqsmatch, companyName, constitution, eduQualification, relativeOfEEmployee, profession, noOfYrsInprofession, politicallyExposer, adharavailable, cibil, YEARLYIncome)

    Request Format:
        - HEADER with loan amount, application ID, and product code
        - APPLICANTS list with identity, address, and NSDL/PAN info
        - ADDITIONALDATA including risk flags, company info, income, and CIBIL

    API Call:
        - Securely send prepared JSON payload to BRE Engine endpoint

    Validations:
        - Validate presence and format of mandatory fields
        - Ensure NSDL name match and PAN data are consistent
        - Validate enum fields and income/cibil thresholds

    System Behavior:
        - If validation fails:
            - Log issue, halt processing, return error
        - If validation succeeds:
            - Prepare API payload and call BRE engine

Phase 2: Analyze BRE Response

    API Response:
        - applicant: Decision-relevant profile data
        - feeDetails: Processing charge metadata
        - DecisionResult: Decision rules with explanations (Approve/Reject/Refer)
        - Decision: Final outcome, FOIR and Eligible Amount

    Response Validations:
        - decision must be present
        - eligibleFOIR and eligibleFOAMOUNT must not be null
        - Status must be "Approve" to proceed

    Failure Conditions:
        - If decision is missing, or equals "Reject"/"Refer", or FOIR/Amount is null:
            - Log failure, stop process, show fallback:
              "We are unable to proceed with your loan application right now. Please try again later."

    Success Conditions:
        - Log the success
        - Forward enriched decision to the requesting system
        - Continue the loan processing journey

    BRE API Error Handling:
        - Retry up to 3 times if API is unreachable or fails
        - If all retries fail, show fallback:
          "We are unable to proceed with your loan application right now. Please try again later."
