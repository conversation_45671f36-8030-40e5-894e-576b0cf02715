BRE Mock API Service – Specification
====================================

Overview
--------
A standalone Spring Boot application simulating the Business Rule Engine (BRE) for loan eligibility evaluation. This mock service enables development and testing of loan decisioning logic without requiring live BRE integration, offering configurable decision outputs (Approve, Reject, Refer), FOIR, and eligible loan amounts based on input conditions.

Purpose
-------
Simulate BRE decision logic for loan application evaluations
Allow frontend/backend teams to test all decision outcomes
Provide detailed responses mimicking real BRE payloads
Support custom error, delay, and decision scenarios via admin APIs

Technical Requirements
----------------------
Spring Boot version: 3.2.3 (or compatible)
Java version: 21
Server Port: 8083
OpenAPI/Swagger documentation included
Optional: Basic Authentication
In-memory data store for request history
Logging with masking of personal/sensitive data
Unique X-Trace-ID on all requests/responses

Implementation Details
----------------------
DTOs for request/response structures
Enums for decision outcomes and professions
Controllers and services for core functionality
MockDecisionService: Implements simulation logic
AdminConfigurationService: Supports override rules
TraceService: Handles logging with masking
Sample application.yml
Swagger configuration
README covering setup, configuration, test samples

API Endpoints
-------------
1. Loan Eligibility Decision API
   - Path: /api/bre/internal/v1/evaluate
   - Method: POST
   - Request Body: Full applicant and loan metadata
   - Response Body:
     {
       "applicationID": "APP_1234",
       "decision": "Approve",
       "eligibleFOIR": "10%",
       "eligibleFOAMOUNT": "200000",
       "decisionResult": [
         { "ruleID": "APP1234", "decisionResult": "Approve", "reasonText": "Credit Score Above 650" }
       ],
       "timestamp": "2025-06-04T12:00:00Z",
       "traceId": "TRACE123456"
     }

Simulated Behavior Logic
-------------------------
CIBIL ≥ 700 and income ≥ 2L → decision: "Approve"
CIBIL < 650 or income < 1L  → decision: "Reject"
If PAN starts with "ERR"    → simulate HTTP 500 error
If DOB is "01-01-1990"      → simulate 5s delay
Admin-configurable overrides supported

Security
--------
Mask PAN, name, DOB, mobile in logs
Include "X-Trace-ID" in header and logs
Optional: Basic Auth for all endpoints

Admin & Configuration Interface
-------------------------------
GET  /api/bre/internal/v1/admin/requests       → Recent request logs (masked)
POST /api/bre/internal/v1/admin/config         → Set response overrides (e.g., force Approve)
POST /api/bre/internal/v1/admin/reset          → Clear in-memory state/history

Service Layer Components
------------------------
MockDecisionService: Simulates decision logic
AdminConfigurationService: Allows override rules (e.g., force reject)
TraceService: Masks logs and injects trace ID

Testing Strategy
----------------
Use Swagger/Postman to test:
  - Approve flows (valid income + CIBIL)
  - Reject flows (low income/CIBIL)
  - Refer flows (edge cases)
  - Errors (PAN = "ERRxxxxx")
  - Delays (DOB = "01-01-1990")
Validate response fields and trace ID
Test fallback and retry logic

README Deliverables
-------------------
Setup steps to run the service
Sample request/response payloads
Instructions to use admin endpoints
Logging and error handling overview
Swagger UI URL for manual testing

Extensibility Suggestions
-------------------------
Add rule-based engine integration (Drools, Easy Rules)
Support for multiple applicants in a single request
Externalize delay/error logic using YAML or DB
Add scoring explanation or audit trail