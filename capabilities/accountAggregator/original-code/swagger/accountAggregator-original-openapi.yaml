openapi: 3.0.3
info:
  title: Account Aggregator Service API
  description: |
    Account Aggregator Service handles the complete flow of user consent initiation for financial data access,
    consent confirmation and status tracking, financial data fetching and storage, and data retention compliance.
    
    This service integrates with Account Aggregator platforms to provide secure access to user's financial data
    with proper consent management and privacy compliance.
  version: 1.0.0
  contact:
    name: Account Aggregator Team
    email: <EMAIL>

servers:
  - url: http://localhost:8106/api/account-aggregator
    description: Development server
  - url: https://api.accountaggregator.com/api/account-aggregator
    description: Production server

paths:
  # Health Check Endpoints
  /v1/account-aggregator/original/health:
    get:
      tags:
        - Health Check
      summary: Health check endpoint
      description: Returns the health status of the Account Aggregator service
      operationId: healthCheck
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
              example:
                status: "UP"
                service: "Account Aggregator Original"
                timestamp: "2024-01-15T10:30:00"
                version: "1.0.0"
        '500':
          description: Service is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Consent Management Endpoints
  /consents/initiate:
    post:
      tags:
        - Consent Management
      summary: Initiate user consent for financial data access
      description: Phase 1 - Initiate user consent request for accessing financial data from various FIPs
      operationId: initiateConsent
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConsentRequestDto'
            example:
              customerId: "CUST12345"
              fiuId: "FIU001"
              consentExpiry: "2024-12-31T23:59:59"
              dataLifeMonths: 3
              dataAccessFrequency: "DAILY"
              dataType: "TRANSACTIONS"
              fiType: "DEPOSIT"
              purposeCode: "101"
              purposeText: "Personal Finance Management"
              purposeCategory: "PERSONAL_FINANCE"
              dataRangeFrom: "2024-01-01T00:00:00"
              dataRangeTo: "2024-12-31T23:59:59"
              mobileNumber: "+************"
              email: "<EMAIL>"
      responses:
        '201':
          description: Consent initiation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConsentResponseDto'
              example:
                transactionId: "TXN123456789"
                consentHandle: "CH1234567890"
                consentId: "consent-abc123xyz"
                status: "PENDING"
                timestamp: "2024-01-15T10:30:00"
                redirectUrl: "https://consent.anumati.co.in/authorize?handle=CH1234567890"
                success: true
                message: "Consent request processed successfully"
        '400':
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /consents/confirm:
    post:
      tags:
        - Consent Management
      summary: Confirm consent (webhook endpoint)
      description: Phase 2 - Webhook endpoint to confirm user consent after user authorization
      operationId: confirmConsent
      parameters:
        - name: consentHandle
          in: query
          required: true
          schema:
            type: string
          description: Consent handle received from consent initiation
          example: "CH1234567890"
      responses:
        '200':
          description: Consent confirmation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConsentResponseDto'
        '400':
          description: Invalid consent handle
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Consent handle not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /consents/status/{consentId}:
    get:
      tags:
        - Consent Management
      summary: Get consent status
      description: Phase 3 - Check the current status of a consent request
      operationId: getConsentStatus
      parameters:
        - name: consentId
          in: path
          required: true
          schema:
            type: string
          description: Unique consent identifier
          example: "consent-abc123xyz"
      responses:
        '200':
          description: Consent status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConsentResponseDto'
        '404':
          description: Consent not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /consents/status/initiate:
    post:
      tags:
        - Consent Management
      summary: Initiate consent status check
      description: Phase 3.5 - Initiate consent status check as per AA specification
      operationId: initiateConsentStatus
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConsentStatusRequestDto'
            example:
              ver: "1.0"
              timestamp: "2024-01-15T10:30:00Z"
              txnid: "TXN123456789"
              consentId: "consent-abc123xyz"
      responses:
        '200':
          description: Consent status initiation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConsentStatusResponseDto'
        '400':
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Consent not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /consents/customer/{customerId}:
    get:
      tags:
        - Consent Management
      summary: Get consents by customer ID
      description: Retrieve all consent requests for a specific customer
      operationId: getCustomerConsents
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
          description: Unique customer identifier
          example: "CUST12345"
      responses:
        '200':
          description: Customer consents retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConsentRequest'
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Data Management Endpoints
  /data/fetch:
    post:
      tags:
        - Data Management
      summary: Request user's financial data
      description: Phase 4 - Request and fetch user's financial data using confirmed consent
      operationId: fetchFinancialData
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DataFetchRequestDto'
            example:
              consentId: "consent-abc123xyz"
              transactionId: "TXN123456789"
              forceRefresh: false
      responses:
        '201':
          description: Financial data fetch initiated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataFetchResponseDto'
        '400':
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Consent not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /data/session/{sessionId}:
    get:
      tags:
        - Data Management
      summary: Get financial data by session ID
      description: Phase 5 - Retrieve financial data using session ID from data fetch response
      operationId: getFinancialDataBySession
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
          description: Unique session identifier from data fetch response
          example: "SESSION123456789"
      responses:
        '200':
          description: Financial data retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AADataFetchResponseDto'
        '404':
          description: Session not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /data/consent/{consentId}:
    get:
      tags:
        - Data Management
      summary: Get financial data by consent ID
      description: Retrieve all financial data records for a specific consent
      operationId: getFinancialDataByConsent
      parameters:
        - name: consentId
          in: path
          required: true
          schema:
            type: string
          description: Unique consent identifier
          example: "consent-abc123xyz"
      responses:
        '200':
          description: Financial data retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FinancialData'
        '404':
          description: Consent not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Customer Management Endpoints
  /customers:
    get:
      tags:
        - Customer Management
      summary: Get all customers
      description: Retrieve all registered customers
      operationId: getAllCustomers
      responses:
        '200':
          description: Customers retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Customer'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /customers/{customerId}:
    get:
      tags:
        - Customer Management
      summary: Get customer by ID
      description: Retrieve a specific customer by their unique identifier
      operationId: getCustomerById
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
          description: Unique customer identifier
          example: "CUST12345"
      responses:
        '200':
          description: Customer retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Customer'
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    # Health Response Schema
    HealthResponse:
      type: object
      properties:
        status:
          type: string
          example: "UP"
        service:
          type: string
          example: "Account Aggregator Original"
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00"
        version:
          type: string
          example: "1.0.0"
      required:
        - status
        - service
        - timestamp
        - version

    # Error Response Schema
    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        errorCode:
          type: string
          example: "VALIDATION_ERROR"
        message:
          type: string
          example: "Invalid request data"
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00"
      required:
        - success
        - message

    # Consent Request DTO
    ConsentRequestDto:
      type: object
      properties:
        customerId:
          type: string
          description: Unique customer identifier
          maxLength: 50
          example: "CUST12345"
        fiuId:
          type: string
          description: Financial Information User identifier
          maxLength: 50
          example: "FIU001"
        consentStart:
          type: string
          format: date-time
          description: Consent start date and time
          example: "2024-01-15T10:30:00"
        consentExpiry:
          type: string
          format: date-time
          description: Consent expiry date and time
          example: "2024-12-31T23:59:59"
        dataLifeMonths:
          type: integer
          description: Data retention period in months
          minimum: 1
          maximum: 60
          example: 3
        dataAccessFrequency:
          type: string
          enum: ["DAILY", "WEEKLY", "MONTHLY", "YEARLY", "ONE_TIME"]
          description: Frequency of data access
          example: "DAILY"
        dataType:
          type: string
          enum: ["TRANSACTIONS", "PROFILE", "SUMMARY"]
          description: Type of financial data requested
          example: "TRANSACTIONS"
        fiType:
          type: string
          enum: ["DEPOSIT", "CREDIT_CARD", "INSURANCE", "MUTUAL_FUNDS", "RECURRING_DEPOSIT", "TERM_DEPOSIT"]
          description: Financial instrument type
          example: "DEPOSIT"
        purposeCode:
          type: string
          description: Purpose code for data access
          maxLength: 10
          example: "101"
        purposeText:
          type: string
          description: Human readable purpose description
          maxLength: 255
          example: "Personal Finance Management"
        purposeCategory:
          type: string
          enum: ["PERSONAL_FINANCE", "WEALTH_MANAGEMENT", "LOAN_APPLICATION", "INSURANCE", "TAX_FILING"]
          description: Category of purpose
          example: "PERSONAL_FINANCE"
        dataRangeFrom:
          type: string
          format: date-time
          description: Start date for data range
          example: "2024-01-01T00:00:00"
        dataRangeTo:
          type: string
          format: date-time
          description: End date for data range
          example: "2024-12-31T23:59:59"
        mobileNumber:
          type: string
          description: Customer mobile number
          maxLength: 15
          pattern: "^\\+?[1-9]\\d{1,14}$"
          example: "+************"
        email:
          type: string
          format: email
          description: Customer email address
          maxLength: 100
          example: "<EMAIL>"
      required:
        - customerId
        - fiuId
        - consentExpiry
        - dataRangeFrom
        - dataRangeTo

    # Consent Response DTO
    ConsentResponseDto:
      type: object
      properties:
        transactionId:
          type: string
          description: Unique transaction identifier
          example: "TXN123456789"
        consentHandle:
          type: string
          description: Consent handle for user authorization
          example: "CH1234567890"
        consentId:
          type: string
          description: Unique consent identifier
          example: "consent-abc123xyz"
        status:
          type: string
          enum: ["PENDING", "ACTIVE", "PAUSED", "REVOKED", "EXPIRED", "REJECTED"]
          description: Current consent status
          example: "PENDING"
        timestamp:
          type: string
          format: date-time
          description: Response timestamp
          example: "2024-01-15T10:30:00"
        redirectUrl:
          type: string
          format: uri
          description: URL for user consent authorization
          example: "https://consent.anumati.co.in/authorize?handle=CH1234567890"
        success:
          type: boolean
          description: Indicates if the operation was successful
          example: true
        message:
          type: string
          description: Response message
          example: "Consent request processed successfully"
        errorCode:
          type: string
          description: Error code if operation failed
          example: "VALIDATION_ERROR"
      required:
        - success

    # Consent Status Request DTO
    ConsentStatusRequestDto:
      type: object
      properties:
        ver:
          type: string
          description: API version
          example: "1.0"
        timestamp:
          type: string
          format: date-time
          description: Request timestamp
          example: "2024-01-15T10:30:00Z"
        txnid:
          type: string
          description: Transaction identifier
          example: "TXN123456789"
        consentId:
          type: string
          description: Consent identifier to check status
          example: "consent-abc123xyz"
      required:
        - ver
        - txnid
        - consentId

    # Consent Status Response DTO
    ConsentStatusResponseDto:
      type: object
      properties:
        ver:
          type: string
          description: API version
          example: "1.0"
        timestamp:
          type: string
          format: date-time
          description: Response timestamp
          example: "2024-01-15T10:30:00Z"
        txnid:
          type: string
          description: Transaction identifier
          example: "TXN123456789"
        consentId:
          type: string
          description: Consent identifier
          example: "consent-abc123xyz"
        sessionId:
          type: string
          description: Session identifier for data fetching
          example: "SESSION123456789"
        status:
          type: string
          enum: ["PENDING", "ACTIVE", "PAUSED", "REVOKED", "EXPIRED", "REJECTED"]
          description: Current consent status
          example: "ACTIVE"
        success:
          type: boolean
          description: Indicates if the operation was successful
          example: true
        errorMessage:
          type: string
          description: Error message if operation failed
          example: "Consent not found"
      required:
        - ver
        - txnid
        - consentId
        - success

    # Data Fetch Request DTO
    DataFetchRequestDto:
      type: object
      properties:
        consentId:
          type: string
          description: Consent identifier for data fetching
          maxLength: 100
          example: "consent-abc123xyz"
        transactionId:
          type: string
          description: Transaction identifier
          maxLength: 50
          example: "TXN123456789"
        forceRefresh:
          type: boolean
          description: Force refresh data from source
          default: false
          example: false
      required:
        - consentId

    # Data Fetch Response DTO
    DataFetchResponseDto:
      type: object
      properties:
        sessionId:
          type: string
          description: Session identifier for retrieving data
          example: "SESSION123456789"
        transactionId:
          type: string
          description: Transaction identifier
          example: "TXN123456789"
        status:
          type: string
          enum: ["PENDING", "COMPLETED", "FAILED", "PARTIAL"]
          description: Data fetch session status
          example: "COMPLETED"
        timestamp:
          type: string
          format: date-time
          description: Response timestamp
          example: "2024-01-15T10:30:00"
        accounts:
          type: array
          description: List of financial accounts
          items:
            $ref: '#/components/schemas/FinancialAccountDto'
        success:
          type: boolean
          description: Indicates if the operation was successful
          example: true
        message:
          type: string
          description: Response message
          example: "Data fetched successfully"
        errorCode:
          type: string
          description: Error code if operation failed
          example: "CONSENT_NOT_FOUND"
        dataExpiryDate:
          type: string
          format: date-time
          description: Data expiry date
          example: "2024-04-15T10:30:00"
      required:
        - success

    # Financial Account DTO
    FinancialAccountDto:
      type: object
      properties:
        accountId:
          type: string
          description: Unique account identifier
          example: "ACC123456789"
        fiType:
          type: string
          enum: ["DEPOSIT", "CREDIT_CARD", "INSURANCE", "MUTUAL_FUNDS", "RECURRING_DEPOSIT", "TERM_DEPOSIT"]
          description: Financial instrument type
          example: "DEPOSIT"
        maskedAccNumber:
          type: string
          description: Masked account number
          example: "XXXX-XXXX-1234"
        accountType:
          type: string
          description: Type of account
          example: "SAVINGS"
        balance:
          type: number
          format: decimal
          description: Account balance
          example: 50000.00
        currency:
          type: string
          description: Currency code
          default: "INR"
          example: "INR"
        bankName:
          type: string
          description: Bank or FIP name
          example: "State Bank of India"
        lastUpdated:
          type: string
          format: date-time
          description: Last updated timestamp
          example: "2024-01-15T10:30:00"

    # AA Data Fetch Response DTO (AA Specification Format)
    AADataFetchResponseDto:
      type: object
      properties:
        FIData:
          type: array
          description: Financial Information Data as per AA specification
          items:
            $ref: '#/components/schemas/AAFIDataDto'
        success:
          type: boolean
          description: Indicates if the operation was successful
          example: true
        errorMessage:
          type: string
          description: Error message if operation failed
          example: "Data not available"
      required:
        - success

    # AA FI Data DTO
    AAFIDataDto:
      type: object
      properties:
        account:
          $ref: '#/components/schemas/AAAccountDto'
        transactions:
          type: array
          description: List of transactions
          items:
            $ref: '#/components/schemas/AATransactionDto'

    # AA Account DTO
    AAAccountDto:
      type: object
      properties:
        type:
          type: string
          description: Account type
          example: "SAVINGS"
        maskedAccNumber:
          type: string
          description: Masked account number
          example: "XXXX-XXXX-1234"
        fiType:
          type: string
          enum: ["DEPOSIT", "CREDIT_CARD", "INSURANCE", "MUTUAL_FUNDS", "RECURRING_DEPOSIT", "TERM_DEPOSIT"]
          description: Financial instrument type
          example: "DEPOSIT"

    # AA Transaction DTO
    AATransactionDto:
      type: object
      properties:
        txnId:
          type: string
          description: Transaction identifier
          example: "TXN987654321"
        amount:
          type: string
          description: Transaction amount
          example: "1500.00"
        currency:
          type: string
          description: Currency code
          example: "INR"
        txnType:
          type: string
          enum: ["DEBIT", "CREDIT"]
          description: Transaction type
          example: "DEBIT"
        txnDate:
          type: string
          format: date
          description: Transaction date
          example: "2024-01-15"
        narration:
          type: string
          description: Transaction description
          example: "ATM Withdrawal"

    # Consent Request Entity
    ConsentRequest:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Unique consent request identifier
          example: 1
        customerId:
          type: string
          description: Customer identifier
          example: "CUST12345"
        consentId:
          type: string
          description: Consent identifier
          example: "consent-abc123xyz"
        consentHandle:
          type: string
          description: Consent handle
          example: "CH1234567890"
        status:
          type: string
          enum: ["PENDING", "ACTIVE", "PAUSED", "REVOKED", "EXPIRED", "REJECTED"]
          description: Consent status
          example: "ACTIVE"
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2024-01-15T10:30:00"
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2024-01-15T10:30:00"

    # Financial Data Entity
    FinancialData:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Unique financial data identifier
          example: 1
        consentId:
          type: string
          description: Associated consent identifier
          example: "consent-abc123xyz"
        accountId:
          type: string
          description: Account identifier
          example: "ACC123456789"
        dataType:
          type: string
          enum: ["TRANSACTIONS", "PROFILE", "SUMMARY"]
          description: Type of financial data
          example: "TRANSACTIONS"
        rawData:
          type: string
          description: Raw financial data (encrypted)
          example: "encrypted_data_string"
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2024-01-15T10:30:00"
        expiryDate:
          type: string
          format: date-time
          description: Data expiry date
          example: "2024-04-15T10:30:00"

    # Customer Entity
    Customer:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Unique customer identifier
          example: 1
        customerId:
          type: string
          description: Customer identifier
          example: "CUST12345"
        name:
          type: string
          description: Customer name
          example: "John Doe"
        email:
          type: string
          format: email
          description: Customer email
          example: "<EMAIL>"
        mobileNumber:
          type: string
          description: Customer mobile number
          example: "+************"
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2024-01-15T10:30:00"
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2024-01-15T10:30:00"

tags:
  - name: Health Check
    description: Health monitoring endpoints
  - name: Consent Management
    description: User consent management operations
  - name: Data Management
    description: Financial data fetching and management
  - name: Customer Management
    description: Customer information management
