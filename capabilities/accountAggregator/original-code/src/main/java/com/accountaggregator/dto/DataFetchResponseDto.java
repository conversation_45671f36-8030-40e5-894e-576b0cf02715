package com.accountaggregator.dto;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.accountaggregator.enums.SessionStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;


/**
 * DTO for data fetch response - supports both internal format and AA specification format
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataFetchResponseDto {

    // Internal format fields
    private String sessionId;

    private String transactionId;

    private SessionStatus status;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime timestamp;

    private List<FinancialAccountDto> accounts = new ArrayList<>();

    private Boolean success;

    private String message;

    private String errorCode;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime dataExpiryDate;

    // AA Specification format fields (removed - using separate DTO instead)

    // Constructors
    public DataFetchResponseDto() {
    }

    public DataFetchResponseDto(String sessionId, String transactionId, SessionStatus status) {
        this.sessionId = sessionId;
        this.transactionId = transactionId;
        this.status = status;
        this.timestamp = LocalDateTime.now();
        this.success = true;
    }

    // Static factory methods
    public static DataFetchResponseDto success(String sessionId, String transactionId,
                                             SessionStatus status, List<FinancialAccountDto> accounts) {
        DataFetchResponseDto response = new DataFetchResponseDto();
        response.setSessionId(sessionId);
        response.setTransactionId(transactionId);
        response.setStatus(status);
        response.setTimestamp(LocalDateTime.now());
        response.setAccounts(accounts);
        response.setSuccess(true);
        response.setMessage("Data fetched successfully");
        return response;
    }

    public static DataFetchResponseDto error(String sessionId, String transactionId,
                                           String errorCode, String message) {
        DataFetchResponseDto response = new DataFetchResponseDto();
        response.setSessionId(sessionId);
        response.setTransactionId(transactionId);
        response.setStatus(SessionStatus.FAILED);
        response.setTimestamp(LocalDateTime.now());
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setMessage(message);
        return response;
    }

    // Getters and Setters
    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public SessionStatus getStatus() {
        return status;
    }

    public void setStatus(SessionStatus status) {
        this.status = status;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public List<FinancialAccountDto> getAccounts() {
        return accounts;
    }

    public void setAccounts(List<FinancialAccountDto> accounts) {
        this.accounts = accounts;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public LocalDateTime getDataExpiryDate() {
        return dataExpiryDate;
    }

    public void setDataExpiryDate(LocalDateTime dataExpiryDate) {
        this.dataExpiryDate = dataExpiryDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DataFetchResponseDto that = (DataFetchResponseDto) o;
        return Objects.equals(sessionId, that.sessionId) &&
               Objects.equals(transactionId, that.transactionId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(sessionId, transactionId);
    }

    @Override
    public String toString() {
        return "DataFetchResponseDto{" +
                "sessionId='" + sessionId + '\'' +
                ", transactionId='" + transactionId + '\'' +
                ", status=" + status +
                ", timestamp=" + timestamp +
                ", accounts=" + accounts.size() + " accounts" +
                ", success=" + success +
                ", message='" + message + '\'' +
                ", errorCode='" + errorCode + '\'' +
                ", dataExpiryDate=" + dataExpiryDate +
                '}';
    }
}
