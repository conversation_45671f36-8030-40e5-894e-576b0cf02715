package com.accountaggregator.controller;

import com.accountaggregator.entity.Customer;
import com.accountaggregator.service.CustomerService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for customer management operations
 */
@RestController
@RequestMapping("/customers")
public class CustomerController {

    private static final Logger logger = LoggerFactory.getLogger(CustomerController.class);

    @Autowired
    private CustomerService customerService;

    /**
     * Get all customers
     */
    @GetMapping
    public ResponseEntity<List<Customer>> getAllCustomers() {
        logger.info("Received request to get all customers");
        
        try {
            List<Customer> customers = customerService.getAllCustomers();
            logger.info("Retrieved {} customers", customers.size());
            return ResponseEntity.ok(customers);
        } catch (Exception e) {
            logger.error("Error retrieving customers", e);
            throw e;
        }
    }

    /**
     * Get customer by ID
     */
    @GetMapping("/{customerId}")
    public ResponseEntity<Customer> getCustomerById(@PathVariable String customerId) {
        logger.info("Received request to get customer: {}", customerId);
        
        try {
            Customer customer = customerService.getCustomerById(customerId);
            if (customer != null) {
                logger.info("Found customer: {}", customerId);
                return ResponseEntity.ok(customer);
            } else {
                logger.warn("Customer not found: {}", customerId);
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("Error retrieving customer: {}", customerId, e);
            throw e;
        }
    }
}
