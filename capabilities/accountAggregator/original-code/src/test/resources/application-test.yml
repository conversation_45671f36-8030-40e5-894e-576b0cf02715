server:
  port: 0

spring:
  application:
    name: account-aggregator-service-test
  
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  flyway:
    enabled: false
  
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# Test configuration
app:
  consent:
    default-data-life-months: 3
    max-consent-duration-months: 12
    default-frequency: DAILY
    supported-consent-modes:
      - STORE
    supported-data-types:
      - TRANSACTIONS
    supported-fi-types:
      - DEPOSIT
  
  external:
    aa-service:
      base-url: http://localhost:8084/api/account-aggregator/internal
      timeout: 5000
      retry-attempts: 1
      retry-delay: 100

# Logging configuration for tests
logging:
  level:
    com.accountaggregator: DEBUG
    org.springframework.test: INFO
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
