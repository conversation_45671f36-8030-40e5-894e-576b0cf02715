package com.accountaggregator.service;

import java.time.LocalDateTime;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

import com.accountaggregator.dto.ConsentRequestDto;
import com.accountaggregator.enums.DataAccessFrequency;
import com.accountaggregator.enums.DataType;
import com.accountaggregator.enums.FIType;
import com.accountaggregator.enums.PurposeCategory;
import com.accountaggregator.exception.ValidationException;
import com.accountaggregator.repository.ConsentRequestRepository;

/**
 * Unit tests for ValidationService
 */
@ExtendWith(MockitoExtension.class)
class ValidationServiceTest {

    @Mock
    private ConsentRequestRepository consentRequestRepository;

    @InjectMocks
    private ValidationService validationService;

    private ConsentRequestDto validRequestDto;

    @BeforeEach
    void setUp() {
        validRequestDto = new ConsentRequestDto();
        validRequestDto.setCustomerId("CUST1231");
        validRequestDto.setFiuId("FIU001");
        validRequestDto.setConsentStart(LocalDateTime.now());
        validRequestDto.setConsentExpiry(LocalDateTime.now().plusMonths(6));
        validRequestDto.setDataLifeMonths(3);
        validRequestDto.setDataAccessFrequency(DataAccessFrequency.DAILY);
        validRequestDto.setDataType(DataType.TRANSACTIONS);
        validRequestDto.setFiType(FIType.DEPOSIT);
        validRequestDto.setPurposeCode("101");
        validRequestDto.setPurposeText("Personal loan underwriting");
        validRequestDto.setPurposeCategory(PurposeCategory.LENDING);
        validRequestDto.setDataRangeFrom(LocalDateTime.now().minusMonths(12));
        validRequestDto.setDataRangeTo(LocalDateTime.now());
    }

    @Test
    void testValidateConsentRequest_ValidRequest_ShouldPass() {
        // Given
        when(consentRequestRepository.findOverlappingActiveConsents(any(), any(), any(), any(), any(), any()))
            .thenReturn(Collections.emptyList());

        // When & Then
        assertDoesNotThrow(() -> {
            validationService.validateConsentRequest(validRequestDto);
            validationService.checkOverlappingConsents(validRequestDto);
        });
    }

    @Test
    void testValidateConsentRequest_UnsupportedDataType_ShouldThrowException() {
        // Given
        validRequestDto.setDataType(DataType.PROFILE);

        // When & Then
        ValidationException exception = assertThrows(ValidationException.class, () ->
            validationService.validateConsentRequest(validRequestDto));

        assertEquals("UNSUPPORTED_DATA_TYPE", exception.getErrorCode());
        assertTrue(exception.getMessage().contains("TRANSACTIONS"));
    }

    @Test
    void testValidateConsentRequest_UnsupportedFIType_ShouldThrowException() {
        // Given
        validRequestDto.setFiType(FIType.INSURANCE);

        // When & Then
        ValidationException exception = assertThrows(ValidationException.class, () ->
            validationService.validateConsentRequest(validRequestDto));

        assertEquals("UNSUPPORTED_FI_TYPE", exception.getErrorCode());
        assertTrue(exception.getMessage().contains("DEPOSIT"));
    }

    @Test
    void testValidateConsentRequest_InvalidPurposeCode_ShouldThrowException() {
        // Given
        validRequestDto.setPurposeCode("102");

        // When & Then
        ValidationException exception = assertThrows(ValidationException.class, () ->
            validationService.validateConsentRequest(validRequestDto));

        assertEquals("INVALID_PURPOSE_CODE", exception.getErrorCode());
        assertTrue(exception.getMessage().contains("101"));
    }

    @Test
    void testValidateConsentRequest_InvalidPurposeCategory_ShouldThrowException() {
        // Given
        validRequestDto.setPurposeCategory(PurposeCategory.WEALTH_MANAGEMENT);

        // When & Then
        ValidationException exception = assertThrows(ValidationException.class, () ->
            validationService.validateConsentRequest(validRequestDto));

        assertEquals("INVALID_PURPOSE_CATEGORY", exception.getErrorCode());
        assertTrue(exception.getMessage().contains("LENDING"));
    }

    @Test
    void testValidateConsentRequest_ConsentStartAfterExpiry_ShouldThrowException() {
        // Given
        validRequestDto.setConsentStart(LocalDateTime.now().plusDays(1));
        validRequestDto.setConsentExpiry(LocalDateTime.now());

        // When & Then
        ValidationException exception = assertThrows(ValidationException.class, () ->
            validationService.validateConsentRequest(validRequestDto));

        assertEquals("INVALID_CONSENT_DATES", exception.getErrorCode());
    }

    @Test
    void testValidateConsentRequest_ConsentStartInPast_ShouldThrowException() {
        // Given
        validRequestDto.setConsentStart(LocalDateTime.now().minusHours(1));

        // When & Then
        ValidationException exception = assertThrows(ValidationException.class, () ->
            validationService.validateConsentRequest(validRequestDto));

        assertEquals("INVALID_CONSENT_START", exception.getErrorCode());
    }

    @Test
    void testValidateConsentRequest_DataRangeToAfterConsentExpiry_ShouldThrowException() {
        // Given
        validRequestDto.setDataRangeTo(validRequestDto.getConsentExpiry().plusDays(1));

        // When & Then
        ValidationException exception = assertThrows(ValidationException.class, () ->
            validationService.validateConsentRequest(validRequestDto));

        assertEquals("INVALID_DATA_RANGE", exception.getErrorCode());
    }

    @Test
    void testValidateConsentRequest_DataRangeFromInFuture_ShouldThrowException() {
        // Given
        validRequestDto.setDataRangeFrom(LocalDateTime.now().plusDays(1));

        // When & Then
        ValidationException exception = assertThrows(ValidationException.class, () ->
            validationService.validateConsentRequest(validRequestDto));

        assertEquals("INVALID_DATA_RANGE_FROM", exception.getErrorCode());
    }

    @Test
    void testValidateConsentRequest_DataLifeExceedsConsentDuration_ShouldThrowException() {
        // Given
        validRequestDto.setConsentExpiry(LocalDateTime.now().plusMonths(2));
        validRequestDto.setDataLifeMonths(6);

        // When & Then
        ValidationException exception = assertThrows(ValidationException.class, () ->
            validationService.validateConsentRequest(validRequestDto));

        assertEquals("DATA_LIFE_EXCEEDED", exception.getErrorCode());
    }

    @Test
    void testValidateConsentRequest_ConsentDurationExceeds12Months_ShouldThrowException() {
        // Given
        validRequestDto.setConsentExpiry(LocalDateTime.now().plusMonths(13));

        // When & Then
        ValidationException exception = assertThrows(ValidationException.class, () ->
            validationService.validateConsentRequest(validRequestDto));

        assertEquals("CONSENT_DURATION_EXCEEDED", exception.getErrorCode());
    }

    @Test
    void testValidateConsentRequest_NegativeDataLife_ShouldThrowException() {
        // Given
        validRequestDto.setDataLifeMonths(-1);

        // When & Then
        ValidationException exception = assertThrows(ValidationException.class, () ->
            validationService.validateConsentRequest(validRequestDto));

        assertEquals("INVALID_DATA_LIFE", exception.getErrorCode());
    }

    @Test
    void testValidateConsentRequest_DataLifeExceeds12Months_ShouldThrowException() {
        // Given
        validRequestDto.setDataLifeMonths(15);

        // When & Then
        ValidationException exception = assertThrows(ValidationException.class, () ->
            validationService.validateConsentRequest(validRequestDto));

        assertEquals("DATA_LIFE_EXCEEDED", exception.getErrorCode());
    }

    @Test
    void testValidateTransactionIdUniqueness_DuplicateId_ShouldThrowException() {
        // Given
        String transactionId = "TXN123456";
        when(consentRequestRepository.existsByTransactionId(transactionId)).thenReturn(true);

        // When & Then
        ValidationException exception = assertThrows(ValidationException.class, () ->
            validationService.validateTransactionIdUniqueness(transactionId));

        assertEquals("DUPLICATE_TRANSACTION_ID", exception.getErrorCode());
        assertTrue(exception.getMessage().contains(transactionId));
    }

    @Test
    void testValidateTransactionIdUniqueness_UniqueId_ShouldPass() {
        // Given
        String transactionId = "TXN123456";
        when(consentRequestRepository.existsByTransactionId(transactionId)).thenReturn(false);

        // When & Then
        assertDoesNotThrow(() ->
            validationService.validateTransactionIdUniqueness(transactionId));
    }
}
