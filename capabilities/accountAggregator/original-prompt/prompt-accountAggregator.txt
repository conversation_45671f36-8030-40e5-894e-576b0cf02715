Business Flow

Phase 1: Initiate User Consent for Financial Data Access
    Trigger: 
        - User initiates a loan application

    Request Payload / Mandatory inputs:
        - Consent Start Date (current date and time when the trigger occurs)
        - Consent Expiry Date (up to 12 months from start)
        - Data Retention Period (DataLife, default: 3 months)
        - Data Access Frequency (default: once per day)
        - Data Type Requested (TRANSACTIONS from DEPOSIT accounts only)
        - Purpose of Access (Code: 101, Text: "Personal loan underwriting", Category: LENDING)
        - Time Range for Data Request (From: 12 months ago, To: Current date)
        - Associate request with unique Customer ID (e.g., mobile@anumati) and FIU ID
        - Include version and transaction ID for traceability

    Validations:
        - Support only STORE consent mode
        - Consent types allowed: TRANSACTIONS
        - consentStart must be ≤ consentExpiry
        - FIDataRange.to must be ≤ consentExpiry
        - FIDataRange.from must be ≤ current date
        - consentStart must not be earlier than the current date
        - DataLife.value should not exceed consentExpiry - consentStart
        - A customer cannot have multiple active consents for the same FIU and purpose with overlapping FIDataRange
        - <PERSON>ur<PERSON> must use code 101 with valid text and category LENDING

    System Behavior Based on API Response:
        - Upon submission:
            - Generate Consent Handle and redirect user to Anumati portal
        - System will receive:
            - Consent ID (for future reference)
            - Consent Handle (unique identifier for this consent request)
            - Initial status (PENDING)
            - Creation timestamp
            - Redirect URL (to send user to Anumati portal)

Phase 2: User Confirms Consent on Anumati Portal
    
    Trigger: 
        - User reviews and confirms consent details at Anumati

    API Response Processing / System: 
        - Upon confirmation:
            - Unique Consent ID is generated
            - Consent status changes to ACTIVE
            - Digitally signed artifact (signedConsent) is created containing:
            - A unique consent artifact ID
            - A digital signature (Base64 encoded)
            - Version and timestamp information
            - System stores Consent ID, signedConsent, and approval timestamp
        - Handle rejection scenarios:
            - User opt-out
            - Invalid dates
            - Missing fields
    
    Validations:
        - Digital signature must be verifiable and match the original consent request
        - System must log signed consent details with timestamp for regulatory traceability
        - Any missing or corrupted digital signature must trigger an exception

Phase 3: Check and Confirm Consent Status

    System Behavior Based on API Response:
        - Verify consent is ACTIVE before accessing financial data
        - Validate signature, timestamps, and FIU association
        - Proceed only if all validations pass

Phase 4: Request User’s Financial Data
    
    Request Payload: 
        - Create data request using validated Consent ID
        - Include version information, timestamp, and unique transaction ID

    API Response Processing / System: 
        - System will receive a unique Session ID with status IN_PROGRESS
        - Enforce consent validity and frequency limit checks
        - Handle duplicate transaction prevention

    Validations:
        - Only fetch if consent status is ACTIVE
        - Only the FIU that created the consent can call this API
        - Transaction ID must be unique per request
        - Reject if consent is revoked, expired, or not ACTIVE
        - Duplicate requests with same transaction ID should either return the same sessionId or be rejected
        - Respect frequency limits based on consent terms (e.g., once per day)
        - Session initiation should complete within 1 second (configurable)

Phase 5: Poll or Receive Fetched Financial Data

    API Response Processing / System: 
        - Receive financial data that includes:
            - Account information:
            - Account Type (e.g., SAVINGS)
            - Masked Account Number (for privacy)
            - Financial Institution Type (e.g., DEPOSIT)
            - Transaction records containing:
            - Transaction ID
            - Amount and currency
            - Transaction type (CREDIT/DEBIT)
            - Transaction date
            - Narration/description
        - Securely store data with proper linkage to Consent ID, Session ID, and customer
        - Enforce DataLife-based retention policies (delete after specified period)
