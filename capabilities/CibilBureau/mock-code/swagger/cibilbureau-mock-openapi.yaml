openapi: 3.0.3
info:
  title: CIBIL Mock API Service
  description: |
    Mock service to simulate CIBIL Bureau API responses for testing and development purposes.
    This service provides realistic credit check responses based on different test scenarios
    determined by PAN number patterns, enabling comprehensive testing of credit workflows.
  version: 1.0.0
  contact:
    name: CIBIL Mock Team
    email: <EMAIL>
  license:
    name: Proprietary
    url: https://www.cibilbureau.com/license

servers:
  - url: http://localhost:8207/api/cibil-bureau/internal
    description: Local development server
  - url: http://cibil-mock-service:8207/api/cibil-bureau/internal
    description: Docker container server

paths:
  /v1/cibil-bureau/mock/credit-check:
    post:
      tags:
        - Mock Credit Check
      summary: Simulate CIBIL credit check
      description: |
        Simulates CIBIL Bureau API behavior for credit check requests.
        Response varies based on PAN number ending:
        - 'A': Strong borrower profile (Score: 720, Low Risk, Eligible)
        - 'B': Weak borrower profile (Score: 600, High Risk, Not Eligible)
        - 'C': Incomplete data response
        - 'D': System error simulation
      operationId: simulateCreditCheck
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CibilCreditCheckRequest'
            examples:
              strongBorrower:
                summary: Strong borrower (PAN ending with A)
                value:
                  firstName: "Alice"
                  lastName: "Johnson"
                  dateOfBirth: "********"
                  panNumber: "**********"
                  idType: "PAN_CARD"
                  phoneNumber: "9876543210"
                  loanPurpose: "HOME_LOAN"
              weakBorrower:
                summary: Weak borrower (PAN ending with B)
                value:
                  firstName: "Bob"
                  lastName: "Smith"
                  dateOfBirth: "********"
                  panNumber: "**********"
                  idType: "PAN_CARD"
                  phoneNumber: "8765432109"
                  loanPurpose: "PERSONAL_LOAN"
      responses:
        '200':
          description: Credit check simulation completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CibilCreditCheckResponse'
              examples:
                strongBorrowerResponse:
                  summary: Strong borrower response
                  value:
                    referenceId: "MOCK123456789"
                    fullName: "Alice Johnson"
                    cibilScore: 720
                    riskLevel: "Low Risk"
                    recommendation: "Eligible"
                    accountSummary: "5 total accounts, 3 active, 0 defaults"
                    recentInquiries: 2
                    isEligible: true
                    message: "Credit profile shows strong borrower characteristics"
                    outcomeStatus: "SUCCESS"
                    dateOfBirth: "********"
                weakBorrowerResponse:
                  summary: Weak borrower response
                  value:
                    referenceId: "MOCK987654321"
                    fullName: "Bob Smith"
                    cibilScore: 600
                    riskLevel: "High Risk"
                    recommendation: "Not Eligible"
                    accountSummary: "8 total accounts, 2 active, 3 defaults"
                    recentInquiries: 5
                    isEligible: false
                    message: "Credit profile shows high risk characteristics"
                    outcomeStatus: "SUCCESS"
                    dateOfBirth: "********"
        '400':
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                validationError:
                  summary: Validation error
                  value:
                    timestamp: "2024-01-15T10:30:00"
                    status: 400
                    error: "Bad Request"
                    message: "Invalid PAN number format"
                    path: "/api/cibil/internal/v1/credit-check"
                    referenceId: null
        '500':
          description: Simulated system error (PAN ending with D)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                systemError:
                  summary: Simulated system error
                  value:
                    timestamp: "2024-01-15T10:30:00"
                    status: 500
                    error: "Internal Server Error"
                    message: "Bureau system temporarily unavailable"
                    path: "/api/cibil/internal/v1/credit-check"
                    referenceId: "MOCK000000000"

  /v1/cibil-bureau/mock/health:
    get:
      tags:
        - Health Check
      summary: Mock service health check
      description: Simple health check endpoint for the mock CIBIL service
      operationId: mockServiceHealth
      responses:
        '200':
          description: Mock service is running
          content:
            text/plain:
              schema:
                type: string
                example: "CIBIL Mock Service is running"

  /v1/cibil-bureau/mock/info:
    get:
      tags:
        - Service Information
      summary: Mock service information
      description: Provides information about the mock service including supported test scenarios
      operationId: mockServiceInfo
      responses:
        '200':
          description: Service information retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceInfo'
              examples:
                serviceInfo:
                  summary: Service information
                  value:
                    service: "CIBIL Mock API Service"
                    version: "1.0.0"
                    description: "Mock service to simulate CIBIL Bureau API responses"
                    supportedScenarios:
                      - "PAN ending with 'A' - Strong borrower profile"
                      - "PAN ending with 'B' - Weak borrower profile"
                      - "PAN ending with 'C' - Incomplete data response"
                      - "PAN ending with 'D' - System error simulation"

  /v1/cibil-bureau/mock/health:
    get:
      tags:
        - Health Check
      summary: Main mock service health check
      description: Comprehensive health check endpoint providing service status and metadata
      operationId: mainMockHealthCheck
      responses:
        '200':
          description: Service health status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
              examples:
                healthy:
                  summary: Healthy mock service
                  value:
                    status: "UP"
                    service: "Cibil Bureau Mock"
                    timestamp: "2024-01-15T10:30:00"
                    version: "1.0.0"

components:
  schemas:
    CibilCreditCheckRequest:
      type: object
      required:
        - firstName
        - lastName
        - dateOfBirth
        - panNumber
        - idType
        - phoneNumber
        - loanPurpose
      properties:
        firstName:
          type: string
          minLength: 2
          maxLength: 50
          description: First name of the borrower
          example: "Alice"
        lastName:
          type: string
          minLength: 2
          maxLength: 50
          description: Last name of the borrower
          example: "Johnson"
        dateOfBirth:
          type: string
          pattern: '^[0-9]{8}$'
          description: Date of birth in DDMMYYYY format
          example: "********"
        panNumber:
          type: string
          pattern: '^[A-Z]{5}[0-9]{4}[A-Z]{1}$'
          description: PAN number in valid format (ending determines test scenario)
          example: "**********"
        idType:
          type: string
          description: Type of identification document
          example: "PAN_CARD"
        phoneNumber:
          type: string
          pattern: '^[0-9]{10}$'
          description: 10-digit phone number
          example: "9876543210"
        loanPurpose:
          type: string
          description: Purpose of the loan
          example: "HOME_LOAN"

    CibilCreditCheckResponse:
      type: object
      properties:
        referenceId:
          type: string
          description: Unique reference ID for the mock response
          example: "MOCK123456789"
        fullName:
          type: string
          description: Full name of the borrower
          example: "Alice Johnson"
        cibilScore:
          type: integer
          minimum: 300
          maximum: 900
          description: Simulated CIBIL score
          example: 720
        riskLevel:
          type: string
          description: Risk level assessment
          example: "Low Risk"
        recommendation:
          type: string
          description: Loan recommendation
          example: "Eligible"
        accountSummary:
          type: string
          description: Summary of credit accounts
          example: "5 total accounts, 3 active, 0 defaults"
        recentInquiries:
          type: integer
          description: Number of recent credit inquiries
          example: 2
        isEligible:
          type: boolean
          description: Whether the borrower is eligible for loan
          example: true
        message:
          type: string
          description: Descriptive message about the result
          example: "Credit profile shows strong borrower characteristics"
        outcomeStatus:
          type: string
          description: Overall outcome status
          example: "SUCCESS"
        dateOfBirth:
          type: string
          description: Date of birth from request
          example: "********"

    ErrorResponse:
      type: object
      properties:
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
          example: "2024-01-15T10:30:00"
        status:
          type: integer
          description: HTTP status code
          example: 400
        error:
          type: string
          description: Error type
          example: "Bad Request"
        message:
          type: string
          description: Error message
          example: "Invalid PAN number format"
        path:
          type: string
          description: Request path that caused the error
          example: "/api/cibil/internal/v1/credit-check"
        referenceId:
          type: string
          nullable: true
          description: Reference ID if available
          example: null

    ServiceInfo:
      type: object
      properties:
        service:
          type: string
          description: Service name
          example: "CIBIL Mock API Service"
        version:
          type: string
          description: Service version
          example: "1.0.0"
        description:
          type: string
          description: Service description
          example: "Mock service to simulate CIBIL Bureau API responses"
        supportedScenarios:
          type: array
          items:
            type: string
          description: List of supported test scenarios
          example:
            - "PAN ending with 'A' - Strong borrower profile"
            - "PAN ending with 'B' - Weak borrower profile"
            - "PAN ending with 'C' - Incomplete data response"
            - "PAN ending with 'D' - System error simulation"

    HealthResponse:
      type: object
      properties:
        status:
          type: string
          description: Service status
          example: "UP"
        service:
          type: string
          description: Service name
          example: "Cibil Bureau Mock"
        timestamp:
          type: string
          format: date-time
          description: Health check timestamp
          example: "2024-01-15T10:30:00"
        version:
          type: string
          description: Service version
          example: "1.0.0"

tags:
  - name: Mock Credit Check
    description: Simulated credit check operations with configurable test scenarios
  - name: Health Check
    description: Service health monitoring endpoints
  - name: Service Information
    description: Mock service metadata and configuration information
