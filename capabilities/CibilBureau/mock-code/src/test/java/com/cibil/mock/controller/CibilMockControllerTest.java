package com.cibil.mock.controller;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.cibil.mock.dto.CibilCreditCheckRequest;
import com.fasterxml.jackson.databind.ObjectMapper;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
class CibilMockControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;



    @Test
    void testInfoEndpoint() throws Exception {
        mockMvc.perform(get("/v1/cibil-bureau/mock/info"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.service").value("CIBIL Mock API Service"))
                .andExpect(jsonPath("$.version").value("1.0.0"));
    }

    @Test
    void testCreditCheckWithValidRequest() throws Exception {
        CibilCreditCheckRequest request = new CibilCreditCheckRequest(
                "John", "Doe", "01011990", "**********",
                "PAN_CARD", "9876543210", "PERSONAL_LOAN"
        );

        mockMvc.perform(post("/v1/cibil-bureau/mock/credit-check")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());
    }

    @Test
    void testCreditCheckWithInvalidPAN() throws Exception {
        CibilCreditCheckRequest request = new CibilCreditCheckRequest(
                "John", "Doe", "01011990", "INVALID_PAN",
                "PAN_CARD", "9876543210", "PERSONAL_LOAN"
        );

        mockMvc.perform(post("/v1/cibil-bureau/mock/credit-check")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }
}
