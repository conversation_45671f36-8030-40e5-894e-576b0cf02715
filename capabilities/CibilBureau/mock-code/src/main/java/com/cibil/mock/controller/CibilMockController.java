package com.cibil.mock.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cibil.mock.dto.CibilCreditCheckRequest;
import com.cibil.mock.dto.CibilCreditCheckResponse;
import com.cibil.mock.service.CibilMockService;

import jakarta.validation.Valid;

/**
 * REST controller for CIBIL Mock API endpoints.
 */
@RestController
@RequestMapping("/v1/cibil-bureau/mock")
public class CibilMockController {

    private static final Logger logger = LoggerFactory.getLogger(CibilMockController.class);

    private final CibilMockService cibilMockService;

    public CibilMockController(CibilMockService cibilMockService) {
        this.cibilMockService = cibilMockService;
    }

    /**
     * CIBIL Credit Check endpoint that simulates bureau API behavior.
     * 
     * @param request the credit check request
     * @return simulated credit check response
     */
    @PostMapping("/credit-check")
    public ResponseEntity<CibilCreditCheckResponse> creditCheck(@Valid @RequestBody CibilCreditCheckRequest request) {
        logger.info("Received credit check request for: {} {}", 
                   request.getFirstName(), request.getLastName());
        
        try {
            CibilCreditCheckResponse response = cibilMockService.processCreditCheck(request);
            logger.info("Successfully processed credit check request. Reference ID: {}", 
                       response.getReferenceId());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error processing credit check request: {}", e.getMessage(), e);
            throw e; // Let the global exception handler deal with it
        }
    }



    /**
     * Info endpoint that provides service information.
     */
    @GetMapping("/info")
    public ResponseEntity<Object> info() {
        return ResponseEntity.ok(new Object() {
            public final String service = "CIBIL Mock API Service";
            public final String version = "1.0.0";
            public final String description = "Mock service to simulate CIBIL Bureau API responses";
            public final String[] supportedScenarios = {
                "PAN ending with 'A' - Strong borrower profile",
                "PAN ending with 'B' - Weak borrower profile", 
                "PAN ending with 'C' - Incomplete data response",
                "PAN ending with 'D' - System error simulation"
            };
        });
    }
}
