package com.cibil.mock.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Response DTO for CIBIL credit check API.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CibilCreditCheckResponse {

    @JsonProperty("referenceId")
    private String referenceId;

    @JsonProperty("fullName")
    private String fullName;

    @JsonProperty("cibilScore")
    private Integer cibilScore;

    @JsonProperty("riskLevel")
    private String riskLevel;

    @JsonProperty("recommendation")
    private String recommendation;

    @JsonProperty("accountSummary")
    private String accountSummary;

    @JsonProperty("recentInquiries")
    private Integer recentInquiries;

    @JsonProperty("isEligible")
    private Boolean isEligible;

    @JsonProperty("message")
    private String message;

    @JsonProperty("outcomeStatus")
    private String outcomeStatus;

    @JsonProperty("dateOfBirth")
    private String dateOfBirth;

    // Default constructor
    public CibilCreditCheckResponse() {}

    // Constructor for successful response
    public CibilCreditCheckResponse(String referenceId, String fullName, Integer cibilScore, 
                                   String riskLevel, String recommendation, String accountSummary,
                                   Integer recentInquiries, Boolean isEligible, String message, 
                                   String outcomeStatus, String dateOfBirth) {
        this.referenceId = referenceId;
        this.fullName = fullName;
        this.cibilScore = cibilScore;
        this.riskLevel = riskLevel;
        this.recommendation = recommendation;
        this.accountSummary = accountSummary;
        this.recentInquiries = recentInquiries;
        this.isEligible = isEligible;
        this.message = message;
        this.outcomeStatus = outcomeStatus;
        this.dateOfBirth = dateOfBirth;
    }

    // Getters and Setters
    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public Integer getCibilScore() {
        return cibilScore;
    }

    public void setCibilScore(Integer cibilScore) {
        this.cibilScore = cibilScore;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getRecommendation() {
        return recommendation;
    }

    public void setRecommendation(String recommendation) {
        this.recommendation = recommendation;
    }

    public String getAccountSummary() {
        return accountSummary;
    }

    public void setAccountSummary(String accountSummary) {
        this.accountSummary = accountSummary;
    }

    public Integer getRecentInquiries() {
        return recentInquiries;
    }

    public void setRecentInquiries(Integer recentInquiries) {
        this.recentInquiries = recentInquiries;
    }

    public Boolean getIsEligible() {
        return isEligible;
    }

    public void setIsEligible(Boolean isEligible) {
        this.isEligible = isEligible;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getOutcomeStatus() {
        return outcomeStatus;
    }

    public void setOutcomeStatus(String outcomeStatus) {
        this.outcomeStatus = outcomeStatus;
    }

    public String getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(String dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    @Override
    public String toString() {
        return "CibilCreditCheckResponse{" +
                "referenceId='" + referenceId + '\'' +
                ", fullName='" + fullName + '\'' +
                ", cibilScore=" + cibilScore +
                ", riskLevel='" + riskLevel + '\'' +
                ", recommendation='" + recommendation + '\'' +
                ", accountSummary='" + accountSummary + '\'' +
                ", recentInquiries=" + recentInquiries +
                ", isEligible=" + isEligible +
                ", message='" + message + '\'' +
                ", outcomeStatus='" + outcomeStatus + '\'' +
                ", dateOfBirth='" + dateOfBirth + '\'' +
                '}';
    }
}
