package com.cibil.mock.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * Request DTO for CIBIL credit check API.
 */
public class CibilCreditCheckRequest {

    @NotBlank(message = "First name is required")
    @Size(min = 2, max = 50, message = "First name must be between 2 and 50 characters")
    @JsonProperty("firstName")
    private String firstName;

    @NotBlank(message = "Last name is required")
    @Size(min = 2, max = 50, message = "Last name must be between 2 and 50 characters")
    @JsonProperty("lastName")
    private String lastName;

    @NotBlank(message = "Date of birth is required")
    @Pattern(regexp = "\\d{8}", message = "Date of birth must be in DDMMYYYY format")
    @JsonProperty("dateOfBirth")
    private String dateOfBirth;

    @NotBlank(message = "PAN number is required")
    @Pattern(regexp = "[A-Z]{5}[0-9]{4}[A-Z]{1}", message = "Invalid PAN number format")
    @JsonProperty("panNumber")
    private String panNumber;

    @NotBlank(message = "ID type is required")
    @JsonProperty("idType")
    private String idType;

    @NotBlank(message = "Phone number is required")
    @Pattern(regexp = "[0-9]{10}", message = "Phone number must be 10 digits")
    @JsonProperty("phoneNumber")
    private String phoneNumber;

    @NotBlank(message = "Loan purpose is required")
    @JsonProperty("loanPurpose")
    private String loanPurpose;

    // Default constructor
    public CibilCreditCheckRequest() {}

    // Constructor with all fields
    public CibilCreditCheckRequest(String firstName, String lastName, String dateOfBirth, 
                                  String panNumber, String idType, String phoneNumber, String loanPurpose) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.dateOfBirth = dateOfBirth;
        this.panNumber = panNumber;
        this.idType = idType;
        this.phoneNumber = phoneNumber;
        this.loanPurpose = loanPurpose;
    }

    // Getters and Setters
    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(String dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getPanNumber() {
        return panNumber;
    }

    public void setPanNumber(String panNumber) {
        this.panNumber = panNumber;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(String loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    @Override
    public String toString() {
        return "CibilCreditCheckRequest{" +
                "firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", dateOfBirth='" + dateOfBirth + '\'' +
                ", panNumber='" + panNumber + '\'' +
                ", idType='" + idType + '\'' +
                ", phoneNumber='" + phoneNumber + '\'' +
                ", loanPurpose='" + loanPurpose + '\'' +
                '}';
    }
}
