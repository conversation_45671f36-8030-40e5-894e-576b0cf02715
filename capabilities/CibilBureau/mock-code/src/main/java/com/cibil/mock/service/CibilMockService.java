package com.cibil.mock.service;

import com.cibil.mock.dto.CibilCreditCheckRequest;
import com.cibil.mock.dto.CibilCreditCheckResponse;
import com.cibil.mock.exception.CibilServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * Service class that implements the CIBIL mock simulation logic.
 */
@Service
public class CibilMockService {

    private static final Logger logger = LoggerFactory.getLogger(CibilMockService.class);

    /**
     * Processes credit check request and returns simulated response based on PAN suffix.
     *
     * @param request the credit check request
     * @return simulated credit check response
     * @throws CibilServiceException if PAN ends with 'D' (simulates system error)
     */
    public CibilCreditCheckResponse processCreditCheck(CibilCreditCheckRequest request) {
        logger.info("Processing credit check for PAN: {}", maskPan(request.getPanNumber()));

        String panNumber = request.getPanNumber();
        char lastChar = panNumber.charAt(panNumber.length() - 1);

        String referenceId = "CIBIL-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        String fullName = request.getFirstName() + " " + request.getLastName();

        switch (lastChar) {
            case 'A':
                return createStrongBorrowerResponse(referenceId, fullName, request);
            case 'B':
                return createWeakBorrowerResponse(referenceId, fullName, request);
            case 'C':
                return createIncompleteDataResponse(referenceId, fullName, request);
            case 'D':
                logger.error("Simulating system error for PAN ending with 'D': {}", maskPan(panNumber));
                throw new CibilServiceException("CIBIL service unavailable - System maintenance in progress");
            default:
                // Default to strong borrower for any other suffix
                return createStrongBorrowerResponse(referenceId, fullName, request);
        }
    }

    /**
     * Creates response for strong borrower profile (PAN ends with 'A').
     */
    private CibilCreditCheckResponse createStrongBorrowerResponse(String referenceId, String fullName,
                                                                CibilCreditCheckRequest request) {
        logger.info("Creating strong borrower response for reference: {}", referenceId);

        CibilCreditCheckResponse response = new CibilCreditCheckResponse();
        response.setReferenceId(referenceId);
        response.setFullName(fullName);
        response.setCibilScore(720);
        response.setRiskLevel("Low Risk");
        response.setRecommendation("ELIGIBLE");
        response.setAccountSummary("Total accounts: 5, Active: 4, Overdue: 0");
        response.setRecentInquiries(1);
        response.setIsEligible(true);
        response.setMessage("Excellent credit profile. Your loan application has been approved for digital processing.");
        response.setOutcomeStatus("SUCCESS");
        response.setDateOfBirth(request.getDateOfBirth());

        return response;
    }

    /**
     * Creates response for weak borrower profile (PAN ends with 'B').
     */
    private CibilCreditCheckResponse createWeakBorrowerResponse(String referenceId, String fullName,
                                                              CibilCreditCheckRequest request) {
        logger.info("Creating weak borrower response for reference: {}", referenceId);

        CibilCreditCheckResponse response = new CibilCreditCheckResponse();
        response.setReferenceId(referenceId);
        response.setFullName(fullName);
        response.setCibilScore(600);
        response.setRiskLevel("High Risk");
        response.setRecommendation("NOT_ELIGIBLE");
        response.setAccountSummary("Total accounts: 3, Active: 1, Overdue: 2");
        response.setRecentInquiries(5);
        response.setIsEligible(false);
        response.setMessage("Credit profile indicates high risk. Loan application requires manual review.");
        response.setOutcomeStatus("SUCCESS");
        response.setDateOfBirth(request.getDateOfBirth());

        return response;
    }

    /**
     * Creates response with incomplete data (PAN ends with 'C').
     * Note: dateOfBirth is intentionally omitted to simulate incomplete response.
     */
    private CibilCreditCheckResponse createIncompleteDataResponse(String referenceId, String fullName,
                                                                CibilCreditCheckRequest request) {
        logger.info("Creating incomplete data response for reference: {}", referenceId);

        CibilCreditCheckResponse response = new CibilCreditCheckResponse();
        response.setReferenceId(referenceId);
        response.setFullName(fullName);
        response.setCibilScore(680);
        response.setRiskLevel("Medium Risk");
        response.setRecommendation("REVIEW_REQUIRED");
        response.setAccountSummary("Total accounts: 2, Active: 2, Overdue: 0");
        response.setRecentInquiries(2);
        response.setIsEligible(null); // Intentionally null to simulate incomplete data
        response.setMessage("Partial credit information available. Additional verification required.");
        response.setOutcomeStatus("PARTIAL_SUCCESS");
        // dateOfBirth is intentionally not set to simulate incomplete response

        return response;
    }

    /**
     * Masks PAN number for logging (shows only first 2 and last 1 characters).
     */
    private String maskPan(String pan) {
        if (pan == null || pan.length() < 3) {
            return "***";
        }
        return pan.substring(0, 2) + "****" + pan.substring(pan.length() - 1);
    }
}
