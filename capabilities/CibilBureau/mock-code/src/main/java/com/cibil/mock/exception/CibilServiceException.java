package com.cibil.mock.exception;

/**
 * Custom exception for CIBIL service errors.
 */
public class CibilServiceException extends RuntimeException {

    private final String errorCode;

    public CibilServiceException(String message) {
        super(message);
        this.errorCode = "CIBIL_SERVICE_ERROR";
    }

    public CibilServiceException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    public CibilServiceException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "CIBIL_SERVICE_ERROR";
    }

    public CibilServiceException(String message, String errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }
}
