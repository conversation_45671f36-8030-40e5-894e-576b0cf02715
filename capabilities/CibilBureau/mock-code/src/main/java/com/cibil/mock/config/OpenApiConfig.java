package com.cibil.mock.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;

/**
 * OpenAPI configuration for CIBIL Mock API Service.
 * Configures Swagger UI documentation and CORS settings.
 */
@Configuration
public class OpenApiConfig implements WebMvcConfigurer {

    @Value("${server.port:8207}")
    private String serverPort;

    @Value("${server.servlet.context-path:/api/cibil-bureau/internal}")
    private String contextPath;

    /**
     * Configures OpenAPI documentation.
     *
     * @return OpenAPI configuration
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("CIBIL Mock API Service")
                        .description("""
                                Mock service to simulate CIBIL Bureau API responses for testing and development purposes.
                                This service provides realistic credit check responses based on different test scenarios
                                determined by PAN number patterns, enabling comprehensive testing of credit workflows.
                                
                                Test Scenarios:
                                - PAN ending with 'A': Strong borrower profile (Score: 720, Low Risk, Eligible)
                                - PAN ending with 'B': Weak borrower profile (Score: 600, High Risk, Not Eligible)
                                - PAN ending with 'C': Incomplete data response
                                - PAN ending with 'D': System error simulation
                                """)
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("CIBIL Mock Team")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("Proprietary")
                                .url("https://www.cibilbureau.com/license")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + contextPath)
                                .description("Local development server"),
                        new Server()
                                .url("http://cibil-mock-service:" + serverPort + contextPath)
                                .description("Docker container server")
                ));
    }

    /**
     * Configures CORS settings to allow Swagger UI to function properly.
     *
     * @param registry CORS registry
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
}
