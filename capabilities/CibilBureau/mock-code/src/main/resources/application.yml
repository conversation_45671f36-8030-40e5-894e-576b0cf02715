server:
  port: ${SERVER_PORT:8207}
  servlet:
    context-path: /api/cibil-bureau/internal

spring:
  application:
    name: cibil-mock-service

  jackson:
    serialization:
      write-dates-as-timestamps: false
      indent-output: true
    deserialization:
      fail-on-unknown-properties: false
    default-property-inclusion: non_null

# Logging Configuration
logging:
  level:
    com.cibil.mock: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/cibil-mock-service.log
    max-size: 10MB
    max-history: 30

# Management and Actuator
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
  server:
    port: 8091

# OpenAPI Documentation Configuration
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    try-it-out-enabled: true
    operations-sorter: method
    tags-sorter: alpha
    doc-expansion: none
    display-request-duration: true
  show-actuator: true
  default-consumes-media-type: application/json
  default-produces-media-type: application/json
