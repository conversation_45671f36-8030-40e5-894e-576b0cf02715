openapi: 3.0.3
info:
  title: CIBIL Bureau Credit Check Service API
  description: |
    End-to-end credit check service using CIBIL Bureau API for loan eligibility assessment.
    This service provides comprehensive credit analysis including risk assessment, eligibility determination,
    and administrative functions for monitoring and data management.
  version: 1.0.0
  contact:
    name: CIBIL Bureau Team
    email: <EMAIL>
  license:
    name: Proprietary
    url: https://www.cibilbureau.com/license

servers:
  - url: http://localhost:8107/api/cibil-bureau
    description: Local development server
  - url: http://cibil-bureau-service:8107/api/cibil-bureau
    description: Docker container server

paths:
  /credit-check:
    post:
      tags:
        - Credit Check
      summary: Process credit check request
      description: |
        Processes a comprehensive credit check request by validating borrower information,
        calling CIBIL Bureau API, and performing risk analysis to determine loan eligibility.
      operationId: processCreditCheck
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreditCheckRequest'
            examples:
              validRequest:
                summary: Valid credit check request
                value:
                  firstName: "John"
                  lastName: "Doe"
                  dateOfBirth: "********"
                  panNumber: "**********"
                  idType: "PAN_CARD"
                  phoneNumber: "**********"
                  loanPurpose: "PERSONAL_LOAN"
      responses:
        '200':
          description: Credit check processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreditCheckResponse'
              examples:
                eligibleBorrower:
                  summary: Eligible borrower response
                  value:
                    referenceNumber: "REF123456789"
                    fullName: "John Doe"
                    dateOfBirth: "********"
                    panNumber: "**********"
                    phoneNumber: "**********"
                    creditScore: 750
                    riskLevel: "LOW_RISK"
                    recommendation: "APPROVE"
                    message: "Borrower is eligible for loan"
                    status: "ELIGIBLE"
                    eligible: true
                    creditSummary:
                      totalAccounts: 5
                      activeAccounts: 3
                      defaultsCount: 0
                      recentInquiries: 2
        '400':
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                validationError:
                  summary: Validation error
                  value:
                    error: "Bad Request"
                    message: "Validation failed"
                    status: 400
                    path: "/api/v1/credit-check"
                    referenceNumber: null
                    details: ["PAN number must be in valid format"]
                    timestamp: "2024-01-15 10:30:00"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                serverError:
                  summary: Server error
                  value:
                    error: "Internal Server Error"
                    message: "Credit check processing failed"
                    status: 500
                    path: "/api/v1/credit-check"
                    referenceNumber: "REF123456789"
                    timestamp: "2024-01-15 10:30:00"

  /credit-check/{referenceNumber}:
    get:
      tags:
        - Credit Check
      summary: Get credit check status
      description: Retrieves the status and details of a previously submitted credit check request using the reference number.
      operationId: getCreditCheckStatus
      parameters:
        - name: referenceNumber
          in: path
          required: true
          description: The reference number of the credit check request
          schema:
            type: string
            pattern: '^REF[0-9]{9}$'
            example: "REF123456789"
      responses:
        '200':
          description: Credit check status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreditCheckResponse'
        '404':
          description: Credit check not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                notFound:
                  summary: Credit check not found
                  value:
                    error: "Not Found"
                    message: "Credit check not found for reference: REF123456789"
                    status: 404
                    path: "/api/v1/credit-check/REF123456789"
                    timestamp: "2024-01-15 10:30:00"

  /credit-check/health:
    get:
      tags:
        - Health Check
      summary: Credit check service health
      description: Simple health check endpoint for the credit check service
      operationId: creditCheckHealth
      responses:
        '200':
          description: Service is healthy
          content:
            text/plain:
              schema:
                type: string
                example: "Credit Check Service is healthy"

  /v1/cibil-bureau/original/health:
    get:
      tags:
        - Health Check
      summary: Main service health check
      description: Comprehensive health check endpoint providing service status and metadata
      operationId: mainHealthCheck
      responses:
        '200':
          description: Service health status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
              examples:
                healthy:
                  summary: Healthy service
                  value:
                    status: "UP"
                    service: "Cibil Bureau Original"
                    timestamp: "2024-01-15T10:30:00"
                    version: "1.0.0"

  /admin/audit/summary:
    get:
      tags:
        - Administration
      summary: Get audit summary
      description: Retrieves audit summary including request counts and processing statistics
      operationId: getAuditSummary
      responses:
        '200':
          description: Audit summary retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuditSummary'
              examples:
                summary:
                  summary: Audit summary
                  value:
                    totalRequests: 1250
                    successfulRequests: 1180
                    failedRequests: 70
                    averageProcessingTime: 2.5
                    lastProcessedAt: "2024-01-15T10:30:00"

  /admin/retention/cleanup:
    post:
      tags:
        - Administration
      summary: Trigger data cleanup
      description: Manually triggers data cleanup process based on retention policies
      operationId: triggerDataCleanup
      responses:
        '200':
          description: Data cleanup completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataRetentionResult'
              examples:
                cleanupResult:
                  summary: Cleanup result
                  value:
                    recordsDeleted: 150
                    spaceSavedMB: 25.6
                    executionTimeMs: 1200
                    status: "COMPLETED"
        '500':
          description: Data cleanup failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /admin/ping:
    get:
      tags:
        - Administration
      summary: Admin service health
      description: Simple health check endpoint for admin services
      operationId: adminPing
      responses:
        '200':
          description: Admin service is healthy
          content:
            text/plain:
              schema:
                type: string
                example: "Admin service is healthy"

components:
  schemas:
    CreditCheckRequest:
      type: object
      required:
        - firstName
        - lastName
        - dateOfBirth
        - panNumber
        - idType
        - phoneNumber
        - loanPurpose
      properties:
        firstName:
          type: string
          minLength: 2
          maxLength: 50
          pattern: '^[a-zA-Z\s]+$'
          description: First name of the borrower
          example: "John"
        lastName:
          type: string
          minLength: 2
          maxLength: 50
          pattern: '^[a-zA-Z\s]+$'
          description: Last name of the borrower
          example: "Doe"
        dateOfBirth:
          type: string
          pattern: '^[0-9]{8}$'
          description: Date of birth in DDMMYYYY format
          example: "********"
        panNumber:
          type: string
          pattern: '^[A-Z]{5}[0-9]{4}[A-Z]{1}$'
          description: PAN number in valid format
          example: "**********"
        idType:
          type: string
          description: Type of identification document
          example: "PAN_CARD"
        phoneNumber:
          type: string
          pattern: '^[0-9]{10}$'
          description: 10-digit phone number
          example: "**********"
        loanPurpose:
          type: string
          description: Purpose of the loan
          example: "PERSONAL_LOAN"

    CreditCheckResponse:
      type: object
      properties:
        referenceNumber:
          type: string
          description: Unique reference number for the credit check
          example: "REF123456789"
        fullName:
          type: string
          description: Full name of the borrower
          example: "John Doe"
        dateOfBirth:
          type: string
          description: Date of birth
          example: "********"
        panNumber:
          type: string
          description: PAN number (masked for security)
          example: "ABCDE****F"
        phoneNumber:
          type: string
          description: Phone number (masked for security)
          example: "98765*****"
        creditScore:
          type: integer
          minimum: 300
          maximum: 900
          description: Credit score from bureau
          example: 750
        riskLevel:
          type: string
          enum: [LOW_RISK, MEDIUM_RISK, HIGH_RISK, UNKNOWN]
          description: Risk level assessment
          example: "LOW_RISK"
        recommendation:
          type: string
          description: Loan recommendation
          example: "APPROVE"
        message:
          type: string
          description: Descriptive message about the result
          example: "Borrower is eligible for loan"
        status:
          type: string
          enum: [INITIATED, VALIDATION_PASSED, VALIDATION_FAILED, BUREAU_REQUEST_SENT, BUREAU_RESPONSE_RECEIVED, BUREAU_REQUEST_FAILED, ANALYSIS_COMPLETED, ELIGIBLE, NOT_ELIGIBLE, INCOMPLETE_RESPONSE, BUREAU_UNAVAILABLE, ERROR]
          description: Processing status
          example: "ELIGIBLE"
        creditSummary:
          $ref: '#/components/schemas/CreditSummaryDto'
        eligible:
          type: boolean
          description: Whether the borrower is eligible for loan
          example: true

    CreditSummaryDto:
      type: object
      properties:
        totalAccounts:
          type: integer
          description: Total number of credit accounts
          example: 5
        activeAccounts:
          type: integer
          description: Number of active credit accounts
          example: 3
        defaultsCount:
          type: integer
          description: Number of defaults
          example: 0
        recentInquiries:
          type: integer
          description: Number of recent credit inquiries
          example: 2

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: Error type
          example: "Bad Request"
        message:
          type: string
          description: Error message
          example: "Validation failed"
        status:
          type: integer
          description: HTTP status code
          example: 400
        path:
          type: string
          description: Request path that caused the error
          example: "/api/v1/credit-check"
        referenceNumber:
          type: string
          nullable: true
          description: Reference number if available
          example: "REF123456789"
        details:
          type: array
          items:
            type: string
          description: Detailed error messages
          example: ["PAN number must be in valid format"]
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
          example: "2024-01-15 10:30:00"

    HealthResponse:
      type: object
      properties:
        status:
          type: string
          description: Service status
          example: "UP"
        service:
          type: string
          description: Service name
          example: "Cibil Bureau Original"
        timestamp:
          type: string
          format: date-time
          description: Health check timestamp
          example: "2024-01-15T10:30:00"
        version:
          type: string
          description: Service version
          example: "1.0.0"

    AuditSummary:
      type: object
      properties:
        totalRequests:
          type: integer
          description: Total number of credit check requests processed
          example: 1250
        successfulRequests:
          type: integer
          description: Number of successful requests
          example: 1180
        failedRequests:
          type: integer
          description: Number of failed requests
          example: 70
        averageProcessingTime:
          type: number
          format: double
          description: Average processing time in seconds
          example: 2.5
        lastProcessedAt:
          type: string
          format: date-time
          description: Timestamp of last processed request
          example: "2024-01-15T10:30:00"

    DataRetentionResult:
      type: object
      properties:
        recordsDeleted:
          type: integer
          description: Number of records deleted during cleanup
          example: 150
        spaceSavedMB:
          type: number
          format: double
          description: Space saved in megabytes
          example: 25.6
        executionTimeMs:
          type: integer
          description: Execution time in milliseconds
          example: 1200
        status:
          type: string
          enum: [COMPLETED, FAILED, IN_PROGRESS]
          description: Cleanup operation status
          example: "COMPLETED"

tags:
  - name: Credit Check
    description: Credit check operations for loan eligibility assessment
  - name: Health Check
    description: Service health monitoring endpoints
  - name: Administration
    description: Administrative operations for monitoring and data management
