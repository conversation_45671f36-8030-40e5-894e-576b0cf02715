server:
  port: ${SERVER_PORT:8107}
  servlet:
    context-path: /api/cibil-bureau

spring:
  application:
    name: cibil-bureau-credit-check-service

  datasource:
    url: *********************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true

  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# CIBIL Bureau API Configuration
cibil:
  bureau:
    api:
      base-url: http://localhost:8207/api/cibil-bureau/internal
      timeout: 30000
      retry:
        max-attempts: 3
        delay: 1000
        multiplier: 2.0
      endpoints:
        credit-report: /v1/cibil-bureau/mock/credit-check

# Logging Configuration
logging:
  level:
    com.cibil.bureau: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: logs/cibil-bureau-service.log
    max-size: 10MB
    max-history: 30

# Management and Actuator
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# OpenAPI Documentation Configuration
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    try-it-out-enabled: true
    operations-sorter: method
    tags-sorter: alpha
    doc-expansion: none
    display-request-duration: true
  show-actuator: true
  default-consumes-media-type: application/json
  default-produces-media-type: application/json

# Application specific configuration
app:
  credit-check:
    minimum-score: 650
    data-retention:
      days: 90
    validation:
      pan-pattern: "[A-Z]{5}[0-9]{4}[A-Z]{1}"
      phone-pattern: "[0-9]{10}"
      name-pattern: "[a-zA-Z\\s]{2,50}"
    audit:
      mask-pii: true
      log-requests: true
      log-responses: true
