spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
  
  flyway:
    enabled: false

# Test configuration for CIBIL Bureau API
cibil:
  bureau:
    api:
      base-url: http://localhost:8207/api/cibil-bureau/internal
      timeout: 5000
      retry:
        max-attempts: 2
        delay: 500
        multiplier: 1.5
      endpoints:
        credit-report: /v1/cibil-bureau/mock/credit-check

logging:
  level:
    com.cibil.bureau: DEBUG
    org.springframework.web: WARN
    org.hibernate.SQL: DEBUG
