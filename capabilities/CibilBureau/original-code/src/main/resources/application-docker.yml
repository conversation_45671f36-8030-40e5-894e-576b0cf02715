server:
  port: 8080

spring:
  application:
    name: cibil-bureau-credit-check-service
  
  datasource:
    url: ${SPRING_DATASOURCE_URL:********************************************}
    username: ${SPRING_DATASOURCE_USERNAME:cibil_user}
    password: ${SPRING_DATASOURCE_PASSWORD:cibil_password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true

# CIBIL Bureau API Configuration
cibil:
  bureau:
    api:
      base-url: ${CIBIL_BUREAU_API_BASE_URL:http://mock-cibil-api:8080}
      timeout: 30000
      retry:
        max-attempts: 3
        delay: 1000
        multiplier: 2.0

# Logging Configuration
logging:
  level:
    com.cibil.bureau: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: /app/logs/cibil-bureau-service.log
    max-size: 10MB
    max-history: 30

# Management and Actuator
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
