-- Remove unique constraint on reference_number from audit_log table
-- This allows multiple audit entries for the same reference number (different operations)

-- Drop the existing unique constraint
ALTER TABLE audit_log DROP CONSTRAINT IF EXISTS audit_log_reference_number_key;

-- The reference_number should still be indexed for performance but not unique
-- The index already exists from V1 migration, so no need to recreate it

-- Add a comment to clarify the change
COMMENT ON COLUMN audit_log.reference_number IS 'Reference number for tracking (allows multiple entries per reference for different operations)';
