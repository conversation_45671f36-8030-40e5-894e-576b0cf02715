-- Create audit log table
CREATE TABLE audit_log (
    id BIGSERIAL PRIMARY KEY,
    reference_number VARCHAR(50) NOT NULL UNIQUE,
    operation_type VARCHAR(50) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id VARCHAR(100),
    request_data TEXT,
    response_data TEXT,
    status VARCHAR(20) NOT NULL,
    error_message TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM'
);

-- Create borrower information table
CREATE TABLE borrower_info (
    id BIGSERIAL PRIMARY KEY,
    reference_number VARCHAR(50) NOT NULL UNIQUE,
    first_name VA<PERSON>HA<PERSON>(50) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(50) NOT NULL,
    date_of_birth DATE NOT NULL,
    pan_number VARCHAR(10) NOT NULL,
    id_type VARCHAR(20) NOT NULL DEFAULT 'PAN_CARD',
    phone_number VARCHAR(15) NOT NULL,
    loan_purpose VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create credit report table
CREATE TABLE credit_report (
    id BIGSERIAL PRIMARY KEY,
    reference_number VARCHAR(50) NOT NULL UNIQUE,
    borrower_id BIGINT NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    date_of_birth DATE NOT NULL,
    pan_number VARCHAR(10) NOT NULL,
    phone_number VARCHAR(15) NOT NULL,
    credit_score INTEGER,
    risk_level VARCHAR(20),
    recommendation VARCHAR(50),
    bureau_response TEXT,
    bureau_message TEXT,
    processing_status VARCHAR(20) NOT NULL,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (borrower_id) REFERENCES borrower_info(id)
);

-- Create credit summary table
CREATE TABLE credit_summary (
    id BIGSERIAL PRIMARY KEY,
    credit_report_id BIGINT NOT NULL,
    total_accounts INTEGER DEFAULT 0,
    active_accounts INTEGER DEFAULT 0,
    defaults_count INTEGER DEFAULT 0,
    recent_inquiries INTEGER DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (credit_report_id) REFERENCES credit_report(id)
);

-- Create indexes for performance
CREATE INDEX idx_borrower_info_reference_number ON borrower_info(reference_number);
CREATE INDEX idx_borrower_info_pan_number ON borrower_info(pan_number);
CREATE INDEX idx_borrower_info_created_at ON borrower_info(created_at);

CREATE INDEX idx_credit_report_reference_number ON credit_report(reference_number);
CREATE INDEX idx_credit_report_borrower_id ON credit_report(borrower_id);
CREATE INDEX idx_credit_report_pan_number ON credit_report(pan_number);
CREATE INDEX idx_credit_report_created_at ON credit_report(created_at);

CREATE INDEX idx_audit_log_reference_number ON audit_log(reference_number);
CREATE INDEX idx_audit_log_created_at ON audit_log(created_at);
CREATE INDEX idx_audit_log_operation_type ON audit_log(operation_type);

-- Add comments for documentation
COMMENT ON TABLE borrower_info IS 'Stores validated borrower information for credit check requests';
COMMENT ON TABLE credit_report IS 'Stores credit reports received from CIBIL bureau';
COMMENT ON TABLE credit_summary IS 'Stores summarized credit information';
COMMENT ON TABLE audit_log IS 'Stores audit trail for all operations with masked PII data';

COMMENT ON COLUMN borrower_info.reference_number IS 'Unique reference number for tracking';
COMMENT ON COLUMN credit_report.bureau_response IS 'Full response from CIBIL bureau (JSON)';
COMMENT ON COLUMN audit_log.request_data IS 'Masked request data for audit purposes';
COMMENT ON COLUMN audit_log.response_data IS 'Masked response data for audit purposes';
