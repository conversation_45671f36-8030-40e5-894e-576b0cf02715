-- Fix processing_status column length in credit_report table
-- The BUREAU_REQUEST_FAILED status is 21 characters but column is only 20

-- Update credit_report table processing_status column length
ALTER TABLE credit_report ALTER COLUMN processing_status TYPE VARCHAR(30);

-- Update borrower_info table status column length for consistency
ALTER TABLE borrower_info ALTER COLUMN status TYPE VARCHAR(30);

-- Add comments to clarify the change
COMMENT ON COLUMN credit_report.processing_status IS 'Processing status (increased to 30 chars to accommodate BUREAU_REQUEST_FAILED)';
COMMENT ON COLUMN borrower_info.status IS 'Borrower status (increased to 30 chars for consistency)';
