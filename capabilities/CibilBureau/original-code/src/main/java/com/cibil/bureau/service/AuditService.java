package com.cibil.bureau.service;

import com.cibil.bureau.config.ApplicationProperties;
import com.cibil.bureau.entity.AuditLog;
import com.cibil.bureau.enums.OperationType;
import com.cibil.bureau.repository.AuditLogRepository;
import com.cibil.bureau.util.DataMaskingUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service for audit logging with masked PII data.
 */
@Service
public class AuditService {

    private static final Logger logger = LoggerFactory.getLogger(AuditService.class);

    private final AuditLogRepository auditLogRepository;
    private final DataMaskingUtil dataMaskingUtil;
    private final ApplicationProperties applicationProperties;

    public AuditService(AuditLogRepository auditLogRepository,
                       DataMaskingUtil dataMaskingUtil,
                       ApplicationProperties applicationProperties) {
        this.auditLogRepository = auditLogRepository;
        this.dataMaskingUtil = dataMaskingUtil;
        this.applicationProperties = applicationProperties;
    }

    /**
     * Logs an operation with request and response data.
     * 
     * @param referenceNumber the reference number
     * @param operationType the operation type
     * @param entityType the entity type
     * @param entityId the entity ID
     * @param requestData the request data
     * @param responseData the response data
     * @param status the operation status
     */
    @Async
    @Transactional
    public void logOperation(String referenceNumber, OperationType operationType, String entityType,
                           String entityId, Object requestData, Object responseData, String status) {
        try {
            String maskedRequestData = null;
            String maskedResponseData = null;

            if (applicationProperties.getAudit().isLogRequests() && requestData != null) {
                maskedRequestData = applicationProperties.getAudit().isMaskPii() 
                    ? dataMaskingUtil.maskObject(requestData)
                    : requestData.toString();
            }

            if (applicationProperties.getAudit().isLogResponses() && responseData != null) {
                maskedResponseData = applicationProperties.getAudit().isMaskPii()
                    ? dataMaskingUtil.maskObject(responseData)
                    : responseData.toString();
            }

            AuditLog auditLog = new AuditLog(referenceNumber, operationType, entityType, entityId,
                                           maskedRequestData, maskedResponseData, status);
            
            auditLogRepository.save(auditLog);
            
            logger.debug("Audit log created for reference: {} - Operation: {} - Status: {}", 
                        referenceNumber, operationType, status);
        } catch (Exception e) {
            logger.error("Failed to create audit log for reference: {} - Operation: {}", 
                        referenceNumber, operationType, e);
        }
    }

    /**
     * Logs an error operation.
     * 
     * @param referenceNumber the reference number
     * @param operationType the operation type
     * @param entityType the entity type
     * @param entityId the entity ID
     * @param status the operation status
     * @param errorMessage the error message
     */
    @Async
    @Transactional
    public void logError(String referenceNumber, OperationType operationType, String entityType,
                        String entityId, String status, String errorMessage) {
        try {
            AuditLog auditLog = new AuditLog(referenceNumber, operationType, entityType, entityId, status, errorMessage);
            auditLogRepository.save(auditLog);
            
            logger.debug("Error audit log created for reference: {} - Operation: {} - Status: {} - Error: {}", 
                        referenceNumber, operationType, status, errorMessage);
        } catch (Exception e) {
            logger.error("Failed to create error audit log for reference: {} - Operation: {}", 
                        referenceNumber, operationType, e);
        }
    }

    /**
     * Logs request initiation.
     * 
     * @param referenceNumber the reference number
     * @param operationType the operation type
     * @param requestData the request data
     */
    public void logRequestInitiation(String referenceNumber, OperationType operationType, Object requestData) {
        logOperation(referenceNumber, operationType, "REQUEST", referenceNumber, requestData, null, "INITIATED");
    }

    /**
     * Logs successful completion.
     * 
     * @param referenceNumber the reference number
     * @param operationType the operation type
     * @param responseData the response data
     */
    public void logSuccessfulCompletion(String referenceNumber, OperationType operationType, Object responseData) {
        logOperation(referenceNumber, operationType, "RESPONSE", referenceNumber, null, responseData, "SUCCESS");
    }

    /**
     * Logs failure.
     * 
     * @param referenceNumber the reference number
     * @param operationType the operation type
     * @param errorMessage the error message
     */
    public void logFailure(String referenceNumber, OperationType operationType, String errorMessage) {
        logError(referenceNumber, operationType, "ERROR", referenceNumber, "FAILED", errorMessage);
    }

    /**
     * Logs retry attempt.
     * 
     * @param referenceNumber the reference number
     * @param operationType the operation type
     * @param attemptNumber the attempt number
     */
    public void logRetryAttempt(String referenceNumber, OperationType operationType, int attemptNumber) {
        logError(referenceNumber, OperationType.RETRY_ATTEMPT, "RETRY", referenceNumber, 
                "RETRY_" + attemptNumber, "Retry attempt " + attemptNumber + " for " + operationType);
    }

    /**
     * Gets audit logs by reference number.
     * 
     * @param referenceNumber the reference number
     * @return list of audit logs
     */
    @Transactional(readOnly = true)
    public List<AuditLog> getAuditLogsByReference(String referenceNumber) {
        return auditLogRepository.findByReferenceNumber(referenceNumber);
    }

    /**
     * Gets audit logs by operation type.
     * 
     * @param operationType the operation type
     * @return list of audit logs
     */
    @Transactional(readOnly = true)
    public List<AuditLog> getAuditLogsByOperationType(OperationType operationType) {
        return auditLogRepository.findByOperationType(operationType);
    }

    /**
     * Gets error audit logs.
     * 
     * @return list of error audit logs
     */
    @Transactional(readOnly = true)
    public List<AuditLog> getErrorLogs() {
        return auditLogRepository.findErrorLogs();
    }

    /**
     * Cleans up old audit logs based on data retention policy.
     * 
     * @return number of deleted records
     */
    @Transactional
    public int cleanupOldAuditLogs() {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(applicationProperties.getDataRetention().getDays());
        int deletedCount = auditLogRepository.deleteByCreatedAtBefore(cutoffDate);
        
        logger.info("Cleaned up {} old audit logs before {}", deletedCount, cutoffDate);
        return deletedCount;
    }

    /**
     * Gets audit log statistics.
     * 
     * @return audit log statistics
     */
    @Transactional(readOnly = true)
    public AuditStatistics getAuditStatistics() {
        long totalLogs = auditLogRepository.count();
        long creditCheckLogs = auditLogRepository.countByOperationType(OperationType.CREDIT_CHECK_REQUEST);
        long bureauApiLogs = auditLogRepository.countByOperationType(OperationType.BUREAU_API_CALL);
        long errorLogs = auditLogRepository.findErrorLogs().size();
        
        return new AuditStatistics(totalLogs, creditCheckLogs, bureauApiLogs, errorLogs);
    }

    /**
     * Inner class for audit statistics.
     */
    public static class AuditStatistics {
        private final long totalLogs;
        private final long creditCheckLogs;
        private final long bureauApiLogs;
        private final long errorLogs;

        public AuditStatistics(long totalLogs, long creditCheckLogs, long bureauApiLogs, long errorLogs) {
            this.totalLogs = totalLogs;
            this.creditCheckLogs = creditCheckLogs;
            this.bureauApiLogs = bureauApiLogs;
            this.errorLogs = errorLogs;
        }

        public long getTotalLogs() {
            return totalLogs;
        }

        public long getCreditCheckLogs() {
            return creditCheckLogs;
        }

        public long getBureauApiLogs() {
            return bureauApiLogs;
        }

        public long getErrorLogs() {
            return errorLogs;
        }

        @Override
        public String toString() {
            return "AuditStatistics{" +
                    "totalLogs=" + totalLogs +
                    ", creditCheckLogs=" + creditCheckLogs +
                    ", bureauApiLogs=" + bureauApiLogs +
                    ", errorLogs=" + errorLogs +
                    '}';
        }
    }
}
