package com.cibil.bureau.entity;

import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * Entity representing credit summary information.
 */
@Entity
@Table(name = "credit_summary")
@EntityListeners(AuditingEntityListener.class)
public class CreditSummary {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "credit_report_id", nullable = false)
    private CreditReport creditReport;

    @Column(name = "total_accounts")
    private Integer totalAccounts = 0;

    @Column(name = "active_accounts")
    private Integer activeAccounts = 0;

    @Column(name = "defaults_count")
    private Integer defaultsCount = 0;

    @Column(name = "recent_inquiries")
    private Integer recentInquiries = 0;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    // Default constructor
    public CreditSummary() {
    }

    // Constructor with required fields
    public CreditSummary(CreditReport creditReport, Integer totalAccounts, Integer activeAccounts,
                        Integer defaultsCount, Integer recentInquiries) {
        this.creditReport = creditReport;
        this.totalAccounts = totalAccounts;
        this.activeAccounts = activeAccounts;
        this.defaultsCount = defaultsCount;
        this.recentInquiries = recentInquiries;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public CreditReport getCreditReport() {
        return creditReport;
    }

    public void setCreditReport(CreditReport creditReport) {
        this.creditReport = creditReport;
    }

    public Integer getTotalAccounts() {
        return totalAccounts;
    }

    public void setTotalAccounts(Integer totalAccounts) {
        this.totalAccounts = totalAccounts;
    }

    public Integer getActiveAccounts() {
        return activeAccounts;
    }

    public void setActiveAccounts(Integer activeAccounts) {
        this.activeAccounts = activeAccounts;
    }

    public Integer getDefaultsCount() {
        return defaultsCount;
    }

    public void setDefaultsCount(Integer defaultsCount) {
        this.defaultsCount = defaultsCount;
    }

    public Integer getRecentInquiries() {
        return recentInquiries;
    }

    public void setRecentInquiries(Integer recentInquiries) {
        this.recentInquiries = recentInquiries;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "CreditSummary{" +
                "id=" + id +
                ", totalAccounts=" + totalAccounts +
                ", activeAccounts=" + activeAccounts +
                ", defaultsCount=" + defaultsCount +
                ", recentInquiries=" + recentInquiries +
                ", createdAt=" + createdAt +
                '}';
    }
}
