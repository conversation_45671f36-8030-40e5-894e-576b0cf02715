package com.cibil.bureau.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import com.cibil.bureau.enums.ProcessingStatus;
import com.cibil.bureau.enums.RiskLevel;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

/**
 * Entity representing credit reports received from CIBIL bureau.
 */
@Entity
@Table(name = "credit_report")
@EntityListeners(AuditingEntityListener.class)
public class CreditReport {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "reference_number", nullable = false, unique = true, length = 50)
    private String referenceNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "borrower_id", nullable = false)
    private BorrowerInfo borrower;

    @Column(name = "full_name", nullable = false, length = 100)
    private String fullName;

    @Column(name = "date_of_birth", nullable = false)
    private LocalDate dateOfBirth;

    @Column(name = "pan_number", nullable = false, length = 10)
    private String panNumber;

    @Column(name = "phone_number", nullable = false, length = 15)
    private String phoneNumber;

    @Column(name = "credit_score")
    private Integer creditScore;

    @Enumerated(EnumType.STRING)
    @Column(name = "risk_level", length = 20)
    private RiskLevel riskLevel;

    @Column(name = "recommendation", length = 50)
    private String recommendation;

    @Column(name = "bureau_response", columnDefinition = "TEXT")
    private String bureauResponse;

    @Column(name = "bureau_message", columnDefinition = "TEXT")
    private String bureauMessage;

    @Enumerated(EnumType.STRING)
    @Column(name = "processing_status", nullable = false, length = 30)
    private ProcessingStatus processingStatus;

    @Column(name = "retry_count", nullable = false)
    private Integer retryCount = 0;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Default constructor
    public CreditReport() {
    }

    // Constructor with required fields
    public CreditReport(String referenceNumber, BorrowerInfo borrower, String fullName,
                       LocalDate dateOfBirth, String panNumber, String phoneNumber,
                       ProcessingStatus processingStatus) {
        this.referenceNumber = referenceNumber;
        this.borrower = borrower;
        this.fullName = fullName;
        this.dateOfBirth = dateOfBirth;
        this.panNumber = panNumber;
        this.phoneNumber = phoneNumber;
        this.processingStatus = processingStatus;
        this.retryCount = 0;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public BorrowerInfo getBorrower() {
        return borrower;
    }

    public void setBorrower(BorrowerInfo borrower) {
        this.borrower = borrower;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getPanNumber() {
        return panNumber;
    }

    public void setPanNumber(String panNumber) {
        this.panNumber = panNumber;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public Integer getCreditScore() {
        return creditScore;
    }

    public void setCreditScore(Integer creditScore) {
        this.creditScore = creditScore;
    }

    public RiskLevel getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(RiskLevel riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getRecommendation() {
        return recommendation;
    }

    public void setRecommendation(String recommendation) {
        this.recommendation = recommendation;
    }

    public String getBureauResponse() {
        return bureauResponse;
    }

    public void setBureauResponse(String bureauResponse) {
        this.bureauResponse = bureauResponse;
    }

    public String getBureauMessage() {
        return bureauMessage;
    }

    public void setBureauMessage(String bureauMessage) {
        this.bureauMessage = bureauMessage;
    }

    public ProcessingStatus getProcessingStatus() {
        return processingStatus;
    }

    public void setProcessingStatus(ProcessingStatus processingStatus) {
        this.processingStatus = processingStatus;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * Increments the retry count.
     */
    public void incrementRetryCount() {
        this.retryCount++;
    }

    @Override
    public String toString() {
        return "CreditReport{" +
                "id=" + id +
                ", referenceNumber='" + referenceNumber + '\'' +
                ", fullName='" + fullName + '\'' +
                ", dateOfBirth=" + dateOfBirth +
                ", panNumber='" + maskPan(panNumber) + '\'' +
                ", phoneNumber='" + maskPhone(phoneNumber) + '\'' +
                ", creditScore=" + creditScore +
                ", riskLevel=" + riskLevel +
                ", recommendation='" + recommendation + '\'' +
                ", processingStatus=" + processingStatus +
                ", retryCount=" + retryCount +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    /**
     * Masks PAN number for logging purposes.
     */
    private String maskPan(String pan) {
        if (pan == null || pan.length() < 4) {
            return "****";
        }
        return pan.substring(0, 2) + "****" + pan.substring(pan.length() - 2);
    }

    /**
     * Masks phone number for logging purposes.
     */
    private String maskPhone(String phone) {
        if (phone == null || phone.length() < 4) {
            return "****";
        }
        return "****" + phone.substring(phone.length() - 4);
    }
}
