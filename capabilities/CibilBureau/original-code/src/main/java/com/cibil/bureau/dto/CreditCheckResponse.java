package com.cibil.bureau.dto;

import com.cibil.bureau.enums.ProcessingStatus;
import com.cibil.bureau.enums.RiskLevel;

/**
 * DTO for credit check response containing the result of credit analysis.
 */
public class CreditCheckResponse {

    private String referenceNumber;
    private String fullName;
    private String dateOfBirth;
    private String panNumber;
    private String phoneNumber;
    private Integer creditScore;
    private RiskLevel riskLevel;
    private String recommendation;
    private String message;
    private ProcessingStatus status;
    private CreditSummaryDto creditSummary;
    private boolean eligible;

    // Default constructor
    public CreditCheckResponse() {
    }

    // Constructor for successful response
    public CreditCheckResponse(String referenceNumber, String fullName, String dateOfBirth,
                              String panNumber, String phoneNumber, Integer creditScore,
                              RiskLevel riskLevel, String recommendation, String message,
                              ProcessingStatus status, boolean eligible) {
        this.referenceNumber = referenceNumber;
        this.fullName = fullName;
        this.dateOfBirth = dateOfBirth;
        this.panNumber = panNumber;
        this.phoneNumber = phoneNumber;
        this.creditScore = creditScore;
        this.riskLevel = riskLevel;
        this.recommendation = recommendation;
        this.message = message;
        this.status = status;
        this.eligible = eligible;
    }

    // Constructor for error response
    public CreditCheckResponse(String referenceNumber, String message, ProcessingStatus status) {
        this.referenceNumber = referenceNumber;
        this.message = message;
        this.status = status;
        this.eligible = false;
    }

    // Getters and Setters
    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(String dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getPanNumber() {
        return panNumber;
    }

    public void setPanNumber(String panNumber) {
        this.panNumber = panNumber;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public Integer getCreditScore() {
        return creditScore;
    }

    public void setCreditScore(Integer creditScore) {
        this.creditScore = creditScore;
    }

    public RiskLevel getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(RiskLevel riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getRecommendation() {
        return recommendation;
    }

    public void setRecommendation(String recommendation) {
        this.recommendation = recommendation;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public ProcessingStatus getStatus() {
        return status;
    }

    public void setStatus(ProcessingStatus status) {
        this.status = status;
    }

    public CreditSummaryDto getCreditSummary() {
        return creditSummary;
    }

    public void setCreditSummary(CreditSummaryDto creditSummary) {
        this.creditSummary = creditSummary;
    }

    public boolean isEligible() {
        return eligible;
    }

    public void setEligible(boolean eligible) {
        this.eligible = eligible;
    }

    @Override
    public String toString() {
        return "CreditCheckResponse{" +
                "referenceNumber='" + referenceNumber + '\'' +
                ", fullName='" + fullName + '\'' +
                ", dateOfBirth='" + dateOfBirth + '\'' +
                ", panNumber='" + maskPan(panNumber) + '\'' +
                ", phoneNumber='" + maskPhone(phoneNumber) + '\'' +
                ", creditScore=" + creditScore +
                ", riskLevel=" + riskLevel +
                ", recommendation='" + recommendation + '\'' +
                ", message='" + message + '\'' +
                ", status=" + status +
                ", eligible=" + eligible +
                '}';
    }

    /**
     * Masks PAN number for logging purposes.
     */
    private String maskPan(String pan) {
        if (pan == null || pan.length() < 4) {
            return "****";
        }
        return pan.substring(0, 2) + "****" + pan.substring(pan.length() - 2);
    }

    /**
     * Masks phone number for logging purposes.
     */
    private String maskPhone(String phone) {
        if (phone == null || phone.length() < 4) {
            return "****";
        }
        return "****" + phone.substring(phone.length() - 4);
    }
}
