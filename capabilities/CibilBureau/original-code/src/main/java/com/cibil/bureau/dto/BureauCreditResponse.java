package com.cibil.bureau.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * DTO for CIBIL Bureau API credit response.
 */
public class BureauCreditResponse {

    @JsonProperty("referenceId")
    private String referenceId;

    @JsonProperty("fullName")
    private String fullName;

    @JsonProperty("cibilScore")
    private Integer cibilScore;

    @JsonProperty("riskLevel")
    private String riskLevel;

    @JsonProperty("recommendation")
    private String recommendation;

    @JsonProperty("accountSummary")
    private String accountSummary;

    @JsonProperty("recentInquiries")
    private Integer recentInquiries;

    @JsonProperty("isEligible")
    private Boolean isEligible;

    @JsonProperty("message")
    private String message;

    @JsonProperty("outcomeStatus")
    private String outcomeStatus;

    @JsonProperty("dateOfBirth")
    private String dateOfBirth;

    // Default constructor
    public BureauCreditResponse() {
    }

    // Getters and Setters
    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public Integer getCibilScore() {
        return cibilScore;
    }

    public void setCibilScore(Integer cibilScore) {
        this.cibilScore = cibilScore;
    }

    // For backward compatibility with existing code that expects getCreditScore()
    public Integer getCreditScore() {
        return cibilScore;
    }

    public void setCreditScore(Integer creditScore) {
        this.cibilScore = creditScore;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getRecommendation() {
        return recommendation;
    }

    public void setRecommendation(String recommendation) {
        this.recommendation = recommendation;
    }

    public String getAccountSummary() {
        return accountSummary;
    }

    public void setAccountSummary(String accountSummary) {
        this.accountSummary = accountSummary;
    }

    public Integer getRecentInquiries() {
        return recentInquiries;
    }

    public void setRecentInquiries(Integer recentInquiries) {
        this.recentInquiries = recentInquiries;
    }

    public Boolean getIsEligible() {
        return isEligible;
    }

    public void setIsEligible(Boolean isEligible) {
        this.isEligible = isEligible;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getOutcomeStatus() {
        return outcomeStatus;
    }

    public void setOutcomeStatus(String outcomeStatus) {
        this.outcomeStatus = outcomeStatus;
    }

    public String getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(String dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    // For backward compatibility - create a CreditSummaryDto from accountSummary
    public CreditSummaryDto getCreditSummary() {
        if (accountSummary != null && recentInquiries != null) {
            // Parse the accountSummary string to extract values
            // Format: "Total accounts: 5, Active: 4, Overdue: 0"
            try {
                String[] parts = accountSummary.split(",");
                int totalAccounts = 0;
                int activeAccounts = 0;
                int overdueAccounts = 0;

                for (String part : parts) {
                    part = part.trim();
                    if (part.startsWith("Total accounts:")) {
                        totalAccounts = Integer.parseInt(part.split(":")[1].trim());
                    } else if (part.startsWith("Active:")) {
                        activeAccounts = Integer.parseInt(part.split(":")[1].trim());
                    } else if (part.startsWith("Overdue:")) {
                        overdueAccounts = Integer.parseInt(part.split(":")[1].trim());
                    }
                }

                return new CreditSummaryDto(totalAccounts, activeAccounts, overdueAccounts, recentInquiries);
            } catch (Exception e) {
                // If parsing fails, return null
                return null;
            }
        }
        return null;
    }

    /**
     * Validates if the response contains all required fields.
     *
     * @return true if all required fields are present
     */
    public boolean isComplete() {
        return fullName != null &&
               dateOfBirth != null &&
               cibilScore != null &&
               outcomeStatus != null;
    }

    @Override
    public String toString() {
        return "BureauCreditResponse{" +
                "referenceId='" + referenceId + '\'' +
                ", fullName='" + fullName + '\'' +
                ", cibilScore=" + cibilScore +
                ", riskLevel='" + riskLevel + '\'' +
                ", recommendation='" + recommendation + '\'' +
                ", accountSummary='" + accountSummary + '\'' +
                ", recentInquiries=" + recentInquiries +
                ", isEligible=" + isEligible +
                ", message='" + message + '\'' +
                ", outcomeStatus='" + outcomeStatus + '\'' +
                ", dateOfBirth='" + dateOfBirth + '\'' +
                '}';
    }

}
