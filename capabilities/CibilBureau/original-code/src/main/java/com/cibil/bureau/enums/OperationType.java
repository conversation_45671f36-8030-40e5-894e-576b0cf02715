package com.cibil.bureau.enums;

/**
 * Enum representing different types of operations for audit logging.
 */
public enum OperationType {
    CREDIT_CHECK_REQUEST("Credit Check Request"),
    BORROWER_VALIDATION("Borrower Validation"),
    BUREAU_API_CALL("Bureau API Call"),
    CREDIT_ANALYSIS("Credit Analysis"),
    DECISION_MADE("Decision Made"),
    ERROR_OCCURRED("Error Occurred"),
    RETRY_ATTEMPT("Retry Attempt");

    private final String displayName;

    OperationType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
