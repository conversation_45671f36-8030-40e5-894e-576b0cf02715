package com.cibil.bureau.service;

import java.time.LocalDate;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.cibil.bureau.dto.BureauCreditRequest;
import com.cibil.bureau.dto.BureauCreditResponse;
import com.cibil.bureau.dto.CreditCheckRequest;
import com.cibil.bureau.dto.CreditCheckResponse;
import com.cibil.bureau.dto.CreditSummaryDto;
import com.cibil.bureau.entity.BorrowerInfo;
import com.cibil.bureau.entity.CreditReport;
import com.cibil.bureau.entity.CreditSummary;
import com.cibil.bureau.enums.IdType;
import com.cibil.bureau.enums.LoanPurpose;
import com.cibil.bureau.enums.OperationType;
import com.cibil.bureau.enums.ProcessingStatus;
import com.cibil.bureau.enums.RiskLevel;
import com.cibil.bureau.exception.BureauApiException;
import com.cibil.bureau.exception.CreditCheckException;
import com.cibil.bureau.exception.ValidationException;
import com.cibil.bureau.repository.BorrowerInfoRepository;
import com.cibil.bureau.repository.CreditReportRepository;
import com.cibil.bureau.repository.CreditSummaryRepository;
import com.cibil.bureau.util.ReferenceNumberGenerator;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Main service for orchestrating the end-to-end credit check workflow.
 */
@Service
public class CreditCheckService {

    private static final Logger logger = LoggerFactory.getLogger(CreditCheckService.class);

    private final ValidationService validationService;
    private final CibilBureauApiService cibilBureauApiService;
    private final CreditAnalysisService creditAnalysisService;
    private final AuditService auditService;
    private final ReferenceNumberGenerator referenceNumberGenerator;
    private final BorrowerInfoRepository borrowerInfoRepository;
    private final CreditReportRepository creditReportRepository;
    private final CreditSummaryRepository creditSummaryRepository;
    private final ObjectMapper objectMapper;

    public CreditCheckService(ValidationService validationService,
                             CibilBureauApiService cibilBureauApiService,
                             CreditAnalysisService creditAnalysisService,
                             AuditService auditService,
                             ReferenceNumberGenerator referenceNumberGenerator,
                             BorrowerInfoRepository borrowerInfoRepository,
                             CreditReportRepository creditReportRepository,
                             CreditSummaryRepository creditSummaryRepository,
                             ObjectMapper objectMapper) {
        this.validationService = validationService;
        this.cibilBureauApiService = cibilBureauApiService;
        this.creditAnalysisService = creditAnalysisService;
        this.auditService = auditService;
        this.referenceNumberGenerator = referenceNumberGenerator;
        this.borrowerInfoRepository = borrowerInfoRepository;
        this.creditReportRepository = creditReportRepository;
        this.creditSummaryRepository = creditSummaryRepository;
        this.objectMapper = objectMapper;
    }

    /**
     * Processes credit check request through the complete workflow.
     *
     * @param request the credit check request
     * @return the credit check response
     */
    @Transactional
    public CreditCheckResponse processCreditCheck(CreditCheckRequest request) {
        String referenceNumber = referenceNumberGenerator.generateCreditCheckReference();

        // Set trace ID in MDC for logging
        MDC.put("traceId", referenceNumber);

        logger.info("Starting credit check process for reference: {}", referenceNumber);

        try {
            // Log request initiation
            auditService.logRequestInitiation(referenceNumber, OperationType.CREDIT_CHECK_REQUEST, request);

            // Phase 1: Collect and validate borrower information
            BorrowerInfo borrowerInfo = validateAndSaveBorrowerInfo(request, referenceNumber);

            // Phase 2: Request and receive credit report from CIBIL
            CreditReport creditReport = fetchAndSaveCreditReport(borrowerInfo, referenceNumber);

            // Phase 3: Analyze bureau response and make lending decision
            CreditCheckResponse response = analyzeCreditAndMakeDecision(creditReport, referenceNumber);

            // Update final status
            updateFinalStatus(borrowerInfo, creditReport, response);

            logger.info("Credit check process completed successfully for reference: {} - Eligible: {}",
                       referenceNumber, response.isEligible());

            auditService.logSuccessfulCompletion(referenceNumber, OperationType.CREDIT_CHECK_REQUEST, response);

            return response;

        } catch (ValidationException e) {
            logger.warn("Validation failed for reference: {} - Error: {}", referenceNumber, e.getMessage());
            auditService.logFailure(referenceNumber, OperationType.BORROWER_VALIDATION, e.getMessage());
            throw e; // Let GlobalExceptionHandler handle this

        } catch (BureauApiException e) {
            logger.error("Bureau API failed for reference: {} - Error: {}", referenceNumber, e.getMessage());
            // We need to get the credit report to include borrower information in the response
            CreditReport creditReport = creditReportRepository.findByReferenceNumber(referenceNumber)
                    .orElseThrow(() -> new CreditCheckException("Credit report not found for reference: " + referenceNumber, referenceNumber));
            return creditAnalysisService.createBureauUnavailableResponse(creditReport);

        } catch (CreditCheckException e) {
            logger.error("Credit check failed for reference: {} - Error: {}", referenceNumber, e.getMessage());
            auditService.logFailure(referenceNumber, OperationType.CREDIT_CHECK_REQUEST, e.getMessage());
            return new CreditCheckResponse(referenceNumber, e.getMessage(), ProcessingStatus.ERROR);

        } catch (Exception e) {
            logger.error("Unexpected error during credit check for reference: {}", referenceNumber, e);
            auditService.logFailure(referenceNumber, OperationType.CREDIT_CHECK_REQUEST, "Unexpected error: " + e.getMessage());
            return new CreditCheckResponse(referenceNumber, "An unexpected error occurred. Please try again later.", ProcessingStatus.ERROR);

        } finally {
            MDC.remove("traceId");
        }
    }

    /**
     * Phase 1: Validates and saves borrower information.
     */
    private BorrowerInfo validateAndSaveBorrowerInfo(CreditCheckRequest request, String referenceNumber) {
        logger.debug("Phase 1: Validating borrower information for reference: {}", referenceNumber);

        // Validate request
        validationService.validateCreditCheckRequest(request, referenceNumber);

        // Create and save borrower info
        LocalDate dateOfBirth = validationService.parseDateOfBirth(request.getDateOfBirth());

        BorrowerInfo borrowerInfo = new BorrowerInfo(
                referenceNumber,
                request.getFirstName().trim(),
                request.getLastName().trim(),
                dateOfBirth,
                request.getPanNumber().trim().toUpperCase(),
                IdType.fromDisplayName(request.getIdType()),
                request.getPhoneNumber().trim(),
                LoanPurpose.fromDisplayName(request.getLoanPurpose()),
                ProcessingStatus.VALIDATION_PASSED
        );

        borrowerInfo = borrowerInfoRepository.save(borrowerInfo);

        logger.debug("Borrower information validated and saved for reference: {}", referenceNumber);
        auditService.logSuccessfulCompletion(referenceNumber, OperationType.BORROWER_VALIDATION, borrowerInfo);

        return borrowerInfo;
    }

    /**
     * Phase 2: Fetches credit report from CIBIL bureau.
     */
    private CreditReport fetchAndSaveCreditReport(BorrowerInfo borrowerInfo, String referenceNumber) {
        logger.debug("Phase 2: Fetching credit report for reference: {}", referenceNumber);

        // Create credit report entity
        CreditReport creditReport = new CreditReport(
                referenceNumber,
                borrowerInfo,
                borrowerInfo.getFullName(),
                borrowerInfo.getDateOfBirth(),
                borrowerInfo.getPanNumber(),
                borrowerInfo.getPhoneNumber(),
                ProcessingStatus.BUREAU_REQUEST_SENT
        );

        creditReport = creditReportRepository.save(creditReport);

        try {
            // Prepare bureau request
            BureauCreditRequest bureauRequest = new BureauCreditRequest(
                    borrowerInfo.getFirstName(),
                    borrowerInfo.getLastName(),
                    validationService.formatDateOfBirth(borrowerInfo.getDateOfBirth()),
                    borrowerInfo.getPanNumber(),
                    borrowerInfo.getIdType().getDisplayName(),
                    borrowerInfo.getPhoneNumber(),
                    borrowerInfo.getLoanPurpose().getDisplayName()
            );

            // Call CIBIL Bureau API
            BureauCreditResponse bureauResponse = cibilBureauApiService.fetchCreditReport(bureauRequest, referenceNumber);

            // Update credit report with bureau response
            updateCreditReportWithBureauResponse(creditReport, bureauResponse);

            logger.debug("Credit report fetched and saved for reference: {}", referenceNumber);
            return creditReport;

        } catch (BureauApiException e) {
            creditReport.setProcessingStatus(ProcessingStatus.BUREAU_REQUEST_FAILED);
            creditReport.setBureauMessage(e.getMessage());
            creditReport.incrementRetryCount();
            creditReportRepository.save(creditReport);
            throw e;
        }
    }

    /**
     * Updates credit report with bureau response data.
     */
    private void updateCreditReportWithBureauResponse(CreditReport creditReport, BureauCreditResponse bureauResponse) {
        try {
            creditReport.setCreditScore(bureauResponse.getCreditScore());
            creditReport.setRiskLevel(RiskLevel.fromDisplayName(bureauResponse.getRiskLevel()));
            creditReport.setRecommendation(bureauResponse.getRecommendation());
            creditReport.setBureauResponse(objectMapper.writeValueAsString(bureauResponse));
            creditReport.setBureauMessage(bureauResponse.getMessage());
            creditReport.setProcessingStatus(ProcessingStatus.BUREAU_RESPONSE_RECEIVED);

            creditReport = creditReportRepository.save(creditReport);

            // Save credit summary if available
            if (bureauResponse.getCreditSummary() != null) {
                saveCreditSummary(creditReport, bureauResponse.getCreditSummary());
            }

        } catch (Exception e) {
            logger.error("Failed to update credit report with bureau response for reference: {}",
                        creditReport.getReferenceNumber(), e);
            throw new CreditCheckException("Failed to process bureau response", creditReport.getReferenceNumber(), e);
        }
    }

    /**
     * Saves credit summary information.
     */
    private void saveCreditSummary(CreditReport creditReport, CreditSummaryDto summaryDto) {
        CreditSummary creditSummary = new CreditSummary(
                creditReport,
                summaryDto.getTotalAccounts(),
                summaryDto.getActiveAccounts(),
                summaryDto.getDefaultsCount(),
                summaryDto.getRecentInquiries()
        );

        creditSummaryRepository.save(creditSummary);
    }

    /**
     * Phase 3: Analyzes credit report and makes lending decision.
     */
    private CreditCheckResponse analyzeCreditAndMakeDecision(CreditReport creditReport, String referenceNumber) {
        logger.debug("Phase 3: Analyzing credit report for reference: {}", referenceNumber);

        try {
            BureauCreditResponse bureauResponse = objectMapper.readValue(
                    creditReport.getBureauResponse(), BureauCreditResponse.class);

            CreditCheckResponse response = creditAnalysisService.analyzeCreditReport(bureauResponse, creditReport);

            // Update credit report status
            creditReport.setProcessingStatus(response.getStatus());
            creditReportRepository.save(creditReport);

            return response;

        } catch (Exception e) {
            logger.error("Failed to analyze credit report for reference: {}", referenceNumber, e);
            throw new CreditCheckException("Failed to analyze credit report", referenceNumber, e);
        }
    }

    /**
     * Updates final status of borrower info and credit report.
     */
    private void updateFinalStatus(BorrowerInfo borrowerInfo, CreditReport creditReport, CreditCheckResponse response) {
        borrowerInfo.setStatus(response.getStatus());
        borrowerInfoRepository.save(borrowerInfo);

        creditReport.setProcessingStatus(response.getStatus());
        creditReportRepository.save(creditReport);
    }

    /**
     * Gets credit check status by reference number.
     *
     * @param referenceNumber the reference number
     * @return the credit check response or null if not found
     */
    @Transactional(readOnly = true)
    public CreditCheckResponse getCreditCheckStatus(String referenceNumber) {
        return creditReportRepository.findByReferenceNumber(referenceNumber)
                .map(this::buildCreditCheckResponseFromEntity)
                .orElse(null);
    }

    /**
     * Builds credit check response from credit report entity.
     */
    private CreditCheckResponse buildCreditCheckResponseFromEntity(CreditReport creditReport) {
        CreditCheckResponse response = new CreditCheckResponse();
        response.setReferenceNumber(creditReport.getReferenceNumber());
        response.setFullName(creditReport.getFullName());
        response.setDateOfBirth(validationService.formatDateOfBirth(creditReport.getDateOfBirth()));
        response.setPanNumber(creditReport.getPanNumber());
        response.setPhoneNumber(creditReport.getPhoneNumber());
        response.setCreditScore(creditReport.getCreditScore());
        response.setRiskLevel(creditReport.getRiskLevel());
        response.setRecommendation(creditReport.getRecommendation());
        response.setMessage(creditReport.getBureauMessage());
        response.setStatus(creditReport.getProcessingStatus());
        response.setEligible(creditReport.getProcessingStatus() == ProcessingStatus.ELIGIBLE);

        // Add credit summary if available
        creditSummaryRepository.findByCreditReportId(creditReport.getId())
                .ifPresent(summary -> {
                    CreditSummaryDto summaryDto = new CreditSummaryDto(
                            summary.getTotalAccounts(),
                            summary.getActiveAccounts(),
                            summary.getDefaultsCount(),
                            summary.getRecentInquiries()
                    );
                    response.setCreditSummary(summaryDto);
                });

        return response;
    }
}
