package com.cibil.bureau.enums;

/**
 * Enum representing the type of identification document.
 * Currently only PAN Card is supported as per business requirements.
 */
public enum IdType {
    PAN_CARD("PAN Card");

    private final String displayName;

    IdType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    /**
     * Validates if the given ID type is supported.
     * 
     * @param idType the ID type to validate
     * @return true if supported, false otherwise
     */
    public static boolean isSupported(String idType) {
        if (idType == null) {
            return false;
        }
        
        for (IdType type : values()) {
            if (type.displayName.equalsIgnoreCase(idType.trim())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Gets the enum value from display name.
     * 
     * @param idType the display name
     * @return the corresponding enum value
     * @throws IllegalArgumentException if ID type is not supported
     */
    public static IdType fromDisplayName(String idType) {
        if (idType == null) {
            throw new IllegalArgumentException("ID type cannot be null");
        }
        
        for (IdType type : values()) {
            if (type.displayName.equalsIgnoreCase(idType.trim())) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unsupported ID type: " + idType);
    }
}
