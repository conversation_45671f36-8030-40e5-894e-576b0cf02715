package com.cibil.bureau.exception;

/**
 * Exception thrown when CIBIL Bureau API calls fail.
 */
public class BureauApiException extends RuntimeException {

    private final int statusCode;
    private final String responseBody;

    public BureauApiException(String message) {
        super(message);
        this.statusCode = 0;
        this.responseBody = null;
    }

    public BureauApiException(String message, Throwable cause) {
        super(message, cause);
        this.statusCode = 0;
        this.responseBody = null;
    }

    public BureauApiException(String message, int statusCode, String responseBody) {
        super(message);
        this.statusCode = statusCode;
        this.responseBody = responseBody;
    }

    public BureauApiException(String message, int statusCode, String responseBody, Throwable cause) {
        super(message, cause);
        this.statusCode = statusCode;
        this.responseBody = responseBody;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public String getResponseBody() {
        return responseBody;
    }
}
