package com.cibil.bureau.repository;

import com.cibil.bureau.entity.BorrowerInfo;
import com.cibil.bureau.enums.ProcessingStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for BorrowerInfo entity.
 */
@Repository
public interface BorrowerInfoRepository extends JpaRepository<BorrowerInfo, Long> {

    /**
     * Finds borrower information by reference number.
     * 
     * @param referenceNumber the reference number
     * @return optional borrower information
     */
    Optional<BorrowerInfo> findByReferenceNumber(String referenceNumber);

    /**
     * Finds borrower information by PAN number.
     * 
     * @param panNumber the PAN number
     * @return optional borrower information
     */
    Optional<BorrowerInfo> findByPanNumber(String panNumber);

    /**
     * Finds borrower information by PAN number and phone number.
     * 
     * @param panNumber the PAN number
     * @param phoneNumber the phone number
     * @return optional borrower information
     */
    Optional<BorrowerInfo> findByPanNumberAndPhoneNumber(String panNumber, String phoneNumber);

    /**
     * Finds all borrower information by status.
     * 
     * @param status the processing status
     * @return list of borrower information
     */
    List<BorrowerInfo> findByStatus(ProcessingStatus status);

    /**
     * Finds borrower information created after a specific date.
     * 
     * @param createdAfter the date after which to search
     * @return list of borrower information
     */
    List<BorrowerInfo> findByCreatedAtAfter(LocalDateTime createdAfter);

    /**
     * Finds borrower information created before a specific date for data retention.
     * 
     * @param createdBefore the date before which to search
     * @return list of borrower information
     */
    List<BorrowerInfo> findByCreatedAtBefore(LocalDateTime createdBefore);

    /**
     * Counts borrower information by status.
     * 
     * @param status the processing status
     * @return count of borrower information
     */
    long countByStatus(ProcessingStatus status);

    /**
     * Checks if borrower information exists by reference number.
     * 
     * @param referenceNumber the reference number
     * @return true if exists, false otherwise
     */
    boolean existsByReferenceNumber(String referenceNumber);

    /**
     * Checks if borrower information exists by PAN number.
     * 
     * @param panNumber the PAN number
     * @return true if exists, false otherwise
     */
    boolean existsByPanNumber(String panNumber);

    /**
     * Finds borrower information by status and created date range.
     * 
     * @param status the processing status
     * @param startDate the start date
     * @param endDate the end date
     * @return list of borrower information
     */
    @Query("SELECT b FROM BorrowerInfo b WHERE b.status = :status AND b.createdAt BETWEEN :startDate AND :endDate")
    List<BorrowerInfo> findByStatusAndCreatedAtBetween(@Param("status") ProcessingStatus status,
                                                       @Param("startDate") LocalDateTime startDate,
                                                       @Param("endDate") LocalDateTime endDate);

    /**
     * Deletes borrower information created before a specific date for data retention.
     * 
     * @param createdBefore the date before which to delete
     * @return number of deleted records
     */
    @Query("DELETE FROM BorrowerInfo b WHERE b.createdAt < :createdBefore")
    int deleteByCreatedAtBefore(@Param("createdBefore") LocalDateTime createdBefore);
}
