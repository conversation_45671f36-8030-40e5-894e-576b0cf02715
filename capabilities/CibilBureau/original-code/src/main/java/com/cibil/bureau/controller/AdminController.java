package com.cibil.bureau.controller;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cibil.bureau.service.AuditService;
import com.cibil.bureau.service.DataRetentionService;

/**
 * Admin controller for monitoring and management operations.
 */
@RestController
@RequestMapping("/admin")
public class AdminController {

    private static final Logger logger = LoggerFactory.getLogger(AdminController.class);

    private final AuditService auditService;
    private final DataRetentionService dataRetentionService;

    public AdminController(AuditService auditService, DataRetentionService dataRetentionService) {
        this.auditService = auditService;
        this.dataRetentionService = dataRetentionService;
    }

    /**
     * Gets audit statistics.
     *
     * @return audit statistics
     */
    @GetMapping("/audit/statistics")
    public ResponseEntity<AuditService.AuditStatistics> getAuditStatistics() {
        logger.info("Retrieving audit statistics");

        AuditService.AuditStatistics statistics = auditService.getAuditStatistics();

        logger.info("Audit statistics retrieved: {}", statistics);
        return ResponseEntity.ok(statistics);
    }

    /**
     * Gets data retention statistics.
     *
     * @return data retention statistics
     */
    @GetMapping("/retention/statistics")
    public ResponseEntity<DataRetentionService.DataRetentionStatistics> getRetentionStatistics() {
        logger.info("Retrieving data retention statistics");

        DataRetentionService.DataRetentionStatistics statistics = dataRetentionService.getRetentionStatistics();

        logger.info("Data retention statistics retrieved: {}", statistics);
        return ResponseEntity.ok(statistics);
    }

    /**
     * Triggers manual data cleanup.
     *
     * @return cleanup result
     */
    @PostMapping("/retention/cleanup")
    public ResponseEntity<DataRetentionService.DataRetentionResult> triggerDataCleanup() {
        logger.info("Manual data cleanup triggered");

        try {
            DataRetentionService.DataRetentionResult result = dataRetentionService.performDataCleanup();

            logger.info("Manual data cleanup completed: {}", result);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("Manual data cleanup failed", e);
            throw e;
        }
    }

    /**
     * Validates data retention configuration.
     *
     * @return validation result
     */
    @GetMapping("/retention/validate")
    public ResponseEntity<Map<String, Object>> validateRetentionConfiguration() {
        logger.info("Validating data retention configuration");

        boolean isValid = dataRetentionService.validateRetentionConfiguration();

        Map<String, Object> response = new HashMap<>();
        response.put("valid", isValid);
        response.put("message", isValid ? "Configuration is valid" : "Configuration validation failed");

        logger.info("Data retention configuration validation result: {}", isValid);
        return ResponseEntity.ok(response);
    }

    /**
     * Gets system health information.
     *
     * @return system health status
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getSystemHealth() {
        logger.debug("Retrieving system health information");

        Map<String, Object> health = new HashMap<>();

        try {
            // Get audit statistics
            AuditService.AuditStatistics auditStats = auditService.getAuditStatistics();
            health.put("auditLogs", Map.of(
                "total", auditStats.getTotalLogs(),
                "errors", auditStats.getErrorLogs()
            ));

            // Get retention statistics
            DataRetentionService.DataRetentionStatistics retentionStats = dataRetentionService.getRetentionStatistics();
            health.put("dataRetention", Map.of(
                "totalRecords", retentionStats.getTotalBorrowerInfo() + retentionStats.getTotalCreditReports(),
                "expiredRecords", retentionStats.getExpiredBorrowerInfo() + retentionStats.getExpiredCreditReports(),
                "retentionDays", retentionStats.getRetentionDays()
            ));

            // Validate configuration
            boolean configValid = dataRetentionService.validateRetentionConfiguration();
            health.put("configuration", Map.of("valid", configValid));

            health.put("status", "UP");
            health.put("timestamp", java.time.LocalDateTime.now());

            return ResponseEntity.ok(health);

        } catch (Exception e) {
            logger.error("Failed to retrieve system health", e);

            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", java.time.LocalDateTime.now());

            return ResponseEntity.status(500).body(health);
        }
    }

    /**
     * Gets application metrics.
     *
     * @return application metrics
     */
    @GetMapping("/metrics")
    public ResponseEntity<Map<String, Object>> getApplicationMetrics() {
        logger.debug("Retrieving application metrics");

        Map<String, Object> metrics = new HashMap<>();

        try {
            // Runtime metrics
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;

            metrics.put("memory", Map.of(
                "total", totalMemory,
                "used", usedMemory,
                "free", freeMemory,
                "usagePercentage", (double) usedMemory / totalMemory * 100
            ));

            // System properties
            metrics.put("system", Map.of(
                "javaVersion", System.getProperty("java.version"),
                "osName", System.getProperty("os.name"),
                "osVersion", System.getProperty("os.version"),
                "processors", Runtime.getRuntime().availableProcessors()
            ));

            // Application metrics
            AuditService.AuditStatistics auditStats = auditService.getAuditStatistics();
            metrics.put("application", Map.of(
                "totalAuditLogs", auditStats.getTotalLogs(),
                "creditCheckRequests", auditStats.getCreditCheckLogs(),
                "bureauApiCalls", auditStats.getBureauApiLogs(),
                "errorCount", auditStats.getErrorLogs()
            ));

            metrics.put("timestamp", java.time.LocalDateTime.now());

            return ResponseEntity.ok(metrics);

        } catch (Exception e) {
            logger.error("Failed to retrieve application metrics", e);

            metrics.put("error", e.getMessage());
            metrics.put("timestamp", java.time.LocalDateTime.now());

            return ResponseEntity.status(500).body(metrics);
        }
    }

    /**
     * Admin health check endpoint.
     *
     * @return simple health status
     */
    @GetMapping("/ping")
    public ResponseEntity<String> ping() {
        return ResponseEntity.ok("Admin service is healthy");
    }
}
