package com.cibil.bureau.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;

/**
 * OpenAPI configuration for CIBIL Bureau Credit Check Service.
 * Configures Swagger UI documentation and CORS settings.
 */
@Configuration
public class OpenApiConfig implements WebMvcConfigurer {

    @Value("${server.port:8107}")
    private String serverPort;

    @Value("${server.servlet.context-path:/api/cibil-bureau}")
    private String contextPath;

    /**
     * Configures OpenAPI documentation.
     *
     * @return OpenAPI configuration
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("CIBIL Bureau Credit Check Service API")
                        .description("""
                                End-to-end credit check service using CIBIL Bureau API for loan eligibility assessment.
                                This service provides comprehensive credit analysis including risk assessment, eligibility determination,
                                and administrative functions for monitoring and data management.
                                """)
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("CIBIL Bureau Team")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("Proprietary")
                                .url("https://www.cibilbureau.com/license")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + contextPath)
                                .description("Local development server"),
                        new Server()
                                .url("http://cibil-bureau-service:" + serverPort + contextPath)
                                .description("Docker container server")
                ));
    }

    /**
     * Configures CORS settings to allow Swagger UI to function properly.
     *
     * @param registry CORS registry
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
}
