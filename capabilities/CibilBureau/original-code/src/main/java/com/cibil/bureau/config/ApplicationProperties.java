package com.cibil.bureau.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Application-specific configuration properties.
 */
@Component
@ConfigurationProperties(prefix = "app.credit-check")
public class ApplicationProperties {

    private int minimumScore = 650;
    private DataRetention dataRetention = new DataRetention();
    private Validation validation = new Validation();
    private Audit audit = new Audit();

    // Getters and Setters
    public int getMinimumScore() {
        return minimumScore;
    }

    public void setMinimumScore(int minimumScore) {
        this.minimumScore = minimumScore;
    }

    public DataRetention getDataRetention() {
        return dataRetention;
    }

    public void setDataRetention(DataRetention dataRetention) {
        this.dataRetention = dataRetention;
    }

    public Validation getValidation() {
        return validation;
    }

    public void setValidation(Validation validation) {
        this.validation = validation;
    }

    public Audit getAudit() {
        return audit;
    }

    public void setAudit(Audit audit) {
        this.audit = audit;
    }

    /**
     * Data retention configuration.
     */
    public static class DataRetention {
        private int days = 90;

        public int getDays() {
            return days;
        }

        public void setDays(int days) {
            this.days = days;
        }
    }

    /**
     * Validation patterns configuration.
     */
    public static class Validation {
        private String panPattern = "[A-Z]{5}[0-9]{4}[A-Z]{1}";
        private String phonePattern = "[0-9]{10}";
        private String namePattern = "[a-zA-Z\\s]{2,50}";

        public String getPanPattern() {
            return panPattern;
        }

        public void setPanPattern(String panPattern) {
            this.panPattern = panPattern;
        }

        public String getPhonePattern() {
            return phonePattern;
        }

        public void setPhonePattern(String phonePattern) {
            this.phonePattern = phonePattern;
        }

        public String getNamePattern() {
            return namePattern;
        }

        public void setNamePattern(String namePattern) {
            this.namePattern = namePattern;
        }
    }

    /**
     * Audit configuration.
     */
    public static class Audit {
        private boolean maskPii = true;
        private boolean logRequests = true;
        private boolean logResponses = true;

        public boolean isMaskPii() {
            return maskPii;
        }

        public void setMaskPii(boolean maskPii) {
            this.maskPii = maskPii;
        }

        public boolean isLogRequests() {
            return logRequests;
        }

        public void setLogRequests(boolean logRequests) {
            this.logRequests = logRequests;
        }

        public boolean isLogResponses() {
            return logResponses;
        }

        public void setLogResponses(boolean logResponses) {
            this.logResponses = logResponses;
        }
    }
}
