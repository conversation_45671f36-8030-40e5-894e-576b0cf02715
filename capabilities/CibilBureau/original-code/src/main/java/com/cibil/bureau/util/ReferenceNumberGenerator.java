package com.cibil.bureau.util;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Utility class for generating unique reference numbers for tracking and logging.
 */
@Component
public class ReferenceNumberGenerator {

    private static final String PREFIX = "CIBIL";
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    /**
     * Generates a unique reference number for credit check requests.
     * Format: CIBIL-YYYYMMDDHHMMSS-XXXXXX (where X is random number)
     * 
     * @return unique reference number
     */
    public String generateCreditCheckReference() {
        return generateReference("CC");
    }

    /**
     * Generates a unique reference number for bureau API calls.
     * Format: CIBIL-BA-YYYYMMDDHHMMSS-XXXXXX (where X is random number)
     * 
     * @return unique reference number
     */
    public String generateBureauApiReference() {
        return generateReference("BA");
    }

    /**
     * Generates a unique reference number for audit logs.
     * Format: CIBIL-AL-YYYYMMDDHHMMSS-XXXXXX (where X is random number)
     * 
     * @return unique reference number
     */
    public String generateAuditLogReference() {
        return generateReference("AL");
    }

    /**
     * Generates a unique reference number with the given type.
     * 
     * @param type the type of reference (CC, BA, AL, etc.)
     * @return unique reference number
     */
    private String generateReference(String type) {
        LocalDateTime now = LocalDateTime.now();
        String timestamp = now.format(DATE_FORMAT);
        int randomNumber = ThreadLocalRandom.current().nextInt(100000, 999999);
        
        return String.format("%s-%s-%s-%06d", PREFIX, type, timestamp, randomNumber);
    }

    /**
     * Validates if a reference number has the correct format.
     * 
     * @param referenceNumber the reference number to validate
     * @return true if valid format, false otherwise
     */
    public boolean isValidReferenceNumber(String referenceNumber) {
        if (referenceNumber == null || referenceNumber.trim().isEmpty()) {
            return false;
        }
        
        // Expected format: CIBIL-XX-YYYYMMDDHHMMSS-XXXXXX
        String[] parts = referenceNumber.split("-");
        if (parts.length != 4) {
            return false;
        }
        
        // Check prefix
        if (!PREFIX.equals(parts[0])) {
            return false;
        }
        
        // Check type (should be 2 characters)
        if (parts[1].length() != 2) {
            return false;
        }
        
        // Check timestamp (should be 14 digits)
        if (parts[2].length() != 14 || !parts[2].matches("\\d{14}")) {
            return false;
        }
        
        // Check random number (should be 6 digits)
        if (parts[3].length() != 6 || !parts[3].matches("\\d{6}")) {
            return false;
        }
        
        return true;
    }

    /**
     * Extracts the type from a reference number.
     * 
     * @param referenceNumber the reference number
     * @return the type or null if invalid format
     */
    public String extractType(String referenceNumber) {
        if (!isValidReferenceNumber(referenceNumber)) {
            return null;
        }
        
        String[] parts = referenceNumber.split("-");
        return parts[1];
    }

    /**
     * Extracts the timestamp from a reference number.
     * 
     * @param referenceNumber the reference number
     * @return the timestamp as LocalDateTime or null if invalid format
     */
    public LocalDateTime extractTimestamp(String referenceNumber) {
        if (!isValidReferenceNumber(referenceNumber)) {
            return null;
        }
        
        try {
            String[] parts = referenceNumber.split("-");
            return LocalDateTime.parse(parts[2], DATE_FORMAT);
        } catch (Exception e) {
            return null;
        }
    }
}
