package com.cibil.bureau.dto;

/**
 * DTO for CIBIL Bureau API credit request.
 */
public class BureauCreditRequest {

    private String firstName;
    private String lastName;
    private String dateOfBirth;
    private String panNumber;
    private String idType;
    private String phoneNumber;
    private String loanPurpose;

    // Default constructor
    public BureauCreditRequest() {
    }

    // Constructor with all fields
    public BureauCreditRequest(String firstName, String lastName, String dateOfBirth, String panNumber,
                              String idType, String phoneNumber, String loanPurpose) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.dateOfBirth = dateOfBirth;
        this.panNumber = panNumber;
        this.idType = idType;
        this.phoneNumber = phoneNumber;
        this.loanPurpose = loanPurpose;
    }

    // Getters and Setters
    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(String dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getPanNumber() {
        return panNumber;
    }

    public void setPanNumber(String panNumber) {
        this.panNumber = panNumber;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(String loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    @Override
    public String toString() {
        return "BureauCreditRequest{" +
                "firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", dateOfBirth='" + dateOfBirth + '\'' +
                ", panNumber='" + maskPan(panNumber) + '\'' +
                ", idType='" + idType + '\'' +
                ", phoneNumber='" + maskPhone(phoneNumber) + '\'' +
                ", loanPurpose='" + loanPurpose + '\'' +
                '}';
    }

    /**
     * Masks PAN number for logging purposes.
     */
    private String maskPan(String pan) {
        if (pan == null || pan.length() < 4) {
            return "****";
        }
        return pan.substring(0, 2) + "****" + pan.substring(pan.length() - 2);
    }

    /**
     * Masks phone number for logging purposes.
     */
    private String maskPhone(String phone) {
        if (phone == null || phone.length() < 4) {
            return "****";
        }
        return "****" + phone.substring(phone.length() - 4);
    }
}
