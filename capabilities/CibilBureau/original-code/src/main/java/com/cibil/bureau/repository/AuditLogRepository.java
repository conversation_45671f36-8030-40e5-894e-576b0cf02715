package com.cibil.bureau.repository;

import com.cibil.bureau.entity.AuditLog;
import com.cibil.bureau.enums.OperationType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository interface for AuditLog entity.
 */
@Repository
public interface AuditLogRepository extends JpaRepository<AuditLog, Long> {

    /**
     * Finds audit logs by reference number.
     * 
     * @param referenceNumber the reference number
     * @return list of audit logs
     */
    List<AuditLog> findByReferenceNumber(String referenceNumber);

    /**
     * Finds audit logs by operation type.
     * 
     * @param operationType the operation type
     * @return list of audit logs
     */
    List<AuditLog> findByOperationType(OperationType operationType);

    /**
     * Finds audit logs by entity type.
     * 
     * @param entityType the entity type
     * @return list of audit logs
     */
    List<AuditLog> findByEntityType(String entityType);

    /**
     * Finds audit logs by entity ID.
     * 
     * @param entityId the entity ID
     * @return list of audit logs
     */
    List<AuditLog> findByEntityId(String entityId);

    /**
     * Finds audit logs by status.
     * 
     * @param status the status
     * @return list of audit logs
     */
    List<AuditLog> findByStatus(String status);

    /**
     * Finds audit logs created after a specific date.
     * 
     * @param createdAfter the date after which to search
     * @return list of audit logs
     */
    List<AuditLog> findByCreatedAtAfter(LocalDateTime createdAfter);

    /**
     * Finds audit logs created before a specific date for data retention.
     * 
     * @param createdBefore the date before which to search
     * @return list of audit logs
     */
    List<AuditLog> findByCreatedAtBefore(LocalDateTime createdBefore);

    /**
     * Finds audit logs by reference number and operation type.
     * 
     * @param referenceNumber the reference number
     * @param operationType the operation type
     * @return list of audit logs
     */
    List<AuditLog> findByReferenceNumberAndOperationType(String referenceNumber, OperationType operationType);

    /**
     * Finds audit logs by operation type and created date range.
     * 
     * @param operationType the operation type
     * @param startDate the start date
     * @param endDate the end date
     * @return list of audit logs
     */
    @Query("SELECT al FROM AuditLog al WHERE al.operationType = :operationType AND al.createdAt BETWEEN :startDate AND :endDate")
    List<AuditLog> findByOperationTypeAndCreatedAtBetween(@Param("operationType") OperationType operationType,
                                                          @Param("startDate") LocalDateTime startDate,
                                                          @Param("endDate") LocalDateTime endDate);

    /**
     * Finds error audit logs (status contains 'ERROR' or 'FAILED').
     * 
     * @return list of error audit logs
     */
    @Query("SELECT al FROM AuditLog al WHERE al.status LIKE '%ERROR%' OR al.status LIKE '%FAILED%'")
    List<AuditLog> findErrorLogs();

    /**
     * Counts audit logs by operation type.
     * 
     * @param operationType the operation type
     * @return count of audit logs
     */
    long countByOperationType(OperationType operationType);

    /**
     * Counts audit logs by status.
     * 
     * @param status the status
     * @return count of audit logs
     */
    long countByStatus(String status);

    /**
     * Counts audit logs by reference number.
     * 
     * @param referenceNumber the reference number
     * @return count of audit logs
     */
    long countByReferenceNumber(String referenceNumber);

    /**
     * Deletes audit logs created before a specific date for data retention.
     * 
     * @param createdBefore the date before which to delete
     * @return number of deleted records
     */
    @Query("DELETE FROM AuditLog al WHERE al.createdAt < :createdBefore")
    int deleteByCreatedAtBefore(@Param("createdBefore") LocalDateTime createdBefore);
}
