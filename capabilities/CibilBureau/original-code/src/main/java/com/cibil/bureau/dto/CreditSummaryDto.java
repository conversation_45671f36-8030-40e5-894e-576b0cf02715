package com.cibil.bureau.dto;

/**
 * DTO for credit summary information.
 */
public class CreditSummaryDto {

    private Integer totalAccounts;
    private Integer activeAccounts;
    private Integer defaultsCount;
    private Integer recentInquiries;

    // Default constructor
    public CreditSummaryDto() {
    }

    // Constructor with all fields
    public CreditSummaryDto(Integer totalAccounts, Integer activeAccounts, 
                           Integer defaultsCount, Integer recentInquiries) {
        this.totalAccounts = totalAccounts;
        this.activeAccounts = activeAccounts;
        this.defaultsCount = defaultsCount;
        this.recentInquiries = recentInquiries;
    }

    // Getters and Setters
    public Integer getTotalAccounts() {
        return totalAccounts;
    }

    public void setTotalAccounts(Integer totalAccounts) {
        this.totalAccounts = totalAccounts;
    }

    public Integer getActiveAccounts() {
        return activeAccounts;
    }

    public void setActiveAccounts(Integer activeAccounts) {
        this.activeAccounts = activeAccounts;
    }

    public Integer getDefaultsCount() {
        return defaultsCount;
    }

    public void setDefaultsCount(Integer defaultsCount) {
        this.defaultsCount = defaultsCount;
    }

    public Integer getRecentInquiries() {
        return recentInquiries;
    }

    public void setRecentInquiries(Integer recentInquiries) {
        this.recentInquiries = recentInquiries;
    }

    @Override
    public String toString() {
        return "CreditSummaryDto{" +
                "totalAccounts=" + totalAccounts +
                ", activeAccounts=" + activeAccounts +
                ", defaultsCount=" + defaultsCount +
                ", recentInquiries=" + recentInquiries +
                '}';
    }
}
