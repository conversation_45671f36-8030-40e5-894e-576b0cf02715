package com.cibil.bureau.exception;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.cibil.bureau.dto.ErrorResponse;
import com.cibil.bureau.util.DataMaskingUtil;

import jakarta.servlet.http.HttpServletRequest;

/**
 * Global exception handler for standardized error responses.
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    private final DataMaskingUtil dataMaskingUtil;

    public GlobalExceptionHandler(DataMaskingUtil dataMaskingUtil) {
        this.dataMaskingUtil = dataMaskingUtil;
    }

    /**
     * Handles validation exceptions.
     */
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ErrorResponse> handleValidationException(ValidationException ex,
                                                                  HttpServletRequest request) {
        logger.warn("Validation exception: {}", ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
                "VALIDATION_ERROR",
                ex.getMessage(),
                HttpStatus.BAD_REQUEST.value(),
                request.getRequestURI()
        );

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Handles method argument validation exceptions.
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex,
                                                                              HttpServletRequest request) {
        logger.warn("Method argument validation exception: {}", ex.getMessage());

        List<String> details = new ArrayList<>();
        for (FieldError error : ex.getBindingResult().getFieldErrors()) {
            details.add(error.getField() + ": " + error.getDefaultMessage());
        }

        ErrorResponse errorResponse = new ErrorResponse(
                "VALIDATION_ERROR",
                "Request validation failed",
                HttpStatus.BAD_REQUEST.value(),
                request.getRequestURI(),
                null,
                details
        );

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Handles JSON parsing exceptions.
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<ErrorResponse> handleHttpMessageNotReadableException(HttpMessageNotReadableException ex,
                                                                               HttpServletRequest request) {
        logger.warn("JSON parsing exception: {}", ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
                "INVALID_JSON",
                "Invalid JSON format in request body",
                HttpStatus.BAD_REQUEST.value(),
                request.getRequestURI()
        );

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Handles bureau API exceptions.
     */
    @ExceptionHandler(BureauApiException.class)
    public ResponseEntity<ErrorResponse> handleBureauApiException(BureauApiException ex,
                                                                 HttpServletRequest request) {
        logger.error("Bureau API exception: {}", ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
                "BUREAU_API_ERROR",
                "We're unable to fetch your credit score right now. Please try again later.",
                HttpStatus.SERVICE_UNAVAILABLE.value(),
                request.getRequestURI()
        );

        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(errorResponse);
    }

    /**
     * Handles credit check exceptions.
     */
    @ExceptionHandler(CreditCheckException.class)
    public ResponseEntity<ErrorResponse> handleCreditCheckException(CreditCheckException ex,
                                                                   HttpServletRequest request) {
        logger.error("Credit check exception for reference {}: {}", ex.getReferenceNumber(), ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
                "CREDIT_CHECK_ERROR",
                ex.getMessage(),
                HttpStatus.INTERNAL_SERVER_ERROR.value(),
                request.getRequestURI(),
                ex.getReferenceNumber()
        );

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    /**
     * Handles illegal argument exceptions.
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ErrorResponse> handleIllegalArgumentException(IllegalArgumentException ex,
                                                                       HttpServletRequest request) {
        logger.warn("Illegal argument exception: {}", ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
                "INVALID_ARGUMENT",
                ex.getMessage(),
                HttpStatus.BAD_REQUEST.value(),
                request.getRequestURI()
        );

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Handles all other exceptions.
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(Exception ex,
                                                               HttpServletRequest request) {
        logger.error("Unexpected exception: {}", ex.getMessage(), ex);

        ErrorResponse errorResponse = new ErrorResponse(
                "INTERNAL_SERVER_ERROR",
                "An unexpected error occurred. Please try again later.",
                HttpStatus.INTERNAL_SERVER_ERROR.value(),
                request.getRequestURI()
        );

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    /**
     * Handles runtime exceptions.
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ErrorResponse> handleRuntimeException(RuntimeException ex,
                                                               HttpServletRequest request) {
        logger.error("Runtime exception: {}", ex.getMessage(), ex);

        ErrorResponse errorResponse = new ErrorResponse(
                "RUNTIME_ERROR",
                "A runtime error occurred. Please try again later.",
                HttpStatus.INTERNAL_SERVER_ERROR.value(),
                request.getRequestURI()
        );

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
}
