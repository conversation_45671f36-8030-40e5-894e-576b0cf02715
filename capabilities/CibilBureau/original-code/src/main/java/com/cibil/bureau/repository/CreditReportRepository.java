package com.cibil.bureau.repository;

import com.cibil.bureau.entity.CreditReport;
import com.cibil.bureau.enums.ProcessingStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for CreditReport entity.
 */
@Repository
public interface CreditReportRepository extends JpaRepository<CreditReport, Long> {

    /**
     * Finds credit report by reference number.
     * 
     * @param referenceNumber the reference number
     * @return optional credit report
     */
    Optional<CreditReport> findByReferenceNumber(String referenceNumber);

    /**
     * Finds credit report by borrower ID.
     * 
     * @param borrowerId the borrower ID
     * @return optional credit report
     */
    Optional<CreditReport> findByBorrowerId(Long borrowerId);

    /**
     * Finds credit report by PAN number.
     * 
     * @param panNumber the PAN number
     * @return optional credit report
     */
    Optional<CreditReport> findByPanNumber(String panNumber);

    /**
     * Finds all credit reports by processing status.
     * 
     * @param processingStatus the processing status
     * @return list of credit reports
     */
    List<CreditReport> findByProcessingStatus(ProcessingStatus processingStatus);

    /**
     * Finds credit reports that need retry (failed status and retry count less than max).
     * 
     * @param maxRetryCount the maximum retry count
     * @return list of credit reports
     */
    @Query("SELECT cr FROM CreditReport cr WHERE cr.processingStatus = 'BUREAU_REQUEST_FAILED' AND cr.retryCount < :maxRetryCount")
    List<CreditReport> findRetryableCreditReports(@Param("maxRetryCount") int maxRetryCount);

    /**
     * Finds credit reports by credit score range.
     * 
     * @param minScore the minimum credit score
     * @param maxScore the maximum credit score
     * @return list of credit reports
     */
    @Query("SELECT cr FROM CreditReport cr WHERE cr.creditScore BETWEEN :minScore AND :maxScore")
    List<CreditReport> findByCreditScoreBetween(@Param("minScore") int minScore, @Param("maxScore") int maxScore);

    /**
     * Finds credit reports created after a specific date.
     * 
     * @param createdAfter the date after which to search
     * @return list of credit reports
     */
    List<CreditReport> findByCreatedAtAfter(LocalDateTime createdAfter);

    /**
     * Finds credit reports created before a specific date for data retention.
     * 
     * @param createdBefore the date before which to search
     * @return list of credit reports
     */
    List<CreditReport> findByCreatedAtBefore(LocalDateTime createdBefore);

    /**
     * Counts credit reports by processing status.
     * 
     * @param processingStatus the processing status
     * @return count of credit reports
     */
    long countByProcessingStatus(ProcessingStatus processingStatus);

    /**
     * Counts credit reports with credit score above threshold.
     * 
     * @param threshold the credit score threshold
     * @return count of credit reports
     */
    @Query("SELECT COUNT(cr) FROM CreditReport cr WHERE cr.creditScore >= :threshold")
    long countByCreditScoreGreaterThanEqual(@Param("threshold") int threshold);

    /**
     * Checks if credit report exists by reference number.
     * 
     * @param referenceNumber the reference number
     * @return true if exists, false otherwise
     */
    boolean existsByReferenceNumber(String referenceNumber);

    /**
     * Finds credit reports by processing status and created date range.
     * 
     * @param processingStatus the processing status
     * @param startDate the start date
     * @param endDate the end date
     * @return list of credit reports
     */
    @Query("SELECT cr FROM CreditReport cr WHERE cr.processingStatus = :processingStatus AND cr.createdAt BETWEEN :startDate AND :endDate")
    List<CreditReport> findByProcessingStatusAndCreatedAtBetween(@Param("processingStatus") ProcessingStatus processingStatus,
                                                                @Param("startDate") LocalDateTime startDate,
                                                                @Param("endDate") LocalDateTime endDate);

    /**
     * Deletes credit reports created before a specific date for data retention.
     * 
     * @param createdBefore the date before which to delete
     * @return number of deleted records
     */
    @Query("DELETE FROM CreditReport cr WHERE cr.createdAt < :createdBefore")
    int deleteByCreatedAtBefore(@Param("createdBefore") LocalDateTime createdBefore);
}
