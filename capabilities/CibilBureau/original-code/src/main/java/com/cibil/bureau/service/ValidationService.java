package com.cibil.bureau.service;

import com.cibil.bureau.config.ApplicationProperties;
import com.cibil.bureau.dto.CreditCheckRequest;
import com.cibil.bureau.enums.IdType;
import com.cibil.bureau.enums.LoanPurpose;
import com.cibil.bureau.exception.ValidationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Service for validating borrower information according to business rules.
 */
@Service
public class ValidationService {

    private static final Logger logger = LoggerFactory.getLogger(ValidationService.class);
    private static final DateTimeFormatter DOB_FORMAT = DateTimeFormatter.ofPattern("ddMMyyyy");

    private final ApplicationProperties applicationProperties;

    public ValidationService(ApplicationProperties applicationProperties) {
        this.applicationProperties = applicationProperties;
    }

    /**
     * Validates the credit check request according to business rules.
     * 
     * @param request the credit check request
     * @param referenceNumber the reference number for logging
     * @throws ValidationException if validation fails
     */
    public void validateCreditCheckRequest(CreditCheckRequest request, String referenceNumber) {
        logger.debug("Starting validation for reference: {}", referenceNumber);
        
        List<String> validationErrors = new ArrayList<>();

        // Validate first name
        validateFirstName(request.getFirstName(), validationErrors);

        // Validate last name
        validateLastName(request.getLastName(), validationErrors);

        // Validate date of birth
        validateDateOfBirth(request.getDateOfBirth(), validationErrors);

        // Validate PAN number
        validatePanNumber(request.getPanNumber(), validationErrors);

        // Validate ID type
        validateIdType(request.getIdType(), validationErrors);

        // Validate phone number
        validatePhoneNumber(request.getPhoneNumber(), validationErrors);

        // Validate loan purpose
        validateLoanPurpose(request.getLoanPurpose(), validationErrors);

        if (!validationErrors.isEmpty()) {
            String errorMessage = String.join("; ", validationErrors);
            logger.warn("Validation failed for reference: {} - Errors: {}", referenceNumber, errorMessage);
            throw new ValidationException("Validation failed: " + errorMessage);
        }

        logger.debug("Validation successful for reference: {}", referenceNumber);
    }

    /**
     * Validates first name.
     */
    private void validateFirstName(String firstName, List<String> errors) {
        if (firstName == null || firstName.trim().isEmpty()) {
            errors.add("First name is mandatory");
            return;
        }

        String trimmedName = firstName.trim();
        if (trimmedName.length() < 2 || trimmedName.length() > 50) {
            errors.add("First name must be between 2 and 50 characters");
        }

        if (!Pattern.matches(applicationProperties.getValidation().getNamePattern(), trimmedName)) {
            errors.add("First name can only contain letters and spaces");
        }
    }

    /**
     * Validates last name.
     */
    private void validateLastName(String lastName, List<String> errors) {
        if (lastName == null || lastName.trim().isEmpty()) {
            errors.add("Last name is mandatory");
            return;
        }

        String trimmedName = lastName.trim();
        if (trimmedName.length() < 2 || trimmedName.length() > 50) {
            errors.add("Last name must be between 2 and 50 characters");
        }

        if (!Pattern.matches(applicationProperties.getValidation().getNamePattern(), trimmedName)) {
            errors.add("Last name can only contain letters and spaces");
        }
    }

    /**
     * Validates date of birth.
     */
    private void validateDateOfBirth(String dateOfBirth, List<String> errors) {
        if (dateOfBirth == null || dateOfBirth.trim().isEmpty()) {
            errors.add("Date of birth is mandatory");
            return;
        }

        String trimmedDob = dateOfBirth.trim();
        if (!Pattern.matches("\\d{8}", trimmedDob)) {
            errors.add("Date of birth must be in DDMMYYYY format");
            return;
        }

        try {
            LocalDate dob = LocalDate.parse(trimmedDob, DOB_FORMAT);
            LocalDate today = LocalDate.now();
            
            if (dob.isAfter(today)) {
                errors.add("Date of birth cannot be in the future");
            }
            
            if (dob.isBefore(today.minusYears(100))) {
                errors.add("Date of birth cannot be more than 100 years ago");
            }
            
            if (dob.isAfter(today.minusYears(18))) {
                errors.add("Borrower must be at least 18 years old");
            }
        } catch (DateTimeParseException e) {
            errors.add("Invalid date of birth format");
        }
    }

    /**
     * Validates PAN number.
     */
    private void validatePanNumber(String panNumber, List<String> errors) {
        if (panNumber == null || panNumber.trim().isEmpty()) {
            errors.add("PAN number is mandatory");
            return;
        }

        String trimmedPan = panNumber.trim().toUpperCase();
        if (!Pattern.matches(applicationProperties.getValidation().getPanPattern(), trimmedPan)) {
            errors.add("PAN number must be in valid format (e.g., **********)");
        }
    }

    /**
     * Validates ID type.
     */
    private void validateIdType(String idType, List<String> errors) {
        if (idType == null || idType.trim().isEmpty()) {
            errors.add("ID type is mandatory");
            return;
        }

        if (!IdType.isSupported(idType)) {
            errors.add("Only PAN Card is supported as ID type");
        }
    }

    /**
     * Validates phone number.
     */
    private void validatePhoneNumber(String phoneNumber, List<String> errors) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            errors.add("Phone number is mandatory");
            return;
        }

        String trimmedPhone = phoneNumber.trim();
        if (!Pattern.matches(applicationProperties.getValidation().getPhonePattern(), trimmedPhone)) {
            errors.add("Phone number must be exactly 10 digits");
        }
    }

    /**
     * Validates loan purpose.
     */
    private void validateLoanPurpose(String loanPurpose, List<String> errors) {
        if (loanPurpose == null || loanPurpose.trim().isEmpty()) {
            errors.add("Loan purpose is mandatory");
            return;
        }

        if (!LoanPurpose.isSupported(loanPurpose)) {
            errors.add("Only Personal Loan is supported as loan purpose");
        }
    }

    /**
     * Parses date of birth from string to LocalDate.
     * 
     * @param dateOfBirth the date of birth string in DDMMYYYY format
     * @return parsed LocalDate
     * @throws ValidationException if parsing fails
     */
    public LocalDate parseDateOfBirth(String dateOfBirth) {
        try {
            return LocalDate.parse(dateOfBirth.trim(), DOB_FORMAT);
        } catch (DateTimeParseException e) {
            throw new ValidationException("Invalid date of birth format: " + dateOfBirth);
        }
    }

    /**
     * Formats LocalDate to string in DDMMYYYY format.
     * 
     * @param date the LocalDate to format
     * @return formatted date string
     */
    public String formatDateOfBirth(LocalDate date) {
        return date.format(DOB_FORMAT);
    }
}
