package com.cibil.bureau.util;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * Utility class for masking PII data in logs and audit trails.
 */
@Component
public class DataMaskingUtil {

    private static final Set<String> PII_FIELDS = new HashSet<>(Arrays.asList(
            "panNumber", "pan", "panNo",
            "phoneNumber", "phone", "mobile", "mobileNumber",
            "firstName", "lastName", "fullName", "name",
            "dateOfBirth", "dob", "birthDate",
            "email", "emailAddress",
            "address", "addressLine1", "addressLine2",
            "pincode", "zipcode", "postalCode"
    ));

    private final ObjectMapper objectMapper;

    public DataMaskingUtil(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * Masks PII data in a JSON string.
     *
     * @param jsonData the JSON data to mask
     * @return masked JSON string
     */
    public String maskJsonData(String jsonData) {
        if (jsonData == null || jsonData.trim().isEmpty()) {
            return jsonData;
        }

        try {
            JsonNode rootNode = objectMapper.readTree(jsonData);
            JsonNode maskedNode = maskJsonNode(rootNode);
            return objectMapper.writeValueAsString(maskedNode);
        } catch (Exception e) {
            // If JSON parsing fails, mask as plain text
            return maskPlainText(jsonData);
        }
    }

    /**
     * Masks PII data in an object by converting to JSON and masking.
     *
     * @param object the object to mask
     * @return masked JSON string
     */
    public String maskObject(Object object) {
        if (object == null) {
            return null;
        }

        try {
            String jsonData = objectMapper.writeValueAsString(object);
            return maskJsonData(jsonData);
        } catch (Exception e) {
            return maskPlainText(object.toString());
        }
    }

    /**
     * Masks PAN number.
     *
     * @param pan the PAN number
     * @return masked PAN number
     */
    public String maskPan(String pan) {
        if (pan == null || pan.length() < 4) {
            return "****";
        }
        // For PAN format **********, show first 2 and last 1 character
        return pan.substring(0, 2) + "****" + pan.substring(pan.length() - 1);
    }

    /**
     * Masks phone number.
     *
     * @param phone the phone number
     * @return masked phone number
     */
    public String maskPhone(String phone) {
        if (phone == null || phone.length() < 4) {
            return "****";
        }
        return "****" + phone.substring(phone.length() - 4);
    }

    /**
     * Masks name by showing only first and last character.
     *
     * @param name the name
     * @return masked name
     */
    public String maskName(String name) {
        if (name == null || name.length() < 2) {
            return "****";
        }
        if (name.length() == 2) {
            return name.charAt(0) + "*";
        }
        if (name.length() == 3) {
            return name.charAt(0) + "*" + name.charAt(name.length() - 1);
        }
        return name.charAt(0) + "****" + name.charAt(name.length() - 1);
    }

    /**
     * Masks email address.
     *
     * @param email the email address
     * @return masked email address
     */
    public String maskEmail(String email) {
        if (email == null || !email.contains("@")) {
            return "****";
        }
        String[] parts = email.split("@");
        String localPart = parts[0];
        String domain = parts[1];

        if (localPart.length() <= 2) {
            return "****@" + domain;
        }
        return localPart.charAt(0) + "****" + localPart.charAt(localPart.length() - 1) + "@" + domain;
    }

    /**
     * Masks a JSON node recursively.
     *
     * @param node the JSON node to mask
     * @return masked JSON node
     */
    private JsonNode maskJsonNode(JsonNode node) {
        if (node.isObject()) {
            ObjectNode objectNode = (ObjectNode) node;
            ObjectNode maskedNode = objectMapper.createObjectNode();

            objectNode.fields().forEachRemaining(entry -> {
                String fieldName = entry.getKey();
                JsonNode fieldValue = entry.getValue();

                if (isPiiField(fieldName) && fieldValue.isTextual()) {
                    maskedNode.put(fieldName, maskFieldValue(fieldName, fieldValue.asText()));
                } else if (fieldValue.isObject() || fieldValue.isArray()) {
                    maskedNode.set(fieldName, maskJsonNode(fieldValue));
                } else {
                    maskedNode.set(fieldName, fieldValue);
                }
            });

            return maskedNode;
        } else if (node.isArray()) {
            ArrayNode arrayNode = (ArrayNode) node;
            ArrayNode maskedArray = objectMapper.createArrayNode();

            for (int i = 0; i < arrayNode.size(); i++) {
                JsonNode arrayElement = arrayNode.get(i);
                maskedArray.add(maskJsonNode(arrayElement));
            }
            return maskedArray;
        }

        return node;
    }

    /**
     * Checks if a field name represents PII data.
     *
     * @param fieldName the field name
     * @return true if PII field, false otherwise
     */
    private boolean isPiiField(String fieldName) {
        return PII_FIELDS.contains(fieldName.toLowerCase()) ||
               fieldName.toLowerCase().contains("pan") ||
               fieldName.toLowerCase().contains("phone") ||
               fieldName.toLowerCase().contains("name") ||
               fieldName.toLowerCase().contains("email");
    }

    /**
     * Masks field value based on field name.
     *
     * @param fieldName the field name
     * @param value the field value
     * @return masked value
     */
    private String maskFieldValue(String fieldName, String value) {
        String lowerFieldName = fieldName.toLowerCase();

        if (lowerFieldName.contains("pan")) {
            return maskPan(value);
        } else if (lowerFieldName.contains("phone") || lowerFieldName.contains("mobile")) {
            return maskPhone(value);
        } else if (lowerFieldName.contains("email")) {
            return maskEmail(value);
        } else if (lowerFieldName.contains("name")) {
            return maskName(value);
        } else {
            return maskName(value); // Default masking for other PII fields
        }
    }

    /**
     * Masks plain text data by replacing common PII patterns.
     *
     * @param text the text to mask
     * @return masked text
     */
    private String maskPlainText(String text) {
        if (text == null) {
            return null;
        }

        // Mask PAN pattern (5 letters, 4 digits, 1 letter)
        text = text.replaceAll("[A-Z]{5}[0-9]{4}[A-Z]{1}", "XX****XX");

        // Mask phone numbers (10 digits)
        text = text.replaceAll("\\b[0-9]{10}\\b", "****XXXX");

        // Mask email patterns
        text = text.replaceAll("\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b", "****@****.***");

        return text;
    }
}
