package com.cibil.bureau.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Standardized error response structure.
 */
public class ErrorResponse {

    private String error;
    private String message;
    private int status;
    private String path;
    private String referenceNumber;
    private List<String> details;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;

    // Default constructor
    public ErrorResponse() {
        this.timestamp = LocalDateTime.now();
    }

    // Constructor with basic fields
    public ErrorResponse(String error, String message, int status, String path) {
        this();
        this.error = error;
        this.message = message;
        this.status = status;
        this.path = path;
    }

    // Constructor with reference number
    public ErrorResponse(String error, String message, int status, String path, String referenceNumber) {
        this(error, message, status, path);
        this.referenceNumber = referenceNumber;
    }

    // Constructor with details
    public ErrorResponse(String error, String message, int status, String path, String referenceNumber, List<String> details) {
        this(error, message, status, path, referenceNumber);
        this.details = details;
    }

    // Getters and Setters
    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public List<String> getDetails() {
        return details;
    }

    public void setDetails(List<String> details) {
        this.details = details;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return "ErrorResponse{" +
                "error='" + error + '\'' +
                ", message='" + message + '\'' +
                ", status=" + status +
                ", path='" + path + '\'' +
                ", referenceNumber='" + referenceNumber + '\'' +
                ", details=" + details +
                ", timestamp=" + timestamp +
                '}';
    }
}
