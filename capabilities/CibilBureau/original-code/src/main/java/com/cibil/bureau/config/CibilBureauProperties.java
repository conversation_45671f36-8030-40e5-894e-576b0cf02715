package com.cibil.bureau.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Configuration properties for CIBIL Bureau API integration.
 */
@Component
@ConfigurationProperties(prefix = "cibil.bureau.api")
public class CibilBureauProperties {

    private String baseUrl;
    private int timeout = 30000;
    private Retry retry = new Retry();
    private Endpoints endpoints = new Endpoints();

    // Getters and Setters
    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public int getTimeout() {
        return timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }

    public Retry getRetry() {
        return retry;
    }

    public void setRetry(Retry retry) {
        this.retry = retry;
    }

    public Endpoints getEndpoints() {
        return endpoints;
    }

    public void setEndpoints(Endpoints endpoints) {
        this.endpoints = endpoints;
    }

    /**
     * Retry configuration for CIBIL Bureau API calls.
     */
    public static class Retry {
        private int maxAttempts = 3;
        private long delay = 1000;
        private double multiplier = 2.0;

        public int getMaxAttempts() {
            return maxAttempts;
        }

        public void setMaxAttempts(int maxAttempts) {
            this.maxAttempts = maxAttempts;
        }

        public long getDelay() {
            return delay;
        }

        public void setDelay(long delay) {
            this.delay = delay;
        }

        public double getMultiplier() {
            return multiplier;
        }

        public void setMultiplier(double multiplier) {
            this.multiplier = multiplier;
        }
    }

    /**
     * API endpoints configuration.
     */
    public static class Endpoints {
        private String creditReport = "/credit-report";

        public String getCreditReport() {
            return creditReport;
        }

        public void setCreditReport(String creditReport) {
            this.creditReport = creditReport;
        }
    }
}
