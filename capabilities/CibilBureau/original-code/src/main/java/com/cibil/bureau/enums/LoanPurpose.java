package com.cibil.bureau.enums;

/**
 * Enum representing the purpose of the loan.
 * Currently only Personal Loan is supported as per business requirements.
 */
public enum LoanPurpose {
    PERSONAL_LOAN("Personal Loan");

    private final String displayName;

    LoanPurpose(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    /**
     * Validates if the given loan purpose is supported.
     * 
     * @param purpose the loan purpose to validate
     * @return true if supported, false otherwise
     */
    public static boolean isSupported(String purpose) {
        if (purpose == null) {
            return false;
        }
        
        for (LoanPurpose loanPurpose : values()) {
            if (loanPurpose.displayName.equalsIgnoreCase(purpose.trim())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Gets the enum value from display name.
     * 
     * @param purpose the display name
     * @return the corresponding enum value
     * @throws IllegalArgumentException if purpose is not supported
     */
    public static LoanPurpose fromDisplayName(String purpose) {
        if (purpose == null) {
            throw new IllegalArgumentException("Loan purpose cannot be null");
        }
        
        for (LoanPurpose loanPurpose : values()) {
            if (loanPurpose.displayName.equalsIgnoreCase(purpose.trim())) {
                return loanPurpose;
            }
        }
        throw new IllegalArgumentException("Unsupported loan purpose: " + purpose);
    }
}
