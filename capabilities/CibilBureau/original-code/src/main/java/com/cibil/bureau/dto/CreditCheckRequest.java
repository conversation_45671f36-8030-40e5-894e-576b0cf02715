package com.cibil.bureau.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * DTO for credit check request containing borrower information.
 * All fields are mandatory as per business requirements.
 */
public class CreditCheckRequest {

    @NotBlank(message = "First name is mandatory")
    @Size(min = 2, max = 50, message = "First name must be between 2 and 50 characters")
    @Pattern(regexp = "[a-zA-Z\\s]{2,50}", message = "First name can only contain letters and spaces")
    private String firstName;

    @NotBlank(message = "Last name is mandatory")
    @Size(min = 2, max = 50, message = "Last name must be between 2 and 50 characters")
    @Pattern(regexp = "[a-zA-Z\\s]{2,50}", message = "Last name can only contain letters and spaces")
    private String lastName;

    @NotBlank(message = "Date of birth is mandatory")
    @Pattern(regexp = "\\d{2}\\d{2}\\d{4}", message = "Date of birth must be in DDMMYYYY format")
    private String dateOfBirth;

    @NotBlank(message = "PAN number is mandatory")
    @Pattern(regexp = "[A-Z]{5}[0-9]{4}[A-Z]{1}", message = "PAN number must be in valid format (e.g., **********)")
    private String panNumber;

    @NotBlank(message = "ID type is mandatory")
    private String idType;

    @NotBlank(message = "Phone number is mandatory")
    @Pattern(regexp = "[0-9]{10}", message = "Phone number must be exactly 10 digits")
    private String phoneNumber;

    @NotBlank(message = "Loan purpose is mandatory")
    private String loanPurpose;

    // Default constructor
    public CreditCheckRequest() {
    }

    // Constructor with all fields
    public CreditCheckRequest(String firstName, String lastName, String dateOfBirth, 
                             String panNumber, String idType, String phoneNumber, String loanPurpose) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.dateOfBirth = dateOfBirth;
        this.panNumber = panNumber;
        this.idType = idType;
        this.phoneNumber = phoneNumber;
        this.loanPurpose = loanPurpose;
    }

    // Getters and Setters
    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(String dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getPanNumber() {
        return panNumber;
    }

    public void setPanNumber(String panNumber) {
        this.panNumber = panNumber;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(String loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    /**
     * Gets the full name by combining first and last name.
     * 
     * @return the full name
     */
    public String getFullName() {
        return firstName + " " + lastName;
    }

    @Override
    public String toString() {
        return "CreditCheckRequest{" +
                "firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", dateOfBirth='" + dateOfBirth + '\'' +
                ", panNumber='" + maskPan(panNumber) + '\'' +
                ", idType='" + idType + '\'' +
                ", phoneNumber='" + maskPhone(phoneNumber) + '\'' +
                ", loanPurpose='" + loanPurpose + '\'' +
                '}';
    }

    /**
     * Masks PAN number for logging purposes.
     */
    private String maskPan(String pan) {
        if (pan == null || pan.length() < 4) {
            return "****";
        }
        return pan.substring(0, 2) + "****" + pan.substring(pan.length() - 2);
    }

    /**
     * Masks phone number for logging purposes.
     */
    private String maskPhone(String phone) {
        if (phone == null || phone.length() < 4) {
            return "****";
        }
        return "****" + phone.substring(phone.length() - 4);
    }
}
