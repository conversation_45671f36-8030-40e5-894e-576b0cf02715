package com.cibil.bureau.service;

import java.time.Duration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.cibil.bureau.config.CibilBureauProperties;
import com.cibil.bureau.dto.BureauCreditRequest;
import com.cibil.bureau.dto.BureauCreditResponse;
import com.cibil.bureau.enums.OperationType;
import com.cibil.bureau.exception.BureauApiException;

import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

/**
 * Service for integrating with CIBIL Bureau API.
 */
@Service
public class CibilBureauApiService {

    private static final Logger logger = LoggerFactory.getLogger(CibilBureauApiService.class);

    private final WebClient cibilBureauWebClient;
    private final CibilBureauProperties cibilBureauProperties;
    private final AuditService auditService;

    public CibilBureauApiService(WebClient cibilBureauWebClient,
                                CibilBureauProperties cibilBureauProperties,
                                AuditService auditService) {
        this.cibilBureauWebClient = cibilBureauWebClient;
        this.cibilBureauProperties = cibilBureauProperties;
        this.auditService = auditService;
    }

    /**
     * Fetches credit report from CIBIL Bureau API with retry logic.
     *
     * @param request the bureau credit request
     * @param referenceNumber the reference number for tracking
     * @return the bureau credit response
     * @throws BureauApiException if API call fails after all retries
     */
    public BureauCreditResponse fetchCreditReport(BureauCreditRequest request, String referenceNumber) {
        logger.info("Fetching credit report from CIBIL Bureau for reference: {}", referenceNumber);

        // Set trace ID in MDC for logging
        MDC.put("traceId", referenceNumber);

        try {
            // Log request initiation
            auditService.logRequestInitiation(referenceNumber, OperationType.BUREAU_API_CALL, request);

            BureauCreditResponse response = cibilBureauWebClient
                    .post()
                    .uri(cibilBureauProperties.getEndpoints().getCreditReport())
                    .bodyValue(request)
                    .retrieve()
                    .onStatus(status -> status.isError(), clientResponse -> {
                        return clientResponse.bodyToMono(String.class)
                                .flatMap(errorBody -> {
                                    String errorMessage = String.format("CIBIL Bureau API error: %s - %s",
                                                                       clientResponse.statusCode(), errorBody);
                                    logger.error("Bureau API error for reference: {} - Status: {} - Body: {}",
                                               referenceNumber, clientResponse.statusCode(), errorBody);

                                    auditService.logFailure(referenceNumber, OperationType.BUREAU_API_CALL, errorMessage);

                                    return Mono.error(new BureauApiException(errorMessage,
                                                                           clientResponse.statusCode().value(),
                                                                           errorBody));
                                });
                    })
                    .bodyToMono(BureauCreditResponse.class)
                    .retryWhen(Retry.backoff(cibilBureauProperties.getRetry().getMaxAttempts(),
                                           Duration.ofMillis(cibilBureauProperties.getRetry().getDelay()))
                                   .jitter(0.5)
                                   .doBeforeRetry(retrySignal -> {
                                       int attemptNumber = (int) retrySignal.totalRetries() + 1;
                                       logger.warn("Retrying CIBIL Bureau API call for reference: {} - Attempt: {}",
                                                 referenceNumber, attemptNumber);
                                       auditService.logRetryAttempt(referenceNumber, OperationType.BUREAU_API_CALL, attemptNumber);
                                   })
                                   .filter(throwable -> throwable instanceof WebClientResponseException &&
                                                      isRetryableError((WebClientResponseException) throwable)))
                    .block();

            if (response == null) {
                String errorMessage = "Received null response from CIBIL Bureau API";
                logger.error("Null response from Bureau API for reference: {}", referenceNumber);
                auditService.logFailure(referenceNumber, OperationType.BUREAU_API_CALL, errorMessage);
                throw new BureauApiException(errorMessage);
            }

            logger.info("Successfully received credit report from CIBIL Bureau for reference: {}", referenceNumber);
            auditService.logSuccessfulCompletion(referenceNumber, OperationType.BUREAU_API_CALL, response);

            return response;

        } catch (WebClientResponseException e) {
            String errorMessage = String.format("CIBIL Bureau API call failed for reference: %s - Status: %s - Body: %s",
                                               referenceNumber, e.getStatusCode(), e.getResponseBodyAsString());
            logger.error(errorMessage, e);
            auditService.logFailure(referenceNumber, OperationType.BUREAU_API_CALL, errorMessage);
            throw new BureauApiException(errorMessage, e.getStatusCode().value(), e.getResponseBodyAsString(), e);

        } catch (Exception e) {
            String errorMessage = String.format("Unexpected error during CIBIL Bureau API call for reference: %s", referenceNumber);
            logger.error(errorMessage, e);
            auditService.logFailure(referenceNumber, OperationType.BUREAU_API_CALL, errorMessage + " - " + e.getMessage());
            throw new BureauApiException(errorMessage, e);

        } finally {
            MDC.remove("traceId");
        }
    }

    /**
     * Checks if an error is retryable.
     *
     * @param exception the WebClientResponseException
     * @return true if retryable, false otherwise
     */
    private boolean isRetryableError(WebClientResponseException exception) {
        int statusCode = exception.getStatusCode().value();

        // Retry on server errors (5xx) and some client errors
        return statusCode >= 500 ||
               statusCode == 408 || // REQUEST_TIMEOUT
               statusCode == 429;   // TOO_MANY_REQUESTS
    }

    /**
     * Checks if CIBIL Bureau API is available.
     *
     * @return true if available, false otherwise
     */
    public boolean isApiAvailable() {
        try {
            // Simple health check - could be a dedicated health endpoint
            cibilBureauWebClient
                    .get()
                    .uri("/health")
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(5))
                    .block();
            return true;
        } catch (Exception e) {
            logger.warn("CIBIL Bureau API health check failed", e);
            return false;
        }
    }

    /**
     * Gets the maximum retry attempts configured.
     *
     * @return maximum retry attempts
     */
    public int getMaxRetryAttempts() {
        return cibilBureauProperties.getRetry().getMaxAttempts();
    }

    /**
     * Gets the retry delay configured.
     *
     * @return retry delay in milliseconds
     */
    public long getRetryDelay() {
        return cibilBureauProperties.getRetry().getDelay();
    }
}
