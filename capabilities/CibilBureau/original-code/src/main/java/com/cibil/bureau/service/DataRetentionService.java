package com.cibil.bureau.service;

import com.cibil.bureau.config.ApplicationProperties;
import com.cibil.bureau.repository.AuditLogRepository;
import com.cibil.bureau.repository.BorrowerInfoRepository;
import com.cibil.bureau.repository.CreditReportRepository;
import com.cibil.bureau.repository.CreditSummaryRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * Service for managing data retention policies and cleanup operations.
 */
@Service
public class DataRetentionService {

    private static final Logger logger = LoggerFactory.getLogger(DataRetentionService.class);

    private final ApplicationProperties applicationProperties;
    private final BorrowerInfoRepository borrowerInfoRepository;
    private final CreditReportRepository creditReportRepository;
    private final CreditSummaryRepository creditSummaryRepository;
    private final AuditLogRepository auditLogRepository;

    public DataRetentionService(ApplicationProperties applicationProperties,
                               BorrowerInfoRepository borrowerInfoRepository,
                               CreditReportRepository creditReportRepository,
                               CreditSummaryRepository creditSummaryRepository,
                               AuditLogRepository auditLogRepository) {
        this.applicationProperties = applicationProperties;
        this.borrowerInfoRepository = borrowerInfoRepository;
        this.creditReportRepository = creditReportRepository;
        this.creditSummaryRepository = creditSummaryRepository;
        this.auditLogRepository = auditLogRepository;
    }

    /**
     * Scheduled cleanup job that runs daily at 2 AM.
     * Removes data older than the configured retention period.
     */
    @Scheduled(cron = "0 0 2 * * ?") // Daily at 2 AM
    @Transactional
    public void performScheduledCleanup() {
        logger.info("Starting scheduled data retention cleanup");
        
        try {
            DataRetentionResult result = performDataCleanup();
            
            logger.info("Scheduled data retention cleanup completed successfully. " +
                       "Deleted - Borrower Info: {}, Credit Reports: {}, Credit Summaries: {}, Audit Logs: {}",
                       result.getBorrowerInfoDeleted(),
                       result.getCreditReportsDeleted(),
                       result.getCreditSummariesDeleted(),
                       result.getAuditLogsDeleted());
                       
        } catch (Exception e) {
            logger.error("Scheduled data retention cleanup failed", e);
        }
    }

    /**
     * Performs data cleanup based on retention policy.
     * 
     * @return cleanup result with counts of deleted records
     */
    @Transactional
    public DataRetentionResult performDataCleanup() {
        LocalDateTime cutoffDate = LocalDateTime.now()
                .minusDays(applicationProperties.getDataRetention().getDays());
        
        logger.info("Performing data cleanup for records older than: {}", cutoffDate);
        
        DataRetentionResult result = new DataRetentionResult();
        
        // Clean up credit summaries first (foreign key dependency)
        int creditSummariesDeleted = creditSummaryRepository.deleteByCreatedAtBefore(cutoffDate);
        result.setCreditSummariesDeleted(creditSummariesDeleted);
        logger.debug("Deleted {} credit summaries", creditSummariesDeleted);
        
        // Clean up credit reports
        int creditReportsDeleted = creditReportRepository.deleteByCreatedAtBefore(cutoffDate);
        result.setCreditReportsDeleted(creditReportsDeleted);
        logger.debug("Deleted {} credit reports", creditReportsDeleted);
        
        // Clean up borrower info
        int borrowerInfoDeleted = borrowerInfoRepository.deleteByCreatedAtBefore(cutoffDate);
        result.setBorrowerInfoDeleted(borrowerInfoDeleted);
        logger.debug("Deleted {} borrower info records", borrowerInfoDeleted);
        
        // Clean up audit logs (keep longer for compliance)
        LocalDateTime auditCutoffDate = LocalDateTime.now()
                .minusDays(applicationProperties.getDataRetention().getDays() * 2); // Keep audit logs twice as long
        int auditLogsDeleted = auditLogRepository.deleteByCreatedAtBefore(auditCutoffDate);
        result.setAuditLogsDeleted(auditLogsDeleted);
        logger.debug("Deleted {} audit logs", auditLogsDeleted);
        
        return result;
    }

    /**
     * Gets data retention statistics.
     * 
     * @return retention statistics
     */
    @Transactional(readOnly = true)
    public DataRetentionStatistics getRetentionStatistics() {
        LocalDateTime cutoffDate = LocalDateTime.now()
                .minusDays(applicationProperties.getDataRetention().getDays());
        
        long totalBorrowerInfo = borrowerInfoRepository.count();
        long expiredBorrowerInfo = borrowerInfoRepository.findByCreatedAtBefore(cutoffDate).size();
        
        long totalCreditReports = creditReportRepository.count();
        long expiredCreditReports = creditReportRepository.findByCreatedAtBefore(cutoffDate).size();
        
        long totalAuditLogs = auditLogRepository.count();
        LocalDateTime auditCutoffDate = LocalDateTime.now()
                .minusDays(applicationProperties.getDataRetention().getDays() * 2);
        long expiredAuditLogs = auditLogRepository.findByCreatedAtBefore(auditCutoffDate).size();
        
        return new DataRetentionStatistics(
                totalBorrowerInfo, expiredBorrowerInfo,
                totalCreditReports, expiredCreditReports,
                totalAuditLogs, expiredAuditLogs,
                applicationProperties.getDataRetention().getDays()
        );
    }

    /**
     * Validates data retention configuration.
     * 
     * @return true if configuration is valid
     */
    public boolean validateRetentionConfiguration() {
        int retentionDays = applicationProperties.getDataRetention().getDays();
        
        if (retentionDays <= 0) {
            logger.error("Invalid retention period: {} days. Must be positive.", retentionDays);
            return false;
        }
        
        if (retentionDays < 30) {
            logger.warn("Retention period is less than 30 days: {} days. This may not comply with regulations.", retentionDays);
        }
        
        return true;
    }

    /**
     * Result class for data cleanup operations.
     */
    public static class DataRetentionResult {
        private int borrowerInfoDeleted;
        private int creditReportsDeleted;
        private int creditSummariesDeleted;
        private int auditLogsDeleted;

        public int getBorrowerInfoDeleted() {
            return borrowerInfoDeleted;
        }

        public void setBorrowerInfoDeleted(int borrowerInfoDeleted) {
            this.borrowerInfoDeleted = borrowerInfoDeleted;
        }

        public int getCreditReportsDeleted() {
            return creditReportsDeleted;
        }

        public void setCreditReportsDeleted(int creditReportsDeleted) {
            this.creditReportsDeleted = creditReportsDeleted;
        }

        public int getCreditSummariesDeleted() {
            return creditSummariesDeleted;
        }

        public void setCreditSummariesDeleted(int creditSummariesDeleted) {
            this.creditSummariesDeleted = creditSummariesDeleted;
        }

        public int getAuditLogsDeleted() {
            return auditLogsDeleted;
        }

        public void setAuditLogsDeleted(int auditLogsDeleted) {
            this.auditLogsDeleted = auditLogsDeleted;
        }

        public int getTotalDeleted() {
            return borrowerInfoDeleted + creditReportsDeleted + creditSummariesDeleted + auditLogsDeleted;
        }

        @Override
        public String toString() {
            return "DataRetentionResult{" +
                    "borrowerInfoDeleted=" + borrowerInfoDeleted +
                    ", creditReportsDeleted=" + creditReportsDeleted +
                    ", creditSummariesDeleted=" + creditSummariesDeleted +
                    ", auditLogsDeleted=" + auditLogsDeleted +
                    ", totalDeleted=" + getTotalDeleted() +
                    '}';
        }
    }

    /**
     * Statistics class for data retention information.
     */
    public static class DataRetentionStatistics {
        private final long totalBorrowerInfo;
        private final long expiredBorrowerInfo;
        private final long totalCreditReports;
        private final long expiredCreditReports;
        private final long totalAuditLogs;
        private final long expiredAuditLogs;
        private final int retentionDays;

        public DataRetentionStatistics(long totalBorrowerInfo, long expiredBorrowerInfo,
                                     long totalCreditReports, long expiredCreditReports,
                                     long totalAuditLogs, long expiredAuditLogs,
                                     int retentionDays) {
            this.totalBorrowerInfo = totalBorrowerInfo;
            this.expiredBorrowerInfo = expiredBorrowerInfo;
            this.totalCreditReports = totalCreditReports;
            this.expiredCreditReports = expiredCreditReports;
            this.totalAuditLogs = totalAuditLogs;
            this.expiredAuditLogs = expiredAuditLogs;
            this.retentionDays = retentionDays;
        }

        // Getters
        public long getTotalBorrowerInfo() { return totalBorrowerInfo; }
        public long getExpiredBorrowerInfo() { return expiredBorrowerInfo; }
        public long getTotalCreditReports() { return totalCreditReports; }
        public long getExpiredCreditReports() { return expiredCreditReports; }
        public long getTotalAuditLogs() { return totalAuditLogs; }
        public long getExpiredAuditLogs() { return expiredAuditLogs; }
        public int getRetentionDays() { return retentionDays; }

        @Override
        public String toString() {
            return "DataRetentionStatistics{" +
                    "totalBorrowerInfo=" + totalBorrowerInfo +
                    ", expiredBorrowerInfo=" + expiredBorrowerInfo +
                    ", totalCreditReports=" + totalCreditReports +
                    ", expiredCreditReports=" + expiredCreditReports +
                    ", totalAuditLogs=" + totalAuditLogs +
                    ", expiredAuditLogs=" + expiredAuditLogs +
                    ", retentionDays=" + retentionDays +
                    '}';
        }
    }
}
