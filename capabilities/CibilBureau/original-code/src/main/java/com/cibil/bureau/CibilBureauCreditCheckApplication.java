package com.cibil.bureau;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main application class for CIBIL Bureau Credit Check Service.
 * 
 * This service provides end-to-end credit check functionality by:
 * 1. Validating borrower information
 * 2. Integrating with CIBIL bureau API
 * 3. Analyzing credit reports and making lending decisions
 * 4. Providing comprehensive audit logging
 */
@SpringBootApplication
@EnableJpaAuditing
@EnableAsync
@EnableTransactionManagement
@ConfigurationPropertiesScan
public class CibilBureauCreditCheckApplication {

    public static void main(String[] args) {
        SpringApplication.run(CibilBureauCreditCheckApplication.class, args);
    }
}
