package com.cibil.bureau.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cibil.bureau.dto.CreditCheckRequest;
import com.cibil.bureau.dto.CreditCheckResponse;
import com.cibil.bureau.service.CreditCheckService;
import com.cibil.bureau.util.DataMaskingUtil;

import jakarta.validation.Valid;

/**
 * REST controller for credit check operations.
 */
@RestController
@RequestMapping("/credit-check")
public class CreditCheckController {

    private static final Logger logger = LoggerFactory.getLogger(CreditCheckController.class);

    private final CreditCheckService creditCheckService;
    private final DataMaskingUtil dataMaskingUtil;

    public CreditCheckController(CreditCheckService creditCheckService, DataMaskingUtil dataMaskingUtil) {
        this.creditCheckService = creditCheckService;
        this.dataMaskingUtil = dataMaskingUtil;
    }

    /**
     * Processes a credit check request.
     *
     * @param request the credit check request
     * @return the credit check response
     */
    @PostMapping
    public ResponseEntity<CreditCheckResponse> processCreditCheck(@Valid @RequestBody CreditCheckRequest request) {
        logger.info("Received credit check request: {}", dataMaskingUtil.maskObject(request));

        try {
            CreditCheckResponse response = creditCheckService.processCreditCheck(request);

            logger.info("Credit check completed - Reference: {} - Eligible: {}",
                       response.getReferenceNumber(), response.isEligible());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Credit check processing failed", e);
            throw e; // Let global exception handler deal with it
        }
    }

    /**
     * Gets credit check status by reference number.
     *
     * @param referenceNumber the reference number
     * @return the credit check response
     */
    @GetMapping("/{referenceNumber}")
    public ResponseEntity<CreditCheckResponse> getCreditCheckStatus(@PathVariable String referenceNumber) {
        logger.info("Received status request for reference: {}", referenceNumber);

        CreditCheckResponse response = creditCheckService.getCreditCheckStatus(referenceNumber);

        if (response == null) {
            logger.warn("Credit check not found for reference: {}", referenceNumber);
            return ResponseEntity.notFound().build();
        }

        logger.info("Credit check status retrieved for reference: {} - Status: {}",
                   referenceNumber, response.getStatus());

        return ResponseEntity.ok(response);
    }

    /**
     * Health check endpoint.
     *
     * @return health status
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("Credit Check Service is healthy");
    }
}
