package com.cibil.bureau.repository;

import com.cibil.bureau.entity.CreditSummary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for CreditSummary entity.
 */
@Repository
public interface CreditSummaryRepository extends JpaRepository<CreditSummary, Long> {

    /**
     * Finds credit summary by credit report ID.
     * 
     * @param creditReportId the credit report ID
     * @return optional credit summary
     */
    Optional<CreditSummary> findByCreditReportId(Long creditReportId);

    /**
     * Finds credit summaries with defaults count greater than threshold.
     * 
     * @param threshold the defaults count threshold
     * @return list of credit summaries
     */
    List<CreditSummary> findByDefaultsCountGreaterThan(int threshold);

    /**
     * Finds credit summaries with recent inquiries greater than threshold.
     * 
     * @param threshold the recent inquiries threshold
     * @return list of credit summaries
     */
    List<CreditSummary> findByRecentInquiriesGreaterThan(int threshold);

    /**
     * Finds credit summaries created after a specific date.
     * 
     * @param createdAfter the date after which to search
     * @return list of credit summaries
     */
    List<CreditSummary> findByCreatedAtAfter(LocalDateTime createdAfter);

    /**
     * Finds credit summaries created before a specific date for data retention.
     * 
     * @param createdBefore the date before which to search
     * @return list of credit summaries
     */
    List<CreditSummary> findByCreatedAtBefore(LocalDateTime createdBefore);

    /**
     * Counts credit summaries with no defaults.
     * 
     * @return count of credit summaries
     */
    @Query("SELECT COUNT(cs) FROM CreditSummary cs WHERE cs.defaultsCount = 0")
    long countByNoDefaults();

    /**
     * Counts credit summaries with active accounts greater than threshold.
     * 
     * @param threshold the active accounts threshold
     * @return count of credit summaries
     */
    @Query("SELECT COUNT(cs) FROM CreditSummary cs WHERE cs.activeAccounts > :threshold")
    long countByActiveAccountsGreaterThan(@Param("threshold") int threshold);

    /**
     * Finds credit summaries by total accounts range.
     * 
     * @param minAccounts the minimum total accounts
     * @param maxAccounts the maximum total accounts
     * @return list of credit summaries
     */
    @Query("SELECT cs FROM CreditSummary cs WHERE cs.totalAccounts BETWEEN :minAccounts AND :maxAccounts")
    List<CreditSummary> findByTotalAccountsBetween(@Param("minAccounts") int minAccounts, 
                                                   @Param("maxAccounts") int maxAccounts);

    /**
     * Checks if credit summary exists by credit report ID.
     * 
     * @param creditReportId the credit report ID
     * @return true if exists, false otherwise
     */
    boolean existsByCreditReportId(Long creditReportId);

    /**
     * Deletes credit summaries created before a specific date for data retention.
     * 
     * @param createdBefore the date before which to delete
     * @return number of deleted records
     */
    @Query("DELETE FROM CreditSummary cs WHERE cs.createdAt < :createdBefore")
    int deleteByCreatedAtBefore(@Param("createdBefore") LocalDateTime createdBefore);
}
