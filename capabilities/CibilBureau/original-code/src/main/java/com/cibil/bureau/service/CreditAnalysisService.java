package com.cibil.bureau.service;

import java.time.format.DateTimeFormatter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.cibil.bureau.config.ApplicationProperties;
import com.cibil.bureau.dto.BureauCreditResponse;
import com.cibil.bureau.dto.CreditCheckResponse;
import com.cibil.bureau.entity.CreditReport;
import com.cibil.bureau.enums.OperationType;
import com.cibil.bureau.enums.ProcessingStatus;
import com.cibil.bureau.enums.RiskLevel;

/**
 * Service for analyzing credit reports and making lending decisions.
 */
@Service
public class CreditAnalysisService {

    private static final Logger logger = LoggerFactory.getLogger(CreditAnalysisService.class);

    // Business messages as per requirements
    private static final String MESSAGE_ELIGIBLE = "Congratulations! You are eligible for the loan.";
    private static final String MESSAGE_LOW_SCORE = "We are unable to process your loan digitally at this point in time. Please visit your nearest branch for assistance.";
    private static final String MESSAGE_INCOMPLETE_RESPONSE = "We are unable to process your loan digitally at this point in time. Please visit your nearest branch for assistance.";
    private static final String MESSAGE_BUREAU_UNAVAILABLE = "We're unable to fetch your credit score right now. Please try again later.";

    private final ApplicationProperties applicationProperties;
    private final AuditService auditService;

    public CreditAnalysisService(ApplicationProperties applicationProperties, AuditService auditService) {
        this.applicationProperties = applicationProperties;
        this.auditService = auditService;
    }

    /**
     * Analyzes bureau response and makes lending decision.
     *
     * @param bureauResponse the bureau credit response
     * @param creditReport the credit report entity
     * @return the credit check response
     */
    public CreditCheckResponse analyzeCreditReport(BureauCreditResponse bureauResponse, CreditReport creditReport) {
        String referenceNumber = creditReport.getReferenceNumber();
        logger.info("Starting credit analysis for reference: {}", referenceNumber);

        auditService.logRequestInitiation(referenceNumber, OperationType.CREDIT_ANALYSIS, bureauResponse);

        try {
            // Phase 3: Analyze bureau response and make lending decision
            CreditCheckResponse response = performCreditAnalysis(bureauResponse, creditReport);

            auditService.logSuccessfulCompletion(referenceNumber, OperationType.CREDIT_ANALYSIS, response);
            auditService.logOperation(referenceNumber, OperationType.DECISION_MADE, "DECISION",
                                    referenceNumber, null, response, response.getStatus().name());

            logger.info("Credit analysis completed for reference: {} - Decision: {} - Eligible: {}",
                       referenceNumber, response.getStatus(), response.isEligible());

            return response;

        } catch (Exception e) {
            String errorMessage = "Credit analysis failed for reference: " + referenceNumber;
            logger.error(errorMessage, e);
            auditService.logFailure(referenceNumber, OperationType.CREDIT_ANALYSIS, errorMessage + " - " + e.getMessage());

            return createErrorResponse(referenceNumber, MESSAGE_INCOMPLETE_RESPONSE, ProcessingStatus.ERROR);
        }
    }

    /**
     * Performs the actual credit analysis based on business rules.
     */
    private CreditCheckResponse performCreditAnalysis(BureauCreditResponse bureauResponse, CreditReport creditReport) {
        String referenceNumber = creditReport.getReferenceNumber();

        // Check if response is complete (all key fields present)
        if (!isResponseComplete(bureauResponse)) {
            logger.warn("Incomplete bureau response for reference: {} - Missing key fields", referenceNumber);
            return createErrorResponseWithBorrowerInfo(creditReport, bureauResponse, MESSAGE_INCOMPLETE_RESPONSE, ProcessingStatus.INCOMPLETE_RESPONSE);
        }

        // Check credit score threshold
        Integer creditScore = bureauResponse.getCreditScore();
        if (creditScore == null || creditScore < applicationProperties.getMinimumScore()) {
            logger.warn("Credit score below threshold for reference: {} - Score: {} - Threshold: {}",
                       referenceNumber, creditScore, applicationProperties.getMinimumScore());
            return createErrorResponseWithBorrowerInfo(creditReport, bureauResponse, MESSAGE_LOW_SCORE, ProcessingStatus.NOT_ELIGIBLE);
        }

        // All checks passed - borrower is eligible
        logger.info("All credit checks passed for reference: {} - Score: {}", referenceNumber, creditScore);
        return createSuccessResponse(bureauResponse, creditReport);
    }

    /**
     * Checks if bureau response contains all required fields.
     * Note: Credit score is validated separately to distinguish between incomplete response and low score.
     */
    private boolean isResponseComplete(BureauCreditResponse bureauResponse) {
        if (bureauResponse == null) {
            return false;
        }

        // Credit score is NOT checked here - null credit score should be treated as NOT_ELIGIBLE, not INCOMPLETE_RESPONSE
        return bureauResponse.getFullName() != null && !bureauResponse.getFullName().trim().isEmpty() &&
               bureauResponse.getDateOfBirth() != null && !bureauResponse.getDateOfBirth().trim().isEmpty() &&
               bureauResponse.getOutcomeStatus() != null && !bureauResponse.getOutcomeStatus().trim().isEmpty();
    }

    /**
     * Creates a successful credit check response.
     */
    private CreditCheckResponse createSuccessResponse(BureauCreditResponse bureauResponse, CreditReport creditReport) {
        CreditCheckResponse response = new CreditCheckResponse(
                creditReport.getReferenceNumber(),
                bureauResponse.getFullName(),
                bureauResponse.getDateOfBirth(),
                creditReport.getPanNumber(),  // Use from credit report as it's more reliable
                creditReport.getPhoneNumber(), // Use from credit report as it's more reliable
                bureauResponse.getCreditScore(),
                RiskLevel.fromDisplayName(bureauResponse.getRiskLevel()),
                bureauResponse.getRecommendation(),
                MESSAGE_ELIGIBLE,
                ProcessingStatus.ELIGIBLE,
                bureauResponse.getIsEligible() != null ? bureauResponse.getIsEligible() : true
        );

        // Add credit summary if available
        if (bureauResponse.getCreditSummary() != null) {
            response.setCreditSummary(bureauResponse.getCreditSummary());
        }

        return response;
    }

    /**
     * Creates an error credit check response.
     */
    private CreditCheckResponse createErrorResponse(String referenceNumber, String message, ProcessingStatus status) {
        return new CreditCheckResponse(referenceNumber, message, status);
    }

    /**
     * Creates an error credit check response with borrower information when available.
     */
    private CreditCheckResponse createErrorResponseWithBorrowerInfo(CreditReport creditReport, BureauCreditResponse bureauResponse, String message, ProcessingStatus status) {
        CreditCheckResponse response = new CreditCheckResponse();
        response.setReferenceNumber(creditReport.getReferenceNumber());
        response.setMessage(message);
        response.setStatus(status);
        response.setEligible(false);

        // Include borrower information from credit report (always available)
        response.setFullName(creditReport.getFullName());
        response.setPanNumber(creditReport.getPanNumber());
        response.setPhoneNumber(creditReport.getPhoneNumber());

        // Include date of birth if available in credit report
        if (creditReport.getDateOfBirth() != null) {
            response.setDateOfBirth(creditReport.getDateOfBirth().format(DateTimeFormatter.ofPattern("ddMMyyyy")));
        }

        // Include bureau data if available (for NOT_ELIGIBLE cases where we have partial data)
        if (bureauResponse != null) {
            if (bureauResponse.getCreditScore() != null) {
                response.setCreditScore(bureauResponse.getCreditScore());
            }
            if (bureauResponse.getRiskLevel() != null) {
                response.setRiskLevel(RiskLevel.fromDisplayName(bureauResponse.getRiskLevel()));
            }
            if (bureauResponse.getRecommendation() != null) {
                response.setRecommendation(bureauResponse.getRecommendation());
            }
            if (bureauResponse.getCreditSummary() != null) {
                response.setCreditSummary(bureauResponse.getCreditSummary());
            }
        }

        return response;
    }

    /**
     * Creates response for bureau unavailable scenario.
     */
    public CreditCheckResponse createBureauUnavailableResponse(String referenceNumber) {
        logger.warn("Bureau unavailable for reference: {}", referenceNumber);
        auditService.logFailure(referenceNumber, OperationType.CREDIT_ANALYSIS, "Bureau unavailable after all retries");
        return createErrorResponse(referenceNumber, MESSAGE_BUREAU_UNAVAILABLE, ProcessingStatus.BUREAU_UNAVAILABLE);
    }

    /**
     * Creates response for bureau unavailable scenario with borrower information.
     */
    public CreditCheckResponse createBureauUnavailableResponse(CreditReport creditReport) {
        String referenceNumber = creditReport.getReferenceNumber();
        logger.warn("Bureau unavailable for reference: {}", referenceNumber);
        auditService.logFailure(referenceNumber, OperationType.CREDIT_ANALYSIS, "Bureau unavailable after all retries");

        CreditCheckResponse response = new CreditCheckResponse();
        response.setReferenceNumber(referenceNumber);
        response.setFullName(creditReport.getFullName());
        response.setDateOfBirth(creditReport.getDateOfBirth().format(DateTimeFormatter.ofPattern("ddMMyyyy")));
        response.setPanNumber(creditReport.getPanNumber());
        response.setPhoneNumber(creditReport.getPhoneNumber());
        response.setMessage(MESSAGE_BUREAU_UNAVAILABLE);
        response.setStatus(ProcessingStatus.BUREAU_UNAVAILABLE);
        response.setEligible(false);

        return response;
    }

    /**
     * Validates credit score against business rules.
     *
     * @param creditScore the credit score to validate
     * @return true if score meets minimum threshold
     */
    public boolean isScoreEligible(Integer creditScore) {
        return creditScore != null && creditScore >= applicationProperties.getMinimumScore();
    }

    /**
     * Gets the minimum credit score threshold.
     *
     * @return minimum credit score
     */
    public int getMinimumCreditScore() {
        return applicationProperties.getMinimumScore();
    }

    /**
     * Determines risk level based on credit score.
     *
     * @param creditScore the credit score
     * @return the risk level
     */
    public RiskLevel determineRiskLevel(Integer creditScore) {
        if (creditScore == null) {
            return RiskLevel.UNKNOWN;
        }

        if (creditScore >= 750) {
            return RiskLevel.LOW_RISK;
        } else if (creditScore >= 650) {
            return RiskLevel.MEDIUM_RISK;
        } else {
            return RiskLevel.HIGH_RISK;
        }
    }

    /**
     * Generates recommendation based on credit analysis.
     *
     * @param creditScore the credit score
     * @param riskLevel the risk level
     * @return the recommendation
     */
    public String generateRecommendation(Integer creditScore, RiskLevel riskLevel) {
        if (creditScore == null || creditScore < applicationProperties.getMinimumScore()) {
            return "Not Eligible";
        }

        return switch (riskLevel) {
            case LOW_RISK -> "Highly Eligible";
            case MEDIUM_RISK -> "Eligible";
            case HIGH_RISK -> "Conditionally Eligible";
            default -> "Eligible";
        };
    }
}
