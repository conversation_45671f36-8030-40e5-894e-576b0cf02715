package com.cibil.bureau.exception;

/**
 * Exception thrown during credit check processing.
 */
public class CreditCheckException extends RuntimeException {

    private final String referenceNumber;

    public CreditCheckException(String message, String referenceNumber) {
        super(message);
        this.referenceNumber = referenceNumber;
    }

    public CreditCheckException(String message, String referenceNumber, Throwable cause) {
        super(message, cause);
        this.referenceNumber = referenceNumber;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }
}
