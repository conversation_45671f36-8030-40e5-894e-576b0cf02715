package com.cibil.bureau.enums;

/**
 * Enum representing the risk level assessment from credit bureau.
 */
public enum RiskLevel {
    LOW_RISK("Low Risk"),
    MEDIUM_RISK("Medium Risk"),
    HIGH_RISK("High Risk"),
    UNKNOWN("Unknown");

    private final String displayName;

    RiskLevel(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    /**
     * Gets the enum value from display name.
     * 
     * @param riskLevel the display name
     * @return the corresponding enum value, defaults to UNKNOWN if not found
     */
    public static RiskLevel fromDisplayName(String riskLevel) {
        if (riskLevel == null) {
            return UNKNOWN;
        }
        
        for (RiskLevel level : values()) {
            if (level.displayName.equalsIgnoreCase(riskLevel.trim())) {
                return level;
            }
        }
        return UNKNOWN;
    }
}
