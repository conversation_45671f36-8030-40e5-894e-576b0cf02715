package com.cibil.bureau.enums;

/**
 * Enum representing the processing status of credit check requests.
 */
public enum ProcessingStatus {
    INITIATED("Initiated"),
    VALIDATION_PASSED("Validation Passed"),
    VALIDATION_FAILED("Validation Failed"),
    BUREAU_REQUEST_SENT("Bureau Request Sent"),
    BUREAU_RESPONSE_RECEIVED("Bureau Response Received"),
    BUREAU_REQUEST_FAILED("Bureau Request Failed"),
    ANALYSIS_COMPLETED("Analysis Completed"),
    ELIGIBLE("Eligible"),
    NOT_ELIGIBLE("Not Eligible"),
    INCOMPLETE_RESPONSE("Incomplete Response"),
    BUREAU_UNAVAILABLE("Bureau Unavailable"),
    ERROR("Error");

    private final String displayName;

    ProcessingStatus(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    /**
     * Checks if the status represents a final state.
     * 
     * @return true if this is a final status
     */
    public boolean isFinalStatus() {
        return this == ELIGIBLE || 
               this == NOT_ELIGIBLE || 
               this == INCOMPLETE_RESPONSE || 
               this == BUREAU_UNAVAILABLE || 
               this == ERROR ||
               this == VALIDATION_FAILED;
    }

    /**
     * Checks if the status represents a successful completion.
     * 
     * @return true if this represents success
     */
    public boolean isSuccessful() {
        return this == ELIGIBLE;
    }
}
