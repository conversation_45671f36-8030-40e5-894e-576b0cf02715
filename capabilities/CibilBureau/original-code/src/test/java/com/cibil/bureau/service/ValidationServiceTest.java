package com.cibil.bureau.service;

import com.cibil.bureau.config.ApplicationProperties;
import com.cibil.bureau.dto.CreditCheckRequest;
import com.cibil.bureau.exception.ValidationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ValidationService.
 */
@ExtendWith(SpringExtension.class)
class ValidationServiceTest {

    private ValidationService validationService;
    private ApplicationProperties applicationProperties;

    @BeforeEach
    void setUp() {
        applicationProperties = new ApplicationProperties();
        applicationProperties.setMinimumScore(650);
        
        ApplicationProperties.Validation validation = new ApplicationProperties.Validation();
        validation.setPanPattern("[A-Z]{5}[0-9]{4}[A-Z]{1}");
        validation.setPhonePattern("[0-9]{10}");
        validation.setNamePattern("[a-zA-Z\\s]{2,50}");
        applicationProperties.setValidation(validation);
        
        validationService = new ValidationService(applicationProperties);
    }

    @Test
    void testValidCreditCheckRequest() {
        CreditCheckRequest request = createValidRequest();
        
        assertDoesNotThrow(() -> validationService.validateCreditCheckRequest(request, "TEST-REF-001"));
    }

    @Test
    void testInvalidFirstName() {
        CreditCheckRequest request = createValidRequest();
        request.setFirstName("A"); // Too short
        
        ValidationException exception = assertThrows(ValidationException.class, 
            () -> validationService.validateCreditCheckRequest(request, "TEST-REF-001"));
        
        assertTrue(exception.getMessage().contains("First name must be between 2 and 50 characters"));
    }

    @Test
    void testInvalidPanNumber() {
        CreditCheckRequest request = createValidRequest();
        request.setPanNumber("INVALID"); // Invalid format
        
        ValidationException exception = assertThrows(ValidationException.class, 
            () -> validationService.validateCreditCheckRequest(request, "TEST-REF-001"));
        
        assertTrue(exception.getMessage().contains("PAN number must be in valid format"));
    }

    @Test
    void testInvalidPhoneNumber() {
        CreditCheckRequest request = createValidRequest();
        request.setPhoneNumber("123"); // Too short
        
        ValidationException exception = assertThrows(ValidationException.class, 
            () -> validationService.validateCreditCheckRequest(request, "TEST-REF-001"));
        
        assertTrue(exception.getMessage().contains("Phone number must be exactly 10 digits"));
    }

    @Test
    void testInvalidDateOfBirth() {
        CreditCheckRequest request = createValidRequest();
        request.setDateOfBirth("32012000"); // Invalid date
        
        ValidationException exception = assertThrows(ValidationException.class, 
            () -> validationService.validateCreditCheckRequest(request, "TEST-REF-001"));
        
        assertTrue(exception.getMessage().contains("Invalid date of birth format"));
    }

    @Test
    void testFutureDateOfBirth() {
        CreditCheckRequest request = createValidRequest();
        LocalDate futureDate = LocalDate.now().plusDays(1);
        request.setDateOfBirth(futureDate.format(java.time.format.DateTimeFormatter.ofPattern("ddMMyyyy")));
        
        ValidationException exception = assertThrows(ValidationException.class, 
            () -> validationService.validateCreditCheckRequest(request, "TEST-REF-001"));
        
        assertTrue(exception.getMessage().contains("Date of birth cannot be in the future"));
    }

    @Test
    void testUnderage() {
        CreditCheckRequest request = createValidRequest();
        LocalDate underageDate = LocalDate.now().minusYears(17);
        request.setDateOfBirth(underageDate.format(java.time.format.DateTimeFormatter.ofPattern("ddMMyyyy")));
        
        ValidationException exception = assertThrows(ValidationException.class, 
            () -> validationService.validateCreditCheckRequest(request, "TEST-REF-001"));
        
        assertTrue(exception.getMessage().contains("Borrower must be at least 18 years old"));
    }

    @Test
    void testInvalidLoanPurpose() {
        CreditCheckRequest request = createValidRequest();
        request.setLoanPurpose("Home Loan"); // Not supported
        
        ValidationException exception = assertThrows(ValidationException.class, 
            () -> validationService.validateCreditCheckRequest(request, "TEST-REF-001"));
        
        assertTrue(exception.getMessage().contains("Only Personal Loan is supported"));
    }

    @Test
    void testInvalidIdType() {
        CreditCheckRequest request = createValidRequest();
        request.setIdType("Aadhar Card"); // Not supported
        
        ValidationException exception = assertThrows(ValidationException.class, 
            () -> validationService.validateCreditCheckRequest(request, "TEST-REF-001"));
        
        assertTrue(exception.getMessage().contains("Only PAN Card is supported"));
    }

    @Test
    void testParseDateOfBirth() {
        LocalDate parsed = validationService.parseDateOfBirth("15081990");
        assertEquals(LocalDate.of(1990, 8, 15), parsed);
    }

    @Test
    void testFormatDateOfBirth() {
        LocalDate date = LocalDate.of(1990, 8, 15);
        String formatted = validationService.formatDateOfBirth(date);
        assertEquals("15081990", formatted);
    }

    private CreditCheckRequest createValidRequest() {
        CreditCheckRequest request = new CreditCheckRequest();
        request.setFirstName("John");
        request.setLastName("Doe");
        request.setDateOfBirth("15081990");
        request.setPanNumber("**********");
        request.setIdType("PAN Card");
        request.setPhoneNumber("9876543210");
        request.setLoanPurpose("Personal Loan");
        return request;
    }
}
