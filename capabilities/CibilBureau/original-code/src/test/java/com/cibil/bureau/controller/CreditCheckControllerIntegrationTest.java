package com.cibil.bureau.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import org.springframework.transaction.annotation.Transactional;

import com.cibil.bureau.dto.CreditCheckRequest;
import com.cibil.bureau.dto.CreditCheckResponse;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Integration tests for CreditCheckController.
 */
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
class CreditCheckControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testProcessCreditCheckSuccess() throws Exception {
        CreditCheckRequest request = createValidRequest();

        MvcResult result = mockMvc.perform(post("/credit-check")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.referenceNumber").exists())
                .andExpect(jsonPath("$.fullName").value("John Doe"))
                .andExpect(jsonPath("$.panNumber").value("**********"))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        CreditCheckResponse response = objectMapper.readValue(responseContent, CreditCheckResponse.class);

        assertNotNull(response.getReferenceNumber());
        assertEquals("John Doe", response.getFullName());
        assertEquals("**********", response.getPanNumber());
        assertNotNull(response.getStatus());
    }

    @Test
    void testProcessCreditCheckValidationFailure() throws Exception {
        CreditCheckRequest request = createValidRequest();
        request.setFirstName(""); // Invalid first name

        mockMvc.perform(post("/credit-check")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.error").value("VALIDATION_ERROR"));
    }

    @Test
    void testProcessCreditCheckInvalidPan() throws Exception {
        CreditCheckRequest request = createValidRequest();
        request.setPanNumber("INVALID"); // Invalid PAN format

        mockMvc.perform(post("/credit-check")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.error").value("VALIDATION_ERROR"));
    }

    @Test
    void testProcessCreditCheckInvalidPhoneNumber() throws Exception {
        CreditCheckRequest request = createValidRequest();
        request.setPhoneNumber("123"); // Invalid phone number

        mockMvc.perform(post("/credit-check")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.error").value("VALIDATION_ERROR"));
    }

    @Test
    void testProcessCreditCheckInvalidLoanPurpose() throws Exception {
        CreditCheckRequest request = createValidRequest();
        request.setLoanPurpose("Home Loan"); // Not supported

        mockMvc.perform(post("/credit-check")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.error").value("VALIDATION_ERROR"));
    }

    @Test
    void testGetCreditCheckStatusNotFound() throws Exception {
        mockMvc.perform(get("/credit-check/INVALID-REF"))
                .andExpect(status().isNotFound());
    }

    @Test
    void testHealthEndpoint() throws Exception {
        mockMvc.perform(get("/credit-check/health"))
                .andExpect(status().isOk())
                .andExpect(content().string("Credit Check Service is healthy"));
    }

    @Test
    void testProcessCreditCheckMissingFields() throws Exception {
        CreditCheckRequest request = new CreditCheckRequest();
        // Missing all required fields

        mockMvc.perform(post("/credit-check")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.error").value("VALIDATION_ERROR"))
                .andExpect(jsonPath("$.details").isArray());
    }

    @Test
    void testProcessCreditCheckInvalidJson() throws Exception {
        mockMvc.perform(post("/credit-check")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{ invalid json }"))
                .andExpect(status().isBadRequest());
    }

    private CreditCheckRequest createValidRequest() {
        CreditCheckRequest request = new CreditCheckRequest();
        request.setFirstName("John");
        request.setLastName("Doe");
        request.setDateOfBirth("15081990");
        request.setPanNumber("**********");
        request.setIdType("PAN Card");
        request.setPhoneNumber("9876543210");
        request.setLoanPurpose("Personal Loan");
        return request;
    }
}
