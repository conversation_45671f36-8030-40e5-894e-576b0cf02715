package com.cibil.bureau.service;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.cibil.bureau.config.ApplicationProperties;
import com.cibil.bureau.dto.BureauCreditResponse;
import com.cibil.bureau.dto.CreditCheckResponse;
import com.cibil.bureau.entity.BorrowerInfo;
import com.cibil.bureau.entity.CreditReport;
import com.cibil.bureau.enums.IdType;
import com.cibil.bureau.enums.LoanPurpose;
import com.cibil.bureau.enums.OperationType;
import com.cibil.bureau.enums.ProcessingStatus;
import com.cibil.bureau.enums.RiskLevel;

/**
 * Unit tests for CreditAnalysisService.
 */
@ExtendWith(SpringExtension.class)
class CreditAnalysisServiceTest {

    private CreditAnalysisService creditAnalysisService;
    private ApplicationProperties applicationProperties;
    private AuditService auditService;

    @BeforeEach
    void setUp() {
        applicationProperties = new ApplicationProperties();
        applicationProperties.setMinimumScore(650);

        // Mock audit service for testing
        auditService = new AuditService(null, null, applicationProperties) {
            @Override
            public void logRequestInitiation(String referenceNumber, OperationType operationType, Object requestData) {
                // Mock implementation
            }

            @Override
            public void logSuccessfulCompletion(String referenceNumber, OperationType operationType, Object responseData) {
                // Mock implementation
            }

            @Override
            public void logFailure(String referenceNumber, OperationType operationType, String errorMessage) {
                // Mock implementation
            }

            @Override
            public void logOperation(String referenceNumber, OperationType operationType, String entityType,
                                   String entityId, Object requestData, Object responseData, String status) {
                // Mock implementation
            }
        };

        creditAnalysisService = new CreditAnalysisService(applicationProperties, auditService);
    }

    @Test
    void testAnalyzeCreditReportEligible() {
        // Arrange
        BureauCreditResponse bureauResponse = createValidBureauResponse(720);
        CreditReport creditReport = createCreditReport();

        // Act
        CreditCheckResponse response = creditAnalysisService.analyzeCreditReport(bureauResponse, creditReport);

        // Assert
        assertNotNull(response);
        assertEquals("TEST-REF-001", response.getReferenceNumber());
        assertEquals(ProcessingStatus.ELIGIBLE, response.getStatus());
        assertTrue(response.isEligible());
        assertEquals("John Doe", response.getFullName());
        assertEquals(720, response.getCreditScore());
        assertEquals(RiskLevel.LOW_RISK, response.getRiskLevel());
        assertNotNull(response.getCreditSummary());
    }

    @Test
    void testAnalyzeCreditReportLowScore() {
        // Arrange
        BureauCreditResponse bureauResponse = createValidBureauResponse(580);
        CreditReport creditReport = createCreditReport();

        // Act
        CreditCheckResponse response = creditAnalysisService.analyzeCreditReport(bureauResponse, creditReport);

        // Assert
        assertNotNull(response);
        assertEquals("TEST-REF-001", response.getReferenceNumber());
        assertEquals(ProcessingStatus.NOT_ELIGIBLE, response.getStatus());
        assertFalse(response.isEligible());
        assertTrue(response.getMessage().contains("unable to process your loan digitally"));
    }

    @Test
    void testAnalyzeCreditReportIncompleteResponse() {
        // Arrange
        BureauCreditResponse bureauResponse = createIncompleteBureauResponse();
        CreditReport creditReport = createCreditReport();

        // Act
        CreditCheckResponse response = creditAnalysisService.analyzeCreditReport(bureauResponse, creditReport);

        // Assert
        assertNotNull(response);
        assertEquals("TEST-REF-001", response.getReferenceNumber());
        assertEquals(ProcessingStatus.INCOMPLETE_RESPONSE, response.getStatus());
        assertFalse(response.isEligible());
        assertTrue(response.getMessage().contains("unable to process your loan digitally"));
    }

    @Test
    void testAnalyzeCreditReportNullCreditScore() {
        // Arrange
        BureauCreditResponse bureauResponse = createValidBureauResponse(null);
        CreditReport creditReport = createCreditReport();

        // Act
        CreditCheckResponse response = creditAnalysisService.analyzeCreditReport(bureauResponse, creditReport);

        // Assert
        assertNotNull(response);
        assertEquals(ProcessingStatus.NOT_ELIGIBLE, response.getStatus());
        assertFalse(response.isEligible());
    }

    @Test
    void testCreateBureauUnavailableResponse() {
        // Act
        CreditCheckResponse response = creditAnalysisService.createBureauUnavailableResponse("TEST-REF-001");

        // Assert
        assertNotNull(response);
        assertEquals("TEST-REF-001", response.getReferenceNumber());
        assertEquals(ProcessingStatus.BUREAU_UNAVAILABLE, response.getStatus());
        assertFalse(response.isEligible());
        assertTrue(response.getMessage().contains("unable to fetch your credit score"));
    }

    @Test
    void testIsScoreEligible() {
        assertTrue(creditAnalysisService.isScoreEligible(650));
        assertTrue(creditAnalysisService.isScoreEligible(720));
        assertFalse(creditAnalysisService.isScoreEligible(649));
        assertFalse(creditAnalysisService.isScoreEligible(null));
    }

    @Test
    void testDetermineRiskLevel() {
        assertEquals(RiskLevel.LOW_RISK, creditAnalysisService.determineRiskLevel(750));
        assertEquals(RiskLevel.MEDIUM_RISK, creditAnalysisService.determineRiskLevel(700));
        assertEquals(RiskLevel.HIGH_RISK, creditAnalysisService.determineRiskLevel(600));
        assertEquals(RiskLevel.UNKNOWN, creditAnalysisService.determineRiskLevel(null));
    }

    @Test
    void testGenerateRecommendation() {
        assertEquals("Highly Eligible", creditAnalysisService.generateRecommendation(750, RiskLevel.LOW_RISK));
        assertEquals("Eligible", creditAnalysisService.generateRecommendation(700, RiskLevel.MEDIUM_RISK));
        assertEquals("Conditionally Eligible", creditAnalysisService.generateRecommendation(650, RiskLevel.HIGH_RISK));
        assertEquals("Not Eligible", creditAnalysisService.generateRecommendation(600, RiskLevel.HIGH_RISK));
        assertEquals("Not Eligible", creditAnalysisService.generateRecommendation(null, RiskLevel.UNKNOWN));
    }

    @Test
    void testGetMinimumCreditScore() {
        assertEquals(650, creditAnalysisService.getMinimumCreditScore());
    }

    private BureauCreditResponse createValidBureauResponse(Integer creditScore) {
        BureauCreditResponse response = new BureauCreditResponse();
        response.setFullName("John Doe");
        response.setDateOfBirth("********");
        response.setCibilScore(creditScore);
        response.setRiskLevel("Low Risk");
        response.setRecommendation("Eligible");
        response.setAccountSummary("Total accounts: 5, Active: 3, Overdue: 0");
        response.setRecentInquiries(2);
        response.setIsEligible(true);
        response.setOutcomeStatus("SUCCESS");
        response.setMessage("Credit report generated successfully");

        return response;
    }

    private BureauCreditResponse createIncompleteBureauResponse() {
        // Missing required fields
        BureauCreditResponse response = new BureauCreditResponse();
        response.setCibilScore(720);
        response.setRiskLevel("Low Risk");
        response.setRecommendation("Eligible");
        // Missing fullName, dateOfBirth, outcomeStatus

        return response;
    }

    private CreditReport createCreditReport() {
        BorrowerInfo borrowerInfo = new BorrowerInfo(
                "TEST-REF-001", "John", "Doe", LocalDate.of(1990, 8, 15),
                "**********", IdType.PAN_CARD, "**********", LoanPurpose.PERSONAL_LOAN,
                ProcessingStatus.VALIDATION_PASSED
        );
        borrowerInfo.setId(1L);

        return new CreditReport(
                "TEST-REF-001", borrowerInfo, "John Doe", LocalDate.of(1990, 8, 15),
                "**********", "**********", ProcessingStatus.BUREAU_RESPONSE_RECEIVED
        );
    }
}
