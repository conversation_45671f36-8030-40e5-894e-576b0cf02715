package com.cibil.bureau.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ReferenceNumberGenerator.
 */
@ExtendWith(SpringExtension.class)
class ReferenceNumberGeneratorTest {

    private ReferenceNumberGenerator referenceNumberGenerator;

    @BeforeEach
    void setUp() {
        referenceNumberGenerator = new ReferenceNumberGenerator();
    }

    @Test
    void testGenerateCreditCheckReference() {
        String reference = referenceNumberGenerator.generateCreditCheckReference();
        
        assertNotNull(reference);
        assertTrue(reference.startsWith("CIBIL-CC-"));
        assertTrue(referenceNumberGenerator.isValidReferenceNumber(reference));
        assertEquals("CC", referenceNumberGenerator.extractType(reference));
    }

    @Test
    void testGenerateBureauApiReference() {
        String reference = referenceNumberGenerator.generateBureauApiReference();
        
        assertNotNull(reference);
        assertTrue(reference.startsWith("CIBIL-BA-"));
        assertTrue(referenceNumberGenerator.isValidReferenceNumber(reference));
        assertEquals("BA", referenceNumberGenerator.extractType(reference));
    }

    @Test
    void testGenerateAuditLogReference() {
        String reference = referenceNumberGenerator.generateAuditLogReference();
        
        assertNotNull(reference);
        assertTrue(reference.startsWith("CIBIL-AL-"));
        assertTrue(referenceNumberGenerator.isValidReferenceNumber(reference));
        assertEquals("AL", referenceNumberGenerator.extractType(reference));
    }

    @Test
    void testReferenceNumberUniqueness() {
        String ref1 = referenceNumberGenerator.generateCreditCheckReference();
        String ref2 = referenceNumberGenerator.generateCreditCheckReference();
        
        assertNotEquals(ref1, ref2);
    }

    @Test
    void testIsValidReferenceNumber() {
        // Valid reference numbers
        assertTrue(referenceNumberGenerator.isValidReferenceNumber("CIBIL-CC-20231215143022-123456"));
        assertTrue(referenceNumberGenerator.isValidReferenceNumber("CIBIL-BA-20231215143022-654321"));
        assertTrue(referenceNumberGenerator.isValidReferenceNumber("CIBIL-AL-20231215143022-789012"));
        
        // Invalid reference numbers
        assertFalse(referenceNumberGenerator.isValidReferenceNumber(null));
        assertFalse(referenceNumberGenerator.isValidReferenceNumber(""));
        assertFalse(referenceNumberGenerator.isValidReferenceNumber("INVALID"));
        assertFalse(referenceNumberGenerator.isValidReferenceNumber("CIBIL-CC-20231215143022")); // Missing random number
        assertFalse(referenceNumberGenerator.isValidReferenceNumber("WRONG-CC-20231215143022-123456")); // Wrong prefix
        assertFalse(referenceNumberGenerator.isValidReferenceNumber("CIBIL-C-20231215143022-123456")); // Wrong type length
        assertFalse(referenceNumberGenerator.isValidReferenceNumber("CIBIL-CC-2023121514302-123456")); // Wrong timestamp length
        assertFalse(referenceNumberGenerator.isValidReferenceNumber("CIBIL-CC-20231215143022-12345")); // Wrong random number length
    }

    @Test
    void testExtractType() {
        assertEquals("CC", referenceNumberGenerator.extractType("CIBIL-CC-20231215143022-123456"));
        assertEquals("BA", referenceNumberGenerator.extractType("CIBIL-BA-20231215143022-123456"));
        assertEquals("AL", referenceNumberGenerator.extractType("CIBIL-AL-20231215143022-123456"));
        
        assertNull(referenceNumberGenerator.extractType("INVALID"));
        assertNull(referenceNumberGenerator.extractType(null));
    }

    @Test
    void testExtractTimestamp() {
        String reference = "CIBIL-CC-20231215143022-123456";
        LocalDateTime timestamp = referenceNumberGenerator.extractTimestamp(reference);
        
        assertNotNull(timestamp);
        assertEquals(2023, timestamp.getYear());
        assertEquals(12, timestamp.getMonthValue());
        assertEquals(15, timestamp.getDayOfMonth());
        assertEquals(14, timestamp.getHour());
        assertEquals(30, timestamp.getMinute());
        assertEquals(22, timestamp.getSecond());
        
        assertNull(referenceNumberGenerator.extractTimestamp("INVALID"));
        assertNull(referenceNumberGenerator.extractTimestamp(null));
    }

    @Test
    void testReferenceNumberFormat() {
        String reference = referenceNumberGenerator.generateCreditCheckReference();
        String[] parts = reference.split("-");
        
        assertEquals(4, parts.length);
        assertEquals("CIBIL", parts[0]);
        assertEquals("CC", parts[1]);
        assertEquals(14, parts[2].length()); // Timestamp should be 14 digits
        assertEquals(6, parts[3].length()); // Random number should be 6 digits
        
        // Verify timestamp is numeric
        assertTrue(parts[2].matches("\\d{14}"));
        
        // Verify random number is numeric
        assertTrue(parts[3].matches("\\d{6}"));
    }

    @Test
    void testTimestampAccuracy() {
        LocalDateTime beforeGeneration = LocalDateTime.now().minusSeconds(1);
        String reference = referenceNumberGenerator.generateCreditCheckReference();
        LocalDateTime afterGeneration = LocalDateTime.now().plusSeconds(1);
        
        LocalDateTime extractedTimestamp = referenceNumberGenerator.extractTimestamp(reference);
        
        assertNotNull(extractedTimestamp);
        assertTrue(extractedTimestamp.isAfter(beforeGeneration));
        assertTrue(extractedTimestamp.isBefore(afterGeneration));
    }

    @Test
    void testRandomNumberRange() {
        String reference = referenceNumberGenerator.generateCreditCheckReference();
        String[] parts = reference.split("-");
        int randomNumber = Integer.parseInt(parts[3]);
        
        assertTrue(randomNumber >= 100000);
        assertTrue(randomNumber <= 999999);
    }

    @Test
    void testMultipleGenerationsHaveDifferentRandomNumbers() {
        String ref1 = referenceNumberGenerator.generateCreditCheckReference();
        String ref2 = referenceNumberGenerator.generateCreditCheckReference();
        
        String[] parts1 = ref1.split("-");
        String[] parts2 = ref2.split("-");
        
        // Random numbers should be different (very high probability)
        assertNotEquals(parts1[3], parts2[3]);
    }
}
