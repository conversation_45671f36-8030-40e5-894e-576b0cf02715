package com.cibil.bureau.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Unit tests for DataMaskingUtil.
 */
@ExtendWith(SpringExtension.class)
class DataMaskingUtilTest {

    private DataMaskingUtil dataMaskingUtil;

    @BeforeEach
    void setUp() {
        ObjectMapper objectMapper = new ObjectMapper();
        dataMaskingUtil = new DataMaskingUtil(objectMapper);
    }

    @Test
    void testMaskPan() {
        assertEquals("AB****F", dataMaskingUtil.maskPan("**********"));
        assertEquals("****", dataMaskingUtil.maskPan("ABC"));
        assertEquals("****", dataMaskingUtil.maskPan(null));
        assertEquals("****", dataMaskingUtil.maskPan(""));
    }

    @Test
    void testMaskPhone() {
        assertEquals("****3210", dataMaskingUtil.maskPhone("9876543210"));
        assertEquals("****", dataMaskingUtil.maskPhone("123"));
        assertEquals("****", dataMaskingUtil.maskPhone(null));
        assertEquals("****", dataMaskingUtil.maskPhone(""));
    }

    @Test
    void testMaskName() {
        assertEquals("J****n", dataMaskingUtil.maskName("John"));
        assertEquals("J*", dataMaskingUtil.maskName("Jo"));
        assertEquals("****", dataMaskingUtil.maskName("J"));
        assertEquals("****", dataMaskingUtil.maskName(null));
        assertEquals("****", dataMaskingUtil.maskName(""));
    }

    @Test
    void testMaskEmail() {
        assertEquals("j****<EMAIL>", dataMaskingUtil.maskEmail("<EMAIL>"));
        assertEquals("****@example.com", dataMaskingUtil.maskEmail("<EMAIL>"));
        assertEquals("****", dataMaskingUtil.maskEmail("invalid-email"));
        assertEquals("****", dataMaskingUtil.maskEmail(null));
    }

    @Test
    void testMaskJsonData() {
        String jsonData = """
                {
                    "firstName": "John",
                    "lastName": "Doe",
                    "panNumber": "**********",
                    "phoneNumber": "9876543210",
                    "email": "<EMAIL>",
                    "creditScore": 720
                }
                """;

        String maskedJson = dataMaskingUtil.maskJsonData(jsonData);

        assertNotNull(maskedJson);
        assertTrue(maskedJson.contains("AB****F")); // Masked PAN
        assertTrue(maskedJson.contains("****3210")); // Masked phone
        assertTrue(maskedJson.contains("J****n")); // Masked first name
        assertTrue(maskedJson.contains("D*e")); // Masked last name
        assertTrue(maskedJson.contains("j****<EMAIL>")); // Masked email
        assertTrue(maskedJson.contains("720")); // Credit score should not be masked
    }

    @Test
    void testMaskJsonDataWithNestedObjects() {
        String jsonData = """
                {
                    "borrowerDetails": {
                        "fullName": "John Doe",
                        "panNumber": "**********",
                        "phoneNumber": "9876543210"
                    },
                    "creditScore": 720
                }
                """;

        String maskedJson = dataMaskingUtil.maskJsonData(jsonData);

        assertNotNull(maskedJson);
        assertTrue(maskedJson.contains("AB****F"));
        assertTrue(maskedJson.contains("****3210"));
        assertTrue(maskedJson.contains("J****e"));
    }

    @Test
    void testMaskJsonDataInvalidJson() {
        String invalidJson = "{ invalid json }";

        String maskedJson = dataMaskingUtil.maskJsonData(invalidJson);

        assertNotNull(maskedJson);
        // Should fall back to plain text masking, but since there's no PII data, text remains unchanged
        assertTrue(maskedJson.contains("invalid json"));
    }

    @Test
    void testMaskObject() {
        TestObject testObject = new TestObject();
        testObject.firstName = "John";
        testObject.lastName = "Doe";
        testObject.panNumber = "**********";
        testObject.phoneNumber = "9876543210";
        testObject.creditScore = 720;

        String maskedJson = dataMaskingUtil.maskObject(testObject);

        assertNotNull(maskedJson);
        assertTrue(maskedJson.contains("AB****F"));
        assertTrue(maskedJson.contains("****3210"));
        assertTrue(maskedJson.contains("J****n"));
        assertTrue(maskedJson.contains("D*e"));
        assertTrue(maskedJson.contains("720"));
    }

    @Test
    void testMaskObjectNull() {
        String result = dataMaskingUtil.maskObject(null);
        assertNull(result);
    }

    @Test
    void testMaskJsonDataNull() {
        String result = dataMaskingUtil.maskJsonData(null);
        assertNull(result);
    }

    @Test
    void testMaskJsonDataEmpty() {
        String result = dataMaskingUtil.maskJsonData("");
        assertEquals("", result);
    }

    @Test
    void testMaskJsonDataWithArrays() {
        String jsonData = """
                {
                    "borrowers": [
                        {
                            "name": "John Doe",
                            "panNumber": "**********"
                        },
                        {
                            "name": "Jane Smith",
                            "panNumber": "**********"
                        }
                    ]
                }
                """;

        String maskedJson = dataMaskingUtil.maskJsonData(jsonData);

        assertNotNull(maskedJson);
        assertTrue(maskedJson.contains("AB****F"));
        assertTrue(maskedJson.contains("FG****K"));
        assertTrue(maskedJson.contains("J****e"));
        assertTrue(maskedJson.contains("J****h"));
    }

    /**
     * Test class for object masking.
     */
    private static class TestObject {
        public String firstName;
        public String lastName;
        public String panNumber;
        public String phoneNumber;
        public Integer creditScore;
    }
}
