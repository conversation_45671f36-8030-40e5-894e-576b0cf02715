package com.cibil.bureau;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import org.springframework.transaction.annotation.Transactional;

import com.cibil.bureau.dto.CreditCheckRequest;
import com.cibil.bureau.dto.CreditCheckResponse;
import com.cibil.bureau.entity.BorrowerInfo;
import com.cibil.bureau.entity.CreditReport;
import com.cibil.bureau.repository.BorrowerInfoRepository;
import com.cibil.bureau.repository.CreditReportRepository;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Comprehensive integration test for the CIBIL Bureau Credit Check Application.
 */
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
class CibilBureauCreditCheckApplicationIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private BorrowerInfoRepository borrowerInfoRepository;

    @Autowired
    private CreditReportRepository creditReportRepository;

    @Test
    void testCompleteWorkflowSuccess() throws Exception {
        // Arrange
        CreditCheckRequest request = createValidRequest();

        // Act - Submit credit check request
        MvcResult result = mockMvc.perform(post("/credit-check")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.referenceNumber").exists())
                .andExpect(jsonPath("$.fullName").value("John Doe"))
                .andExpect(jsonPath("$.panNumber").value("**********"))
                .andExpect(jsonPath("$.phoneNumber").value("9876543210"))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        CreditCheckResponse response = objectMapper.readValue(responseContent, CreditCheckResponse.class);

        // Assert response
        assertNotNull(response.getReferenceNumber());
        assertEquals("John Doe", response.getFullName());
        assertEquals("15081990", response.getDateOfBirth());
        assertEquals("**********", response.getPanNumber());
        assertEquals("9876543210", response.getPhoneNumber());
        assertNotNull(response.getStatus());

        // Verify database records were created
        Optional<BorrowerInfo> borrowerInfo = borrowerInfoRepository.findByReferenceNumber(response.getReferenceNumber());
        assertTrue(borrowerInfo.isPresent());
        assertEquals("John", borrowerInfo.get().getFirstName());
        assertEquals("Doe", borrowerInfo.get().getLastName());
        assertEquals("**********", borrowerInfo.get().getPanNumber());

        Optional<CreditReport> creditReport = creditReportRepository.findByReferenceNumber(response.getReferenceNumber());
        assertTrue(creditReport.isPresent());
        assertEquals(response.getReferenceNumber(), creditReport.get().getReferenceNumber());

        // Act - Retrieve status using reference number
        mockMvc.perform(get("/credit-check/" + response.getReferenceNumber()))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.referenceNumber").value(response.getReferenceNumber()))
                .andExpect(jsonPath("$.fullName").value("John Doe"))
                .andExpect(jsonPath("$.status").exists());
    }

    @Test
    void testWorkflowWithValidationFailure() throws Exception {
        // Arrange - Create request with invalid data
        CreditCheckRequest request = createValidRequest();
        request.setFirstName(""); // Invalid first name

        // Act & Assert
        mockMvc.perform(post("/credit-check")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.error").value("VALIDATION_ERROR"))
                .andExpect(jsonPath("$.message").exists());
    }

    @Test
    void testWorkflowWithInvalidPanNumber() throws Exception {
        // Arrange
        CreditCheckRequest request = createValidRequest();
        request.setPanNumber("INVALID123"); // Invalid PAN format

        // Act & Assert
        mockMvc.perform(post("/credit-check")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.error").value("VALIDATION_ERROR"));
    }

    @Test
    void testWorkflowWithInvalidPhoneNumber() throws Exception {
        // Arrange
        CreditCheckRequest request = createValidRequest();
        request.setPhoneNumber("123"); // Invalid phone number

        // Act & Assert
        mockMvc.perform(post("/credit-check")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.error").value("VALIDATION_ERROR"));
    }

    @Test
    void testWorkflowWithUnsupportedLoanPurpose() throws Exception {
        // Arrange
        CreditCheckRequest request = createValidRequest();
        request.setLoanPurpose("Home Loan"); // Not supported

        // Act & Assert
        mockMvc.perform(post("/credit-check")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.error").value("VALIDATION_ERROR"));
    }

    @Test
    void testWorkflowWithInvalidDateOfBirth() throws Exception {
        // Arrange
        CreditCheckRequest request = createValidRequest();
        request.setDateOfBirth("32012000"); // Invalid date

        // Act & Assert
        mockMvc.perform(post("/credit-check")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.error").value("VALIDATION_ERROR"));
    }

    @Test
    void testGetNonExistentCreditCheckStatus() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/credit-check/INVALID-REF-123"))
                .andExpect(status().isNotFound());
    }

    @Test
    void testHealthEndpoint() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/credit-check/health"))
                .andExpect(status().isOk())
                .andExpect(content().string("Credit Check Service is healthy"));
    }

    @Test
    void testAdminHealthEndpoint() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/admin/health"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").exists())
                .andExpect(jsonPath("$.timestamp").exists());
    }

    @Test
    void testAdminAuditStatistics() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/admin/audit/statistics"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.totalLogs").exists())
                .andExpect(jsonPath("$.creditCheckLogs").exists())
                .andExpect(jsonPath("$.bureauApiLogs").exists())
                .andExpect(jsonPath("$.errorLogs").exists());
    }

    @Test
    void testAdminRetentionStatistics() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/admin/retention/statistics"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.totalBorrowerInfo").exists())
                .andExpect(jsonPath("$.totalCreditReports").exists())
                .andExpect(jsonPath("$.retentionDays").exists());
    }

    @Test
    void testAdminRetentionValidation() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/admin/retention/validate"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.valid").exists())
                .andExpect(jsonPath("$.message").exists());
    }

    @Test
    void testAdminMetrics() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/admin/metrics"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.memory").exists())
                .andExpect(jsonPath("$.system").exists())
                .andExpect(jsonPath("$.application").exists())
                .andExpect(jsonPath("$.timestamp").exists());
    }

    @Test
    void testAdminPing() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/admin/ping"))
                .andExpect(status().isOk())
                .andExpect(content().string("Admin service is healthy"));
    }

    @Test
    void testInvalidJsonRequest() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/credit-check")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{ invalid json }"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testEmptyRequestBody() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/credit-check")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.error").value("VALIDATION_ERROR"));
    }

    private CreditCheckRequest createValidRequest() {
        CreditCheckRequest request = new CreditCheckRequest();
        request.setFirstName("John");
        request.setLastName("Doe");
        request.setDateOfBirth("15081990");
        request.setPanNumber("**********");
        request.setIdType("PAN Card");
        request.setPhoneNumber("9876543210");
        request.setLoanPurpose("Personal Loan");
        return request;
    }
}
