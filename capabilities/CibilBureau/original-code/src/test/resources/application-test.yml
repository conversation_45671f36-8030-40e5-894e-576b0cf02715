server:
  port: 0  # Random port for tests

spring:
  application:
    name: cibil-bureau-service-test

  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password:
    driver-class-name: org.h2.Driver

  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: false

  flyway:
    enabled: false  # Disable flyway for tests, use JPA DDL

  h2:
    console:
      enabled: false

# Logging Configuration for Tests
logging:
  level:
    com.cibil.bureau: DEBUG
    org.springframework.web: WARN
    org.hibernate: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# CIBIL Bureau Properties for Testing
cibil:
  bureau:
    api:
      base-url: http://localhost:8207/api/cibil-bureau/internal
      timeout: 5000
      retry:
        max-attempts: 2
        delay: 1000
        multiplier: 1.5
      endpoints:
        credit-report: /v1/cibil-bureau/mock/credit-check

# Application specific configuration for tests
app:
  credit-check:
    minimum-score: 650
    data-retention:
      days: 30
    validation:
      pan-pattern: "[A-Z]{5}[0-9]{4}[A-Z]{1}"
      phone-pattern: "[0-9]{10}"
      name-pattern: "[a-zA-Z\\s]{2,50}"
    audit:
      mask-pii: true
      log-requests: true
      log-responses: true

# Management endpoints for testing
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
