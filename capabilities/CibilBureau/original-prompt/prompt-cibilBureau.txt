Business Flow

Phase 1: Collect and validate borrower information

    Trigger:
        - The process begins when a loan application system requests a credit check.

    Request Payload / Mandatory inputs:
        - First Name
        - Last Name
        - Date of Birth (DDMMYYYY)
        - PAN Number (as ID)
        - ID Type (PAN Card)
        - Phone Number
        - Loan Purpose (must be "Personal Loan")

    Validations:
        - Check all fields for format and completeness.
        - If validation fails:
        - Log the failure event.
        - Stop the process.
        - Return an appropriate message to the requesting system.
        - If valid:
        - Prepare the data for submission to the external credit bureau.

Phase 2: Request and receive credit report from CIBIL

    Request Payload / Mandatory inputs:
        - Combine validated fields into a request format:
        - Full Name
        - DOB
        - PAN
        - Phone Number
        - Loan Purpose

    API Response Processing / System: 
        - Submit the request to the external credit bureau (CIBIL).
        - Await response.
        - Expected Credit Bureau Response includes:
            - Borrower Details (Name, DOB, PAN, Phone)
            - Credit Score (e.g., 700)
            - Risk Level (e.g., "Low Risk")
            - Credit Summary (accounts, defaults, inquiries)
            - Recommendation (e.g., "Eligible")

    System Behavior Based on API Response:
        If the bureau is unavailable or returns an error:
            - Retry the request (up to 3 times).
            - If all retries fail:
            - Return a fallback message:
                "We're unable to fetch your credit score right now. Please try again later."

Phase 3: Analyze bureau response and make a lending decision

    API Response Processing / System:
        - Ensure key fields (Full Name, DOB, PAN, Phone) are not null/missing.
        - Check that the credit score is >= 650.

        If all checks pass:
        - Consider the borrower eligible.
        - Log the successful outcome.
        - Forward the enriched data to the loan application system.

        Incomplete Response:
        - If any key field (Full Name, DOB, PAN, Phone) is null or missing:
        - Stop processing.
        - Log failure.
        - Show message:
            "We are unable to process your loan digitally at this point in time. Please visit your nearest branch for assistance."

    System Behavior Based on API Response:
        Low Credit Score:
            - If credit score < 650:
            - Log reason as "Score Low".
            - Stop the loan journey.
            - Show message:
                "We are unable to process your loan digitally at this point in time. Please visit your nearest branch for assistance."

        Bureau Unreachable (After Retries):
            - If retry attempts are exhausted:
            - Show message:
                "We're unable to fetch your credit score right now. Please try again later."