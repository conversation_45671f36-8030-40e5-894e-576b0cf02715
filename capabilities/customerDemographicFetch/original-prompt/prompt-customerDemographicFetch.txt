
### Business Flow Prompt: Customer Demographic Fetch

**Business Use Case:**
To fetch customer demographic information from internal/external systems based on a unique identifier (e.g. customerId or applicationId) during the loan application process. This capability is used to pre-fill data, reduce manual input errors, and accelerate the onboarding journey.

**Goals:**
Build a secure, responsive, and real-time API service that can fetch customer demographic details (e.g. name, address, contact info, etc.) to enrich the digital loan onboarding flow. The service must integrate seamlessly with existing application systems and be extensible for future data sources.

---

### Business Flow:

#### Phase 1: Trigger Customer Demographic Fetch

* Triggered automatically during digital loan onboarding
* Request includes identification fields like:

  * `applicationId`
  * `customerId`
* Basic validations performed:

  * Missing fields
  * Invalid format

If validation fails:

* Log the error
* Return: `"Unable to fetch customer details at this time. Please try again later."`

If valid:

* Prepare request to internal/external demographic data source

---

#### Phase 2: API Request to Customer Demographic Service

**Request Fields:**

```json
{
  "customerId": "CUST12345",
  "applicationId": "APP98765",
  "productCode": "FOUR_WHEELER_ETB_PA",
  "loanCategory": "ETB"
}
```

| Object Field  | Data Type | Description        | Mandatory | Source |
| ------------- | --------- | ------------------ | --------- | ------ |
| customerId    | String    | Unique customer ID | Yes       | System |
| applicationId | String    | Application ID     | Yes       | System |
| productCode   | String    | Product identifier | Yes       | System |
| loanCategory  | String    | ETB / NTB          | Yes       | System |

---

#### Phase 3: API Response Processing

**Expected Response Fields:**

| Field        | Type   | Description                    |
| ------------ | ------ | ------------------------------ |
| name         | String | Full name of the customer      |
| dob          | String | Date of Birth                  |
| gender       | String | Gender                         |
| email        | String | Email address                  |
| mobileNumber | String | Mobile number                  |
| addressLine1 | String | Address line 1                 |
| addressLine2 | String | Address line 2                 |
| city         | String | City                           |
| state        | String | State                          |
| pincode      | String | Postal Code                    |
| status       | String | Status of the fetch operation  |
| error        | Object | Error details (if fetch fails) |

**Sample Success Response:**

```json
{
  "name": "John Doe",
  "dob": "1985-06-25",
  "gender": "Male",
  "email": "<EMAIL>",
  "mobileNumber": "9876543210",
  "addressLine1": "123 Elm Street",
  "addressLine2": "Block A",
  "city": "Mumbai",
  "state": "Maharashtra",
  "pincode": "400001",
  "status": "SUCCESS"
}
```

**Sample Error Response:**

```json
{
  "status": "FAILED",
  "error": {
    "code": "CUST404",
    "message": "Customer details not found"
  }
}
```

---

#### Phase 4: Response Validation

* If `status == SUCCESS` and mandatory fields are present:

  * Log success
  * Enrich application journey
* If `status == FAILED` or required field is missing:

  * Halt journey
  * Log issue
  * Return: `"Unable to fetch customer details at this time. Please try again later."`

---

### Fallback & Retry Logic

| Scenario                             | Action                                    |
| ------------------------------------ | ----------------------------------------- |
| Customer Demographic fetch fails     | Retry 2 times, then show fallback message |
| Mandatory fields missing in response | Halt, log issue, show error               |
| API timeout / unresponsive           | Retry 3 times                             |
| Customer ID not found                | Show "Customer details not available"     |

---

### API Field-Level Specifications

**Request Payload:**

| Field Name    | Data Type | Description              | Mandatory | Source | Validation Rules                  |
| ------------- | --------- | ------------------------ | --------- | ------ | --------------------------------- |
| customerId    | String    | Unique customer ID       | Yes       | System | Not null, valid alphanumeric ID   |
| applicationId | String    | Application reference ID | Yes       | System | Not null, UUID/internal format    |
| productCode   | String    | Code of loan product     | Yes       | System | Must match pre-configured product |
| loanCategory  | String    | ETB or NTB               | Yes       | System | Enum: ETB, NTB                    |

**Response Payload:**

| Field Name    | Data Type | Description                | Mandatory | Source           | Validation Rules               |
| ------------- | --------- | -------------------------- | --------- | ---------------- | ------------------------------ |
| name          | String    | Full name                  | Yes       | External service | Not empty                      |
| dob           | String    | Date of birth (YYYY-MM-DD) | Yes       | External service | Valid date format              |
| gender        | String    | Gender                     | Yes       | External service | Enum: Male, Female, Other      |
| email         | String    | Email address              | Yes       | External service | Valid email regex              |
| mobileNumber  | String    | Registered mobile number   | Yes       | External service | 10-digit numeric               |
| addressLine1  | String    | First line of address      | Yes       | External service | Not empty                      |
| addressLine2  | String    | Second line of address     | No        | External service | Optional                       |
| city          | String    | City name                  | Yes       | External service | Not empty                      |
| state         | String    | State name                 | Yes       | External service | Not empty                      |
| pincode       | String    | Postal code                | Yes       | External service | 6-digit numeric                |
| status        | String    | Status of fetch operation  | Yes       | Service          | Enum: SUCCESS, FAILED          |
| error.code    | String    | Error code (if failure)    | No        | Service          | Required if `status == FAILED` |
| error.message | String    | Description of the error   | No        | Service          | Required if `status == FAILED` |

---
