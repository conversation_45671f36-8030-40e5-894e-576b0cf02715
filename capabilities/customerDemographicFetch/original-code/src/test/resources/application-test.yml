server:
  port: 0

spring:
  application:
    name: customer-demographic-fetch-original-test
  
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        
  flyway:
    enabled: false
    
  h2:
    console:
      enabled: true

# External service configuration for tests
external:
  demographic-service:
    base-url: http://localhost:8081/api/customer-demographic-fetch/internal
    timeout: 5000
    retry:
      max-attempts: 2
      delay: 100

# CORS configuration
cors:
  allowed-origins: "*"
  allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
  allowed-headers: "*"

# Logging configuration for tests
logging:
  level:
    com.lending.capability.customerdemographicfetch: DEBUG
    org.springframework.web: WARN
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
