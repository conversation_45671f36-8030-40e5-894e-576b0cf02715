package com.lending.capability.customerdemographicfetch.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

/**
 * Unit tests for DataMaskingUtil.
 * Validates PII masking functionality for audit-ready logging.
 */
class DataMaskingUtilTest {

    @Test
    @DisplayName("Should mask customer ID correctly")
    void testMaskCustomerId() {
        // Valid customer IDs
        assertEquals("CU****45", DataMaskingUtil.maskCustomerId("CUST12345"));
        assertEquals("AB****YZ", DataMaskingUtil.maskCustomerId("ABCDEFGHIJKLMNOPQRSTUVWXYZ"));
        
        // Edge cases
        assertEquals("****", DataMaskingUtil.maskCustomerId("ABC"));
        assertEquals("****", DataMaskingUtil.maskCustomerId("ABCD"));
        assertEquals("****", DataMaskingUtil.maskCustomerId(null));
        assertEquals("****", DataMaskingUtil.maskCustomerId(""));
    }

    @Test
    @DisplayName("Should mask email correctly")
    void testMaskEmail() {
        // Valid emails
        assertEquals("jo****@example.com", DataMaskingUtil.maskEmail("<EMAIL>"));
        assertEquals("te****@company.org", DataMaskingUtil.maskEmail("<EMAIL>"));
        assertEquals("ab****@domain.co.in", DataMaskingUtil.maskEmail("<EMAIL>"));
        
        // Edge cases
        assertEquals("****@b.com", DataMaskingUtil.maskEmail("<EMAIL>"));
        assertEquals("****@c.com", DataMaskingUtil.maskEmail("<EMAIL>"));
        assertEquals("****", DataMaskingUtil.maskEmail("invalid-email"));
        assertEquals("****", DataMaskingUtil.maskEmail(null));
        assertEquals("****", DataMaskingUtil.maskEmail(""));
    }

    @Test
    @DisplayName("Should mask mobile number correctly")
    void testMaskMobileNumber() {
        // Valid mobile numbers
        assertEquals("****3210", DataMaskingUtil.maskMobileNumber("9876543210"));
        assertEquals("****7890", DataMaskingUtil.maskMobileNumber("*********0"));
        
        // Invalid mobile numbers
        assertEquals("****", DataMaskingUtil.maskMobileNumber("*********")); // 9 digits
        assertEquals("****", DataMaskingUtil.maskMobileNumber("*********01")); // 11 digits
        assertEquals("****", DataMaskingUtil.maskMobileNumber("abcd123456"));
        assertEquals("****", DataMaskingUtil.maskMobileNumber(null));
        assertEquals("****", DataMaskingUtil.maskMobileNumber(""));
    }

    @Test
    @DisplayName("Should mask Aadhaar number correctly")
    void testMaskAadhaar() {
        // Valid Aadhaar numbers
        assertEquals("****5678", DataMaskingUtil.maskAadhaar("123456785678"));
        assertEquals("****0000", DataMaskingUtil.maskAadhaar("************"));
        
        // Invalid Aadhaar numbers
        assertEquals("****", DataMaskingUtil.maskAadhaar("12345678567")); // 11 digits
        assertEquals("****", DataMaskingUtil.maskAadhaar("1234567856789")); // 13 digits
        assertEquals("****", DataMaskingUtil.maskAadhaar("abcd12345678"));
        assertEquals("****", DataMaskingUtil.maskAadhaar(null));
        assertEquals("****", DataMaskingUtil.maskAadhaar(""));
    }

    @Test
    @DisplayName("Should mask PAN number correctly")
    void testMaskPAN() {
        // Valid PAN numbers
        assertEquals("ABC****Z", DataMaskingUtil.maskPAN("**********"));
        assertEquals("XYZ****A", DataMaskingUtil.maskPAN("**********"));
        
        // Invalid PAN numbers
        assertEquals("****", DataMaskingUtil.maskPAN("ABCD1234Z")); // 4 letters instead of 5
        assertEquals("****", DataMaskingUtil.maskPAN("ABCDE123Z")); // 3 digits instead of 4
        assertEquals("****", DataMaskingUtil.maskPAN("abcde1234z")); // lowercase
        assertEquals("****", DataMaskingUtil.maskPAN("ABCDE1234"));  // missing last letter
        assertEquals("****", DataMaskingUtil.maskPAN(null));
        assertEquals("****", DataMaskingUtil.maskPAN(""));
    }

    @Test
    @DisplayName("Should mask name correctly")
    void testMaskName() {
        // Single name
        assertEquals("Jo****", DataMaskingUtil.maskName("John"));
        assertEquals("****", DataMaskingUtil.maskName("Al"));
        assertEquals("****", DataMaskingUtil.maskName("A"));
        
        // Full names
        assertEquals("John ****", DataMaskingUtil.maskName("John Doe"));
        assertEquals("Jane ****", DataMaskingUtil.maskName("Jane Smith Johnson"));
        assertEquals("A ****", DataMaskingUtil.maskName("A B"));
        
        // Edge cases
        assertEquals("****", DataMaskingUtil.maskName(null));
        assertEquals("****", DataMaskingUtil.maskName(""));
        assertEquals("****", DataMaskingUtil.maskName("   "));
    }

    @Test
    @DisplayName("Should mask generic data correctly")
    void testMaskGenericData() {
        assertEquals("AB****", DataMaskingUtil.maskGenericData("ABCDEF"));
        assertEquals("12****", DataMaskingUtil.maskGenericData("123456"));
        assertEquals("Se****", DataMaskingUtil.maskGenericData("SensitiveData"));
        
        // Edge cases
        assertEquals("****", DataMaskingUtil.maskGenericData("AB"));
        assertEquals("****", DataMaskingUtil.maskGenericData("A"));
        assertEquals("****", DataMaskingUtil.maskGenericData(null));
        assertEquals("****", DataMaskingUtil.maskGenericData(""));
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "123456785678",      // Aadhaar
        "**********",        // PAN
        "<EMAIL>",  // Email
        "9876543210"         // Mobile
    })
    @DisplayName("Should detect PII data correctly")
    void testContainsPII_ShouldReturnTrue(String data) {
        assertTrue(DataMaskingUtil.containsPII(data));
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "CUST12345",         // Customer ID
        "APP98765",          // Application ID
        "John Doe",          // Name
        "Mumbai",            // City
        "400001"             // Pincode
    })
    @DisplayName("Should not detect non-PII data")
    void testContainsPII_ShouldReturnFalse(String data) {
        assertFalse(DataMaskingUtil.containsPII(data));
    }

    @ParameterizedTest
    @NullAndEmptySource
    @DisplayName("Should handle null and empty strings for PII detection")
    void testContainsPII_NullAndEmpty(String data) {
        assertFalse(DataMaskingUtil.containsPII(data));
    }

    @Test
    @DisplayName("Should handle edge cases for all masking methods")
    void testEdgeCases() {
        // Test all methods with null
        assertEquals("****", DataMaskingUtil.maskCustomerId(null));
        assertEquals("****", DataMaskingUtil.maskEmail(null));
        assertEquals("****", DataMaskingUtil.maskMobileNumber(null));
        assertEquals("****", DataMaskingUtil.maskAadhaar(null));
        assertEquals("****", DataMaskingUtil.maskPAN(null));
        assertEquals("****", DataMaskingUtil.maskName(null));
        assertEquals("****", DataMaskingUtil.maskGenericData(null));
        
        // Test all methods with empty string
        assertEquals("****", DataMaskingUtil.maskCustomerId(""));
        assertEquals("****", DataMaskingUtil.maskEmail(""));
        assertEquals("****", DataMaskingUtil.maskMobileNumber(""));
        assertEquals("****", DataMaskingUtil.maskAadhaar(""));
        assertEquals("****", DataMaskingUtil.maskPAN(""));
        assertEquals("****", DataMaskingUtil.maskName(""));
        assertEquals("****", DataMaskingUtil.maskGenericData(""));
    }
}
