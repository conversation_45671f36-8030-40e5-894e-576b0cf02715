package com.lending.capability.customerdemographicfetch.service;

import com.lending.capability.customerdemographicfetch.dto.CustomerDemographicRequest;
import com.lending.capability.customerdemographicfetch.dto.CustomerDemographicResponse;
import com.lending.capability.customerdemographicfetch.dto.ErrorDetails;
import com.lending.capability.customerdemographicfetch.enums.ResponseStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ValidationService.
 * Tests request and response validation logic with edge cases.
 */
class ValidationServiceTest {

    private ValidationService validationService;

    @BeforeEach
    void setUp() {
        validationService = new ValidationService();
    }

    @Test
    @DisplayName("Should validate valid request successfully")
    void testValidateRequest_ValidRequest_ShouldReturnTrue() {
        CustomerDemographicRequest request = new CustomerDemographicRequest(
                "CUST12345", "APP98765", "FOUR_WHEELER_ETB_PA", "ETB");
        
        assertTrue(validationService.validateRequest(request));
    }

    @Test
    @DisplayName("Should reject null request")
    void testValidateRequest_NullRequest_ShouldReturnFalse() {
        assertFalse(validationService.validateRequest(null));
    }

    @ParameterizedTest
    @NullAndEmptySource
    @ValueSource(strings = {"   ", "CUST@123", "CUST-123"})
    @DisplayName("Should reject invalid customer IDs")
    void testValidateRequest_InvalidCustomerId_ShouldReturnFalse(String customerId) {
        CustomerDemographicRequest request = new CustomerDemographicRequest(
                customerId, "APP98765", "FOUR_WHEELER_ETB_PA", "ETB");
        
        assertFalse(validationService.validateRequest(request));
    }

    @ParameterizedTest
    @NullAndEmptySource
    @ValueSource(strings = {"   "})
    @DisplayName("Should reject invalid application IDs")
    void testValidateRequest_InvalidApplicationId_ShouldReturnFalse(String applicationId) {
        CustomerDemographicRequest request = new CustomerDemographicRequest(
                "CUST12345", applicationId, "FOUR_WHEELER_ETB_PA", "ETB");
        
        assertFalse(validationService.validateRequest(request));
    }

    @ParameterizedTest
    @NullAndEmptySource
    @ValueSource(strings = {"   "})
    @DisplayName("Should reject invalid product codes")
    void testValidateRequest_InvalidProductCode_ShouldReturnFalse(String productCode) {
        CustomerDemographicRequest request = new CustomerDemographicRequest(
                "CUST12345", "APP98765", productCode, "ETB");
        
        assertFalse(validationService.validateRequest(request));
    }

    @ParameterizedTest
    @ValueSource(strings = {"INVALID", "etb", "ntb", "ETB_INVALID", ""})
    @DisplayName("Should reject invalid loan categories")
    void testValidateRequest_InvalidLoanCategory_ShouldReturnFalse(String loanCategory) {
        CustomerDemographicRequest request = new CustomerDemographicRequest(
                "CUST12345", "APP98765", "FOUR_WHEELER_ETB_PA", loanCategory);
        
        assertFalse(validationService.validateRequest(request));
    }

    @ParameterizedTest
    @ValueSource(strings = {"ETB", "NTB"})
    @DisplayName("Should accept valid loan categories")
    void testValidateRequest_ValidLoanCategory_ShouldReturnTrue(String loanCategory) {
        CustomerDemographicRequest request = new CustomerDemographicRequest(
                "CUST12345", "APP98765", "FOUR_WHEELER_ETB_PA", loanCategory);
        
        assertTrue(validationService.validateRequest(request));
    }

    @Test
    @DisplayName("Should validate successful response correctly")
    void testValidateResponse_ValidSuccessResponse_ShouldReturnTrue() {
        CustomerDemographicResponse response = new CustomerDemographicResponse(
                "John Doe", "1985-06-25", "Male", "<EMAIL>",
                "9876543210", "123 Elm Street", "Block A", "Mumbai", "Maharashtra", "400001");
        
        assertTrue(validationService.validateResponse(response));
    }

    @Test
    @DisplayName("Should validate failed response with error details correctly")
    void testValidateResponse_ValidFailedResponse_ShouldReturnTrue() {
        CustomerDemographicResponse response = new CustomerDemographicResponse();
        response.setStatus(ResponseStatus.FAILED.getValue());
        response.setError(new ErrorDetails("CUST404", "Customer details not found"));
        
        assertTrue(validationService.validateResponse(response));
    }

    @Test
    @DisplayName("Should reject null response")
    void testValidateResponse_NullResponse_ShouldReturnFalse() {
        assertFalse(validationService.validateResponse(null));
    }

    @Test
    @DisplayName("Should reject failed response without error details")
    void testValidateResponse_FailedResponseWithoutError_ShouldReturnFalse() {
        CustomerDemographicResponse response = new CustomerDemographicResponse();
        response.setStatus(ResponseStatus.FAILED.getValue());
        
        assertFalse(validationService.validateResponse(response));
    }

    @Test
    @DisplayName("Should reject failed response with incomplete error details")
    void testValidateResponse_FailedResponseWithIncompleteError_ShouldReturnFalse() {
        CustomerDemographicResponse response = new CustomerDemographicResponse();
        response.setStatus(ResponseStatus.FAILED.getValue());
        response.setError(new ErrorDetails("CUST404", null)); // Missing message
        
        assertFalse(validationService.validateResponse(response));
        
        response.setError(new ErrorDetails(null, "Customer not found")); // Missing code
        assertFalse(validationService.validateResponse(response));
    }

    @ParameterizedTest
    @NullAndEmptySource
    @ValueSource(strings = {"   "})
    @DisplayName("Should reject success response with invalid name")
    void testValidateResponse_InvalidName_ShouldReturnFalse(String name) {
        CustomerDemographicResponse response = createValidSuccessResponse();
        response.setName(name);
        
        assertFalse(validationService.validateResponse(response));
    }

    @ParameterizedTest
    @ValueSource(strings = {"1985-6-25", "85-06-25", "1985/06/25", "25-06-1985", "invalid-date"})
    @DisplayName("Should reject success response with invalid date format")
    void testValidateResponse_InvalidDateFormat_ShouldReturnFalse(String dob) {
        CustomerDemographicResponse response = createValidSuccessResponse();
        response.setDob(dob);
        
        assertFalse(validationService.validateResponse(response));
    }

    @ParameterizedTest
    @ValueSource(strings = {"male", "MALE", "female", "FEMALE", "other", "INVALID"})
    @DisplayName("Should reject success response with invalid gender")
    void testValidateResponse_InvalidGender_ShouldReturnFalse(String gender) {
        CustomerDemographicResponse response = createValidSuccessResponse();
        response.setGender(gender);
        
        assertFalse(validationService.validateResponse(response));
    }

    @ParameterizedTest
    @ValueSource(strings = {"Male", "Female", "Other"})
    @DisplayName("Should accept success response with valid gender")
    void testValidateResponse_ValidGender_ShouldReturnTrue(String gender) {
        CustomerDemographicResponse response = createValidSuccessResponse();
        response.setGender(gender);
        
        assertTrue(validationService.validateResponse(response));
    }

    @ParameterizedTest
    @ValueSource(strings = {"invalid-email", "test@", "@example.com", "test.example.com"})
    @DisplayName("Should reject success response with invalid email")
    void testValidateResponse_InvalidEmail_ShouldReturnFalse(String email) {
        CustomerDemographicResponse response = createValidSuccessResponse();
        response.setEmail(email);
        
        assertFalse(validationService.validateResponse(response));
    }

    @ParameterizedTest
    @ValueSource(strings = {"987654321", "98765432101", "abcd123456", "************"})
    @DisplayName("Should reject success response with invalid mobile number")
    void testValidateResponse_InvalidMobileNumber_ShouldReturnFalse(String mobile) {
        CustomerDemographicResponse response = createValidSuccessResponse();
        response.setMobileNumber(mobile);
        
        assertFalse(validationService.validateResponse(response));
    }

    @ParameterizedTest
    @ValueSource(strings = {"40000", "4000001", "abcdef", "400-001"})
    @DisplayName("Should reject success response with invalid pincode")
    void testValidateResponse_InvalidPincode_ShouldReturnFalse(String pincode) {
        CustomerDemographicResponse response = createValidSuccessResponse();
        response.setPincode(pincode);
        
        assertFalse(validationService.validateResponse(response));
    }

    @Test
    @DisplayName("Should reject success response with missing mandatory fields")
    void testValidateResponse_MissingMandatoryFields_ShouldReturnFalse() {
        CustomerDemographicResponse response = createValidSuccessResponse();
        
        response.setAddressLine1(null);
        assertFalse(validationService.validateResponse(response));
        
        response = createValidSuccessResponse();
        response.setCity("");
        assertFalse(validationService.validateResponse(response));
        
        response = createValidSuccessResponse();
        response.setState("   ");
        assertFalse(validationService.validateResponse(response));
    }

    @Test
    @DisplayName("Should accept success response with optional addressLine2 as null")
    void testValidateResponse_OptionalAddressLine2_ShouldReturnTrue() {
        CustomerDemographicResponse response = createValidSuccessResponse();
        response.setAddressLine2(null);
        
        assertTrue(validationService.validateResponse(response));
    }

    /**
     * Helper method to create a valid success response for testing.
     */
    private CustomerDemographicResponse createValidSuccessResponse() {
        return new CustomerDemographicResponse(
                "John Doe", "1985-06-25", "Male", "<EMAIL>",
                "9876543210", "123 Elm Street", "Block A", "Mumbai", "Maharashtra", "400001");
    }
}
