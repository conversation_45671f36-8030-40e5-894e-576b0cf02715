package com.lending.capability.customerdemographicfetch.util;

import java.util.regex.Pattern;

/**
 * Utility class for masking sensitive data in logs and responses.
 * Ensures PII compliance and audit-ready logging.
 */
public class DataMaskingUtil {

    private static final String MASK_PATTERN = "****";
    private static final Pattern AADHAAR_PATTERN = Pattern.compile("\\d{12}");
    private static final Pattern PAN_PATTERN = Pattern.compile("[A-Z]{5}\\d{4}[A-Z]");
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    private static final Pattern MOBILE_PATTERN = Pattern.compile("\\d{10}");

    private DataMaskingUtil() {
        // Utility class - prevent instantiation
    }

    /**
     * Mask customer ID for logging purposes.
     * Shows first 2 and last 2 characters, masks the middle.
     * 
     * @param customerId the customer ID to mask
     * @return masked customer ID
     */
    public static String maskCustomerId(String customerId) {
        if (customerId == null || customerId.length() <= 4) {
            return MASK_PATTERN;
        }
        return customerId.substring(0, 2) + MASK_PATTERN + customerId.substring(customerId.length() - 2);
    }

    /**
     * Mask email address for logging purposes.
     * Shows first 2 characters of username and domain.
     * 
     * @param email the email to mask
     * @return masked email
     */
    public static String maskEmail(String email) {
        if (email == null || !EMAIL_PATTERN.matcher(email).matches()) {
            return MASK_PATTERN;
        }
        
        String[] parts = email.split("@");
        if (parts.length != 2) {
            return MASK_PATTERN;
        }
        
        String username = parts[0];
        String domain = parts[1];
        
        String maskedUsername = username.length() > 2 ? 
                username.substring(0, 2) + MASK_PATTERN : MASK_PATTERN;
        
        return maskedUsername + "@" + domain;
    }

    /**
     * Mask mobile number for logging purposes.
     * Shows last 4 digits only.
     * 
     * @param mobile the mobile number to mask
     * @return masked mobile number
     */
    public static String maskMobileNumber(String mobile) {
        if (mobile == null || !MOBILE_PATTERN.matcher(mobile).matches()) {
            return MASK_PATTERN;
        }
        return MASK_PATTERN + mobile.substring(mobile.length() - 4);
    }

    /**
     * Mask Aadhaar number for logging purposes.
     * Shows last 4 digits only.
     * 
     * @param aadhaar the Aadhaar number to mask
     * @return masked Aadhaar number
     */
    public static String maskAadhaar(String aadhaar) {
        if (aadhaar == null || !AADHAAR_PATTERN.matcher(aadhaar).matches()) {
            return MASK_PATTERN;
        }
        return MASK_PATTERN + aadhaar.substring(aadhaar.length() - 4);
    }

    /**
     * Mask PAN number for logging purposes.
     * Shows first 3 and last 1 characters.
     * 
     * @param pan the PAN number to mask
     * @return masked PAN number
     */
    public static String maskPAN(String pan) {
        if (pan == null || !PAN_PATTERN.matcher(pan).matches()) {
            return MASK_PATTERN;
        }
        return pan.substring(0, 3) + MASK_PATTERN + pan.substring(pan.length() - 1);
    }

    /**
     * Mask name for logging purposes.
     * Shows first name and masks last name.
     * 
     * @param name the full name to mask
     * @return masked name
     */
    public static String maskName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return MASK_PATTERN;
        }
        
        String[] parts = name.trim().split("\\s+");
        if (parts.length == 1) {
            return parts[0].length() > 2 ? 
                    parts[0].substring(0, 2) + MASK_PATTERN : MASK_PATTERN;
        }
        
        // Show first name, mask last name
        return parts[0] + " " + MASK_PATTERN;
    }

    /**
     * Mask any generic sensitive data.
     * Shows first 2 characters and masks the rest.
     * 
     * @param data the data to mask
     * @return masked data
     */
    public static String maskGenericData(String data) {
        if (data == null || data.length() <= 2) {
            return MASK_PATTERN;
        }
        return data.substring(0, 2) + MASK_PATTERN;
    }

    /**
     * Check if a string contains potential PII data.
     * 
     * @param data the data to check
     * @return true if contains PII patterns, false otherwise
     */
    public static boolean containsPII(String data) {
        if (data == null) {
            return false;
        }
        
        return AADHAAR_PATTERN.matcher(data).find() ||
               PAN_PATTERN.matcher(data).find() ||
               EMAIL_PATTERN.matcher(data).matches() ||
               MOBILE_PATTERN.matcher(data).find();
    }
}
