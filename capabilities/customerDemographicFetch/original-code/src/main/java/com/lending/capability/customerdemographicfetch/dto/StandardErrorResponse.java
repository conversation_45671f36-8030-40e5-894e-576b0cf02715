package com.lending.capability.customerdemographicfetch.dto;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Standardized error response structure for audit-ready error management.
 * Provides consistent error format across all API endpoints.
 */
public class StandardErrorResponse {

    private String errorCode;
    private String message;
    private LocalDateTime timestamp;
    private String traceId;
    private String path;
    private int status;

    // Default constructor
    public StandardErrorResponse() {
        this.timestamp = LocalDateTime.now();
        this.traceId = UUID.randomUUID().toString();
    }

    // Constructor with required fields
    public StandardErrorResponse(String errorCode, String message, String path, int status) {
        this();
        this.errorCode = errorCode;
        this.message = message;
        this.path = path;
        this.status = status;
    }

    // Getters and Setters
    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "StandardErrorResponse{" +
                "errorCode='" + errorCode + '\'' +
                ", message='" + message + '\'' +
                ", timestamp=" + timestamp +
                ", traceId='" + traceId + '\'' +
                ", path='" + path + '\'' +
                ", status=" + status +
                '}';
    }
}
