package com.lending.capability.customerdemographicfetch.service;

import com.lending.capability.customerdemographicfetch.dto.CustomerDemographicRequest;
import com.lending.capability.customerdemographicfetch.dto.CustomerDemographicResponse;
import com.lending.capability.customerdemographicfetch.enums.Gender;
import com.lending.capability.customerdemographicfetch.enums.LoanCategory;
import com.lending.capability.customerdemographicfetch.enums.ResponseStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

/**
 * Service for validating requests and responses.
 * Implements all validation rules as per business requirements.
 */
@Service
public class ValidationService {

    private static final Logger logger = LoggerFactory.getLogger(ValidationService.class);

    // Validation patterns
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    private static final Pattern MOBILE_PATTERN = Pattern.compile("^\\d{10}$");
    private static final Pattern PINCODE_PATTERN = Pattern.compile("^\\d{6}$");
    private static final Pattern DATE_PATTERN = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}$");
    private static final Pattern CUSTOMER_ID_PATTERN = Pattern.compile("^[A-Za-z0-9]+$");

    /**
     * Validate customer demographic request.
     * 
     * @param request the request to validate
     * @return true if valid, false otherwise
     */
    public boolean validateRequest(CustomerDemographicRequest request) {
        if (request == null) {
            logger.error("Request is null");
            return false;
        }

        // Validate customer ID
        if (!isValidString(request.getCustomerId()) || 
            !CUSTOMER_ID_PATTERN.matcher(request.getCustomerId()).matches()) {
            logger.error("Invalid customer ID: {}", request.getCustomerId());
            return false;
        }

        // Validate application ID
        if (!isValidString(request.getApplicationId())) {
            logger.error("Invalid application ID: {}", request.getApplicationId());
            return false;
        }

        // Validate product code
        if (!isValidString(request.getProductCode())) {
            logger.error("Invalid product code: {}", request.getProductCode());
            return false;
        }

        // Validate loan category
        if (!isValidString(request.getLoanCategory())) {
            logger.error("Invalid loan category: {}", request.getLoanCategory());
            return false;
        }

        try {
            LoanCategory.fromString(request.getLoanCategory());
        } catch (IllegalArgumentException e) {
            logger.error("Invalid loan category enum: {}", request.getLoanCategory());
            return false;
        }

        return true;
    }

    /**
     * Validate customer demographic response.
     * 
     * @param response the response to validate
     * @return true if valid, false otherwise
     */
    public boolean validateResponse(CustomerDemographicResponse response) {
        if (response == null) {
            logger.error("Response is null");
            return false;
        }

        // If status is FAILED, only validate error details
        if (ResponseStatus.FAILED.getValue().equals(response.getStatus())) {
            return validateErrorResponse(response);
        }

        // For SUCCESS status, validate all mandatory fields
        if (!ResponseStatus.SUCCESS.getValue().equals(response.getStatus())) {
            logger.error("Invalid response status: {}", response.getStatus());
            return false;
        }

        // Validate mandatory fields
        if (!isValidString(response.getName())) {
            logger.error("Invalid name in response");
            return false;
        }

        if (!isValidString(response.getDob()) || !DATE_PATTERN.matcher(response.getDob()).matches()) {
            logger.error("Invalid date of birth in response: {}", response.getDob());
            return false;
        }

        if (!isValidString(response.getGender())) {
            logger.error("Invalid gender in response: {}", response.getGender());
            return false;
        }

        try {
            Gender.fromString(response.getGender());
        } catch (IllegalArgumentException e) {
            logger.error("Invalid gender enum in response: {}", response.getGender());
            return false;
        }

        if (!isValidString(response.getEmail()) || !EMAIL_PATTERN.matcher(response.getEmail()).matches()) {
            logger.error("Invalid email in response: {}", response.getEmail());
            return false;
        }

        if (!isValidString(response.getMobileNumber()) || 
            !MOBILE_PATTERN.matcher(response.getMobileNumber()).matches()) {
            logger.error("Invalid mobile number in response: {}", response.getMobileNumber());
            return false;
        }

        if (!isValidString(response.getAddressLine1())) {
            logger.error("Invalid address line 1 in response");
            return false;
        }

        if (!isValidString(response.getCity())) {
            logger.error("Invalid city in response");
            return false;
        }

        if (!isValidString(response.getState())) {
            logger.error("Invalid state in response");
            return false;
        }

        if (!isValidString(response.getPincode()) || 
            !PINCODE_PATTERN.matcher(response.getPincode()).matches()) {
            logger.error("Invalid pincode in response: {}", response.getPincode());
            return false;
        }

        return true;
    }

    /**
     * Validate error response.
     * 
     * @param response the error response to validate
     * @return true if valid error response, false otherwise
     */
    private boolean validateErrorResponse(CustomerDemographicResponse response) {
        if (response.getError() == null) {
            logger.error("Error details missing in FAILED response");
            return false;
        }

        if (!isValidString(response.getError().getCode())) {
            logger.error("Error code missing in FAILED response");
            return false;
        }

        if (!isValidString(response.getError().getMessage())) {
            logger.error("Error message missing in FAILED response");
            return false;
        }

        return true;
    }

    /**
     * Check if string is valid (not null and not empty after trimming).
     * 
     * @param str the string to check
     * @return true if valid, false otherwise
     */
    private boolean isValidString(String str) {
        return str != null && !str.trim().isEmpty();
    }
}
