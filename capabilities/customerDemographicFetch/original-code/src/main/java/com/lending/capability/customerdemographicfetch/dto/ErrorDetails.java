package com.lending.capability.customerdemographicfetch.dto;

/**
 * Error details DTO for API error responses.
 * Contains error code and message information.
 */
public class ErrorDetails {

    private String code;
    private String message;

    // Default constructor
    public ErrorDetails() {
    }

    // Constructor with all fields
    public ErrorDetails(String code, String message) {
        this.code = code;
        this.message = message;
    }

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "ErrorDetails{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
}
