package com.lending.capability.customerdemographicfetch.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.lending.capability.customerdemographicfetch.dto.CustomerDemographicRequest;
import com.lending.capability.customerdemographicfetch.dto.CustomerDemographicResponse;
import com.lending.capability.customerdemographicfetch.dto.ErrorDetails;
import com.lending.capability.customerdemographicfetch.enums.ResponseStatus;
import com.lending.capability.customerdemographicfetch.service.CustomerDemographicService;

import jakarta.validation.Valid;

/**
 * REST Controller for Customer Demographic Fetch API.
 * Provides endpoints for fetching customer demographic information.
 */
@RestController
@RequestMapping("/v1/demographics")
public class CustomerDemographicController {

    private static final Logger logger = LoggerFactory.getLogger(CustomerDemographicController.class);
    private static final String VALIDATION_ERROR_MESSAGE = "Unable to fetch customer details at this time. Please try again later.";

    private final CustomerDemographicService customerDemographicService;

    @Autowired
    public CustomerDemographicController(CustomerDemographicService customerDemographicService) {
        this.customerDemographicService = customerDemographicService;
    }

    /**
     * Fetch customer demographic information.
     * 
     * @param request the demographic fetch request
     * @param bindingResult validation results
     * @return ResponseEntity with demographic data or error
     */
    @PostMapping("/fetch")
    public ResponseEntity<CustomerDemographicResponse> fetchCustomerDemographics(
            @Valid @RequestBody CustomerDemographicRequest request,
            BindingResult bindingResult) {

        logger.info("Received demographic fetch request for customer: {}", request.getCustomerId());

        // Handle validation errors
        if (bindingResult.hasErrors()) {
            logger.error("Validation errors in request: {}", bindingResult.getAllErrors());
            CustomerDemographicResponse errorResponse = createValidationErrorResponse();
            return ResponseEntity.badRequest().body(errorResponse);
        }

        try {
            // Process the request
            CustomerDemographicResponse response = customerDemographicService.fetchCustomerDemographics(request);

            // Determine HTTP status based on response
            if (ResponseStatus.SUCCESS.getValue().equals(response.getStatus())) {
                logger.info("Successfully processed demographic fetch for customer: {}", request.getCustomerId());
                return ResponseEntity.ok(response);
            } else {
                logger.warn("Failed to fetch demographics for customer: {} - Error: {}", 
                           request.getCustomerId(), response.getError());
                
                // Return appropriate HTTP status based on error code
                HttpStatus httpStatus = determineHttpStatus(response.getError());
                return ResponseEntity.status(httpStatus).body(response);
            }

        } catch (Exception e) {
            logger.error("Unexpected error processing demographic fetch for customer: {}", 
                        request.getCustomerId(), e);
            
            CustomerDemographicResponse errorResponse = createInternalErrorResponse();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }



    /**
     * Create validation error response.
     * 
     * @return CustomerDemographicResponse with validation error
     */
    private CustomerDemographicResponse createValidationErrorResponse() {
        CustomerDemographicResponse response = new CustomerDemographicResponse();
        response.setStatus(ResponseStatus.FAILED.getValue());
        response.setError(new ErrorDetails("VALIDATION_ERROR", VALIDATION_ERROR_MESSAGE));
        return response;
    }

    /**
     * Create internal error response.
     * 
     * @return CustomerDemographicResponse with internal error
     */
    private CustomerDemographicResponse createInternalErrorResponse() {
        CustomerDemographicResponse response = new CustomerDemographicResponse();
        response.setStatus(ResponseStatus.FAILED.getValue());
        response.setError(new ErrorDetails("INTERNAL_ERROR", VALIDATION_ERROR_MESSAGE));
        return response;
    }

    /**
     * Determine HTTP status based on error details.
     * 
     * @param error the error details
     * @return appropriate HTTP status
     */
    private HttpStatus determineHttpStatus(ErrorDetails error) {
        if (error == null || error.getCode() == null) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }

        return switch (error.getCode()) {
            case "CUST404" -> HttpStatus.NOT_FOUND;
            case "VALIDATION_ERROR" -> HttpStatus.BAD_REQUEST;
            case "CLIENT_ERROR" -> HttpStatus.BAD_REQUEST;
            case "EXTERNAL_SERVICE_ERROR", "SERVER_ERROR" -> HttpStatus.SERVICE_UNAVAILABLE;
            default -> HttpStatus.INTERNAL_SERVER_ERROR;
        };
    }
}
