package com.lending.capability.customerdemographicfetch.repository;

import com.lending.capability.customerdemographicfetch.entity.DemographicRequestLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for DemographicRequestLog entity.
 * Provides data access methods for demographic request logging.
 */
@Repository
public interface DemographicRequestLogRepository extends JpaRepository<DemographicRequestLog, Long> {

    /**
     * Find logs by customer ID.
     * 
     * @param customerId the customer ID
     * @return list of demographic request logs
     */
    List<DemographicRequestLog> findByCustomerIdOrderByRequestTimestampDesc(String customerId);

    /**
     * Find logs by application ID.
     * 
     * @param applicationId the application ID
     * @return list of demographic request logs
     */
    List<DemographicRequestLog> findByApplicationIdOrderByRequestTimestampDesc(String applicationId);

    /**
     * Find logs by response status.
     * 
     * @param responseStatus the response status
     * @return list of demographic request logs
     */
    List<DemographicRequestLog> findByResponseStatusOrderByRequestTimestampDesc(String responseStatus);

    /**
     * Find logs within a time range.
     * 
     * @param startTime the start time
     * @param endTime the end time
     * @return list of demographic request logs
     */
    List<DemographicRequestLog> findByRequestTimestampBetweenOrderByRequestTimestampDesc(
            LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find the most recent log for a customer.
     * 
     * @param customerId the customer ID
     * @return optional demographic request log
     */
    Optional<DemographicRequestLog> findFirstByCustomerIdOrderByRequestTimestampDesc(String customerId);

    /**
     * Count logs by response status within a time range.
     * 
     * @param responseStatus the response status
     * @param startTime the start time
     * @param endTime the end time
     * @return count of logs
     */
    @Query("SELECT COUNT(d) FROM DemographicRequestLog d WHERE d.responseStatus = :responseStatus " +
           "AND d.requestTimestamp BETWEEN :startTime AND :endTime")
    long countByResponseStatusAndTimestampBetween(
            @Param("responseStatus") String responseStatus,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * Find logs with errors within a time range.
     * 
     * @param startTime the start time
     * @param endTime the end time
     * @return list of demographic request logs with errors
     */
    @Query("SELECT d FROM DemographicRequestLog d WHERE d.responseStatus = 'FAILED' " +
           "AND d.requestTimestamp BETWEEN :startTime AND :endTime " +
           "ORDER BY d.requestTimestamp DESC")
    List<DemographicRequestLog> findErrorLogsInTimeRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * Calculate average processing time within a time range.
     * 
     * @param startTime the start time
     * @param endTime the end time
     * @return average processing time in milliseconds
     */
    @Query("SELECT AVG(d.processingTimeMs) FROM DemographicRequestLog d " +
           "WHERE d.processingTimeMs IS NOT NULL " +
           "AND d.requestTimestamp BETWEEN :startTime AND :endTime")
    Double calculateAverageProcessingTime(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
}
