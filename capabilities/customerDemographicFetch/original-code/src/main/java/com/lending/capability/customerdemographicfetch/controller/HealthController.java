package com.lending.capability.customerdemographicfetch.controller;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Health check controller for Customer Demographic Fetch service.
 * Provides readiness and liveness endpoints as per guidelines.
 */
@RestController
@RequestMapping("/v1/customer-demographic-fetch/original/health")
public class HealthController {

    /**
     * General health check endpoint.
     * Provides overall service health status.
     *
     * @return ResponseEntity with health status
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "customer-demographic-fetch-original");
        response.put("timestamp", LocalDateTime.now());

        return ResponseEntity.ok(response);
    }

    /**
     * Readiness probe endpoint.
     * Indicates whether the service is ready to accept traffic.
     *
     * @return ResponseEntity with readiness status
     */
    @GetMapping("/ready")
    public ResponseEntity<Map<String, Object>> ready() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "customer-demographic-fetch-original");
        response.put("timestamp", LocalDateTime.now());
        response.put("checks", Map.of(
            "database", "UP",
            "external-service", "UP"
        ));
        
        return ResponseEntity.ok(response);
    }

    /**
     * Liveness probe endpoint.
     * Indicates whether the service is alive and running.
     * 
     * @return ResponseEntity with liveness status
     */
    @GetMapping("/live")
    public ResponseEntity<Map<String, Object>> live() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "customer-demographic-fetch-original");
        response.put("timestamp", LocalDateTime.now());
        response.put("uptime", getUptime());
        
        return ResponseEntity.ok(response);
    }

    /**
     * Get application uptime in milliseconds.
     * 
     * @return uptime in milliseconds
     */
    private long getUptime() {
        return System.currentTimeMillis() - 
               java.lang.management.ManagementFactory.getRuntimeMXBean().getStartTime();
    }
}
