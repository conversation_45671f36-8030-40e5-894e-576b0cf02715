package com.lending.capability.customerdemographicfetch.enums;

/**
 * Enum for gender values.
 */
public enum Gender {
    MALE("Male"),
    <PERSON><PERSON><PERSON><PERSON>("Female"),
    <PERSON><PERSON><PERSON>("Other");

    private final String displayName;

    Gender(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    /**
     * Get enum from string value.
     * 
     * @param value the string value
     * @return Gender enum
     * @throws IllegalArgumentException if value is invalid
     */
    public static Gender fromString(String value) {
        if (value == null) {
            throw new IllegalArgumentException("Gender cannot be null");
        }
        
        for (Gender gender : Gender.values()) {
            if (gender.displayName.equals(value)) {
                return gender;
            }
        }
        
        throw new IllegalArgumentException("Invalid gender: " + value);
    }
}
