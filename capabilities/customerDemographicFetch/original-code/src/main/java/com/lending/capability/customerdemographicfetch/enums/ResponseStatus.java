package com.lending.capability.customerdemographicfetch.enums;

/**
 * Enum for API response status.
 */
public enum ResponseStatus {
    SUCCESS("SUCCESS"),
    FAILED("FAILED");

    private final String value;

    ResponseStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    /**
     * Get enum from string value.
     * 
     * @param value the string value
     * @return ResponseStatus enum
     * @throws IllegalArgumentException if value is invalid
     */
    public static ResponseStatus fromString(String value) {
        if (value == null) {
            throw new IllegalArgumentException("Response status cannot be null");
        }
        
        for (ResponseStatus status : ResponseStatus.values()) {
            if (status.value.equalsIgnoreCase(value) || status.name().equalsIgnoreCase(value)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("Invalid response status: " + value);
    }
}
