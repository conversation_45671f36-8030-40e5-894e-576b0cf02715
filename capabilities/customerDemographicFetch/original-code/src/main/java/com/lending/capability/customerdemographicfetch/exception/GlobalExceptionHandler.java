package com.lending.capability.customerdemographicfetch.exception;

import com.lending.capability.customerdemographicfetch.dto.StandardErrorResponse;
import com.lending.capability.customerdemographicfetch.util.DataMaskingUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Global exception handler for standardized error responses.
 * Provides audit-ready error logging and consistent error format.
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    private static final String TRACE_ID_KEY = "traceId";

    /**
     * Handle validation errors.
     * 
     * @param ex the validation exception
     * @param request the HTTP request
     * @return standardized error response
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<StandardErrorResponse> handleValidationErrors(
            MethodArgumentNotValidException ex, HttpServletRequest request) {
        
        String traceId = generateTraceId();
        
        String errorMessage = ex.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));

        StandardErrorResponse errorResponse = new StandardErrorResponse(
                "VALIDATION_ERROR",
                "Validation failed: " + errorMessage,
                request.getRequestURI(),
                HttpStatus.BAD_REQUEST.value()
        );
        errorResponse.setTraceId(traceId);

        logError(traceId, "VALIDATION_ERROR", errorMessage, ex, request);

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Handle illegal argument exceptions.
     * 
     * @param ex the illegal argument exception
     * @param request the HTTP request
     * @return standardized error response
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<StandardErrorResponse> handleIllegalArgumentException(
            IllegalArgumentException ex, HttpServletRequest request) {
        
        String traceId = generateTraceId();
        
        StandardErrorResponse errorResponse = new StandardErrorResponse(
                "INVALID_ARGUMENT",
                "Invalid argument provided",
                request.getRequestURI(),
                HttpStatus.BAD_REQUEST.value()
        );
        errorResponse.setTraceId(traceId);

        logError(traceId, "INVALID_ARGUMENT", ex.getMessage(), ex, request);

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Handle runtime exceptions.
     * 
     * @param ex the runtime exception
     * @param request the HTTP request
     * @return standardized error response
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<StandardErrorResponse> handleRuntimeException(
            RuntimeException ex, HttpServletRequest request) {
        
        String traceId = generateTraceId();
        
        StandardErrorResponse errorResponse = new StandardErrorResponse(
                "INTERNAL_ERROR",
                "An internal error occurred. Please try again later.",
                request.getRequestURI(),
                HttpStatus.INTERNAL_SERVER_ERROR.value()
        );
        errorResponse.setTraceId(traceId);

        logError(traceId, "INTERNAL_ERROR", ex.getMessage(), ex, request);

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    /**
     * Handle all other exceptions.
     * 
     * @param ex the exception
     * @param request the HTTP request
     * @return standardized error response
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<StandardErrorResponse> handleGenericException(
            Exception ex, HttpServletRequest request) {
        
        String traceId = generateTraceId();
        
        StandardErrorResponse errorResponse = new StandardErrorResponse(
                "UNEXPECTED_ERROR",
                "An unexpected error occurred. Please contact support.",
                request.getRequestURI(),
                HttpStatus.INTERNAL_SERVER_ERROR.value()
        );
        errorResponse.setTraceId(traceId);

        logError(traceId, "UNEXPECTED_ERROR", ex.getMessage(), ex, request);

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    /**
     * Generate unique trace ID for request tracking.
     * 
     * @return unique trace ID
     */
    private String generateTraceId() {
        String traceId = UUID.randomUUID().toString();
        MDC.put(TRACE_ID_KEY, traceId);
        return traceId;
    }

    /**
     * Log error with audit-ready format and masked sensitive data.
     * 
     * @param traceId the trace ID
     * @param errorCode the error code
     * @param errorMessage the error message
     * @param exception the exception
     * @param request the HTTP request
     */
    private void logError(String traceId, String errorCode, String errorMessage, 
                         Exception exception, HttpServletRequest request) {
        
        try {
            // Mask any potential PII in error message
            String maskedMessage = DataMaskingUtil.containsPII(errorMessage) ? 
                    DataMaskingUtil.maskGenericData(errorMessage) : errorMessage;

            logger.error("ERROR_OCCURRED - TraceId: {}, ErrorCode: {}, Path: {}, Method: {}, " +
                        "Message: {}, Exception: {}", 
                        traceId, 
                        errorCode, 
                        request.getRequestURI(), 
                        request.getMethod(),
                        maskedMessage,
                        exception.getClass().getSimpleName(),
                        exception);

        } catch (Exception loggingException) {
            // Fallback logging if masking fails
            logger.error("ERROR_OCCURRED - TraceId: {}, ErrorCode: {}, Path: {}, " +
                        "LoggingError: {}", 
                        traceId, errorCode, request.getRequestURI(), 
                        loggingException.getMessage());
        } finally {
            MDC.remove(TRACE_ID_KEY);
        }
    }
}
