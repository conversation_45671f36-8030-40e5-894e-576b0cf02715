package com.lending.capability.customerdemographicfetch.service;

import com.lending.capability.customerdemographicfetch.dto.CustomerDemographicRequest;
import com.lending.capability.customerdemographicfetch.dto.CustomerDemographicResponse;
import com.lending.capability.customerdemographicfetch.dto.ErrorDetails;
import com.lending.capability.customerdemographicfetch.entity.DemographicRequestLog;
import com.lending.capability.customerdemographicfetch.enums.ResponseStatus;
import com.lending.capability.customerdemographicfetch.repository.DemographicRequestLogRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Service class for Customer Demographic Fetch operations.
 * Implements the business flow phases as defined in requirements.
 */
@Service
public class CustomerDemographicService {

    private static final Logger logger = LoggerFactory.getLogger(CustomerDemographicService.class);
    private static final String FALLBACK_MESSAGE = "Unable to fetch customer details at this time. Please try again later.";

    private final ExternalDemographicService externalDemographicService;
    private final ValidationService validationService;
    private final DemographicRequestLogRepository logRepository;

    @Autowired
    public CustomerDemographicService(ExternalDemographicService externalDemographicService,
                                    ValidationService validationService,
                                    DemographicRequestLogRepository logRepository) {
        this.externalDemographicService = externalDemographicService;
        this.validationService = validationService;
        this.logRepository = logRepository;
    }

    /**
     * Fetch customer demographic information.
     * Implements the complete business flow with all phases.
     * 
     * @param request the demographic fetch request
     * @return CustomerDemographicResponse with demographic data or error
     */
    public CustomerDemographicResponse fetchCustomerDemographics(CustomerDemographicRequest request) {
        logger.info("Starting customer demographic fetch for request: {}", request);

        long startTime = System.currentTimeMillis();
        DemographicRequestLog requestLog = new DemographicRequestLog(
                request.getCustomerId(),
                request.getApplicationId(),
                request.getProductCode(),
                request.getLoanCategory()
        );

        try {
            // Phase 1: Trigger Customer Demographic Fetch - Basic validations
            if (!validationService.validateRequest(request)) {
                logger.error("Request validation failed for: {}", request);
                CustomerDemographicResponse errorResponse = createErrorResponse("VALIDATION_ERROR", FALLBACK_MESSAGE);
                logRequest(requestLog, errorResponse, startTime);
                return errorResponse;
            }

            // Phase 2: API Request to Customer Demographic Service
            CustomerDemographicResponse response = externalDemographicService.fetchDemographics(request);

            // Phase 3: API Response Processing
            if (response == null) {
                logger.error("Null response received from external service for request: {}", request);
                CustomerDemographicResponse errorResponse = createErrorResponse("NULL_RESPONSE", FALLBACK_MESSAGE);
                logRequest(requestLog, errorResponse, startTime);
                return errorResponse;
            }

            // Phase 4: Response Validation
            if (ResponseStatus.SUCCESS.getValue().equals(response.getStatus()) &&
                validationService.validateResponse(response)) {
                logger.info("Successfully fetched customer demographics for customer: {}",
                           request.getCustomerId());
                logRequest(requestLog, response, startTime);
                return response;
            } else {
                logger.error("Response validation failed or status is FAILED for request: {}", request);
                if (response.getError() != null) {
                    logRequest(requestLog, response, startTime);
                    return response; // Return the error response as-is
                } else {
                    CustomerDemographicResponse errorResponse = createErrorResponse("RESPONSE_VALIDATION_ERROR", FALLBACK_MESSAGE);
                    logRequest(requestLog, errorResponse, startTime);
                    return errorResponse;
                }
            }

        } catch (Exception e) {
            logger.error("Unexpected error occurred while fetching demographics for request: {}",
                        request, e);
            CustomerDemographicResponse errorResponse = createErrorResponse("INTERNAL_ERROR", FALLBACK_MESSAGE);
            logRequest(requestLog, errorResponse, startTime);
            return errorResponse;
        }
    }

    /**
     * Create error response with given code and message.
     *
     * @param errorCode the error code
     * @param errorMessage the error message
     * @return CustomerDemographicResponse with error details
     */
    private CustomerDemographicResponse createErrorResponse(String errorCode, String errorMessage) {
        CustomerDemographicResponse response = new CustomerDemographicResponse();
        response.setStatus(ResponseStatus.FAILED.getValue());
        response.setError(new ErrorDetails(errorCode, errorMessage));
        return response;
    }

    /**
     * Log the request and response for audit purposes.
     *
     * @param requestLog the request log entity
     * @param response the response
     * @param startTime the start time in milliseconds
     */
    private void logRequest(DemographicRequestLog requestLog, CustomerDemographicResponse response, long startTime) {
        try {
            long processingTime = System.currentTimeMillis() - startTime;

            requestLog.setResponseStatus(response.getStatus());
            requestLog.setProcessingTimeMs(processingTime);

            if (response.getError() != null) {
                requestLog.setErrorCode(response.getError().getCode());
                requestLog.setErrorMessage(response.getError().getMessage());
            }

            logRepository.save(requestLog);
            logger.debug("Request logged successfully for customer: {}", requestLog.getCustomerId());

        } catch (Exception e) {
            logger.error("Failed to log request for customer: {}", requestLog.getCustomerId(), e);
            // Don't throw exception as logging failure shouldn't affect the main flow
        }
    }
}
