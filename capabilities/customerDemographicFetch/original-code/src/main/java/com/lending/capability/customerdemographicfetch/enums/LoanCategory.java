package com.lending.capability.customerdemographicfetch.enums;

/**
 * Enum for loan categories.
 * ETB - Existing to Bank
 * NTB - New to Bank
 */
public enum LoanCategory {
    ETB("Existing to Bank"),
    NTB("New to Bank");

    private final String description;

    LoanCategory(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Get enum from string value.
     * 
     * @param value the string value
     * @return LoanCategory enum
     * @throws IllegalArgumentException if value is invalid
     */
    public static LoanCategory fromString(String value) {
        if (value == null) {
            throw new IllegalArgumentException("Loan category cannot be null");
        }
        
        for (LoanCategory category : LoanCategory.values()) {
            if (category.name().equals(value)) {
                return category;
            }
        }
        
        throw new IllegalArgumentException("Invalid loan category: " + value);
    }
}
