package com.lending.capability.customerdemographicfetch.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

/**
 * Request DTO for Customer Demographic Fetch API.
 * Contains identification fields required to fetch customer demographic information.
 */
public class CustomerDemographicRequest {

    @NotNull(message = "Customer ID is mandatory")
    @NotBlank(message = "Customer ID cannot be blank")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "Customer ID must be alphanumeric")
    private String customerId;

    @NotNull(message = "Application ID is mandatory")
    @NotBlank(message = "Application ID cannot be blank")
    private String applicationId;

    @NotNull(message = "Product code is mandatory")
    @NotBlank(message = "Product code cannot be blank")
    private String productCode;

    @NotNull(message = "Loan category is mandatory")
    @NotBlank(message = "Loan category cannot be blank")
    @Pattern(regexp = "^(ETB|NTB)$", message = "Loan category must be ETB or NTB")
    private String loanCategory;

    // Default constructor
    public CustomerDemographicRequest() {
    }

    // Constructor with all fields
    public CustomerDemographicRequest(String customerId, String applicationId, String productCode, String loanCategory) {
        this.customerId = customerId;
        this.applicationId = applicationId;
        this.productCode = productCode;
        this.loanCategory = loanCategory;
    }

    // Getters and Setters
    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getLoanCategory() {
        return loanCategory;
    }

    public void setLoanCategory(String loanCategory) {
        this.loanCategory = loanCategory;
    }

    @Override
    public String toString() {
        return "CustomerDemographicRequest{" +
                "customerId='" + maskSensitiveData(customerId) + '\'' +
                ", applicationId='" + applicationId + '\'' +
                ", productCode='" + productCode + '\'' +
                ", loanCategory='" + loanCategory + '\'' +
                '}';
    }

    /**
     * Mask sensitive customer ID for logging purposes.
     * 
     * @param data the data to mask
     * @return masked data
     */
    private String maskSensitiveData(String data) {
        if (data == null || data.length() <= 4) {
            return "****";
        }
        return data.substring(0, 2) + "****" + data.substring(data.length() - 2);
    }
}
