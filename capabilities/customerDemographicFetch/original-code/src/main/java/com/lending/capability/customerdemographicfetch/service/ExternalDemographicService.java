package com.lending.capability.customerdemographicfetch.service;

import java.time.Duration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.lending.capability.customerdemographicfetch.dto.CustomerDemographicRequest;
import com.lending.capability.customerdemographicfetch.dto.CustomerDemographicResponse;
import com.lending.capability.customerdemographicfetch.dto.ErrorDetails;
import com.lending.capability.customerdemographicfetch.enums.ResponseStatus;

import reactor.util.retry.Retry;

/**
 * Service for communicating with external demographic data sources.
 * Implements retry logic and fallback mechanisms as per business requirements.
 */
@Service
public class ExternalDemographicService {

    private static final Logger logger = LoggerFactory.getLogger(ExternalDemographicService.class);

    private final WebClient webClient;
    private final int maxRetryAttempts;
    private final long retryDelay;
    private final long timeout;

    public ExternalDemographicService(@Value("${external.demographic-service.base-url}") String baseUrl,
                                    @Value("${external.demographic-service.timeout}") long timeout,
                                    @Value("${external.demographic-service.retry.max-attempts}") int maxRetryAttempts,
                                    @Value("${external.demographic-service.retry.delay}") long retryDelay) {
        this.webClient = WebClient.builder()
                .baseUrl(baseUrl)
                .build();
        this.timeout = timeout;
        this.maxRetryAttempts = maxRetryAttempts;
        this.retryDelay = retryDelay;
    }

    /**
     * Fetch customer demographics from external service with retry logic.
     * 
     * @param request the demographic fetch request
     * @return CustomerDemographicResponse from external service
     */
    public CustomerDemographicResponse fetchDemographics(CustomerDemographicRequest request) {
        logger.info("Calling external demographic service for customer: {}", request.getCustomerId());

        try {
            return webClient.post()
                    .uri("/api/customer/internal/v1/fetch")
                    .bodyValue(request)
                    .retrieve()
                    .bodyToMono(CustomerDemographicResponse.class)
                    .timeout(Duration.ofMillis(timeout))
                    .retryWhen(Retry.backoff(maxRetryAttempts, Duration.ofMillis(retryDelay))
                            .filter(this::isRetryableException)
                            .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                                logger.error("Retry exhausted for customer: {} after {} attempts", 
                                           request.getCustomerId(), maxRetryAttempts);
                                return new RuntimeException("External service unavailable after retries");
                            }))
                    .doOnSuccess(response -> logger.info("Successfully received response from external service for customer: {}", 
                                                       request.getCustomerId()))
                    .doOnError(error -> logger.error("Error calling external service for customer: {}", 
                                                    request.getCustomerId(), error))
                    .block();

        } catch (WebClientResponseException e) {
            logger.error("HTTP error from external service for customer: {} - Status: {}, Body: {}", 
                        request.getCustomerId(), e.getStatusCode(), e.getResponseBodyAsString());
            return handleHttpError(e);
        } catch (Exception e) {
            logger.error("Unexpected error calling external service for customer: {}", 
                        request.getCustomerId(), e);
            return createErrorResponse("EXTERNAL_SERVICE_ERROR", 
                                     "External service is currently unavailable");
        }
    }

    /**
     * Determine if an exception is retryable.
     * 
     * @param throwable the exception to check
     * @return true if retryable, false otherwise
     */
    private boolean isRetryableException(Throwable throwable) {
        if (throwable instanceof WebClientResponseException webClientException) {
            HttpStatus status = (HttpStatus) webClientException.getStatusCode();
            // Retry on server errors (5xx) and specific client errors
            return status.is5xxServerError() || 
                   status == HttpStatus.REQUEST_TIMEOUT ||
                   status == HttpStatus.TOO_MANY_REQUESTS;
        }
        // Retry on timeout and connection issues
        return throwable instanceof java.util.concurrent.TimeoutException ||
               throwable instanceof java.net.ConnectException ||
               throwable.getCause() instanceof java.net.ConnectException;
    }

    /**
     * Handle HTTP errors from external service.
     * 
     * @param e the WebClientResponseException
     * @return appropriate error response
     */
    private CustomerDemographicResponse handleHttpError(WebClientResponseException e) {
        HttpStatus status = (HttpStatus) e.getStatusCode();
        
        if (status == HttpStatus.NOT_FOUND) {
            return createErrorResponse("CUST404", "Customer details not found");
        } else if (status.is4xxClientError()) {
            return createErrorResponse("CLIENT_ERROR", "Invalid request to external service");
        } else {
            return createErrorResponse("SERVER_ERROR", "External service error");
        }
    }

    /**
     * Create error response.
     * 
     * @param errorCode the error code
     * @param errorMessage the error message
     * @return CustomerDemographicResponse with error
     */
    private CustomerDemographicResponse createErrorResponse(String errorCode, String errorMessage) {
        CustomerDemographicResponse response = new CustomerDemographicResponse();
        response.setStatus(ResponseStatus.FAILED.getValue());
        response.setError(new ErrorDetails(errorCode, errorMessage));
        return response;
    }
}
