package com.lending.capability.customerdemographicfetch;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Main application class for Customer Demographic Fetch service.
 * This service provides capability to fetch customer demographic information
 * from internal/external systems during loan application process.
 */
@SpringBootApplication
public class CustomerDemographicFetchApplication {

    public static void main(String[] args) {
        SpringApplication.run(CustomerDemographicFetchApplication.class, args);
    }
}
