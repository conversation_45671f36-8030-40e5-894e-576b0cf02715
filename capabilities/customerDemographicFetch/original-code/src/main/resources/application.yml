server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /api/customer-demographic-fetch

spring:
  application:
    name: customer-demographic-fetch-original
  
  datasource:
    url: ${DATABASE_URL:***********************************************************}
    username: ${DATABASE_USERNAME:postgres}
    password: ${DATABASE_PASSWORD:postgres}
    driver-class-name: org.postgresql.Driver
    
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: ${SHOW_SQL:false}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always

# SpringDoc OpenAPI configuration
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    try-it-out-enabled: true
    operations-sorter: method
    tags-sorter: alpha
    doc-expansion: none
    disable-swagger-default-url: true
  show-actuator: true

# External service configuration
external:
  demographic-service:
    base-url: ${DEMOGRAPHIC_SERVICE_URL:http://localhost:8082/api/customer-demographic-fetch/internal}
    timeout: ${DEMOGRAPHIC_SERVICE_TIMEOUT:30000}
    retry:
      max-attempts: ${DEMOGRAPHIC_SERVICE_RETRY_MAX:3}
      delay: ${DEMOGRAPHIC_SERVICE_RETRY_DELAY:1000}

# CORS configuration
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:*}
  allowed-methods: ${CORS_ALLOWED_METHODS:GET,POST,PUT,DELETE,OPTIONS}
  allowed-headers: ${CORS_ALLOWED_HEADERS:*}

# Logging configuration
logging:
  level:
    com.lending.capability.customerdemographicfetch: ${LOG_LEVEL:INFO}
    org.springframework.web: ${WEB_LOG_LEVEL:WARN}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"