-- Create table for logging demographic fetch requests
-- This table stores audit trail of all demographic fetch requests

CREATE TABLE demographic_request_log (
    id BIGSERIAL PRIMARY KEY,
    customer_id VARCHAR(50) NOT NULL,
    application_id VARCHAR(100) NOT NULL,
    product_code VARCHAR(50) NOT NULL,
    loan_category VARCHAR(10) NOT NULL CHECK (loan_category IN ('ETB', 'NTB')),
    request_timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    response_status VARCHAR(20) NOT NULL CHECK (response_status IN ('SUCCESS', 'FAILED')),
    error_code VARCHAR(50),
    error_message TEXT,
    processing_time_ms BIGINT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX idx_demographic_request_log_customer_id ON demographic_request_log(customer_id);
CREATE INDEX idx_demographic_request_log_application_id ON demographic_request_log(application_id);
CREATE INDEX idx_demographic_request_log_request_timestamp ON demographic_request_log(request_timestamp);
CREATE INDEX idx_demographic_request_log_response_status ON demographic_request_log(response_status);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_demographic_request_log_updated_at 
    BEFORE UPDATE ON demographic_request_log 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
