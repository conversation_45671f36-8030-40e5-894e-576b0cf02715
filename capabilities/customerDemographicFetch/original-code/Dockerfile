# Multi-stage build for Customer Demographic Service
FROM maven:3.9.6-eclipse-temurin-21 AS build

# Set working directory
WORKDIR /app

# Copy pom.xml first for better Docker layer caching
COPY pom.xml .

# Download dependencies
RUN mvn dependency:go-offline -B

# Copy source code
COPY src ./src

# Build the application
RUN mvn clean package -DskipTests

# ── Runtime stage with OpenTelemetry agent ─────────────────────────────
FROM eclipse-temurin:21-jre-jammy

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN groupadd -r cdfservice && useradd -r -g cdfservice cdfservice

# Set working directory
WORKDIR /app

# Copy JAR from build stage
COPY --from=build /app/target/customer-demographic-fetch-original-*.jar app.jar

# Create logs and otel directories
RUN mkdir -p logs /otel && chown -R cdfservice:cdfservice /app /otel

# 📥 Download OpenTelemetry Java agent
ENV OTEL_AGENT_VERSION=2.17.1
RUN curl -sSL https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/download/v${OTEL_AGENT_VERSION}/opentelemetry-javaagent.jar \
    -o /otel/opentelemetry-javaagent.jar && \
    chown cdfservice:cdfservice /otel/opentelemetry-javaagent.jar

# Switch to non-root user
USER cdfservice

# Expose port
EXPOSE 8119

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8119/api/customer-demographic-fetch/v1/customer-demographic-fetch/original/health || exit 1

# Set JVM options
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseContainerSupport" \
    OTEL_AGENT_PATH="/otel/opentelemetry-javaagent.jar" \
    SERVER_PORT=8119 \
    # OpenTelemetry
    OTEL_SERVICE_NAME=${OTEL_SERVICE_NAME:-customer-demographic-fetch} \
    OTEL_TRACES_EXPORTER=${OTEL_TRACES_EXPORTER:-otlp} \
    OTEL_METRICS_EXPORTER=${OTEL_METRICS_EXPORTER:-otlp} \
    OTEL_LOGS_EXPORTER=${OTEL_LOGS_EXPORTER:-otlp} \
    OTEL_EXPORTER_OTLP_ENDPOINT=${OTEL_EXPORTER_OTLP_ENDPOINT:-http://otel-collector.observability:4317} \
    OTEL_EXPORTER_OTLP_PROTOCOL=${OTEL_EXPORTER_OTLP_PROTOCOL:-grpc} \
    OTEL_RESOURCE_ATTRIBUTES=${OTEL_RESOURCE_ATTRIBUTES:-service.name=customer-demographic-fetch,service.version=1.0.0,deployment.environment=docker}

# Run the application with OpenTelemetry Java agent
ENTRYPOINT ["sh", "-c", "java -javaagent:$OTEL_AGENT_PATH $JAVA_OPTS -jar app.jar"]
