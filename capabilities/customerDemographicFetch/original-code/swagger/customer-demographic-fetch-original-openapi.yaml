openapi: 3.0.3
info:
  title: Customer Demographic Fetch - Original Service
  description: |
    Original service for fetching customer demographic information during loan application process.
    This service provides capability to fetch customer demographic data from internal/external systems
    with comprehensive validation, retry logic, and audit-ready logging.
  version: 1.0.0
  contact:
    name: Lending Platform Team
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api/customer-demographic-fetch
    description: Local development server
  - url: https://api.company.com/api/customer-demographic-fetch
    description: Production server

paths:
  /v1/demographics/fetch:
    post:
      summary: Fetch customer demographic information
      description: |
        Fetches customer demographic information from external sources based on customer ID,
        application ID, product code, and loan category. Implements retry logic and fallback
        mechanisms for robust data retrieval.
      operationId: fetchCustomerDemographics
      tags:
        - Demographics
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerDemographicRequest'
            examples:
              etb_customer:
                summary: ETB Customer Request
                value:
                  customerId: "CUST12345"
                  applicationId: "APP98765"
                  productCode: "FOUR_WHEELER_ETB_PA"
                  loanCategory: "ETB"
              ntb_customer:
                summary: NTB Customer Request
                value:
                  customerId: "CUSTNEW001"
                  applicationId: "APPNEW123"
                  productCode: "TWO_WHEELER_NTB_PA"
                  loanCategory: "NTB"
      responses:
        '200':
          description: Successfully retrieved customer demographics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerDemographicResponse'
              examples:
                success_response:
                  summary: Successful demographic fetch
                  value:
                    name: "John Doe"
                    dob: "1985-06-25"
                    gender: "Male"
                    email: "<EMAIL>"
                    mobileNumber: "**********"
                    addressLine1: "123 Elm Street"
                    addressLine2: "Block A"
                    city: "Mumbai"
                    state: "Maharashtra"
                    pincode: "400001"
                    status: "SUCCESS"
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerDemographicResponse'
              examples:
                validation_error:
                  summary: Validation error response
                  value:
                    status: "FAILED"
                    error:
                      code: "VALIDATION_ERROR"
                      message: "Unable to fetch customer details at this time. Please try again later."
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerDemographicResponse'
              examples:
                customer_not_found:
                  summary: Customer not found response
                  value:
                    status: "FAILED"
                    error:
                      code: "CUST404"
                      message: "Customer details not found"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerDemographicResponse'
              examples:
                internal_error:
                  summary: Internal server error response
                  value:
                    status: "FAILED"
                    error:
                      code: "INTERNAL_ERROR"
                      message: "Unable to fetch customer details at this time. Please try again later."
        '503':
          description: Service unavailable - external service error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerDemographicResponse'
              examples:
                service_unavailable:
                  summary: External service unavailable
                  value:
                    status: "FAILED"
                    error:
                      code: "EXTERNAL_SERVICE_ERROR"
                      message: "External service is currently unavailable"

  /v1/demographics/health:
    get:
      summary: Health check for demographics controller
      description: Simple health check endpoint for the demographics controller
      operationId: demographicsHealth
      tags:
        - Health
      responses:
        '200':
          description: Controller is healthy
          content:
            text/plain:
              schema:
                type: string
                example: "Customer Demographic Controller is healthy"

  /v1/customer-demographic-fetch/original/health/ready:
    get:
      summary: Readiness probe
      description: |
        Indicates whether the service is ready to accept traffic.
        Checks database connectivity and external service availability.
      operationId: readinessProbe
      tags:
        - Health
      responses:
        '200':
          description: Service is ready
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
              examples:
                ready_response:
                  summary: Service ready response
                  value:
                    status: "UP"
                    service: "customer-demographic-fetch-original"
                    timestamp: "2023-12-07T10:30:00Z"
                    checks:
                      database: "UP"
                      external-service: "UP"

  /v1/customer-demographic-fetch/original/health/live:
    get:
      summary: Liveness probe
      description: |
        Indicates whether the service is alive and running.
        Returns service uptime and basic status information.
      operationId: livenessProbe
      tags:
        - Health
      responses:
        '200':
          description: Service is alive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LivenessResponse'
              examples:
                live_response:
                  summary: Service alive response
                  value:
                    status: "UP"
                    service: "customer-demographic-fetch-original"
                    timestamp: "2023-12-07T10:30:00Z"
                    uptime: 3600000

components:
  schemas:
    CustomerDemographicRequest:
      type: object
      required:
        - customerId
        - applicationId
        - productCode
        - loanCategory
      properties:
        customerId:
          type: string
          pattern: '^[A-Za-z0-9]+$'
          description: Unique identifier for the customer (alphanumeric only)
          example: "CUST12345"
        applicationId:
          type: string
          description: Unique identifier for the loan application
          example: "APP98765"
        productCode:
          type: string
          description: Product code for the loan type
          example: "FOUR_WHEELER_ETB_PA"
        loanCategory:
          type: string
          enum: ["ETB", "NTB"]
          description: Loan category - ETB (Existing to Bank) or NTB (New to Bank)
          example: "ETB"

    CustomerDemographicResponse:
      type: object
      required:
        - status
      properties:
        name:
          type: string
          description: Full name of the customer
          example: "John Doe"
        dob:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2}$'
          description: Date of birth in YYYY-MM-DD format
          example: "1985-06-25"
        gender:
          type: string
          enum: ["Male", "Female", "Other"]
          description: Gender of the customer
          example: "Male"
        email:
          type: string
          format: email
          description: Email address of the customer
          example: "<EMAIL>"
        mobileNumber:
          type: string
          pattern: '^\d{10}$'
          description: 10-digit mobile number
          example: "**********"
        addressLine1:
          type: string
          description: Primary address line
          example: "123 Elm Street"
        addressLine2:
          type: string
          description: Secondary address line (optional)
          example: "Block A"
        city:
          type: string
          description: City name
          example: "Mumbai"
        state:
          type: string
          description: State name
          example: "Maharashtra"
        pincode:
          type: string
          pattern: '^\d{6}$'
          description: 6-digit postal code
          example: "400001"
        status:
          type: string
          enum: ["SUCCESS", "FAILED"]
          description: Response status indicator
          example: "SUCCESS"
        error:
          $ref: '#/components/schemas/ErrorDetails'

    ErrorDetails:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: string
          description: Error code for categorizing the error
          example: "CUST404"
        message:
          type: string
          description: Human-readable error message
          example: "Customer details not found"

    HealthResponse:
      type: object
      required:
        - status
        - service
        - timestamp
      properties:
        status:
          type: string
          enum: ["UP", "DOWN"]
          description: Overall health status
          example: "UP"
        service:
          type: string
          description: Service name
          example: "customer-demographic-fetch-original"
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the health check
          example: "2023-12-07T10:30:00Z"
        checks:
          type: object
          description: Individual component health checks
          additionalProperties:
            type: string
          example:
            database: "UP"
            external-service: "UP"

    LivenessResponse:
      type: object
      required:
        - status
        - service
        - timestamp
        - uptime
      properties:
        status:
          type: string
          enum: ["UP", "DOWN"]
          description: Liveness status
          example: "UP"
        service:
          type: string
          description: Service name
          example: "customer-demographic-fetch-original"
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the liveness check
          example: "2023-12-07T10:30:00Z"
        uptime:
          type: integer
          format: int64
          description: Service uptime in milliseconds
          example: 3600000

tags:
  - name: Demographics
    description: Customer demographic information operations
  - name: Health
    description: Health check and monitoring endpoints
