openapi: 3.0.3
info:
  title: Customer Demographic Fetch - Mock Service
  description: |
    Mock service for simulating external demographic data sources for testing and development.
    Provides configurable responses, failure simulation, and admin controls for testing scenarios.
  version: 1.0.0
  contact:
    name: Lending Platform Team
    email: <EMAIL>

servers:
  - url: http://localhost:8082/api/customer-demographic-fetch/internal
    description: Local development mock server
  - url: https://mock.company.com/api/customer-demographic-fetch/internal
    description: Mock server environment

paths:
  /api/customer/internal/v1/fetch:
    post:
      summary: Mock endpoint for customer demographic fetch
      description: |
        Simulates external demographic service responses with configurable behavior.
        Supports predefined responses, random failures, and response delays for testing.
      operationId: fetchMockDemographics
      tags:
        - Mock Demographics
      parameters:
        - name: X-Transaction-Id
          in: header
          description: Transaction ID for request tracing
          required: false
          schema:
            type: string
            format: uuid
          example: "123e4567-e89b-12d3-a456-************"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerDemographicRequest'
            examples:
              etb_customer:
                summary: ETB Customer Request
                value:
                  customerId: "CUST12345"
                  applicationId: "APP98765"
                  productCode: "FOUR_WHEELER_ETB_PA"
                  loanCategory: "ETB"
              ntb_customer:
                summary: NTB Customer Request
                value:
                  customerId: "CUSTNEW001"
                  applicationId: "APPNEW123"
                  productCode: "TWO_WHEELER_NTB_PA"
                  loanCategory: "NTB"
              not_found_customer:
                summary: Customer Not Found Scenario
                value:
                  customerId: "CUSTNOTFOUND"
                  applicationId: "APP12345"
                  productCode: "PERSONAL_LOAN"
                  loanCategory: "NTB"
      responses:
        '200':
          description: Successfully retrieved mock demographic data
          headers:
            X-Transaction-Id:
              description: Transaction ID for tracing
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerDemographicResponse'
              examples:
                success_response:
                  summary: Successful mock response
                  value:
                    name: "John Doe"
                    dob: "1985-06-25"
                    gender: "Male"
                    email: "<EMAIL>"
                    mobileNumber: "**********"
                    addressLine1: "123 Elm Street"
                    addressLine2: "Block A"
                    city: "Mumbai"
                    state: "Maharashtra"
                    pincode: "400001"
                    status: "SUCCESS"
        '400':
          description: Bad request - validation errors
          headers:
            X-Transaction-Id:
              description: Transaction ID for tracing
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerDemographicResponse'
              examples:
                validation_error:
                  summary: Validation error response
                  value:
                    status: "FAILED"
                    error:
                      code: "VALIDATION_ERROR"
                      message: "Invalid request parameters"
        '404':
          description: Customer not found
          headers:
            X-Transaction-Id:
              description: Transaction ID for tracing
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerDemographicResponse'
              examples:
                customer_not_found:
                  summary: Customer not found response
                  value:
                    status: "FAILED"
                    error:
                      code: "CUST404"
                      message: "Customer details not found"
        '500':
          description: Internal server error or simulated failure
          headers:
            X-Transaction-Id:
              description: Transaction ID for tracing
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerDemographicResponse'
              examples:
                random_failure:
                  summary: Simulated random failure
                  value:
                    status: "FAILED"
                    error:
                      code: "RANDOM_FAILURE"
                      message: "Simulated service failure"

  /demographics/health:
    get:
      summary: Health check for mock demographics controller
      description: Simple health check endpoint for the mock demographics controller
      operationId: mockDemographicsHealth
      tags:
        - Health
      responses:
        '200':
          description: Mock controller is healthy
          content:
            text/plain:
              schema:
                type: string
                example: "Mock Demographic Controller is healthy"

  /v1/customer-demographic-fetch/mock/health/ready:
    get:
      summary: Mock service readiness probe
      description: Indicates whether the mock service is ready to accept traffic
      operationId: mockReadinessProbe
      tags:
        - Health
      responses:
        '200':
          description: Mock service is ready
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
              examples:
                ready_response:
                  summary: Mock service ready response
                  value:
                    status: "UP"
                    service: "customer-demographic-fetch-mock"
                    timestamp: "2023-12-07T10:30:00Z"
                    checks:
                      mock-data: "UP"

  /v1/customer-demographic-fetch/mock/health/live:
    get:
      summary: Mock service liveness probe
      description: Indicates whether the mock service is alive and running
      operationId: mockLivenessProbe
      tags:
        - Health
      responses:
        '200':
          description: Mock service is alive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LivenessResponse'
              examples:
                live_response:
                  summary: Mock service alive response
                  value:
                    status: "UP"
                    service: "customer-demographic-fetch-mock"
                    timestamp: "2023-12-07T10:30:00Z"
                    uptime: 3600000

  /admin/config:
    get:
      summary: Get current mock configuration
      description: Retrieve current mock service configuration including failure rates and delays
      operationId: getMockConfig
      tags:
        - Admin
      responses:
        '200':
          description: Current mock configuration
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockConfig'
              examples:
                config_response:
                  summary: Current configuration
                  value:
                    failureRate: 0.1
                    responseDelay: 100
                    predefinedResponsesCount: 3
                    defaultFailureRate: 0.1
                    defaultResponseDelay: 100

  /admin/config/failure-rate:
    post:
      summary: Update failure rate
      description: Update the random failure rate for mock responses (0.0 to 1.0)
      operationId: updateFailureRate
      tags:
        - Admin
      parameters:
        - name: failureRate
          in: query
          required: true
          description: New failure rate between 0.0 and 1.0
          schema:
            type: number
            format: double
            minimum: 0.0
            maximum: 1.0
          example: 0.2
      responses:
        '200':
          description: Failure rate updated successfully
          content:
            text/plain:
              schema:
                type: string
                example: "Failure rate updated to: 0.2"
        '400':
          description: Invalid failure rate value
          content:
            text/plain:
              schema:
                type: string
                example: "Failure rate must be between 0.0 and 1.0"

  /admin/config/delay:
    post:
      summary: Update response delay
      description: Update the response delay in milliseconds for mock responses
      operationId: updateResponseDelay
      tags:
        - Admin
      parameters:
        - name: delayMs
          in: query
          required: true
          description: New delay in milliseconds (non-negative)
          schema:
            type: integer
            format: int64
            minimum: 0
          example: 500
      responses:
        '200':
          description: Response delay updated successfully
          content:
            text/plain:
              schema:
                type: string
                example: "Response delay updated to: 500 ms"
        '400':
          description: Invalid delay value
          content:
            text/plain:
              schema:
                type: string
                example: "Delay must be non-negative"

  /admin/customers/{customerId}/response:
    post:
      summary: Set predefined response for customer
      description: Configure a predefined response for a specific customer ID
      operationId: setPredefinedResponse
      tags:
        - Admin
      parameters:
        - name: customerId
          in: path
          required: true
          description: Customer ID to configure
          schema:
            type: string
          example: "CUST12345"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerDemographicResponse'
      responses:
        '200':
          description: Predefined response set successfully
          content:
            text/plain:
              schema:
                type: string
                example: "Predefined response set for customer: CUST12345"
    delete:
      summary: Remove predefined response for customer
      description: Remove the predefined response for a specific customer ID
      operationId: removePredefinedResponse
      tags:
        - Admin
      parameters:
        - name: customerId
          in: path
          required: true
          description: Customer ID to remove configuration
          schema:
            type: string
          example: "CUST12345"
      responses:
        '200':
          description: Predefined response removed successfully
          content:
            text/plain:
              schema:
                type: string
                example: "Predefined response removed for customer: CUST12345"

  /admin/customers/responses:
    get:
      summary: Get all predefined customer responses
      description: Retrieve all configured predefined responses
      operationId: getAllPredefinedResponses
      tags:
        - Admin
      responses:
        '200':
          description: All predefined responses
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  $ref: '#/components/schemas/CustomerDemographicResponse'

  /admin/config/reset:
    post:
      summary: Reset configuration to defaults
      description: Reset all mock configuration to default values
      operationId: resetMockConfig
      tags:
        - Admin
      responses:
        '200':
          description: Configuration reset successfully
          content:
            text/plain:
              schema:
                type: string
                example: "Configuration reset to defaults"

  /admin/stats:
    get:
      summary: Get service statistics
      description: Retrieve mock service usage statistics
      operationId: getMockStats
      tags:
        - Admin
      responses:
        '200':
          description: Service statistics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceStats'

  /admin/health:
    get:
      summary: Admin endpoints health check
      description: Health check for admin functionality
      operationId: adminHealth
      tags:
        - Health
      responses:
        '200':
          description: Admin controller is healthy
          content:
            text/plain:
              schema:
                type: string
                example: "Mock Admin Controller is healthy"

components:
  schemas:
    CustomerDemographicRequest:
      type: object
      required:
        - customerId
        - applicationId
        - productCode
        - loanCategory
      properties:
        customerId:
          type: string
          pattern: '^[A-Za-z0-9]+$'
          description: Unique identifier for the customer (alphanumeric only)
          example: "CUST12345"
        applicationId:
          type: string
          description: Unique identifier for the loan application
          example: "APP98765"
        productCode:
          type: string
          description: Product code for the loan type
          example: "FOUR_WHEELER_ETB_PA"
        loanCategory:
          type: string
          enum: ["ETB", "NTB"]
          description: Loan category - ETB (Existing to Bank) or NTB (New to Bank)
          example: "ETB"

    CustomerDemographicResponse:
      type: object
      required:
        - status
      properties:
        name:
          type: string
          description: Full name of the customer
          example: "John Doe"
        dob:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2}$'
          description: Date of birth in YYYY-MM-DD format
          example: "1985-06-25"
        gender:
          type: string
          enum: ["Male", "Female", "Other"]
          description: Gender of the customer
          example: "Male"
        email:
          type: string
          format: email
          description: Email address of the customer
          example: "<EMAIL>"
        mobileNumber:
          type: string
          pattern: '^\d{10}$'
          description: 10-digit mobile number
          example: "**********"
        addressLine1:
          type: string
          description: Primary address line
          example: "123 Elm Street"
        addressLine2:
          type: string
          description: Secondary address line (optional)
          example: "Block A"
        city:
          type: string
          description: City name
          example: "Mumbai"
        state:
          type: string
          description: State name
          example: "Maharashtra"
        pincode:
          type: string
          pattern: '^\d{6}$'
          description: 6-digit postal code
          example: "400001"
        status:
          type: string
          enum: ["SUCCESS", "FAILED"]
          description: Response status indicator
          example: "SUCCESS"
        error:
          $ref: '#/components/schemas/ErrorDetails'

    ErrorDetails:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: string
          description: Error code for categorizing the error
          example: "CUST404"
        message:
          type: string
          description: Human-readable error message
          example: "Customer details not found"

    HealthResponse:
      type: object
      required:
        - status
        - service
        - timestamp
      properties:
        status:
          type: string
          enum: ["UP", "DOWN"]
          description: Overall health status
          example: "UP"
        service:
          type: string
          description: Service name
          example: "customer-demographic-fetch-mock"
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the health check
          example: "2023-12-07T10:30:00Z"
        checks:
          type: object
          description: Individual component health checks
          additionalProperties:
            type: string
          example:
            mock-data: "UP"

    LivenessResponse:
      type: object
      required:
        - status
        - service
        - timestamp
        - uptime
      properties:
        status:
          type: string
          enum: ["UP", "DOWN"]
          description: Liveness status
          example: "UP"
        service:
          type: string
          description: Service name
          example: "customer-demographic-fetch-mock"
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the liveness check
          example: "2023-12-07T10:30:00Z"
        uptime:
          type: integer
          format: int64
          description: Service uptime in milliseconds
          example: 3600000

    MockConfig:
      type: object
      properties:
        failureRate:
          type: number
          format: double
          description: Current failure rate (0.0 to 1.0)
          example: 0.1
        responseDelay:
          type: integer
          format: int64
          description: Current response delay in milliseconds
          example: 100
        predefinedResponsesCount:
          type: integer
          description: Number of predefined responses configured
          example: 3
        defaultFailureRate:
          type: number
          format: double
          description: Default failure rate
          example: 0.1
        defaultResponseDelay:
          type: integer
          format: int64
          description: Default response delay in milliseconds
          example: 100

    ServiceStats:
      type: object
      properties:
        totalRequests:
          type: integer
          description: Total number of requests processed
          example: 1000
        successfulRequests:
          type: integer
          description: Number of successful requests
          example: 900
        failedRequests:
          type: integer
          description: Number of failed requests
          example: 100
        successRate:
          type: number
          format: double
          description: Success rate (0.0 to 1.0)
          example: 0.9
        averageProcessingTime:
          type: number
          format: double
          description: Average processing time in milliseconds
          example: 150.5
        startTime:
          type: string
          format: date-time
          description: Service start time
          example: "2023-12-07T09:00:00Z"
        uptime:
          type: string
          description: Service uptime duration
          example: "PT1H30M"

tags:
  - name: Mock Demographics
    description: Mock demographic information operations
  - name: Admin
    description: Administrative operations for mock configuration
  - name: Health
    description: Health check and monitoring endpoints
