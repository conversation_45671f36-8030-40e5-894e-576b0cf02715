package com.lending.capability.customerdemographicfetch.mock.dto;

public class CustomerDemographicResponse {
    private String name, dob, gender, email, mobileNumber, addressLine1, addressLine2, city, state, pincode, status;
    private ErrorDetails error;

    public CustomerDemographicResponse() {}
    public CustomerDemographicResponse(String name, String dob, String gender, String email, String mobileNumber, String addressLine1, String addressLine2, String city, String state, String pincode) {
        this.name = name; this.dob = dob; this.gender = gender; this.email = email; this.mobileNumber = mobileNumber;
        this.addressLine1 = addressLine1; this.addressLine2 = addressLine2; this.city = city; this.state = state; this.pincode = pincode; this.status = "SUCCESS";
    }

    public String getName() { return name; } public void setName(String name) { this.name = name; }
    public String getDob() { return dob; } public void setDob(String dob) { this.dob = dob; }
    public String getGender() { return gender; } public void setGender(String gender) { this.gender = gender; }
    public String getEmail() { return email; } public void setEmail(String email) { this.email = email; }
    public String getMobileNumber() { return mobileNumber; } public void setMobileNumber(String mobileNumber) { this.mobileNumber = mobileNumber; }
    public String getAddressLine1() { return addressLine1; } public void setAddressLine1(String addressLine1) { this.addressLine1 = addressLine1; }
    public String getAddressLine2() { return addressLine2; } public void setAddressLine2(String addressLine2) { this.addressLine2 = addressLine2; }
    public String getCity() { return city; } public void setCity(String city) { this.city = city; }
    public String getState() { return state; } public void setState(String state) { this.state = state; }
    public String getPincode() { return pincode; } public void setPincode(String pincode) { this.pincode = pincode; }
    public String getStatus() { return status; } public void setStatus(String status) { this.status = status; }
    public ErrorDetails getError() { return error; } public void setError(ErrorDetails error) { this.error = error; }
}
