package com.lending.capability.customerdemographicfetch.mock.service;

import com.lending.capability.customerdemographicfetch.mock.dto.CustomerDemographicResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Service for managing mock configuration and scenarios.
 * Provides dynamic control over mock service behavior.
 */
@Service
public class MockConfigService {

    private static final Logger logger = LoggerFactory.getLogger(MockConfigService.class);

    // Configuration values
    private volatile double failureRate;
    private volatile long responseDelay;
    private final double defaultFailureRate;
    private final long defaultResponseDelay;

    // Predefined responses
    private final Map<String, CustomerDemographicResponse> predefinedResponses = new ConcurrentHashMap<>();

    // Statistics
    private final AtomicInteger totalRequests = new AtomicInteger(0);
    private final AtomicInteger successfulRequests = new AtomicInteger(0);
    private final AtomicInteger failedRequests = new AtomicInteger(0);
    private final AtomicLong totalProcessingTime = new AtomicLong(0);
    private final LocalDateTime startTime = LocalDateTime.now();

    public MockConfigService(@Value("${mock.failure-rate:0.1}") double defaultFailureRate,
                           @Value("${mock.response-delay:100}") long defaultResponseDelay) {
        this.defaultFailureRate = defaultFailureRate;
        this.defaultResponseDelay = defaultResponseDelay;
        this.failureRate = defaultFailureRate;
        this.responseDelay = defaultResponseDelay;
        
        logger.info("MockConfigService initialized with failure rate: {} and delay: {} ms", 
                   failureRate, responseDelay);
    }

    /**
     * Get current configuration.
     * 
     * @return current configuration map
     */
    public Map<String, Object> getCurrentConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("failureRate", failureRate);
        config.put("responseDelay", responseDelay);
        config.put("predefinedResponsesCount", predefinedResponses.size());
        config.put("defaultFailureRate", defaultFailureRate);
        config.put("defaultResponseDelay", defaultResponseDelay);
        return config;
    }

    /**
     * Update failure rate.
     * 
     * @param newFailureRate the new failure rate
     */
    public void updateFailureRate(double newFailureRate) {
        double oldRate = this.failureRate;
        this.failureRate = newFailureRate;
        logger.info("Failure rate updated from {} to {}", oldRate, newFailureRate);
    }

    /**
     * Update response delay.
     * 
     * @param newDelay the new delay in milliseconds
     */
    public void updateResponseDelay(long newDelay) {
        long oldDelay = this.responseDelay;
        this.responseDelay = newDelay;
        logger.info("Response delay updated from {} ms to {} ms", oldDelay, newDelay);
    }

    /**
     * Set predefined response for a customer.
     * 
     * @param customerId the customer ID
     * @param response the predefined response
     */
    public void setPredefinedResponse(String customerId, CustomerDemographicResponse response) {
        predefinedResponses.put(customerId, response);
        logger.info("Predefined response set for customer: {}", customerId);
    }

    /**
     * Remove predefined response for a customer.
     * 
     * @param customerId the customer ID
     */
    public void removePredefinedResponse(String customerId) {
        CustomerDemographicResponse removed = predefinedResponses.remove(customerId);
        if (removed != null) {
            logger.info("Predefined response removed for customer: {}", customerId);
        } else {
            logger.warn("No predefined response found for customer: {}", customerId);
        }
    }

    /**
     * Get predefined response for a customer.
     * 
     * @param customerId the customer ID
     * @return predefined response or null if not found
     */
    public CustomerDemographicResponse getPredefinedResponse(String customerId) {
        return predefinedResponses.get(customerId);
    }

    /**
     * Get all predefined responses.
     * 
     * @return map of customer ID to response
     */
    public Map<String, CustomerDemographicResponse> getAllPredefinedResponses() {
        return new HashMap<>(predefinedResponses);
    }

    /**
     * Reset configuration to default values.
     */
    public void resetToDefaults() {
        this.failureRate = defaultFailureRate;
        this.responseDelay = defaultResponseDelay;
        predefinedResponses.clear();
        
        // Reset statistics
        totalRequests.set(0);
        successfulRequests.set(0);
        failedRequests.set(0);
        totalProcessingTime.set(0);
        
        logger.info("Configuration reset to defaults - failure rate: {}, delay: {} ms", 
                   failureRate, responseDelay);
    }

    /**
     * Get current failure rate.
     * 
     * @return current failure rate
     */
    public double getFailureRate() {
        return failureRate;
    }

    /**
     * Get current response delay.
     * 
     * @return current response delay in milliseconds
     */
    public long getResponseDelay() {
        return responseDelay;
    }

    /**
     * Record request statistics.
     * 
     * @param success whether the request was successful
     * @param processingTime processing time in milliseconds
     */
    public void recordRequest(boolean success, long processingTime) {
        totalRequests.incrementAndGet();
        totalProcessingTime.addAndGet(processingTime);
        
        if (success) {
            successfulRequests.incrementAndGet();
        } else {
            failedRequests.incrementAndGet();
        }
    }

    /**
     * Get service statistics.
     * 
     * @return service statistics map
     */
    public Map<String, Object> getServiceStats() {
        Map<String, Object> stats = new HashMap<>();
        int total = totalRequests.get();
        
        stats.put("totalRequests", total);
        stats.put("successfulRequests", successfulRequests.get());
        stats.put("failedRequests", failedRequests.get());
        stats.put("successRate", total > 0 ? (double) successfulRequests.get() / total : 0.0);
        stats.put("averageProcessingTime", total > 0 ? (double) totalProcessingTime.get() / total : 0.0);
        stats.put("startTime", startTime);
        stats.put("uptime", java.time.Duration.between(startTime, LocalDateTime.now()).toString());
        
        return stats;
    }
}
