package com.lending.capability.customerdemographicfetch.mock.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * OpenAPI configuration for Customer Demographic Fetch mock service.
 * Configures Swagger UI and handles CORS for API documentation.
 */
@Configuration
public class OpenApiConfig implements WebMvcConfigurer {

    @Value("${server.port:8082}")
    private String serverPort;

    @Value("${server.servlet.context-path:/api/customer-demographic-fetch/internal}")
    private String contextPath;

    /**
     * Configure OpenAPI documentation.
     * 
     * @return OpenAPI configuration
     */
    @Bean
    public OpenAPI customerDemographicFetchMockOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Customer Demographic Fetch - Mock Service")
                        .description("""
                                Mock service for simulating external demographic data sources for testing and development.
                                Provides configurable responses, failure simulation, and admin controls for testing scenarios.
                                """)
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Lending Platform Team")
                                .email("<EMAIL>")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + contextPath)
                                .description("Local development mock server"),
                        new Server()
                                .url("https://mock.company.com" + contextPath)
                                .description("Mock server environment")
                ));
    }

    /**
     * Configure CORS for Swagger UI and API endpoints.
     * This ensures Swagger UI can make requests to the API without CORS issues.
     * 
     * @param registry CORS registry
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
        
        // Specific CORS configuration for Swagger UI
        registry.addMapping("/swagger-ui/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD")
                .allowedHeaders("*")
                .allowCredentials(true);
                
        registry.addMapping("/v3/api-docs/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true);
    }
}
