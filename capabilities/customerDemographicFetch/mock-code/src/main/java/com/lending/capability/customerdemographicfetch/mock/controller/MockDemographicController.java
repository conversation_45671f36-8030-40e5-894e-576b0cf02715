package com.lending.capability.customerdemographicfetch.mock.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.lending.capability.customerdemographicfetch.mock.dto.CustomerDemographicRequest;
import com.lending.capability.customerdemographicfetch.mock.dto.CustomerDemographicResponse;
import com.lending.capability.customerdemographicfetch.mock.dto.ErrorDetails;
import com.lending.capability.customerdemographicfetch.mock.enums.ResponseStatus;
import com.lending.capability.customerdemographicfetch.mock.service.MockDemographicService;

import jakarta.validation.Valid;

/**
 * Mock REST Controller for Customer Demographic service.
 * Simulates external demographic data sources for testing and development.
 */
@RestController
@RequestMapping("/api/customer/internal/v1")
public class MockDemographicController {

    private static final Logger logger = LoggerFactory.getLogger(MockDemographicController.class);

    private final MockDemographicService mockDemographicService;

    @Autowired
    public MockDemographicController(MockDemographicService mockDemographicService) {
        this.mockDemographicService = mockDemographicService;
    }

    /**
     * Mock endpoint to simulate external demographic service.
     * 
     * @param request the demographic fetch request
     * @param bindingResult validation results
     * @return ResponseEntity with mock demographic data or error
     */
    @PostMapping("/fetch")
    public ResponseEntity<CustomerDemographicResponse> fetchDemographics(
            @Valid @RequestBody CustomerDemographicRequest request,
            BindingResult bindingResult,
            @RequestHeader(value = "X-Transaction-Id", required = false) String transactionId) {

        // Generate transaction ID if not provided
        if (transactionId == null || transactionId.trim().isEmpty()) {
            transactionId = java.util.UUID.randomUUID().toString();
        }

        logger.info("Mock service received demographic fetch request - TransactionId: {}, Customer: {}",
                   transactionId, request.getCustomerId());

        // Handle validation errors
        if (bindingResult.hasErrors()) {
            logger.error("Validation errors in mock request: {}", bindingResult.getAllErrors());
            CustomerDemographicResponse errorResponse = createValidationErrorResponse();
            return ResponseEntity.badRequest().body(errorResponse);
        }

        try {
            // Process the request with mock service
            CustomerDemographicResponse response = mockDemographicService.fetchMockDemographics(request, transactionId);

            // Determine HTTP status based on response
            if (ResponseStatus.SUCCESS.getValue().equals(response.getStatus())) {
                logger.info("Mock service successfully processed request - TransactionId: {}, Customer: {}",
                           transactionId, request.getCustomerId());
                return ResponseEntity.ok()
                        .header("X-Transaction-Id", transactionId)
                        .body(response);
            } else {
                logger.warn("Mock service failed to process request - TransactionId: {}, Customer: {}, Error: {}",
                           transactionId, request.getCustomerId(), response.getError());

                // Return appropriate HTTP status based on error code
                HttpStatus httpStatus = determineHttpStatus(response.getError());
                return ResponseEntity.status(httpStatus)
                        .header("X-Transaction-Id", transactionId)
                        .body(response);
            }

        } catch (Exception e) {
            logger.error("Unexpected error in mock service for customer: {}", 
                        request.getCustomerId(), e);
            
            CustomerDemographicResponse errorResponse = createInternalErrorResponse();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }



    /**
     * Create validation error response.
     * 
     * @return CustomerDemographicResponse with validation error
     */
    private CustomerDemographicResponse createValidationErrorResponse() {
        CustomerDemographicResponse response = new CustomerDemographicResponse();
        response.setStatus(ResponseStatus.FAILED.getValue());
        response.setError(new ErrorDetails("VALIDATION_ERROR", "Invalid request parameters"));
        return response;
    }

    /**
     * Create internal error response.
     * 
     * @return CustomerDemographicResponse with internal error
     */
    private CustomerDemographicResponse createInternalErrorResponse() {
        CustomerDemographicResponse response = new CustomerDemographicResponse();
        response.setStatus(ResponseStatus.FAILED.getValue());
        response.setError(new ErrorDetails("INTERNAL_ERROR", "Mock service internal error"));
        return response;
    }

    /**
     * Determine HTTP status based on error details.
     * 
     * @param error the error details
     * @return appropriate HTTP status
     */
    private HttpStatus determineHttpStatus(ErrorDetails error) {
        if (error == null || error.getCode() == null) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }

        return switch (error.getCode()) {
            case "CUST404" -> HttpStatus.NOT_FOUND;
            case "VALIDATION_ERROR" -> HttpStatus.BAD_REQUEST;
            default -> HttpStatus.INTERNAL_SERVER_ERROR;
        };
    }
}
