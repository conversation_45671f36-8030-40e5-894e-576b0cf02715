package com.lending.capability.customerdemographicfetch.mock.controller;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.lending.capability.customerdemographicfetch.mock.dto.CustomerDemographicResponse;
import com.lending.capability.customerdemographicfetch.mock.service.MockConfigService;

/**
 * Admin controller for managing mock service scenarios and configurations.
 * Allows dynamic configuration of mock responses and behavior.
 */
@RestController
@RequestMapping("/admin")
public class MockAdminController {

    private static final Logger logger = LoggerFactory.getLogger(MockAdminController.class);

    private final MockConfigService mockConfigService;

    @Autowired
    public MockAdminController(MockConfigService mockConfigService) {
        this.mockConfigService = mockConfigService;
    }

    /**
     * Get current mock configuration.
     * 
     * @return current configuration
     */
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> getConfig() {
        logger.info("Admin request to get mock configuration");
        Map<String, Object> config = mockConfigService.getCurrentConfig();
        return ResponseEntity.ok(config);
    }

    /**
     * Update failure rate for random failures.
     * 
     * @param failureRate the new failure rate (0.0 to 1.0)
     * @return success response
     */
    @PostMapping("/config/failure-rate")
    public ResponseEntity<String> updateFailureRate(@RequestParam double failureRate) {
        logger.info("Admin request to update failure rate to: {}", failureRate);
        
        if (failureRate < 0.0 || failureRate > 1.0) {
            return ResponseEntity.badRequest().body("Failure rate must be between 0.0 and 1.0");
        }
        
        mockConfigService.updateFailureRate(failureRate);
        return ResponseEntity.ok("Failure rate updated to: " + failureRate);
    }

    /**
     * Update response delay.
     * 
     * @param delayMs the new delay in milliseconds
     * @return success response
     */
    @PostMapping("/config/delay")
    public ResponseEntity<String> updateDelay(@RequestParam long delayMs) {
        logger.info("Admin request to update response delay to: {} ms", delayMs);
        
        if (delayMs < 0) {
            return ResponseEntity.badRequest().body("Delay must be non-negative");
        }
        
        mockConfigService.updateResponseDelay(delayMs);
        return ResponseEntity.ok("Response delay updated to: " + delayMs + " ms");
    }

    /**
     * Add or update predefined response for a customer.
     * 
     * @param customerId the customer ID
     * @param response the predefined response
     * @return success response
     */
    @PostMapping("/customers/{customerId}/response")
    public ResponseEntity<String> setPredefinedResponse(
            @PathVariable String customerId,
            @RequestBody CustomerDemographicResponse response) {
        
        logger.info("Admin request to set predefined response for customer: {}", customerId);
        mockConfigService.setPredefinedResponse(customerId, response);
        return ResponseEntity.ok("Predefined response set for customer: " + customerId);
    }

    /**
     * Remove predefined response for a customer.
     * 
     * @param customerId the customer ID
     * @return success response
     */
    @DeleteMapping("/customers/{customerId}/response")
    public ResponseEntity<String> removePredefinedResponse(@PathVariable String customerId) {
        logger.info("Admin request to remove predefined response for customer: {}", customerId);
        mockConfigService.removePredefinedResponse(customerId);
        return ResponseEntity.ok("Predefined response removed for customer: " + customerId);
    }

    /**
     * Get all predefined customer responses.
     * 
     * @return map of customer ID to response
     */
    @GetMapping("/customers/responses")
    public ResponseEntity<Map<String, CustomerDemographicResponse>> getAllPredefinedResponses() {
        logger.info("Admin request to get all predefined responses");
        Map<String, CustomerDemographicResponse> responses = mockConfigService.getAllPredefinedResponses();
        return ResponseEntity.ok(responses);
    }

    /**
     * Reset all configurations to default values.
     * 
     * @return success response
     */
    @PostMapping("/config/reset")
    public ResponseEntity<String> resetConfig() {
        logger.info("Admin request to reset configuration to defaults");
        mockConfigService.resetToDefaults();
        return ResponseEntity.ok("Configuration reset to defaults");
    }

    /**
     * Get service statistics.
     * 
     * @return service statistics
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getStats() {
        logger.info("Admin request to get service statistics");
        Map<String, Object> stats = mockConfigService.getServiceStats();
        return ResponseEntity.ok(stats);
    }


}
