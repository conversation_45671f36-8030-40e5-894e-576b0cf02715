package com.lending.capability.customerdemographicfetch.mock.service;

import com.lending.capability.customerdemographicfetch.mock.dto.CustomerDemographicRequest;
import com.lending.capability.customerdemographicfetch.mock.dto.CustomerDemographicResponse;
import com.lending.capability.customerdemographicfetch.mock.dto.ErrorDetails;
import com.lending.capability.customerdemographicfetch.mock.enums.ResponseStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * Mock service for simulating external demographic data sources.
 * Provides predefined responses for testing and development.
 */
@Service
public class MockDemographicService {

    private static final Logger logger = LoggerFactory.getLogger(MockDemographicService.class);

    private final Random random = new Random();
    private final Map<String, CustomerDemographicResponse> mockData;
    private final MockConfigService mockConfigService;

    @Autowired
    public MockDemographicService(MockConfigService mockConfigService) {
        this.mockConfigService = mockConfigService;
        this.mockData = initializeMockData();
    }

    /**
     * Fetch mock customer demographics.
     *
     * @param request the demographic fetch request
     * @param transactionId the transaction ID for tracing
     * @return CustomerDemographicResponse with mock data or error
     */
    public CustomerDemographicResponse fetchMockDemographics(CustomerDemographicRequest request, String transactionId) {
        long startTime = System.currentTimeMillis();
        boolean success = false;

        try {
            logger.info("Processing mock demographic request - TransactionId: {}, Customer: {}",
                       transactionId, request.getCustomerId());

            // Simulate response delay
            simulateDelay();

            // Simulate random failures
            if (random.nextDouble() < mockConfigService.getFailureRate()) {
                logger.warn("Simulating random failure - TransactionId: {}, Customer: {}",
                           transactionId, request.getCustomerId());
                return createErrorResponse("RANDOM_FAILURE", "Simulated service failure");
            }

            // Check for predefined mock data from config service
            CustomerDemographicResponse predefinedResponse = mockConfigService.getPredefinedResponse(request.getCustomerId());
            if (predefinedResponse != null) {
                logger.info("Returning predefined mock data - TransactionId: {}, Customer: {}",
                           transactionId, request.getCustomerId());
                success = true;
                return predefinedResponse;
            }

            // Check for static mock data
            CustomerDemographicResponse mockResponse = mockData.get(request.getCustomerId());
            if (mockResponse != null) {
                logger.info("Returning static mock data - TransactionId: {}, Customer: {}",
                           transactionId, request.getCustomerId());
                success = true;
                return mockResponse;
            }

            // Generate dynamic mock data based on customer ID
            CustomerDemographicResponse response = generateDynamicMockData(request, transactionId);
            success = true;
            return response;

        } finally {
            long processingTime = System.currentTimeMillis() - startTime;
            mockConfigService.recordRequest(success, processingTime);
        }
    }

    /**
     * Initialize predefined mock data for testing.
     * 
     * @return Map of customer ID to mock response
     */
    private Map<String, CustomerDemographicResponse> initializeMockData() {
        Map<String, CustomerDemographicResponse> data = new HashMap<>();

        // Success case
        data.put("CUST12345", new CustomerDemographicResponse(
                "John Doe",
                "1985-06-25",
                "Male",
                "<EMAIL>",
                "9876543210",
                "123 Elm Street",
                "Block A",
                "Mumbai",
                "Maharashtra",
                "400001"
        ));

        // Another success case
        data.put("CUST67890", new CustomerDemographicResponse(
                "Jane Smith",
                "1990-03-15",
                "Female",
                "<EMAIL>",
                "9123456789",
                "456 Oak Avenue",
                "Apartment 2B",
                "Delhi",
                "Delhi",
                "110001"
        ));

        // Customer not found case
        CustomerDemographicResponse notFoundResponse = new CustomerDemographicResponse();
        notFoundResponse.setStatus(ResponseStatus.FAILED.getValue());
        notFoundResponse.setError(new ErrorDetails("CUST404", "Customer details not found"));
        data.put("CUSTNOTFOUND", notFoundResponse);

        return data;
    }

    /**
     * Generate dynamic mock data for unknown customer IDs.
     *
     * @param request the demographic fetch request
     * @param transactionId the transaction ID for tracing
     * @return CustomerDemographicResponse with generated data
     */
    private CustomerDemographicResponse generateDynamicMockData(CustomerDemographicRequest request, String transactionId) {
        logger.info("Generating dynamic mock data - TransactionId: {}, Customer: {}",
                   transactionId, request.getCustomerId());

        // Generate data based on customer ID hash for consistency
        int hash = Math.abs(request.getCustomerId().hashCode());
        
        String[] firstNames = {"Amit", "Priya", "Rajesh", "Sunita", "Vikram", "Kavya", "Arjun", "Meera"};
        String[] lastNames = {"Sharma", "Patel", "Kumar", "Singh", "Gupta", "Reddy", "Nair", "Joshi"};
        String[] cities = {"Mumbai", "Delhi", "Bangalore", "Chennai", "Kolkata", "Hyderabad", "Pune", "Ahmedabad"};
        String[] states = {"Maharashtra", "Delhi", "Karnataka", "Tamil Nadu", "West Bengal", "Telangana", "Maharashtra", "Gujarat"};
        String[] genders = {"Male", "Female"};

        String firstName = firstNames[hash % firstNames.length];
        String lastName = lastNames[(hash / 10) % lastNames.length];
        String city = cities[(hash / 100) % cities.length];
        String state = states[(hash / 100) % states.length];
        String gender = genders[hash % genders.length];

        // Generate other fields
        String email = firstName.toLowerCase() + "." + lastName.toLowerCase() + "@example.com";
        String mobile = "9" + String.format("%09d", hash % 1000000000);
        String dob = String.format("19%02d-%02d-%02d", 
                                  70 + (hash % 30), 
                                  1 + (hash % 12), 
                                  1 + (hash % 28));
        String pincode = String.format("%06d", 100000 + (hash % 900000));

        return new CustomerDemographicResponse(
                firstName + " " + lastName,
                dob,
                gender,
                email,
                mobile,
                (hash % 999 + 1) + " Mock Street",
                "Block " + (char)('A' + (hash % 26)),
                city,
                state,
                pincode
        );
    }

    /**
     * Create error response.
     * 
     * @param errorCode the error code
     * @param errorMessage the error message
     * @return CustomerDemographicResponse with error
     */
    private CustomerDemographicResponse createErrorResponse(String errorCode, String errorMessage) {
        CustomerDemographicResponse response = new CustomerDemographicResponse();
        response.setStatus(ResponseStatus.FAILED.getValue());
        response.setError(new ErrorDetails(errorCode, errorMessage));
        return response;
    }

    /**
     * Simulate response delay.
     */
    private void simulateDelay() {
        long delay = mockConfigService.getResponseDelay();
        if (delay > 0) {
            try {
                Thread.sleep(delay);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.warn("Mock service delay interrupted");
            }
        }
    }
}
