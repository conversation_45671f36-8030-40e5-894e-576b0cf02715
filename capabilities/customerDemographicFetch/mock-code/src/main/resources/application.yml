server:
  port: ${SERVER_PORT:8082}
  servlet:
    context-path: /api/customer-demographic-fetch/internal

spring:
  application:
    name: customer-demographic-fetch-mock
    
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always

# SpringDoc OpenAPI configuration
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    try-it-out-enabled: true
    operations-sorter: method
    tags-sorter: alpha
    doc-expansion: none
    disable-swagger-default-url: true
  show-actuator: true

# CORS configuration
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:*}
  allowed-methods: ${CORS_ALLOWED_METHODS:GET,POST,PUT,DELETE,OPTIONS}
  allowed-headers: ${CORS_ALLOWED_HEADERS:*}

# Mock service configuration
mock:
  response-delay: ${MOCK_RESPONSE_DELAY:100}
  failure-rate: ${MOCK_FAILURE_RATE:0.1}

# Logging configuration
logging:
  level:
    com.lending.capability.customerdemographicfetch.mock: ${LOG_LEVEL:INFO}
    org.springframework.web: ${WEB_LOG_LEVEL:WARN}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
