# Customer Demographic Fetch – Mock API Service Specification

## Overview

Create a standalone Spring Boot application that simulates the behavior of the Customer Demographic Fetch API for development and testing purposes. This service enables the digital onboarding platform to test integrations without relying on external or internal live demographic systems.

## Purpose

* Simulate end-to-end demographic fetch flow based on `customerId` or `applicationId`
* Enable downstream system testing with mock responses
* Mirror real API behavior and formats (success, failure, missing fields)
* Support dynamic configuration of scenarios and simulate real-world failures or delays

## Technical Requirements

* **Spring Boot version**: 3.2.3 (or compatible)
* **Java version**: 21
* **Server Port**: 8082
* RESTful API endpoints matching production service structure
* OpenAPI/Swagger documentation included
* In-memory storage for requests and scenario configs
* Logging with PII masking (email, mobile, address, etc.)
* Traceability using headers like `X-Transaction-Id`

---

## Implementation Details

* DTOs for request/response
* Enums for status and gender fields
* Controllers to expose endpoints
* Service layer for simulating business logic
* Utility classes for logging, masking, trace ID
* `application.yml` with port and mock configs
* Swagger config for testing APIs
* README with setup instructions, usage, and scenarios

---

## API Endpoints

### 1. Customer Demographic Fetch Endpoint

* **Path**: `/api/customer-demographic-fetch/internal/api/customer/internal/v1/fetch`
* **Method**: `POST`
* **Request Body**:

```json
{
  "customerId": "CUST12345",
  "applicationId": "APP98765",
  "productCode": "FOUR_WHEELER_ETB_PA",
  "loanCategory": "ETB"
}
```

* **Success Response**:

```json
{
  "name": "John Doe",
  "dob": "1985-06-25",
  "gender": "Male",
  "email": "<EMAIL>",
  "mobileNumber": "9876543210",
  "addressLine1": "123 Elm Street",
  "addressLine2": "Block A",
  "city": "Mumbai",
  "state": "Maharashtra",
  "pincode": "400001",
  "status": "SUCCESS"
}
```

* **Failure Response**:

```json
{
  "status": "FAILED",
  "error": {
    "code": "CUST404",
    "message": "Customer details not found"
  }
}
```

---

## Security

* Mask sensitive fields (email, mobileNumber, addressLine1/2) in logs
* Support custom header `X-Transaction-Id` for traceability

---

## Service Layer Components

* `CustomerDemographicMockService`: Handles demographic fetch logic and scenario simulation
* `DemographicStorageService`: Manages in-memory demographic data
* `MockConfigService`: Controls dynamic behavior (success/failure/timeout/missing fields)
* `LoggingService`: Logs with masking and transaction trace

---

## Features to Implement

### Scenario Simulation

* Simulate:

  * Full success
  * Failure (not found, invalid format)
  * Partial/missing fields
  * Timeout or 500 error
* Configurable via admin API or scenario file
* Add response delays for realism

### Logging and Traceability

* All requests logged with masking
* Transaction tracking via `X-Transaction-Id`
* Status and outcome auditing

### In-Memory Persistence

* Store requests and mock responses for fetch-by-customerId
* Allow admin to predefine specific customer responses

---

## Testing Strategy

* Use Swagger UI or Postman to:

  * Trigger fetch with sample request
  * View configured responses and test fallback logic
  * Simulate failures and missing data
  * Validate log masking and header propagation

---

## README Deliverables

* How to start and run the mock server
* Sample requests/responses
* Admin usage instructions
* Security config info (if any)
* Swagger URL for UI testing

---

## Extensibility Suggestions

* Add endpoint to support batch fetch of demographics
* Store response logs to file system or DB
* Schedule automatic scenario changes (e.g., simulate unavailability window)
* Link mock with other loan processing mock services for end-to-end simulation

---
