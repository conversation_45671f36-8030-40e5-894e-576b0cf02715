# Multi-stage build for PAN NSDL Mock Service
FROM maven:3.9.6-eclipse-temurin-21-alpine AS builder

WORKDIR /app

# Copy pom.xml first for better layer caching
COPY pom.xml .

# Download dependencies
RUN mvn dependency:go-offline -B

# Copy source code
COPY src ./src

# Build the application
RUN mvn clean package -DskipTests -B


# Runtime stage with OpenTelemetry agent
FROM eclipse-temurin:21-jre-alpine

# Install required packages
RUN apk add --no-cache \
    curl \
    bash \
    tzdata

# Set timezone
ENV TZ=Asia/Kolkata
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create application user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Create necessary directories
RUN mkdir -p /app/logs /otel && \
    chown -R appuser:appgroup /app /otel

# Download OpenTelemetry Java agent
ENV OTEL_AGENT_VERSION=2.17.1
RUN curl -sSL \
    https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/download/v${OTEL_AGENT_VERSION}/opentelemetry-javaagent.jar \
    -o /otel/opentelemetry-javaagent.jar

# Set working directory
WORKDIR /app

# Copy the built JAR from builder stage
COPY --from=builder /app/target/*.jar app.jar

# Change ownership of the JAR file
RUN chown appuser:appgroup app.jar

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8202

# Health check - using custom health endpoint
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8202/api/pannsdl/internal/v1/pannsdl/mock/health || exit 1

# Environment variables
ENV JAVA_OPTS="-Xms256m -Xmx512m -XX:+UseG1GC -XX:+UseContainerSupport" \
    OTEL_AGENT_PATH="/otel/opentelemetry-javaagent.jar" \
    SPRING_PROFILES_ACTIVE=docker \
    SERVER_PORT=8202

# Start the application with OTEL agent
ENTRYPOINT ["sh", "-c", "java -javaagent:$OTEL_AGENT_PATH $JAVA_OPTS -jar app.jar"]
