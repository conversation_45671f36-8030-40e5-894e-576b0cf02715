openapi: 3.0.3
info:
  title: PAN NSDL Mock Verification Service API
  version: 1.0.0
  description: |
    Mock REST API for PAN verification testing and development. This service simulates real PAN 
    verification behavior with configurable responses, delays, and error scenarios for comprehensive testing.
    
    ## Mock Behavior Logic
    - **PAN ends in even digit** → Returns status: "Active"
    - **PAN ends in odd digit** → Returns status: "Inactive"  
    - **PA<PERSON> starts with "ZZZ"** → Simulates HTTP 500 system error
    - **PAN ends with "9"** → Introduces artificial delay (5 seconds)
    
    ## Features
    - Configurable response behavior
    - Request tracing and logging
    - Admin endpoints for configuration
    - Realistic error simulation
    - Data masking for security
    
  contact:
    name: PAN Mock Service Team
    email: <EMAIL>

servers:
  - url: http://localhost:8202/api/pannsdl/internal
    description: Local mock server

paths:
  /v1/verify:
    post:
      tags:
        - PAN Verification
      summary: Mock PAN verification
      description: |
        Simulates PAN verification with configurable behavior based on input patterns.
        
        ## Simulation Rules
        - Even-ending PAN → Active status
        - Odd-ending PAN → Inactive status
        - "ZZZ" prefix → Server error
        - "9" suffix → Delayed response
        
      operationId: verifyPan
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PanVerificationRequest'
            examples:
              active_pan:
                summary: PAN that will return Active status (even ending)
                value:
                  pan: "**********"
                  name: "John Doe"
              inactive_pan:
                summary: PAN that will return Inactive status (odd ending)
                value:
                  pan: "**********"
                  name: "Jane Smith"
              error_pan:
                summary: PAN that will trigger server error
                value:
                  pan: "**********"
                  name: "Error Test"
              delay_pan:
                summary: PAN that will cause delay
                value:
                  pan: "ABCDE1239"
                  name: "Delay Test"
      responses:
        '200':
          description: Mock PAN verification successful
          headers:
            X-Trace-Id:
              description: Unique trace ID for request tracking
              schema:
                type: string
                example: "TRC-20250625-001"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PanVerificationResponse'
              examples:
                active_response:
                  summary: Active PAN response
                  value:
                    pan: "**********"
                    name: "John Doe"
                    status: "Active"
                    aadhaarLinked: true
                    transactionId: "TXN-20250625-001"
                    timestamp: "2025-06-25T10:30:00Z"
                inactive_response:
                  summary: Inactive PAN response
                  value:
                    pan: "**********"
                    name: "Jane Smith"
                    status: "Inactive"
                    aadhaarLinked: false
                    transactionId: "TXN-20250625-002"
                    timestamp: "2025-06-25T10:31:00Z"
        '400':
          description: Invalid request data
          headers:
            X-Trace-Id:
              description: Unique trace ID for request tracking
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                invalid_pan:
                  summary: Invalid PAN format
                  value:
                    errorCode: "PAN-ERR-001"
                    message: "PAN number must be in the format: **********"
                    transactionId: "TRC-20250625-003"
                    timestamp: "2025-06-25T10:32:00Z"
        '500':
          description: Simulated server error (for PANs starting with ZZZ)
          headers:
            X-Trace-Id:
              description: Unique trace ID for request tracking
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                simulated_error:
                  summary: Simulated system error
                  value:
                    errorCode: "PAN-ERR-500"
                    message: "Simulated system error for testing purposes"
                    transactionId: "TRC-20250625-004"
                    timestamp: "2025-06-25T10:33:00Z"

  /v1/pannsdl/mock/health:
    get:
      tags:
        - Health Check
      summary: Mock service health check
      description: Returns the health status of the mock PAN verification service
      operationId: healthCheck
      responses:
        '200':
          description: Mock service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
              example:
                status: "UP"
                service: "PAN NSDL Mock"
                timestamp: "2025-06-25T10:34:00Z"
                version: "1.0.0"

  /v1/admin/requests:
    get:
      tags:
        - Admin Operations
      summary: Get recorded requests
      description: Retrieves all recorded PAN verification requests for monitoring and debugging
      operationId: getRequests
      responses:
        '200':
          description: List of recorded requests
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RequestRecord'
              example:
                - pan: "**********"
                  name: "J***"
                  transactionId: "TXN-20250625-001"
                  timestamp: "2025-06-25T10:30:00Z"
                  status: "Active"
                  aadhaarLinked: true
                  isError: false
                  errorMessage: null

  /v1/admin/config:
    post:
      tags:
        - Admin Operations
      summary: Configure mock behavior
      description: Updates the mock service configuration for delays, errors, and status overrides
      operationId: updateConfig
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MockConfigurationRequest'
            examples:
              enable_delays:
                summary: Enable 3-second delays
                value:
                  delayInSeconds: 3
                  simulateErrors: false
                  statusOverride: null
                  aadhaarLinkedOverride: null
              force_inactive:
                summary: Force all responses to Inactive
                value:
                  delayInSeconds: null
                  simulateErrors: false
                  statusOverride: "Inactive"
                  aadhaarLinkedOverride: false
      responses:
        '200':
          description: Configuration updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Mock configuration updated successfully"
                  timestamp:
                    type: string
                    format: date-time
                    example: "2025-06-25T10:35:00Z"

  /v1/admin/reset:
    post:
      tags:
        - Admin Operations
      summary: Reset mock state
      description: Resets all mock configuration and clears request history
      operationId: resetMockState
      responses:
        '200':
          description: Mock state reset successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Mock state reset successfully"
                  timestamp:
                    type: string
                    format: date-time
                    example: "2025-06-25T10:36:00Z"

components:
  schemas:
    PanVerificationRequest:
      type: object
      required:
        - pan
        - name
      properties:
        pan:
          type: string
          pattern: '^[A-Z]{5}[0-9]{4}[A-Z]$'
          description: PAN number in the format **********
          example: "**********"
        name:
          type: string
          minLength: 1
          maxLength: 100
          description: Full name of the PAN holder
          example: "John Doe"
      description: Request object for mock PAN verification

    PanVerificationResponse:
      type: object
      properties:
        pan:
          type: string
          description: Original PAN number (returned as-is in mock)
          example: "**********"
        name:
          type: string
          description: Name of the PAN holder
          example: "John Doe"
        status:
          type: string
          enum: ["Active", "Inactive", "Deactivated", "Cancelled"]
          description: Simulated PAN status based on mock logic
          example: "Active"
        aadhaarLinked:
          type: boolean
          description: Simulated Aadhaar linkage status
          example: true
        transactionId:
          type: string
          description: Mock transaction ID for tracking
          example: "TXN-20250625-001"
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the mock response
          example: "2025-06-25T10:30:00Z"
      description: Mock response object for PAN verification

    ErrorResponse:
      type: object
      properties:
        errorCode:
          type: string
          description: Unique error code for the specific error type
          example: "PAN-ERR-001"
        message:
          type: string
          description: Human-readable error message
          example: "PAN number must be in the format: **********"
        transactionId:
          type: string
          description: Unique transaction ID for error tracking
          example: "TRC-20250625-003"
        timestamp:
          type: string
          format: date-time
          description: Timestamp when the error occurred
          example: "2025-06-25T10:32:00Z"
      description: Standard error response object for mock service

    HealthResponse:
      type: object
      properties:
        status:
          type: string
          enum: ["UP", "DOWN"]
          description: Overall health status of the mock service
          example: "UP"
        service:
          type: string
          description: Name of the mock service
          example: "PAN NSDL Mock"
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the health check
          example: "2025-06-25T10:34:00Z"
        version:
          type: string
          description: Version of the mock service
          example: "1.0.0"
      description: Health check response object for mock service

    RequestRecord:
      type: object
      properties:
        pan:
          type: string
          description: Masked PAN number for security
          example: "**********"
        name:
          type: string
          description: Masked name for security
          example: "J***"
        transactionId:
          type: string
          description: Transaction ID for the request
          example: "TXN-20250625-001"
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the request
          example: "2025-06-25T10:30:00Z"
        status:
          type: string
          description: Status returned for the request
          example: "Active"
        aadhaarLinked:
          type: boolean
          description: Aadhaar linkage status returned
          example: true
        isError:
          type: boolean
          description: Whether the request resulted in an error
          example: false
        errorMessage:
          type: string
          nullable: true
          description: Error message if the request failed
          example: null
      description: Record of a PAN verification request for audit purposes

    MockConfigurationRequest:
      type: object
      properties:
        delayInSeconds:
          type: integer
          nullable: true
          minimum: 0
          maximum: 30
          description: Artificial delay to introduce in responses (seconds)
          example: 3
        simulateErrors:
          type: boolean
          nullable: true
          description: Whether to simulate random errors
          example: false
        statusOverride:
          type: string
          nullable: true
          enum: ["Active", "Inactive", "Deactivated", "Cancelled"]
          description: Override status for all responses
          example: "Inactive"
        aadhaarLinkedOverride:
          type: boolean
          nullable: true
          description: Override Aadhaar linkage status for all responses
          example: false
      description: Configuration request for mock service behavior

tags:
  - name: PAN Verification
    description: Mock operations for PAN number verification testing
  - name: Health Check
    description: Mock service health monitoring endpoints
  - name: Admin Operations
    description: Administrative operations for mock service configuration and monitoring
