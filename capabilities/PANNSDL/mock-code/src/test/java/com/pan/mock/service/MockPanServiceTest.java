package com.pan.mock.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

import com.pan.mock.model.PanVerificationRequest;
import com.pan.mock.model.PanVerificationResponse;

@ExtendWith(MockitoExtension.class)
class MockPanServiceTest {

    @Mock
    private TraceService traceService;

    @Mock
    private MockConfigurationService configService;

    @InjectMocks
    private MockPanService mockPanService;

    @BeforeEach
    void setUp() {
        lenient().when(traceService.getCurrentTraceId()).thenReturn("TRC-12345678");
        lenient().when(configService.getDelayInSeconds()).thenReturn(0);
        lenient().when(configService.isSimulateErrors()).thenReturn(false);
        lenient().when(configService.getStatusOverride()).thenReturn(null);
        lenient().when(configService.getAadhaarLinkedOverride()).thenReturn(null);
    }

    @Test
    void processPanVerification_PanWithEvenDigit_ReturnsActiveStatus() {
        // Given
        PanVerificationRequest request = new PanVerificationRequest("**********", "John Doe");

        // When
        PanVerificationResponse response = mockPanService.processPanVerification(request);

        // Then
        assertEquals("Active", response.getStatus());
        assertEquals(request.getPan(), response.getPan());
        assertEquals(request.getName(), response.getName());
        assertNotNull(response.getTransactionId());
        assertNotNull(response.getTimestamp());
    }

    @Test
    void processPanVerification_PanWithOddDigit_ReturnsInactiveStatus() {
        // Given
        PanVerificationRequest request = new PanVerificationRequest("**********", "John Doe");

        // When
        PanVerificationResponse response = mockPanService.processPanVerification(request);

        // Then
        assertEquals("Inactive", response.getStatus());
    }

    @Test
    void processPanVerification_PanStartingWithZZZ_ThrowsException() {
        // Given
        PanVerificationRequest request = new PanVerificationRequest("**********", "John Doe");

        // When/Then
        Exception exception = assertThrows(RuntimeException.class, () -> {
            mockPanService.processPanVerification(request);
        });

        assertTrue(exception.getMessage().contains("Simulated system error"));
    }

    @Test
    void processPanVerification_WithStatusOverride_ReturnsOverriddenStatus() {
        // Given
        PanVerificationRequest request = new PanVerificationRequest("**********", "John Doe");
        when(configService.getStatusOverride()).thenReturn("Deactivated");

        // When
        PanVerificationResponse response = mockPanService.processPanVerification(request);

        // Then
        assertEquals("Deactivated", response.getStatus());
    }

    @Test
    void processPanVerification_WithAadhaarLinkedOverride_ReturnsOverriddenValue() {
        // Given
        PanVerificationRequest request = new PanVerificationRequest("**********", "John Doe");
        when(configService.getAadhaarLinkedOverride()).thenReturn(false);

        // When
        PanVerificationResponse response = mockPanService.processPanVerification(request);

        // Then
        assertFalse(response.getAadhaarLinked());
    }
}
