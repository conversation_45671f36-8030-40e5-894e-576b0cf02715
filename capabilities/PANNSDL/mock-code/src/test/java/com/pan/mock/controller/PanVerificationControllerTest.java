package com.pan.mock.controller;

import org.junit.jupiter.api.Test;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.header;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pan.mock.model.PanVerificationRequest;
import com.pan.mock.service.MockPanService;
import com.pan.mock.service.TraceService;

@WebMvcTest(PanVerificationController.class)
class PanVerificationControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private MockPanService mockPanService;

    @MockBean
    private TraceService traceService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void verifyPan_ValidRequest_ReturnsOk() throws Exception {
        // Given
        PanVerificationRequest request = new PanVerificationRequest("**********", "John Doe");
        when(traceService.generateTraceId()).thenReturn("TRC-12345678");

        // When/Then
        mockMvc.perform(post("/v1/verify")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(header().exists("X-Trace-Id"));
    }

    @Test
    void verifyPan_InvalidRequest_ReturnsBadRequest() throws Exception {
        // Given
        PanVerificationRequest request = new PanVerificationRequest("INVALID", "John Doe");

        // When/Then
        mockMvc.perform(post("/v1/verify")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void verifyPan_ServerError_ReturnsInternalServerError() throws Exception {
        // Given
        PanVerificationRequest request = new PanVerificationRequest("**********", "John Doe");
        when(traceService.generateTraceId()).thenReturn("TRC-12345678");
        when(mockPanService.processPanVerification(any())).thenThrow(new RuntimeException("Simulated error"));

        // When/Then
        mockMvc.perform(post("/v1/verify")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isInternalServerError())
                .andExpect(header().exists("X-Trace-Id"));
    }
}
