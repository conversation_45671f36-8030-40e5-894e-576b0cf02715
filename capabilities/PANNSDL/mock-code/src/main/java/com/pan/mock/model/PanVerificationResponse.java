package com.pan.mock.model;

import java.time.Instant;

/**
 * Represents a response from the PAN verification service.
 */
public class PanVerificationResponse {

    private String pan;
    private String name;
    private String status;
    private Boolean aadhaarLinked;
    private String transactionId;
    private Instant timestamp;

    // Default constructor
    public PanVerificationResponse() {
    }

    // Constructor with all fields
    public PanVerificationResponse(String pan, String name, String status, Boolean aadhaarLinked, 
                                  String transactionId, Instant timestamp) {
        this.pan = pan;
        this.name = name;
        this.status = status;
        this.aadhaarLinked = aadhaarLinked;
        this.transactionId = transactionId;
        this.timestamp = timestamp;
    }

    // Getters and setters
    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getAadhaarLinked() {
        return aadhaarLinked;
    }

    public void setAadhaarLinked(Boolean aadhaarLinked) {
        this.aadhaarLinked = aadhaarLinked;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public Instant getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Instant timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return "PanVerificationResponse{" +
                "pan='" + maskPan(pan) + '\'' +
                ", name='" + maskName(name) + '\'' +
                ", status='" + status + '\'' +
                ", aadhaarLinked=" + aadhaarLinked +
                ", transactionId='" + transactionId + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }

    // Helper methods for masking sensitive data
    private String maskPan(String pan) {
        if (pan == null || pan.length() < 10) {
            return pan;
        }
        return "XXXX" + pan.substring(4, 9) + pan.charAt(9);
    }

    private String maskName(String name) {
        if (name == null || name.isEmpty()) {
            return name;
        }
        return name.substring(0, 1) + "***";
    }
}
