package com.pan.mock.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.servers.Server;


/**
 * OpenAPI configuration for PAN NSDL Mock service.
 */
@Configuration
public class OpenApiConfig {

    @Value("${server.port:8202}")
    private String serverPort;

    @Value("${server.servlet.context-path:/api/pannsdl/internal}")
    private String contextPath;

    /**
     * Configure OpenAPI documentation.
     *
     * @return OpenAPI configuration
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("PAN NSDL Mock Verification Service API")
                        .version("1.0.0")
                        .description("Mock REST API for PAN verification testing and development. " +
                                "This service simulates real PAN verification behavior with configurable responses, " +
                                "delays, and error scenarios for comprehensive testing.")
                        .contact(new Contact()
                                .name("PAN Mock Service Team")
                                .email("<EMAIL>")))
                .addServersItem(new Server()
                        .url("http://localhost:" + serverPort + contextPath)
                        .description("Local mock server"));
    }


}
