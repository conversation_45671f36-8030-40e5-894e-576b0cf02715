package com.pan.mock.controller;

import java.time.Instant;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pan.mock.model.ErrorResponse;
import com.pan.mock.model.PanVerificationRequest;
import com.pan.mock.model.PanVerificationResponse;
import com.pan.mock.service.MockPanService;
import com.pan.mock.service.TraceService;

import jakarta.validation.Valid;

/**
 * Controller for handling PAN verification requests.
 */
@RestController
@RequestMapping("/v1")
public class PanVerificationController {

    private static final Logger logger = LoggerFactory.getLogger(PanVerificationController.class);
    
    private final MockPanService mockPanService;
    private final TraceService traceService;
    
    public PanVerificationController(MockPanService mockPanService, TraceService traceService) {
        this.mockPanService = mockPanService;
        this.traceService = traceService;
    }
    
    /**
     * Verify a PAN.
     *
     * @param request The PAN verification request
     * @return The PAN verification response
     */
    @PostMapping("/verify")
    public ResponseEntity<?> verifyPan(@Valid @RequestBody PanVerificationRequest request) {
        String traceId = traceService.generateTraceId();
        logger.info("Received PAN verification request with trace ID: {}", traceId);
        
        try {
            // Add trace ID to response headers
            PanVerificationResponse response = mockPanService.processPanVerification(request);
            return ResponseEntity.ok()
                    .header("X-Trace-Id", traceId)
                    .body(response);
        } catch (Exception e) {
            logger.error("Error processing PAN verification request: {}", e.getMessage(), e);
            
            ErrorResponse errorResponse = new ErrorResponse(
                    "PAN-ERR-001",
                    e.getMessage(),
                    traceId,
                    Instant.now()
            );
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header("X-Trace-Id", traceId)
                    .body(errorResponse);
        } finally {
            traceService.clearTraceId();
        }
    }
}
