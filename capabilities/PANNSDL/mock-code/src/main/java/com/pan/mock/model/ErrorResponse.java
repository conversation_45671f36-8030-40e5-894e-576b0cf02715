package com.pan.mock.model;

import java.time.Instant;

/**
 * Represents a standardized error response.
 */
public class ErrorResponse {

    private String errorCode;
    private String message;
    private String transactionId;
    private Instant timestamp;

    // Default constructor
    public ErrorResponse() {
    }

    // Constructor with all fields
    public ErrorResponse(String errorCode, String message, String transactionId, Instant timestamp) {
        this.errorCode = errorCode;
        this.message = message;
        this.transactionId = transactionId;
        this.timestamp = timestamp;
    }

    // Getters and setters
    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public Instant getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Instant timestamp) {
        this.timestamp = timestamp;
    }
}
