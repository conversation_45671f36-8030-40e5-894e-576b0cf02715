package com.pan.mock.model;

import java.time.Instant;

/**
 * Represents a record of a PAN verification request for traceability.
 */
public class RequestRecord {

    private String pan;
    private String name;
    private String transactionId;
    private Instant timestamp;
    private String status;
    private Boolean aadhaarLinked;
    private Boolean isError;
    private String errorMessage;

    // Default constructor
    public RequestRecord() {
    }

    // Constructor with all fields
    public RequestRecord(String pan, String name, String transactionId, Instant timestamp,
                        String status, Boolean aadhaarLinked, Boolean isError, String errorMessage) {
        this.pan = pan;
        this.name = name;
        this.transactionId = transactionId;
        this.timestamp = timestamp;
        this.status = status;
        this.aadhaarLinked = aadhaarLinked;
        this.isError = isError;
        this.errorMessage = errorMessage;
    }

    // Getters and setters
    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public Instant getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Instant timestamp) {
        this.timestamp = timestamp;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getAadhaarLinked() {
        return aadhaarLinked;
    }

    public void setAadhaarLinked(Boolean aadhaarLinked) {
        this.aadhaarLinked = aadhaarLinked;
    }

    public Boolean getIsError() {
        return isError;
    }

    public void setIsError(Boolean isError) {
        this.isError = isError;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    @Override
    public String toString() {
        return "RequestRecord{" +
                "pan='" + maskPan(pan) + '\'' +
                ", name='" + maskName(name) + '\'' +
                ", transactionId='" + transactionId + '\'' +
                ", timestamp=" + timestamp +
                ", status='" + status + '\'' +
                ", aadhaarLinked=" + aadhaarLinked +
                ", isError=" + isError +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }

    // Helper methods for masking sensitive data
    private String maskPan(String pan) {
        if (pan == null || pan.length() < 10) {
            return pan;
        }
        return "XXXX" + pan.substring(4, 9) + pan.charAt(9);
    }

    private String maskName(String name) {
        if (name == null || name.isEmpty()) {
            return name;
        }
        return name.substring(0, 1) + "***";
    }
}
