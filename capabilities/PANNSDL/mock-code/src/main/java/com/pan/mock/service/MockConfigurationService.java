package com.pan.mock.service;

import com.pan.mock.model.MockConfigurationRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * Service for managing the runtime configuration of the mock service.
 */
@Service
public class MockConfigurationService {

    private static final Logger logger = LoggerFactory.getLogger(MockConfigurationService.class);
    
    private Integer delayInSeconds = 0;
    private Boolean simulateErrors = false;
    private String statusOverride = null;
    private Boolean aadhaarLinkedOverride = null;
    
    /**
     * Update the configuration of the mock service.
     *
     * @param config The new configuration
     */
    public void updateConfiguration(MockConfigurationRequest config) {
        logger.info("Updating mock configuration");
        
        if (config.getDelayInSeconds() != null) {
            this.delayInSeconds = config.getDelayInSeconds();
            logger.info("Delay set to {} seconds", this.delayInSeconds);
        }
        
        if (config.getSimulateErrors() != null) {
            this.simulateErrors = config.getSimulateErrors();
            logger.info("Simulate errors set to {}", this.simulateErrors);
        }
        
        if (config.getStatusOverride() != null) {
            this.statusOverride = config.getStatusOverride();
            logger.info("Status override set to {}", this.statusOverride);
        }
        
        if (config.getAadhaarLinkedOverride() != null) {
            this.aadhaarLinkedOverride = config.getAadhaarLinkedOverride();
            logger.info("Aadhaar linked override set to {}", this.aadhaarLinkedOverride);
        }
    }
    
    /**
     * Reset the configuration to default values.
     */
    public void resetConfiguration() {
        logger.info("Resetting mock configuration to defaults");
        this.delayInSeconds = 0;
        this.simulateErrors = false;
        this.statusOverride = null;
        this.aadhaarLinkedOverride = null;
    }
    
    // Getters
    public Integer getDelayInSeconds() {
        return delayInSeconds;
    }
    
    public Boolean isSimulateErrors() {
        return simulateErrors;
    }
    
    public String getStatusOverride() {
        return statusOverride;
    }
    
    public Boolean getAadhaarLinkedOverride() {
        return aadhaarLinkedOverride;
    }
}
