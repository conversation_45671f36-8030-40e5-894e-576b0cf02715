package com.pan.mock.service;

import com.pan.mock.model.PanVerificationRequest;
import com.pan.mock.model.PanVerificationResponse;
import com.pan.mock.model.RequestRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Service for handling PAN verification requests and generating mock responses.
 */
@Service
public class MockPanService {

    private static final Logger logger = LoggerFactory.getLogger(MockPanService.class);
    
    private final TraceService traceService;
    private final MockConfigurationService configService;
    
    public MockPanService(TraceService traceService, MockConfigurationService configService) {
        this.traceService = traceService;
        this.configService = configService;
    }
    
    /**
     * Process a PAN verification request and generate a mock response.
     *
     * @param request The PAN verification request
     * @return The mock PAN verification response
     * @throws RuntimeException if an error is simulated
     */
    public PanVerificationResponse processPanVerification(PanVerificationRequest request) {
        String traceId = traceService.getCurrentTraceId();
        logger.info("Processing PAN verification request with trace ID: {}", traceId);
        
        // Check if we should simulate an error
        if (shouldSimulateError(request.getPan())) {
            String errorMessage = "Simulated system error for PAN starting with ZZZ";
            logger.error("Error processing PAN verification: {}", errorMessage);
            
            // Record the request with error
            traceService.recordRequest(new RequestRecord(
                    request.getPan(),
                    request.getName(),
                    traceId,
                    Instant.now(),
                    null,
                    null,
                    true,
                    errorMessage
            ));
            
            throw new RuntimeException(errorMessage);
        }
        
        // Check if we should simulate a delay
        if (shouldSimulateDelay(request.getPan())) {
            simulateDelay();
        }
        
        // Determine the status based on the PAN
        String status = determineStatus(request.getPan());
        
        // Determine Aadhaar linkage
        Boolean aadhaarLinked = determineAadhaarLinkage(request.getPan());
        
        // Create the response
        PanVerificationResponse response = new PanVerificationResponse(
                request.getPan(),
                request.getName(),
                status,
                aadhaarLinked,
                "TXN" + UUID.randomUUID().toString().substring(0, 6).toUpperCase(),
                Instant.now()
        );
        
        // Record the request
        traceService.recordRequest(new RequestRecord(
                request.getPan(),
                request.getName(),
                traceId,
                Instant.now(),
                status,
                aadhaarLinked,
                false,
                null
        ));
        
        logger.info("PAN verification completed successfully with trace ID: {}", traceId);
        return response;
    }
    
    /**
     * Determine if an error should be simulated based on the PAN.
     *
     * @param pan The PAN number
     * @return true if an error should be simulated, false otherwise
     */
    private boolean shouldSimulateError(String pan) {
        // If configured to simulate errors, check if PAN starts with "ZZZ"
        return configService.isSimulateErrors() || (pan != null && pan.startsWith("ZZZ"));
    }
    
    /**
     * Determine if a delay should be simulated based on the PAN.
     *
     * @param pan The PAN number
     * @return true if a delay should be simulated, false otherwise
     */
    private boolean shouldSimulateDelay(String pan) {
        // If configured with a delay, or if PAN ends with "9"
        return configService.getDelayInSeconds() > 0 || (pan != null && pan.endsWith("9"));
    }
    
    /**
     * Simulate a delay in processing.
     */
    private void simulateDelay() {
        int delayInSeconds = configService.getDelayInSeconds() > 0 ? 
                configService.getDelayInSeconds() : 5; // Default to 5 seconds
        
        logger.info("Simulating delay of {} seconds", delayInSeconds);
        try {
            TimeUnit.SECONDS.sleep(delayInSeconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Delay simulation interrupted", e);
        }
    }
    
    /**
     * Determine the status based on the PAN.
     *
     * @param pan The PAN number
     * @return The status
     */
    private String determineStatus(String pan) {
        // If status override is configured, use that
        if (configService.getStatusOverride() != null) {
            return configService.getStatusOverride();
        }
        
        // Otherwise, determine based on PAN
        if (pan != null && pan.length() >= 9) {
            char lastDigit = pan.charAt(8); // 9th character (index 8) is the last digit
            return Character.isDigit(lastDigit) && (lastDigit - '0') % 2 == 0 ? "Active" : "Inactive";
        }
        
        return "Unknown";
    }
    
    /**
     * Determine Aadhaar linkage based on the PAN.
     *
     * @param pan The PAN number
     * @return true if Aadhaar is linked, false otherwise
     */
    private Boolean determineAadhaarLinkage(String pan) {
        // If Aadhaar linkage override is configured, use that
        if (configService.getAadhaarLinkedOverride() != null) {
            return configService.getAadhaarLinkedOverride();
        }
        
        // Otherwise, determine based on PAN (example logic: linked if PAN contains an even number of vowels)
        if (pan != null) {
            long vowelCount = pan.toLowerCase().chars()
                    .filter(c -> "aeiou".indexOf(c) >= 0)
                    .count();
            return vowelCount % 2 == 0;
        }
        
        return false;
    }
}
