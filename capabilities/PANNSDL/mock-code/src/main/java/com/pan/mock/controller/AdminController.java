package com.pan.mock.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pan.mock.model.MockConfigurationRequest;
import com.pan.mock.model.RequestRecord;
import com.pan.mock.service.MockConfigurationService;
import com.pan.mock.service.TraceService;

/**
 * Controller for admin operations.
 */
@RestController
@RequestMapping("/v1/admin")
public class AdminController {

    private static final Logger logger = LoggerFactory.getLogger(AdminController.class);

    private final MockConfigurationService configService;
    private final TraceService traceService;

    public AdminController(MockConfigurationService configService, TraceService traceService) {
        this.configService = configService;
        this.traceService = traceService;
    }

    /**
     * Get all recorded requests.
     *
     * @return A list of all recorded requests
     */
    @GetMapping("/requests")
    public ResponseEntity<List<RequestRecord>> getRequests() {
        logger.info("Getting all recorded requests");
        return ResponseEntity.ok(traceService.getRequestRecords());
    }

    /**
     * Update the configuration of the mock service.
     *
     * @param config The new configuration
     * @return A success message
     */
    @PostMapping("/config")
    public ResponseEntity<String> updateConfiguration(@RequestBody MockConfigurationRequest config) {
        logger.info("Updating mock configuration");
        configService.updateConfiguration(config);
        return ResponseEntity.ok("Configuration updated successfully");
    }

    /**
     * Reset the mock service state.
     *
     * @return A success message
     */
    @PostMapping("/reset")
    public ResponseEntity<String> resetMockState() {
        logger.info("Resetting mock state");
        configService.resetConfiguration();
        traceService.clearRequestRecords();
        return ResponseEntity.ok("Mock state reset successfully");
    }
}
