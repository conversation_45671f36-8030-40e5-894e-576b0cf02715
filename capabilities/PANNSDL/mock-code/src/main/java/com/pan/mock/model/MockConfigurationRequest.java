package com.pan.mock.model;

/**
 * Represents a request to configure the mock service behavior.
 */
public class MockConfigurationRequest {

    private Integer delayInSeconds;
    private Boolean simulateErrors;
    private String statusOverride;
    private Boolean aadhaarLinkedOverride;

    // Default constructor
    public MockConfigurationRequest() {
    }

    // Constructor with all fields
    public MockConfigurationRequest(Integer delayInSeconds, Boolean simulateErrors, 
                                   String statusOverride, Boolean aadhaarLinkedOverride) {
        this.delayInSeconds = delayInSeconds;
        this.simulateErrors = simulateErrors;
        this.statusOverride = statusOverride;
        this.aadhaarLinkedOverride = aadhaarLinkedOverride;
    }

    // Getters and setters
    public Integer getDelayInSeconds() {
        return delayInSeconds;
    }

    public void setDelayInSeconds(Integer delayInSeconds) {
        this.delayInSeconds = delayInSeconds;
    }

    public Boolean getSimulateErrors() {
        return simulateErrors;
    }

    public void setSimulateErrors(Boolean simulateErrors) {
        this.simulateErrors = simulateErrors;
    }

    public String getStatusOverride() {
        return statusOverride;
    }

    public void setStatusOverride(String statusOverride) {
        this.statusOverride = statusOverride;
    }

    public Boolean getAadhaarLinkedOverride() {
        return aadhaarLinkedOverride;
    }

    public void setAadhaarLinkedOverride(Boolean aadhaarLinkedOverride) {
        this.aadhaarLinkedOverride = aadhaarLinkedOverride;
    }
}
