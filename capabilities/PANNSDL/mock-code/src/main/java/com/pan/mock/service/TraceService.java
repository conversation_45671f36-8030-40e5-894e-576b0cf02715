package com.pan.mock.service;

import com.pan.mock.model.RequestRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * Service for managing trace IDs and request records.
 */
@Service
public class TraceService {

    private static final Logger logger = LoggerFactory.getLogger(TraceService.class);
    private static final String TRACE_ID_KEY = "traceId";
    private static final int MAX_RECORDS = 100;
    
    private final List<RequestRecord> requestRecords = Collections.synchronizedList(new ArrayList<>());
    
    /**
     * Generate a new trace ID and store it in the MDC.
     *
     * @return The generated trace ID
     */
    public String generateTraceId() {
        String traceId = "TRC-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        MDC.put(TRACE_ID_KEY, traceId);
        logger.debug("Generated new trace ID: {}", traceId);
        return traceId;
    }
    
    /**
     * Get the current trace ID from the MDC.
     *
     * @return The current trace ID, or a new one if none exists
     */
    public String getCurrentTraceId() {
        String traceId = MDC.get(TRACE_ID_KEY);
        if (traceId == null) {
            traceId = generateTraceId();
        }
        return traceId;
    }
    
    /**
     * Clear the trace ID from the MDC.
     */
    public void clearTraceId() {
        MDC.remove(TRACE_ID_KEY);
    }
    
    /**
     * Record a request for traceability.
     *
     * @param record The request record to store
     */
    public void recordRequest(RequestRecord record) {
        synchronized (requestRecords) {
            // Add the new record
            requestRecords.add(record);
            
            // If we've exceeded the maximum number of records, remove the oldest one
            if (requestRecords.size() > MAX_RECORDS) {
                requestRecords.remove(0);
            }
        }
        
        logger.debug("Recorded request with trace ID: {}", record.getTransactionId());
    }
    
    /**
     * Get all recorded requests.
     *
     * @return A list of all recorded requests
     */
    public List<RequestRecord> getRequestRecords() {
        synchronized (requestRecords) {
            return new ArrayList<>(requestRecords);
        }
    }
    
    /**
     * Clear all recorded requests.
     */
    public void clearRequestRecords() {
        synchronized (requestRecords) {
            requestRecords.clear();
        }
        logger.info("Cleared all request records");
    }
}
