package com.pan.verification.model.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * DTO for PAN verification request.
 */
public class PanVerificationRequest {

    @NotBlank(message = "PAN number is required")
    @Pattern(regexp = "[A-Z]{5}[0-9]{4}[A-Z]", message = "PAN number must be in the format: **********")
    private String pan;

    @NotBlank(message = "Name is required")
    private String name;

    // Default constructor
    public PanVerificationRequest() {
    }

    // Constructor with all fields
    public PanVerificationRequest(String pan, String name) {
        this.pan = pan;
        this.name = name;
    }

    // Getters and setters
    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "PanVerificationRequest{" +
                "pan='" + maskPan(pan) + '\'' +
                ", name='" + maskName(name) + '\'' +
                '}';
    }

    // Helper methods for masking sensitive data
    private String maskPan(String pan) {
        if (pan == null || pan.length() < 10) {
            return pan;
        }
        return "XXXX" + pan.substring(4, 9) + pan.charAt(9);
    }

    private String maskName(String name) {
        if (name == null || name.isEmpty()) {
            return name;
        }
        return name.substring(0, 1) + "***";
    }
}
