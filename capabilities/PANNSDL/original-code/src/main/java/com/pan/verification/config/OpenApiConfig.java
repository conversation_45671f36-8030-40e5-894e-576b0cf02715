package com.pan.verification.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.servers.Server;


/**
 * OpenAPI configuration for PAN NSDL Original service.
 */
@Configuration
public class OpenApiConfig {

    @Value("${server.port:8102}")
    private String serverPort;

    @Value("${server.servlet.context-path:/api/pannsdl}")
    private String contextPath;

    /**
     * Configure OpenAPI documentation.
     *
     * @return OpenAPI configuration
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("PAN NSDL Original Verification Service API")
                        .version("1.0.0")
                        .description("REST API for PAN verification using NSDL services. " +
                                "This service provides secure PAN verification with A<PERSON><PERSON>ar linkage status " +
                                "and maintains audit trails for compliance.")
                        .contact(new Contact()
                                .name("PAN Verification Team")
                                .email("<EMAIL>")))
                .addServersItem(new Server()
                        .url("http://localhost:" + serverPort + contextPath)
                        .description("Local development server"));
    }


}
