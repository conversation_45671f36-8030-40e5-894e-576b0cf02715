package com.pan.verification.exception;

import com.pan.verification.model.dto.ErrorResponse;
import com.pan.verification.model.enums.ErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.time.Instant;
import java.util.stream.Collectors;

/**
 * Global exception handler for the application.
 */
@ControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * Handle InvalidPanFormatException.
     *
     * @param ex The exception
     * @return The error response
     */
    @ExceptionHandler(InvalidPanFormatException.class)
    public ResponseEntity<ErrorResponse> handleInvalidPanFormatException(InvalidPanFormatException ex) {
        logger.error("Invalid PAN format: {}", ex.getMessage(), ex);
        
        ErrorResponse errorResponse = new ErrorResponse(
                ErrorCode.INVALID_PAN_FORMAT.getCode(),
                ex.getMessage(),
                MDC.get("referenceNumber"),
                Instant.now()
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * Handle ProteanApiException.
     *
     * @param ex The exception
     * @return The error response
     */
    @ExceptionHandler(ProteanApiException.class)
    public ResponseEntity<ErrorResponse> handleProteanApiException(ProteanApiException ex) {
        logger.error("Protean API error: {}", ex.getMessage(), ex);
        
        ErrorResponse errorResponse = new ErrorResponse(
                ErrorCode.PROTEAN_API_ERROR.getCode(),
                ex.getMessage(),
                MDC.get("referenceNumber"),
                Instant.now()
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    /**
     * Handle AadhaarNotLinkedException.
     *
     * @param ex The exception
     * @return The error response
     */
    @ExceptionHandler(AadhaarNotLinkedException.class)
    public ResponseEntity<ErrorResponse> handleAadhaarNotLinkedException(AadhaarNotLinkedException ex) {
        logger.error("Aadhaar not linked: {}", ex.getMessage(), ex);
        
        ErrorResponse errorResponse = new ErrorResponse(
                ErrorCode.AADHAAR_NOT_LINKED.getCode(),
                ex.getMessage(),
                MDC.get("referenceNumber"),
                Instant.now()
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * Handle MethodArgumentNotValidException.
     *
     * @param ex The exception
     * @return The error response
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        String errorMessage = ex.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        logger.error("Validation error: {}", errorMessage, ex);
        
        ErrorResponse errorResponse = new ErrorResponse(
                ErrorCode.INVALID_REQUEST.getCode(),
                errorMessage,
                MDC.get("referenceNumber"),
                Instant.now()
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * Handle all other exceptions.
     *
     * @param ex The exception
     * @return The error response
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleException(Exception ex) {
        logger.error("Unexpected error: {}", ex.getMessage(), ex);
        
        ErrorResponse errorResponse = new ErrorResponse(
                ErrorCode.INTERNAL_SERVER_ERROR.getCode(),
                "An unexpected error occurred",
                MDC.get("referenceNumber"),
                Instant.now()
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
}
