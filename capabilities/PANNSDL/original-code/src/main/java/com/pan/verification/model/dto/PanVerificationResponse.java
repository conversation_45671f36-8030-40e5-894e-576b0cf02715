package com.pan.verification.model.dto;

import java.time.Instant;

/**
 * DTO for PAN verification response.
 */
public class PanVerificationResponse {

    private String referenceNumber;
    private String maskedPan;
    private String status;
    private Boolean aadhaarLinked;
    private Instant timestamp;

    // Default constructor
    public PanVerificationResponse() {
    }

    // Constructor with all fields
    public PanVerificationResponse(String referenceNumber, String maskedPan, String status, 
                                  Boolean aadhaarLinked, Instant timestamp) {
        this.referenceNumber = referenceNumber;
        this.maskedPan = maskedPan;
        this.status = status;
        this.aadhaarLinked = aadhaarLinked;
        this.timestamp = timestamp;
    }

    // Getters and setters
    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getMaskedPan() {
        return maskedPan;
    }

    public void setMaskedPan(String maskedPan) {
        this.maskedPan = maskedPan;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getAadhaarLinked() {
        return aadhaarLinked;
    }

    public void setAadhaarLinked(Boolean aadhaarLinked) {
        this.aadhaarLinked = aadhaarLinked;
    }

    public Instant getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Instant timestamp) {
        this.timestamp = timestamp;
    }
}
