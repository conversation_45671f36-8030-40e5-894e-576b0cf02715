package com.pan.verification.model.dto;

import java.time.Instant;

/**
 * DTO for error response.
 */
public class ErrorResponse {

    private String errorCode;
    private String message;
    private String referenceNumber;
    private Instant timestamp;

    // Default constructor
    public ErrorResponse() {
    }

    // Constructor with all fields
    public ErrorResponse(String errorCode, String message, String referenceNumber, Instant timestamp) {
        this.errorCode = errorCode;
        this.message = message;
        this.referenceNumber = referenceNumber;
        this.timestamp = timestamp;
    }

    // Getters and setters
    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public Instant getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Instant timestamp) {
        this.timestamp = timestamp;
    }
}
