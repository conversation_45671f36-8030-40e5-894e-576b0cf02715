package com.pan.verification.model.entity;

import jakarta.persistence.*;
import java.time.Instant;

/**
 * Entity class for storing PAN verification records.
 */
@Entity
@Table(name = "pan_verification")
public class PanVerification {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "reference_number", nullable = false, unique = true)
    private String referenceNumber;

    @Column(name = "masked_pan", nullable = false)
    private String maskedPan;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "aadhaar_linked", nullable = false)
    private Boolean aadhaarLinked;

    @Column(name = "request_timestamp", nullable = false)
    private Instant requestTimestamp;

    @Column(name = "response_timestamp", nullable = false)
    private Instant responseTimestamp;

    @Column(name = "persisted_timestamp", nullable = false)
    private Instant persistedTimestamp;

    @Column(name = "transaction_id")
    private String transactionId;

    @Column(name = "retry_count", nullable = false)
    private Integer retryCount;

    @Column(name = "created_at", nullable = false, updatable = false)
    private Instant createdAt;

    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    // Default constructor
    public PanVerification() {
    }

    // Constructor with all fields
    public PanVerification(String referenceNumber, String maskedPan, String status, Boolean aadhaarLinked,
                          Instant requestTimestamp, Instant responseTimestamp, Instant persistedTimestamp,
                          String transactionId, Integer retryCount, Instant createdAt, Instant updatedAt) {
        this.referenceNumber = referenceNumber;
        this.maskedPan = maskedPan;
        this.status = status;
        this.aadhaarLinked = aadhaarLinked;
        this.requestTimestamp = requestTimestamp;
        this.responseTimestamp = responseTimestamp;
        this.persistedTimestamp = persistedTimestamp;
        this.transactionId = transactionId;
        this.retryCount = retryCount;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getMaskedPan() {
        return maskedPan;
    }

    public void setMaskedPan(String maskedPan) {
        this.maskedPan = maskedPan;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getAadhaarLinked() {
        return aadhaarLinked;
    }

    public void setAadhaarLinked(Boolean aadhaarLinked) {
        this.aadhaarLinked = aadhaarLinked;
    }

    public Instant getRequestTimestamp() {
        return requestTimestamp;
    }

    public void setRequestTimestamp(Instant requestTimestamp) {
        this.requestTimestamp = requestTimestamp;
    }

    public Instant getResponseTimestamp() {
        return responseTimestamp;
    }

    public void setResponseTimestamp(Instant responseTimestamp) {
        this.responseTimestamp = responseTimestamp;
    }

    public Instant getPersistedTimestamp() {
        return persistedTimestamp;
    }

    public void setPersistedTimestamp(Instant persistedTimestamp) {
        this.persistedTimestamp = persistedTimestamp;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    @PrePersist
    protected void onCreate() {
        createdAt = Instant.now();
        updatedAt = Instant.now();
        persistedTimestamp = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
    }
}
