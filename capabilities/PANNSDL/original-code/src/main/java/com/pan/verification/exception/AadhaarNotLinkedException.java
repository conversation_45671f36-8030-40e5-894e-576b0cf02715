package com.pan.verification.exception;

/**
 * Exception thrown when <PERSON><PERSON><PERSON><PERSON> is not linked and linkage is required.
 */
public class A<PERSON><PERSON>arNotLinkedException extends RuntimeException {

    public AadhaarNotLinkedException(String message) {
        super(message);
    }

    public AadhaarNotLinkedException(String message, Throwable cause) {
        super(message, cause);
    }
}
