package com.pan.verification.model.dto;

/**
 * DTO for Protean API request.
 */
public class ProteanApiRequest {

    private String pan;
    private String name;

    // Default constructor
    public ProteanApiRequest() {
    }

    // Constructor with all fields
    public ProteanApiRequest(String pan, String name) {
        this.pan = pan;
        this.name = name;
    }

    // Getters and setters
    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "ProteanApiRequest{" +
                "pan='" + maskPan(pan) + '\'' +
                ", name='" + maskName(name) + '\'' +
                '}';
    }

    // Helper methods for masking sensitive data
    private String maskPan(String pan) {
        if (pan == null || pan.length() < 10) {
            return pan;
        }
        return "XXXX" + pan.substring(4, 9) + pan.charAt(9);
    }

    private String maskName(String name) {
        if (name == null || name.isEmpty()) {
            return name;
        }
        return name.substring(0, 1) + "***";
    }
}
