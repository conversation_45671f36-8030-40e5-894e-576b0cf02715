package com.pan.verification.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pan.verification.model.dto.PanVerificationRequest;
import com.pan.verification.model.dto.PanVerificationResponse;
import com.pan.verification.service.PanVerificationService;

import jakarta.validation.Valid;

/**
 * Controller for PAN verification.
 */
@RestController
@RequestMapping("/v1")
public class PanVerificationController {

    private static final Logger logger = LoggerFactory.getLogger(PanVerificationController.class);

    private final PanVerificationService panVerificationService;

    public PanVerificationController(PanVerificationService panVerificationService) {
        this.panVerificationService = panVerificationService;
    }

    /**
     * Verify a PAN.
     *
     * @param request The PAN verification request
     * @return The PAN verification response
     */
    @PostMapping("/verify")
    public ResponseEntity<PanVerificationResponse> verifyPan(@Valid @RequestBody PanVerificationRequest request) {
        logger.info("Received PAN verification request");
        PanVerificationResponse response = panVerificationService.verifyPan(request);
        return ResponseEntity.ok(response);
    }
}
