package com.pan.verification.config;

import com.pan.verification.service.PanVerificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * Configuration for scheduling tasks.
 */
@Configuration
@EnableScheduling
public class SchedulingConfig {

    private static final Logger logger = LoggerFactory.getLogger(SchedulingConfig.class);
    
    private final PanVerificationService panVerificationService;
    
    public SchedulingConfig(PanVerificationService panVerificationService) {
        this.panVerificationService = panVerificationService;
    }
    
    /**
     * Clean up old verification records daily at midnight.
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void cleanupOldRecords() {
        logger.info("Starting scheduled cleanup of old verification records");
        panVerificationService.cleanupOldRecords();
        logger.info("Completed scheduled cleanup of old verification records");
    }
}
