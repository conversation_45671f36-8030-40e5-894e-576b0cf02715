PAN Mock API Service - Specification
====================================

Overview
--------
A standalone Spring Boot application designed to simulate the behavior of a real PAN Verification API for development and testing purposes. The mock service provides configurable responses and realistic behavior for different PAN input patterns, allowing robust testing without dependence on external systems.

Purpose
-------
- Simulate PAN verification behavior for dependent applications
- Enable testing with realistic scenarios (Active, Inactive, Errors, Delays)
- Provide traceable, secure, and masked logging for all requests and responses

Technical Requirements
----------------------
- Spring Boot version: 3.2.3 (or compatible)
- Java version: 21
- Server Port: 8082
- OpenAPI/Swagger documentation included
- Basic Authentication (optional, to simulate secure APIs)
- In-memory data storage for traceability
- Logging with sensitive data masking
- Unique trace ID included in every transaction

Implementation Details
----------------------
- DTOs
- Enums (if applicable)
- Controllers
- Services (include mock logic as per spec)
- Utility Classes (logging with PII masking)
- Sample `application.yml`
- Sample Swagger config
- README with setup, test data, and usage

API Endpoint
------------
1. PAN Verification Endpoint
   - Path: /api/pan/internal/v1/verify
   - Method: POST
   - Request Body:
     {
       "pan": "**********",
       "name": "John Doe"
     }
   - Response Body:
     {
       "pan": "**********",
       "name": "John Doe",
       "status": "Active",
       "aadhaarLinked": true,
       "transactionId": "TXN123456",
       "timestamp": "2025-05-23T10:00:00Z"
     }

Simulated Behavior Logic
-------------------------
- PAN ends in even digit    → Respond with status: "Active"
- PAN ends in odd digit     → Respond with status: "Inactive"
- PAN starts with "ZZZ"     → Simulate HTTP 500 system error
- PAN ends with "9"         → Introduce artificial delay (e.g., 5 seconds)

Security
--------
- Mask PAN and name fields in logs
- Include a unique "X-Trace-Id" header in each request/response
- Optional: Basic Authentication to simulate real service protection

Admin & Configuration Interface
-------------------------------
- GET  /api/pan/internal/v1/admin/requests       → View recent requests (masked)
- POST /api/pan/internal/v1/admin/config         → Configure delays, errors, status overrides
- POST /api/pan/internal/v1/admin/reset          → Reset mock state and history

Service Layer Components
------------------------
- MockPanService: Handles PAN evaluation and response generation
- MockConfigurationService: Manages runtime behavior configuration
- TraceService: Logs and masks request/response data with trace IDs

Testing Strategy
----------------
- Use Swagger UI or Postman to simulate:
    - Valid PANs (ending with even/odd digits)
    - PANs that cause delays (ending in "9")
    - PANs that trigger system errors (starting with "ZZZ")
- Validate correct status, latency, error codes, and masked logs
- Check trace ID propagation and in-memory request storage

README Deliverables
-------------------
- Setup instructions for running the mock service
- Example requests/responses for each scenario
- How to use the admin endpoints to configure behavior
- Logging and traceability explanation
- Swagger UI access for manual testing

Extensibility Suggestions
-------------------------
- Add PAN format validation
- Add Aadhaar linkage logic configuration
- Extend for additional PAN patterns or blacklists
- Optionally persist logs to disk or external DB for traceability