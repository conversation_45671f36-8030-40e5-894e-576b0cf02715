Mock UIDAI API Service - Specification
======================================

Overview
--------
A standalone Spring Boot application designed to simulate the real UIDAI API for Aadhaar-based OTP and eKYC flows in development and testing environments. This mock service helps validate how applications handle both success and failure scenarios across the Aadhaar authentication lifecycle without requiring live UIDAI access.

Purpose
-------
- Simulate UIDAI eKYC and OTP flows for application development and QA
- Enable testing of success, failure, expired OTP, and system error scenarios
- Provide realistic API structure, behavior, and timing simulation
- Isolate development environments from the production UIDAI service

Technical Requirements
----------------------
- Spring Boot version: 3.2.3 (or compatible)
- Java version: 21
- Server Port: 8082
- OpenAPI/Swagger documentation included
- Optional Basic Authentication for API protection
- In-memory request audit with masked PII logging
- UUID-based "X-Trace-Id" tagging on every request and response
- Simulated latency for realistic response timing

Implementation Details
----------------------
- DTOs
- Enums (if applicable)
- Controllers
- Services (include mock logic as per spec)
- Utility Classes (logging with PII masking)
- Sample `application.yml`
- Sample Swagger config
- README with setup, test data, and usage

API Endpoints
-------------
1. eKYC OTP Initiation
   - Path: /api/uidai/internal/v1/ekyc/initiate
   - Method: POST
   - Request Body:
     {
       "aadhaarOrVid": "123456789012",
       "transactionId": "TXN100001"
     }
   - Response (Success):
     {
       "status": "OTP_SENT",
       "referenceId": "REF1234567890",
       "timestamp": "2025-05-23T10:00:00Z"
     }

2. OTP Verification
   - Path: /api/uidai/internal/v1/ekyc/verify
   - Method: POST
   - Request Body:
     {
       "referenceId": "REF1234567890",
       "otp": "123456"
     }
   - Response (Success):
     {
       "status": "VERIFIED",
       "kycData": {
         "name": "Ravi Kumar",
         "dob": "1987-01-01",
         "gender": "M"
       },
       "timestamp": "2025-05-23T10:01:00Z"
     }

Simulated Behavior Logic
-------------------------
- Aadhaar/VID ends with even digit → OTP initiation succeeds
- Aadhaar/VID ends with odd digit → OTP initiation fails with 400 and "OTP_GENERATION_FAILED"
- Aadhaar/VID starts with "999" → Respond with HTTP 500 and "UIDAI_SERVICE_FAILURE"

OTP Verification:
- OTP = "123456" → Success (VERIFIED + kycData)
- All other OTPs → Return 400 with error code "INVALID_OTP"
- Predefined referenceIds (e.g., REFEXPIRED001) → Return "EXPIRED_OTP" error

Security
--------
- Mask Aadhaar/VID, OTP, and KYC data in logs
- All responses tagged with "X-Trace-Id" UUID
- Optional basic authentication for simulating secure access

Admin & Configuration Interface
-------------------------------
- GET  /api/uidai/internal/v1/admin/requests     → View request/response history with PII masking
- POST /api/uidai/internal/v1/admin/config       → Configure response overrides (OTP/kyc scenarios)
- POST /api/uidai/internal/v1/admin/reset        → Clear in-memory logs and configuration

Service Layer Components
------------------------
- MockUidaiService: Simulates OTP initiation, verification, and eKYC data
- MockConfigService: Enables response manipulation at runtime
- TraceLoggerService: Adds trace ID and PII-masked logs

Testing Strategy
----------------
- Use Swagger/Postman to test Aadhaar IDs ending in 0–9:
  - Even digit → OTP sent
  - Odd digit → OTP failure
  - Starts with 999 → System error
- Test OTP values:
  - 123456 → Success
  - Others → INVALID_OTP
  - REFEXPIRED001 → EXPIRED_OTP
- Confirm all logs mask Aadhaar and OTP
- Check trace ID tagging and admin logs

README Deliverables
-------------------
- Setup guide for running the mock UIDAI service
- Sample requests for each outcome
- Swagger UI URL for interactive testing
- Log masking and traceability guidelines
- Admin usage documentation

Extensibility Suggestions
-------------------------
- Add biometric or face authentication mock endpoints
- Simulate response delays or OTP throttling
- Add configurable retry-after headers for rate limiting
- Support KYC image and address fields in mock responses
