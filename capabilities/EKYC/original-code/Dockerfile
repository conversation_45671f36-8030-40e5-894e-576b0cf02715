# Multi‑stage build for EKYC Original Service
FROM maven:3.9.6-eclipse-temurin-17-alpine AS builder

WORKDIR /app

# Better layer caching
COPY pom.xml .
RUN mvn dependency:go-offline -B

COPY src ./src
RUN mvn clean package -DskipTests -B


FROM eclipse-temurin:17-jre-alpine

# Packages & timezone
RUN apk add --no-cache curl bash tzdata
ENV TZ=Asia/Kolkata
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Non‑root user & dirs
RUN addgroup -g 1001 -S appgroup && \
    adduser  -u 1001 -S appuser  -G appgroup && \
    mkdir -p /app/logs && \
    chown -R appuser:appgroup /app

# App JAR
WORKDIR /app

# Download OpenTelemetry Java agent
RUN curl -L -o opentelemetry-javaagent.jar \
    https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/latest/download/opentelemetry-javaagent.jar

# Copy the built JAR from builder stage
COPY --from=builder /app/target/*.jar app.jar

# Change ownership of the JAR file and agent
RUN chown appuser:appgroup app.jar opentelemetry-javaagent.jar

USER appuser
EXPOSE 8101

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8101/api/ekyc/v1/ekyc/original/health || exit 1

# Environment variables with configurable defaults
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseContainerSupport" \
    # Spring
    SPRING_PROFILES_ACTIVE=docker \
    SERVER_PORT=8101 \
    SPRING_DATASOURCE_URL=************************************ \
    SPRING_DATASOURCE_USERNAME=postgres \
    SPRING_DATASOURCE_PASSWORD=postgres \
    # OpenTelemetry
    OTEL_SERVICE_NAME=${OTEL_SERVICE_NAME:-ekyc} \
    OTEL_TRACES_EXPORTER=${OTEL_TRACES_EXPORTER:-otlp} \
    OTEL_METRICS_EXPORTER=${OTEL_METRICS_EXPORTER:-otlp} \
    OTEL_LOGS_EXPORTER=${OTEL_LOGS_EXPORTER:-otlp} \
    OTEL_EXPORTER_OTLP_ENDPOINT=${OTEL_EXPORTER_OTLP_ENDPOINT:-http://otel-collector.observability:4317} \
    OTEL_EXPORTER_OTLP_PROTOCOL=${OTEL_EXPORTER_OTLP_PROTOCOL:-grpc} \
    OTEL_RESOURCE_ATTRIBUTES=${OTEL_RESOURCE_ATTRIBUTES:-service.name=ekyc,service.version=1.0.0,deployment.environment=docker}

# Start the application with OpenTelemetry agent
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -javaagent:opentelemetry-javaagent.jar -jar app.jar"]
