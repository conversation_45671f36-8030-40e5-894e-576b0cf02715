openapi: 3.0.3
info:
  title: EKYC Verification Service API
  description: |
    API for Aadhaar-based eKYC verification service that provides secure identity verification through OTP-based authentication.
    
    This service enables:
    - Initiation of eKYC verification requests
    - OTP-based verification of identity documents
    - Status tracking of verification requests
    - Secure handling of PII data with proper masking
    
    **Security Note**: All sensitive data including Aadhaar numbers and OTPs are handled securely with proper hashing and masking.
  version: 1.0.0
  contact:
    name: EKYC Team
    email: <EMAIL>
    url: https://company.com/ekyc
  license:
    name: Proprietary
    url: https://company.com/license

servers:
  - url: http://localhost:8101/api/ekyc
    description: Development server
  - url: https://api.company.com/api/ekyc
    description: Production server

paths:
  /v1/ekyc/initiate:
    post:
      tags:
        - EKYC Verification
      summary: Initiate eKYC verification
      description: |
        Initiates an eKYC verification request for the provided Aadhaar number or VID.
        This endpoint validates the input, creates a verification request, and triggers OTP generation.
      operationId: initiateEkyc
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EkycRequestDto'
            examples:
              aadhaar_request:
                summary: Aadhaar-based verification
                value:
                  idValue: "123456789012"
                  idType: "AADHAAR"
                  identityVerificationConsent: "YES"
                  mobileEmailConsent: "YES"
                  sessionId: "SESSION-123456"
                  parentProcessId: "PROCESS-789"
              vid_request:
                summary: VID-based verification
                value:
                  idValue: "************"
                  idType: "VID"
                  identityVerificationConsent: "YES"
                  mobileEmailConsent: "NO"
                  sessionId: "SESSION-654321"
      responses:
        '200':
          description: eKYC verification initiated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EkycResponseDto'
              examples:
                success:
                  summary: Successful initiation
                  value:
                    referenceNumber: "EKYC-20250624-001"
                    status: "IN_PROGRESS"
                    message: "eKYC verification initiated successfully. OTP sent to registered mobile number."
                    timestamp: "2025-06-24T10:30:00Z"
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponseDto'
              examples:
                validation_error:
                  summary: Validation error
                  value:
                    errorCode: "VALIDATION_ERROR"
                    message: "Invalid input data"
                    details: ["Aadhaar/VID must be exactly 12 digits", "Consent is required"]
                    timestamp: "2025-06-24T10:30:00Z"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponseDto'
              examples:
                server_error:
                  summary: Server error
                  value:
                    errorCode: "INTERNAL_ERROR"
                    message: "An unexpected error occurred"
                    details: []
                    timestamp: "2025-06-24T10:30:00Z"

  /v1/ekyc/verify-otp:
    post:
      tags:
        - EKYC Verification
      summary: Verify OTP for eKYC
      description: |
        Verifies the OTP provided by the user for the eKYC verification request.
        Upon successful verification, the eKYC status is updated to VERIFIED.
      operationId: verifyOtp
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OtpVerificationRequestDto'
            examples:
              valid_otp:
                summary: Valid OTP verification
                value:
                  otp: "123456"
                  referenceNumber: "EKYC-20250624-001"
      responses:
        '200':
          description: OTP verification result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OtpVerificationResponseDto'
              examples:
                success:
                  summary: Successful verification
                  value:
                    referenceNumber: "EKYC-20250624-001"
                    status: "VERIFIED"
                    message: "OTP verification successful"
                    timestamp: "2025-06-24T10:35:00Z"
                failed:
                  summary: Failed verification
                  value:
                    referenceNumber: "EKYC-20250624-001"
                    status: "FAILED"
                    failureReason: "INVALID_OTP"
                    message: "Invalid OTP provided"
                    timestamp: "2025-06-24T10:35:00Z"
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponseDto'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponseDto'

  /v1/ekyc/status/{referenceNumber}:
    get:
      tags:
        - EKYC Verification
      summary: Get eKYC verification status
      description: |
        Retrieves the current status of an eKYC verification request using the reference number.
      operationId: getEkycStatus
      parameters:
        - name: referenceNumber
          in: path
          required: true
          description: The reference number of the eKYC request
          schema:
            type: string
            pattern: '^EKYC-[0-9]{8}-[0-9]{3}$'
            example: "EKYC-20250624-001"
      responses:
        '200':
          description: eKYC status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EkycResponseDto'
              examples:
                in_progress:
                  summary: Verification in progress
                  value:
                    referenceNumber: "EKYC-20250624-001"
                    status: "IN_PROGRESS"
                    message: "eKYC verification is in progress"
                    timestamp: "2025-06-24T10:30:00Z"
                verified:
                  summary: Verification completed
                  value:
                    referenceNumber: "EKYC-20250624-001"
                    status: "VERIFIED"
                    message: "eKYC verification completed successfully"
                    timestamp: "2025-06-24T10:35:00Z"
        '404':
          description: eKYC request not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponseDto'
              examples:
                not_found:
                  summary: Request not found
                  value:
                    errorCode: "NOT_FOUND"
                    message: "eKYC request not found"
                    details: ["No eKYC request found with the provided reference number"]
                    timestamp: "2025-06-24T10:30:00Z"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponseDto'

  /v1/ekyc/original/health:
    get:
      tags:
        - Health Check
      summary: Health check endpoint
      description: Returns the health status of the EKYC verification service
      operationId: healthCheck
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "UP"
                  service:
                    type: string
                    example: "EKYC Original"
                  timestamp:
                    type: string
                    format: date-time
                    example: "2025-06-24T10:30:00Z"
                  version:
                    type: string
                    example: "1.0.0"
              example:
                status: "UP"
                service: "EKYC Original"
                timestamp: "2025-06-24T10:30:00Z"
                version: "1.0.0"

components:
  schemas:
    EkycRequestDto:
      type: object
      required:
        - idValue
        - idType
        - identityVerificationConsent
        - mobileEmailConsent
        - sessionId
      properties:
        idValue:
          type: string
          pattern: '^[0-9]{12}$'
          description: 12-digit Aadhaar number or VID
          example: "123456789012"
        idType:
          $ref: '#/components/schemas/IdType'
        identityVerificationConsent:
          $ref: '#/components/schemas/ConsentType'
        mobileEmailConsent:
          $ref: '#/components/schemas/ConsentType'
        sessionId:
          type: string
          maxLength: 50
          description: Unique session identifier
          example: "SESSION-123456"
        parentProcessId:
          type: string
          maxLength: 50
          description: Optional parent process identifier
          example: "PROCESS-789"

    EkycResponseDto:
      type: object
      properties:
        referenceNumber:
          type: string
          description: Unique reference number for the eKYC request
          example: "EKYC-20250624-001"
        status:
          $ref: '#/components/schemas/VerificationStatus'
        message:
          type: string
          description: Human-readable message about the request status
          example: "eKYC verification initiated successfully"
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the response
          example: "2025-06-24T10:30:00Z"

    OtpVerificationRequestDto:
      type: object
      required:
        - otp
        - referenceNumber
      properties:
        otp:
          type: string
          pattern: '^[0-9]{6}$'
          description: 6-digit OTP
          example: "123456"
        referenceNumber:
          type: string
          maxLength: 50
          description: eKYC reference number
          example: "EKYC-20250624-001"

    OtpVerificationResponseDto:
      type: object
      properties:
        referenceNumber:
          type: string
          description: eKYC reference number
          example: "EKYC-20250624-001"
        status:
          $ref: '#/components/schemas/VerificationStatus'
        failureReason:
          $ref: '#/components/schemas/OtpFailureReason'
        message:
          type: string
          description: Human-readable message about the verification result
          example: "OTP verification successful"
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the response
          example: "2025-06-24T10:35:00Z"

    ErrorResponseDto:
      type: object
      properties:
        errorCode:
          type: string
          description: Error code identifying the type of error
          example: "VALIDATION_ERROR"
        message:
          type: string
          description: Human-readable error message
          example: "Invalid input data"
        details:
          type: array
          items:
            type: string
          description: Additional error details
          example: ["Aadhaar/VID must be exactly 12 digits"]
        timestamp:
          type: string
          format: date-time
          description: Timestamp when the error occurred
          example: "2025-06-24T10:30:00Z"

    IdType:
      type: string
      enum:
        - AADHAAR
        - VID
      description: Type of identification document
      example: "AADHAAR"

    ConsentType:
      type: string
      enum:
        - YES
        - NO
      description: Consent status
      example: "YES"

    VerificationStatus:
      type: string
      enum:
        - CREATED
        - IN_PROGRESS
        - VERIFIED
        - FAILED
      description: Status of the eKYC verification
      example: "IN_PROGRESS"

    OtpFailureReason:
      type: string
      enum:
        - INVALID_OTP
        - EXPIRED_OTP
        - MAX_ATTEMPTS_EXCEEDED
        - SYSTEM_ERROR
      description: Reason for OTP verification failure
      example: "INVALID_OTP"
