# Test Configuration
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop

# Disable Flyway for tests
spring.flyway.enabled=false

# Use mock API for tests
uidai.api.base-url=http://localhost:8080/api/ekyc/internal/uidai/internal/v1
uidai.api.ekyc-endpoint=/ekyc/initiate
uidai.api.otp-verification-endpoint=/ekyc/verify
uidai.api.use-mock=true

# Logging
logging.level.root=INFO
logging.level.com.ekyc=DEBUG
logging.level.org.springframework=INFO
logging.level.org.hibernate=INFO

# Security
security.pii.hash-algorithm=SHA-256
