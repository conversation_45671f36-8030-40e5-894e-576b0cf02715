package com.ekyc.verification.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;

import com.ekyc.verification.config.EkycTestConfiguration;
import com.ekyc.verification.model.dto.EkycRequestDto;
import com.ekyc.verification.model.dto.EkycResponseDto;
import com.ekyc.verification.model.dto.OtpVerificationRequestDto;
import com.ekyc.verification.model.dto.OtpVerificationResponseDto;
import com.ekyc.verification.model.enums.ConsentType;
import com.ekyc.verification.model.enums.IdType;
import com.ekyc.verification.model.enums.VerificationStatus;
import com.ekyc.verification.service.EkycService;
import com.fasterxml.jackson.databind.ObjectMapper;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Import(EkycTestConfiguration.class)
@ActiveProfiles("test")
public class EkycControllerTest {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private EkycService ekycService;

    private EkycRequestDto validRequestDto;
    private OtpVerificationRequestDto validOtpRequestDto;

    @BeforeEach
    void setUp() {
        // Set up valid eKYC request DTO
        validRequestDto = new EkycRequestDto(
                "123456789012",
                IdType.AADHAAR,
                ConsentType.YES,
                ConsentType.YES,
                "SESSION-123",
                null
        );

        // Set up valid OTP request DTO
        validOtpRequestDto = new OtpVerificationRequestDto(
                "123456",
                "EKYC-12345678"
        );
    }

    @Test
    void initiateEkyc_WithValidRequest_ShouldReturnSuccess() throws Exception {
        // Arrange
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<EkycRequestDto> request = new HttpEntity<>(validRequestDto, headers);

        // Act
        ResponseEntity<EkycResponseDto> response = restTemplate.postForEntity(
                "http://localhost:" + port + "/api/ekyc/v1/ekyc/initiate",
                request,
                EkycResponseDto.class
        );

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getReferenceNumber());
        assertEquals(VerificationStatus.IN_PROGRESS, response.getBody().getStatus());
        assertEquals("eKYC request initiated successfully", response.getBody().getMessage());
    }

    @Test
    void initiateEkyc_WithInvalidRequest_ShouldReturnBadRequest() throws Exception {
        // Arrange - create invalid request with missing required fields
        EkycRequestDto invalidRequestDto = new EkycRequestDto();
        invalidRequestDto.setIdType(IdType.AADHAAR);
        invalidRequestDto.setIdentityVerificationConsent(ConsentType.YES);
        // Missing sessionId and mobileEmailConsent

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<EkycRequestDto> request = new HttpEntity<>(invalidRequestDto, headers);

        // Act
        ResponseEntity<String> response = restTemplate.postForEntity(
                "http://localhost:" + port + "/api/ekyc/v1/ekyc/initiate",
                request,
                String.class
        );

        // Assert
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    void verifyOtp_WithValidRequest_ShouldReturnSuccess() throws Exception {
        // Arrange - First create an eKYC request to get a valid reference number
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<EkycRequestDto> ekycRequest = new HttpEntity<>(validRequestDto, headers);

        ResponseEntity<EkycResponseDto> ekycResponse = restTemplate.postForEntity(
                "http://localhost:" + port + "/api/ekyc/v1/ekyc/initiate",
                ekycRequest,
                EkycResponseDto.class
        );

        validOtpRequestDto.setReferenceNumber(ekycResponse.getBody().getReferenceNumber());
        HttpEntity<OtpVerificationRequestDto> otpRequest = new HttpEntity<>(validOtpRequestDto, headers);

        // Act
        ResponseEntity<OtpVerificationResponseDto> response = restTemplate.postForEntity(
                "http://localhost:" + port + "/api/ekyc/v1/ekyc/verify-otp",
                otpRequest,
                OtpVerificationResponseDto.class
        );

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(ekycResponse.getBody().getReferenceNumber(), response.getBody().getReferenceNumber());
        assertEquals(VerificationStatus.VERIFIED, response.getBody().getStatus());
    }

    @Test
    void verifyOtp_WithInvalidRequest_ShouldReturnBadRequest() throws Exception {
        // Arrange - create invalid request with missing required fields
        OtpVerificationRequestDto invalidRequestDto = new OtpVerificationRequestDto();
        invalidRequestDto.setReferenceNumber("EKYC-12345678");
        // Missing otpValue

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<OtpVerificationRequestDto> request = new HttpEntity<>(invalidRequestDto, headers);

        // Act
        ResponseEntity<String> response = restTemplate.postForEntity(
                "http://localhost:" + port + "/api/ekyc/v1/ekyc/verify-otp",
                request,
                String.class
        );

        // Assert
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    void getEkycStatus_WithValidReference_ShouldReturnStatus() throws Exception {
        // Arrange - First create an eKYC request to get a valid reference number
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<EkycRequestDto> request = new HttpEntity<>(validRequestDto, headers);

        ResponseEntity<EkycResponseDto> ekycResponse = restTemplate.postForEntity(
                "http://localhost:" + port + "/api/ekyc/v1/ekyc/initiate",
                request,
                EkycResponseDto.class
        );

        // Act
        ResponseEntity<EkycResponseDto> response = restTemplate.getForEntity(
                "http://localhost:" + port + "/api/ekyc/v1/ekyc/status/" + ekycResponse.getBody().getReferenceNumber(),
                EkycResponseDto.class
        );

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(ekycResponse.getBody().getReferenceNumber(), response.getBody().getReferenceNumber());
        assertEquals(VerificationStatus.IN_PROGRESS, response.getBody().getStatus());
    }

    @Test
    void getEkycStatus_WithInvalidReference_ShouldReturnBadRequest() throws Exception {
        // Act - Use an invalid reference number format
        ResponseEntity<String> response = restTemplate.getForEntity(
                "http://localhost:" + port + "/api/ekyc/v1/ekyc/status/INVALID-REF-123",
                String.class
        );

        // Assert - Should return 400 BAD_REQUEST for invalid reference format
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }
}
