package com.ekyc.verification.config;

import com.ekyc.verification.model.dto.EkycRequestDto;
import com.ekyc.verification.model.dto.OtpVerificationRequestDto;
import com.ekyc.verification.service.AuditService;
import com.ekyc.verification.service.UidaiApiClient;
import com.ekyc.verification.util.LoggingUtil;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

/**
 * Test configuration providing test implementations for dependencies.
 */
@TestConfiguration
public class EkycTestConfiguration {

    @Bean
    @Primary
    public UidaiApiClient testUidaiApiClient() {
        return new TestUidaiApiClient();
    }

    @Bean
    @Primary
    public AuditService testAuditService() {
        return new TestAuditService();
    }

    @Bean
    @Primary
    public LoggingUtil testLoggingUtil() {
        return new TestLoggingUtil();
    }

    @Bean
    @Primary
    public com.ekyc.verification.util.SecurityUtil testSecurityUtil() {
        return new TestSecurityUtil();
    }

    /**
     * Test implementation of UidaiApiClient that can be controlled for testing.
     */
    public static class TestUidaiApiClient implements UidaiApiClient {
        private boolean shouldSucceed = true;
        private String lastErrorMessage = null;

        @Override
        public boolean initiateEkyc(EkycRequestDto requestDto, String referenceNumber) {
            if (shouldSucceed) {
                return true;
            } else {
                lastErrorMessage = "API Error";
                return false;
            }
        }

        @Override
        public boolean verifyOtp(OtpVerificationRequestDto requestDto) {
            if (shouldSucceed) {
                return true;
            } else {
                lastErrorMessage = "Invalid OTP";
                return false;
            }
        }

        @Override
        public String getLastErrorMessage() {
            return lastErrorMessage;
        }

        // Test control methods
        public void setShouldSucceed(boolean shouldSucceed) {
            this.shouldSucceed = shouldSucceed;
        }

        public void setLastErrorMessage(String errorMessage) {
            this.lastErrorMessage = errorMessage;
        }
    }

    /**
     * Test implementation of AuditService that tracks calls for verification.
     */
    public static class TestAuditService implements AuditService {
        private int ekycRequestEventCount = 0;
        private int otpVerificationEventCount = 0;

        @Override
        public void logAuditEvent(String entityType, Long entityId, String action, String actor, String details) {
            // Generic audit logging - we can track this if needed
        }

        @Override
        public void logEkycRequestEvent(Long requestId, String action, String userId, String details) {
            ekycRequestEventCount++;
        }

        @Override
        public void logOtpVerificationEvent(Long requestId, String action, String userId, String details) {
            otpVerificationEventCount++;
        }

        // Test verification methods
        public int getEkycRequestEventCount() {
            return ekycRequestEventCount;
        }

        public int getOtpVerificationEventCount() {
            return otpVerificationEventCount;
        }

        public void reset() {
            ekycRequestEventCount = 0;
            otpVerificationEventCount = 0;
        }
    }

    /**
     * Test implementation of LoggingUtil that tracks calls for verification.
     */
    public static class TestLoggingUtil extends LoggingUtil {
        private int infoCallCount = 0;
        private int warnCallCount = 0;
        private int errorCallCount = 0;

        public TestLoggingUtil() {
            super(new TestSecurityUtil()); // Pass test SecurityUtil
        }

        @Override
        public void info(String message) {
            infoCallCount++;
        }

        @Override
        public void info(String referenceNumber, String message) {
            infoCallCount++;
        }

        @Override
        public void warn(String message) {
            warnCallCount++;
        }

        @Override
        public void warn(String referenceNumber, String message) {
            warnCallCount++;
        }

        @Override
        public void error(String message, Throwable throwable) {
            errorCallCount++;
        }

        @Override
        public void error(String referenceNumber, String message, Throwable throwable) {
            errorCallCount++;
        }

        // Test verification methods
        public int getInfoCallCount() {
            return infoCallCount;
        }

        public int getWarnCallCount() {
            return warnCallCount;
        }

        public int getErrorCallCount() {
            return errorCallCount;
        }

        public void reset() {
            infoCallCount = 0;
            warnCallCount = 0;
            errorCallCount = 0;
        }
    }

    /**
     * Test implementation of SecurityUtil for testing.
     */
    public static class TestSecurityUtil extends com.ekyc.verification.util.SecurityUtil {

        @Override
        public String maskPii(String data) {
            if (data == null || data.length() <= 4) {
                return "****";
            }
            return data.substring(0, 2) + "****" + data.substring(data.length() - 2);
        }

        @Override
        public String hashData(String data) {
            return "hashed_" + data;
        }
    }
}
