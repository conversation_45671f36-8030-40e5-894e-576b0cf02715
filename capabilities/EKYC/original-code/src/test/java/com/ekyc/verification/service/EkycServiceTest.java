package com.ekyc.verification.service;

import com.ekyc.verification.config.EkycTestConfiguration;
import com.ekyc.verification.exception.EkycException;
import com.ekyc.verification.model.dto.EkycRequestDto;
import com.ekyc.verification.model.dto.EkycResponseDto;
import com.ekyc.verification.model.dto.OtpVerificationRequestDto;
import com.ekyc.verification.model.dto.OtpVerificationResponseDto;
import com.ekyc.verification.model.entity.EkycRequest;
import com.ekyc.verification.model.entity.OtpVerification;
import com.ekyc.verification.model.enums.ConsentType;
import com.ekyc.verification.model.enums.IdType;
import com.ekyc.verification.model.enums.VerificationStatus;
import com.ekyc.verification.repository.EkycRequestRepository;
import com.ekyc.verification.repository.OtpVerificationRepository;
import com.ekyc.verification.util.SecurityUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

@DataJpaTest
@Import({EkycTestConfiguration.class, com.ekyc.verification.service.impl.EkycServiceImpl.class, SecurityUtil.class})
@ActiveProfiles("test")
public class EkycServiceTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private EkycService ekycService;

    @Autowired
    private EkycRequestRepository ekycRequestRepository;

    @Autowired
    private OtpVerificationRepository otpVerificationRepository;

    @Autowired
    private UidaiApiClient uidaiApiClient;

    @Autowired
    private AuditService auditService;

    @Autowired
    private SecurityUtil securityUtil;

    private EkycTestConfiguration.TestUidaiApiClient testUidaiApiClient;
    private EkycTestConfiguration.TestAuditService testAuditService;

    private EkycRequestDto validRequestDto;
    private OtpVerificationRequestDto validOtpRequestDto;
    private String referenceNumber;

    @BeforeEach
    void setUp() {
        // Get test implementations for control
        testUidaiApiClient = (EkycTestConfiguration.TestUidaiApiClient) uidaiApiClient;
        testAuditService = (EkycTestConfiguration.TestAuditService) auditService;

        // Reset test implementations
        testUidaiApiClient.setShouldSucceed(true);
        testUidaiApiClient.setLastErrorMessage(null);
        testAuditService.reset();

        // Set up valid eKYC request DTO
        validRequestDto = new EkycRequestDto(
                "123456789012",
                IdType.AADHAAR,
                ConsentType.YES,
                ConsentType.YES,
                "SESSION-123",
                null
        );

        // Set up reference number
        referenceNumber = "EKYC-12345678";

        // Set up valid OTP request DTO
        validOtpRequestDto = new OtpVerificationRequestDto(
                "123456",
                referenceNumber
        );
    }

    @Test
    void initiateEkycRequest_WithValidData_ShouldSucceed() {
        // Arrange
        testUidaiApiClient.setShouldSucceed(true);

        // Act
        EkycResponseDto response = ekycService.initiateEkycRequest(validRequestDto);

        // Assert
        assertNotNull(response);
        assertEquals(VerificationStatus.IN_PROGRESS, response.getStatus());
        assertNotNull(response.getReferenceNumber());

        // Verify data was saved to database
        assertTrue(ekycRequestRepository.findByReferenceNumber(response.getReferenceNumber()).isPresent());

        // Verify audit events were logged
        assertEquals(2, testAuditService.getEkycRequestEventCount());
    }

    @Test
    void initiateEkycRequest_WithApiFailure_ShouldReturnFailure() {
        // Arrange
        testUidaiApiClient.setShouldSucceed(false);
        testUidaiApiClient.setLastErrorMessage("API Error");

        // Act
        EkycResponseDto response = ekycService.initiateEkycRequest(validRequestDto);

        // Assert
        assertNotNull(response);
        assertEquals(VerificationStatus.FAILED, response.getStatus());
        assertNotNull(response.getReferenceNumber());

        // Verify data was saved to database
        assertTrue(ekycRequestRepository.findByReferenceNumber(response.getReferenceNumber()).isPresent());

        // Verify audit events were logged
        assertEquals(2, testAuditService.getEkycRequestEventCount());
    }

    @Test
    void initiateEkycRequest_WithInvalidIdFormat_ShouldThrowException() {
        // Arrange
        validRequestDto.setIdValue("12345"); // Invalid ID (too short)

        // Act & Assert
        assertThrows(EkycException.class, () -> ekycService.initiateEkycRequest(validRequestDto));

        // Verify no data was saved to database
        assertEquals(0, ekycRequestRepository.count());
    }

    @Test
    void initiateEkycRequest_WithoutMandatoryConsent_ShouldThrowException() {
        // Arrange
        validRequestDto.setIdentityVerificationConsent(ConsentType.NO);

        // Act & Assert
        assertThrows(EkycException.class, () -> ekycService.initiateEkycRequest(validRequestDto));

        // Verify no data was saved to database
        assertEquals(0, ekycRequestRepository.count());
    }

    @Test
    void verifyOtp_WithValidOtp_ShouldSucceed() {
        // Arrange - First create an eKYC request
        EkycResponseDto ekycResponse = ekycService.initiateEkycRequest(validRequestDto);
        validOtpRequestDto.setReferenceNumber(ekycResponse.getReferenceNumber());

        testUidaiApiClient.setShouldSucceed(true);

        // Act
        OtpVerificationResponseDto response = ekycService.verifyOtp(validOtpRequestDto);

        // Assert
        assertNotNull(response);
        assertEquals(VerificationStatus.VERIFIED, response.getStatus());
        assertEquals(ekycResponse.getReferenceNumber(), response.getReferenceNumber());

        // Verify OTP verification was saved to database
        assertTrue(otpVerificationRepository.count() > 0);
    }

    @Test
    void verifyOtp_WithInvalidOtp_ShouldReturnFailure() {
        // Arrange - First create an eKYC request
        EkycResponseDto ekycResponse = ekycService.initiateEkycRequest(validRequestDto);
        validOtpRequestDto.setReferenceNumber(ekycResponse.getReferenceNumber());

        testUidaiApiClient.setShouldSucceed(false);
        testUidaiApiClient.setLastErrorMessage("Invalid OTP");

        // Act
        OtpVerificationResponseDto response = ekycService.verifyOtp(validOtpRequestDto);

        // Assert
        assertNotNull(response);
        assertEquals(VerificationStatus.FAILED, response.getStatus());
        assertNotNull(response.getFailureReason());
        assertEquals(ekycResponse.getReferenceNumber(), response.getReferenceNumber());

        // Verify OTP verification was saved to database
        assertTrue(otpVerificationRepository.count() > 0);
    }

    @Test
    void verifyOtp_WithNonExistentRequest_ShouldThrowException() {
        // Arrange - Use a non-existent reference number
        validOtpRequestDto.setReferenceNumber("NON-EXISTENT-REF");

        // Act & Assert
        assertThrows(EkycException.class, () -> ekycService.verifyOtp(validOtpRequestDto));

        // Verify no OTP verification was saved
        assertEquals(0, otpVerificationRepository.count());
    }

    @Test
    void verifyOtp_WithMaxAttemptsExceeded_ShouldReturnFailure() {
        // Arrange - First create an eKYC request
        EkycResponseDto ekycResponse = ekycService.initiateEkycRequest(validRequestDto);
        validOtpRequestDto.setReferenceNumber(ekycResponse.getReferenceNumber());

        // Create 3 failed OTP attempts
        EkycRequest ekycRequest = ekycRequestRepository.findByReferenceNumber(ekycResponse.getReferenceNumber()).get();
        for (int i = 0; i < 3; i++) {
            OtpVerification otpVerification = new OtpVerification();
            otpVerification.setEkycRequest(ekycRequest);
            otpVerification.setOtpHash("hashedOtp123456");
            otpVerification.setStatus(VerificationStatus.FAILED);
            otpVerificationRepository.save(otpVerification);
        }

        // Act
        OtpVerificationResponseDto response = ekycService.verifyOtp(validOtpRequestDto);

        // Assert
        assertNotNull(response);
        assertEquals(VerificationStatus.FAILED, response.getStatus());
        assertNotNull(response.getFailureReason());
        assertEquals(ekycResponse.getReferenceNumber(), response.getReferenceNumber());

        // Verify no additional OTP verification was saved (still 3)
        assertEquals(3, otpVerificationRepository.count());
    }

    @Test
    void getEkycStatus_WithValidReference_ShouldReturnStatus() {
        // Arrange - First create an eKYC request
        EkycResponseDto ekycResponse = ekycService.initiateEkycRequest(validRequestDto);

        // Act
        EkycResponseDto response = ekycService.getEkycStatus(ekycResponse.getReferenceNumber());

        // Assert
        assertNotNull(response);
        assertEquals(VerificationStatus.IN_PROGRESS, response.getStatus());
        assertEquals(ekycResponse.getReferenceNumber(), response.getReferenceNumber());
    }

    @Test
    void getEkycStatus_WithNonExistentReference_ShouldThrowException() {
        // Act & Assert
        assertThrows(EkycException.class, () -> ekycService.getEkycStatus("INVALID-REF"));
    }
}
