package com.ekyc.verification.repository;

import com.ekyc.verification.model.entity.AuditLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository for AuditLog entities.
 */
@Repository
public interface AuditLogRepository extends JpaRepository<AuditLog, Long> {

    /**
     * Find all audit logs for a specific entity type and ID.
     *
     * @param entityType The entity type to search for
     * @param entityId The entity ID to search for
     * @return A list of audit logs for the specified entity type and ID
     */
    List<AuditLog> findByEntityTypeAndEntityId(String entityType, Long entityId);

    /**
     * Find all audit logs for a specific entity type.
     *
     * @param entityType The entity type to search for
     * @return A list of audit logs for the specified entity type
     */
    List<AuditLog> findByEntityType(String entityType);

    /**
     * Find all audit logs for a specific action.
     *
     * @param action The action to search for
     * @return A list of audit logs for the specified action
     */
    List<AuditLog> findByAction(String action);

    /**
     * Find all audit logs for a specific actor.
     *
     * @param actor The actor to search for
     * @return A list of audit logs for the specified actor
     */
    List<AuditLog> findByActor(String actor);

    /**
     * Find all audit logs created within a specific time range.
     *
     * @param startTime The start time of the range
     * @param endTime The end time of the range
     * @return A list of audit logs created within the specified time range
     */
    List<AuditLog> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);
}
