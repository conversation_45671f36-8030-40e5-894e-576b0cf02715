package com.ekyc.verification.repository;

import com.ekyc.verification.model.entity.EkycRequest;
import com.ekyc.verification.model.entity.OtpVerification;
import com.ekyc.verification.model.enums.VerificationStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for OtpVerification entities.
 */
@Repository
public interface OtpVerificationRepository extends JpaRepository<OtpVerification, Long> {

    /**
     * Find all OTP verifications for a specific eKYC request.
     *
     * @param ekycRequest The eKYC request to search for
     * @return A list of OTP verifications for the specified eKYC request
     */
    List<OtpVerification> findByEkycRequest(EkycRequest ekycRequest);

    /**
     * Find all OTP verifications for a specific eKYC request ID.
     *
     * @param ekycRequestId The eKYC request ID to search for
     * @return A list of OTP verifications for the specified eKYC request ID
     */
    List<OtpVerification> findByEkycRequestId(Long ekycRequestId);

    /**
     * Find all OTP verifications with a specific status.
     *
     * @param status The status to search for
     * @return A list of OTP verifications with the specified status
     */
    List<OtpVerification> findByStatus(VerificationStatus status);

    /**
     * Find the latest OTP verification for a specific eKYC request.
     *
     * @param ekycRequest The eKYC request to search for
     * @return An Optional containing the latest OTP verification if found, or empty if not found
     */
    Optional<OtpVerification> findFirstByEkycRequestOrderByCreatedAtDesc(EkycRequest ekycRequest);

    /**
     * Count the number of OTP verification attempts for a specific eKYC request.
     *
     * @param ekycRequest The eKYC request to count for
     * @return The number of OTP verification attempts
     */
    long countByEkycRequest(EkycRequest ekycRequest);
}
