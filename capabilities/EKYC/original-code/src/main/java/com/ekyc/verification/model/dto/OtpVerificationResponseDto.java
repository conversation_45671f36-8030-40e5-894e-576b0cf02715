package com.ekyc.verification.model.dto;

import com.ekyc.verification.model.enums.OtpFailureReason;
import com.ekyc.verification.model.enums.VerificationStatus;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * DTO for OTP verification response.
 */
public class OtpVerificationResponseDto {

    private String referenceNumber;
    private VerificationStatus status;
    private OtpFailureReason failureReason;
    private String message;
    private LocalDateTime timestamp;

    // Default constructor
    public OtpVerificationResponseDto() {
        this.timestamp = LocalDateTime.now();
    }

    // Constructor with required fields
    public OtpVerificationResponseDto(String referenceNumber, VerificationStatus status, String message) {
        this.referenceNumber = referenceNumber;
        this.status = status;
        this.message = message;
        this.timestamp = LocalDateTime.now();
    }

    // Constructor with all fields
    public OtpVerificationResponseDto(String referenceNumber, VerificationStatus status, 
                                     OtpFailureReason failureReason, String message) {
        this.referenceNumber = referenceNumber;
        this.status = status;
        this.failureReason = failureReason;
        this.message = message;
        this.timestamp = LocalDateTime.now();
    }

    // Static factory methods for common responses
    public static OtpVerificationResponseDto success(String referenceNumber) {
        return new OtpVerificationResponseDto(referenceNumber, VerificationStatus.VERIFIED, 
                "OTP verification successful");
    }

    public static OtpVerificationResponseDto failure(String referenceNumber, OtpFailureReason reason) {
        return new OtpVerificationResponseDto(referenceNumber, VerificationStatus.FAILED, 
                reason, "OTP verification failed: " + reason);
    }

    // Getters and Setters
    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public VerificationStatus getStatus() {
        return status;
    }

    public void setStatus(VerificationStatus status) {
        this.status = status;
    }

    public OtpFailureReason getFailureReason() {
        return failureReason;
    }

    public void setFailureReason(OtpFailureReason failureReason) {
        this.failureReason = failureReason;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OtpVerificationResponseDto that = (OtpVerificationResponseDto) o;
        return Objects.equals(referenceNumber, that.referenceNumber) &&
                status == that.status;
    }

    @Override
    public int hashCode() {
        return Objects.hash(referenceNumber, status);
    }

    @Override
    public String toString() {
        return "OtpVerificationResponseDto{" +
                "referenceNumber='" + referenceNumber + '\'' +
                ", status=" + status +
                ", failureReason=" + failureReason +
                ", message='" + message + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}
