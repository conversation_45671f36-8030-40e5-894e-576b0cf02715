package com.ekyc.verification.service;

import com.ekyc.verification.model.dto.EkycRequestDto;
import com.ekyc.verification.model.dto.EkycResponseDto;
import com.ekyc.verification.model.dto.OtpVerificationRequestDto;
import com.ekyc.verification.model.dto.OtpVerificationResponseDto;

/**
 * Service interface for eKYC verification operations.
 */
public interface EkycService {

    /**
     * Initiates an eKYC verification request.
     *
     * @param requestDto The eKYC request DTO
     * @return The eKYC response DTO
     */
    EkycResponseDto initiateEkycRequest(EkycRequestDto requestDto);

    /**
     * Verifies an OTP for an eKYC request.
     *
     * @param requestDto The OTP verification request DTO
     * @return The OTP verification response DTO
     */
    OtpVerificationResponseDto verifyOtp(OtpVerificationRequestDto requestDto);

    /**
     * Gets the status of an eKYC request.
     *
     * @param referenceNumber The reference number of the eKYC request
     * @return The eKYC response DTO
     */
    EkycResponseDto getEkycStatus(String referenceNumber);
}
