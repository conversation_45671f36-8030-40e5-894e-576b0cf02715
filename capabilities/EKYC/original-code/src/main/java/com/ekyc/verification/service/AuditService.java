package com.ekyc.verification.service;

/**
 * Service interface for audit logging operations.
 */
public interface AuditService {

    /**
     * Logs an audit event for an entity.
     *
     * @param entityType The type of entity being audited
     * @param entityId The ID of the entity being audited
     * @param action The action being performed
     * @param actor The actor performing the action
     * @param details Additional details about the action
     */
    void logAuditEvent(String entityType, Long entityId, String action, String actor, String details);

    /**
     * Logs an audit event for an eKYC request.
     *
     * @param ekycRequestId The ID of the eKYC request
     * @param action The action being performed
     * @param actor The actor performing the action
     * @param details Additional details about the action
     */
    void logEkycRequestEvent(Long ekycRequestId, String action, String actor, String details);

    /**
     * Logs an audit event for an OTP verification.
     *
     * @param otpVerificationId The ID of the OTP verification
     * @param action The action being performed
     * @param actor The actor performing the action
     * @param details Additional details about the action
     */
    void logOtpVerificationEvent(Long otpVerificationId, String action, String actor, String details);
}
