package com.ekyc.verification.model.enums;

/**
 * Enum representing the types of identification that can be used for eKYC verification.
 */
public enum IdType {
    AADHAAR,
    VID;
    
    /**
     * Validates if the provided ID value matches the format requirements for this ID type.
     *
     * @param idValue The ID value to validate
     * @return true if the ID value is valid for this type, false otherwise
     */
    public boolean isValidFormat(String idValue) {
        if (idValue == null || idValue.length() != 12) {
            return false;
        }
        
        // Check if the ID consists only of digits
        return idValue.matches("\\d{12}");
    }
}
