package com.ekyc.verification.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ekyc.verification.model.dto.EkycRequestDto;
import com.ekyc.verification.model.dto.EkycResponseDto;
import com.ekyc.verification.model.dto.OtpVerificationRequestDto;
import com.ekyc.verification.model.dto.OtpVerificationResponseDto;
import com.ekyc.verification.service.EkycService;
import com.ekyc.verification.util.LoggingUtil;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

/**
 * Controller for eKYC verification endpoints.
 */
@RestController
@RequestMapping("/v1/ekyc")
@Validated
public class EkycController {

    private final EkycService ekycService;
    private final LoggingUtil loggingUtil;

    @Autowired
    public EkycController(EkycService ekycService, LoggingUtil loggingUtil) {
        this.ekycService = ekycService;
        this.loggingUtil = loggingUtil;
    }

    /**
     * Initiates an eKYC verification request.
     *
     * @param requestDto The eKYC request DTO
     * @return The eKYC response DTO
     */
    @PostMapping("/initiate")
    public ResponseEntity<EkycResponseDto> initiateEkyc(@Valid @RequestBody EkycRequestDto requestDto) {
        loggingUtil.info("Received eKYC initiation request");
        EkycResponseDto response = ekycService.initiateEkycRequest(requestDto);
        return ResponseEntity.ok(response);
    }

    /**
     * Verifies an OTP for an eKYC request.
     *
     * @param requestDto The OTP verification request DTO
     * @return The OTP verification response DTO
     */
    @PostMapping("/verify-otp")
    public ResponseEntity<OtpVerificationResponseDto> verifyOtp(@Valid @RequestBody OtpVerificationRequestDto requestDto) {
        loggingUtil.info("Received OTP verification request for reference: " + requestDto.getReferenceNumber());
        OtpVerificationResponseDto response = ekycService.verifyOtp(requestDto);
        return ResponseEntity.ok(response);
    }

    /**
     * Gets the status of an eKYC request.
     *
     * @param referenceNumber The reference number of the eKYC request
     * @return The eKYC response DTO
     */
    @GetMapping("/status/{referenceNumber}")
    public ResponseEntity<EkycResponseDto> getEkycStatus(
            @PathVariable @NotBlank(message = "Reference number is required") String referenceNumber) {
        loggingUtil.info("Received status check request for reference: " + referenceNumber);
        EkycResponseDto response = ekycService.getEkycStatus(referenceNumber);
        return ResponseEntity.ok(response);
    }
}
