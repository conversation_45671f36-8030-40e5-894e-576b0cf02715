package com.ekyc.verification.model.enums;

/**
 * Enum representing the possible reasons for OTP verification failure.
 */
public enum OtpFailureReason {
    /**
     * The OTP provided was invalid.
     */
    INVALID_OTP,
    
    /**
     * The OTP has expired.
     */
    EXPIRED_OTP,
    
    /**
     * The maximum number of OTP attempts has been exceeded.
     */
    MAX_ATTEMPTS_EXCEEDED,
    
    /**
     * The eKYC request associated with the OTP verification was not found.
     */
    INVALID_REQUEST,
    
    /**
     * An error occurred while communicating with the external API.
     */
    API_ERROR,
    
    /**
     * An unexpected error occurred during OTP verification.
     */
    UNKNOWN_ERROR
}
