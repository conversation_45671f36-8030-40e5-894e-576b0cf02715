package com.ekyc.verification.model.enums;

/**
 * Enum representing the possible values for consent.
 */
public enum ConsentType {
    YES(true),
    NO(false);
    
    private final boolean value;
    
    ConsentType(boolean value) {
        this.value = value;
    }
    
    public boolean getValue() {
        return value;
    }
    
    /**
     * Converts a string representation to a ConsentType enum.
     *
     * @param value The string value to convert
     * @return The corresponding ConsentType, or null if not valid
     */
    public static ConsentType fromString(String value) {
        if (value == null) {
            return null;
        }
        
        try {
            return ConsentType.valueOf(value.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}
