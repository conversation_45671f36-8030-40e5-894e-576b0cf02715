package com.ekyc.verification.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;

/**
 * OpenAPI configuration for EKYC Verification Service.
 */
@Configuration
public class OpenApiConfig implements WebMvcConfigurer {

    @Value("${server.port:8101}")
    private String serverPort;

    @Bean
    public OpenAPI ekycOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("EKYC Verification Service API")
                        .description("API for Aadhaar-based eKYC verification service that provides secure identity verification through OTP-based authentication")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("EKYC Team")
                                .email("<EMAIL>")
                                .url("https://company.com/ekyc"))
                        .license(new License()
                                .name("Proprietary")
                                .url("https://company.com/license")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + "/api/ekyc")
                                .description("Development server"),
                        new Server()
                                .url("https://api.company.com/api/ekyc")
                                .description("Production server")
                ));
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .exposedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
}
