package com.ekyc.verification.exception;

import com.ekyc.verification.model.dto.ErrorResponseDto;
import com.ekyc.verification.util.LoggingUtil;
import jakarta.validation.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import java.util.ArrayList;
import java.util.List;

/**
 * Global exception handler for the application.
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private final LoggingUtil loggingUtil;

    @Autowired
    public GlobalExceptionHandler(LoggingUtil loggingUtil) {
        this.loggingUtil = loggingUtil;
    }

    /**
     * Handles EkycException.
     *
     * @param ex The exception
     * @param request The web request
     * @return The error response
     */
    @ExceptionHandler(EkycException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResponseDto> handleEkycException(EkycException ex, WebRequest request) {
        loggingUtil.error("EkycException: " + ex.getMessage(), ex);
        
        ErrorResponseDto errorResponse = new ErrorResponseDto(
                "EKYC-ERR-001",
                ex.getMessage()
        );
        
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handles validation exceptions.
     *
     * @param ex The exception
     * @param request The web request
     * @return The error response
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResponseDto> handleValidationExceptions(
            MethodArgumentNotValidException ex, WebRequest request) {
        
        List<String> details = new ArrayList<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            details.add(fieldName + ": " + errorMessage);
        });
        
        loggingUtil.error("Validation error: " + details, ex);
        
        ErrorResponseDto errorResponse = new ErrorResponseDto(
                "EKYC-ERR-002",
                "Validation failed",
                details
        );
        
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handles constraint violation exceptions.
     *
     * @param ex The exception
     * @param request The web request
     * @return The error response
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResponseDto> handleConstraintViolationException(
            ConstraintViolationException ex, WebRequest request) {
        
        List<String> details = new ArrayList<>();
        ex.getConstraintViolations().forEach(violation -> {
            details.add(violation.getPropertyPath() + ": " + violation.getMessage());
        });
        
        loggingUtil.error("Constraint violation: " + details, ex);
        
        ErrorResponseDto errorResponse = new ErrorResponseDto(
                "EKYC-ERR-003",
                "Constraint violation",
                details
        );
        
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handles all other exceptions.
     *
     * @param ex The exception
     * @param request The web request
     * @return The error response
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ErrorResponseDto> handleAllExceptions(Exception ex, WebRequest request) {
        loggingUtil.error("Unexpected error: " + ex.getMessage(), ex);
        
        ErrorResponseDto errorResponse = new ErrorResponseDto(
                "EKYC-ERR-999",
                "An unexpected error occurred"
        );
        
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
