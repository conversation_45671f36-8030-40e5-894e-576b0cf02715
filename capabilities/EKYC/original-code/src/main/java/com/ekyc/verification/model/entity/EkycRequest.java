package com.ekyc.verification.model.entity;

import com.ekyc.verification.model.enums.IdType;
import com.ekyc.verification.model.enums.VerificationStatus;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Entity representing an eKYC verification request.
 */
@Entity
@Table(name = "ekyc_request")
public class EkycRequest {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "reference_number", nullable = false, unique = true, length = 50)
    private String referenceNumber;

    @Column(name = "session_id", nullable = false, length = 50)
    private String sessionId;

    @Column(name = "parent_process_id", length = 50)
    private String parentProcessId;

    @Column(name = "id_value", nullable = false, length = 12)
    private String idValue;

    @Enumerated(EnumType.STRING)
    @Column(name = "id_type", nullable = false, length = 10)
    private IdType idType;

    @Column(name = "identity_verification_consent", nullable = false)
    private boolean identityVerificationConsent;

    @Column(name = "mobile_email_consent", nullable = false)
    private boolean mobileEmailConsent;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private VerificationStatus status;

    @Column(name = "error_details")
    private String errorDetails;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Default constructor required by JPA
    public EkycRequest() {
    }

    // Constructor with required fields
    public EkycRequest(String referenceNumber, String sessionId, String idValue, IdType idType,
                      boolean identityVerificationConsent, boolean mobileEmailConsent) {
        this.referenceNumber = referenceNumber;
        this.sessionId = sessionId;
        this.idValue = idValue;
        this.idType = idType;
        this.identityVerificationConsent = identityVerificationConsent;
        this.mobileEmailConsent = mobileEmailConsent;
        this.status = VerificationStatus.CREATED;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getParentProcessId() {
        return parentProcessId;
    }

    public void setParentProcessId(String parentProcessId) {
        this.parentProcessId = parentProcessId;
    }

    public String getIdValue() {
        return idValue;
    }

    public void setIdValue(String idValue) {
        this.idValue = idValue;
    }

    public IdType getIdType() {
        return idType;
    }

    public void setIdType(IdType idType) {
        this.idType = idType;
    }

    public boolean isIdentityVerificationConsent() {
        return identityVerificationConsent;
    }

    public void setIdentityVerificationConsent(boolean identityVerificationConsent) {
        this.identityVerificationConsent = identityVerificationConsent;
    }

    public boolean isMobileEmailConsent() {
        return mobileEmailConsent;
    }

    public void setMobileEmailConsent(boolean mobileEmailConsent) {
        this.mobileEmailConsent = mobileEmailConsent;
    }

    public VerificationStatus getStatus() {
        return status;
    }

    public void setStatus(VerificationStatus status) {
        this.status = status;
        this.updatedAt = LocalDateTime.now();
    }

    public String getErrorDetails() {
        return errorDetails;
    }

    public void setErrorDetails(String errorDetails) {
        this.errorDetails = errorDetails;
        this.updatedAt = LocalDateTime.now();
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @PrePersist
    protected void onCreate() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EkycRequest that = (EkycRequest) o;
        return Objects.equals(id, that.id) &&
               Objects.equals(referenceNumber, that.referenceNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, referenceNumber);
    }

    @Override
    public String toString() {
        return "EkycRequest{" +
                "id=" + id +
                ", referenceNumber='" + referenceNumber + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", idType=" + idType +
                ", status=" + status +
                '}';
    }
}
