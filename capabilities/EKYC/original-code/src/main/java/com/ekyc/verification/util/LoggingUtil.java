package com.ekyc.verification.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Utility class for audit-ready logging with PII masking.
 */
@Component
public class LoggingUtil {

    private static final Logger logger = LoggerFactory.getLogger(LoggingUtil.class);

    private final SecurityUtil securityUtil;

    @Autowired
    public LoggingUtil(SecurityUtil securityUtil) {
        this.securityUtil = securityUtil;
    }

    /**
     * Logs an informational message.
     *
     * @param message The message to log
     */
    public void info(String message) {
        logger.info(message);
    }

    /**
     * Logs an informational message with a reference number.
     *
     * @param referenceNumber The reference number for tracking
     * @param message The message to log
     */
    public void info(String referenceNumber, String message) {
        logger.info("[{}] {}", referenceNumber, message);
    }

    /**
     * Logs a debug message.
     *
     * @param message The message to log
     */
    public void debug(String message) {
        logger.debug(message);
    }

    /**
     * Logs a debug message with a reference number.
     *
     * @param referenceNumber The reference number for tracking
     * @param message The message to log
     */
    public void debug(String referenceNumber, String message) {
        logger.debug("[{}] {}", referenceNumber, message);
    }

    /**
     * Logs a warning message.
     *
     * @param message The message to log
     */
    public void warn(String message) {
        logger.warn(message);
    }

    /**
     * Logs a warning message with a reference number.
     *
     * @param referenceNumber The reference number for tracking
     * @param message The message to log
     */
    public void warn(String referenceNumber, String message) {
        logger.warn("[{}] {}", referenceNumber, message);
    }

    /**
     * Logs an error message.
     *
     * @param message The message to log
     * @param throwable The exception to log
     */
    public void error(String message, Throwable throwable) {
        logger.error(message, throwable);
    }

    /**
     * Logs an error message with a reference number.
     *
     * @param referenceNumber The reference number for tracking
     * @param message The message to log
     * @param throwable The exception to log
     */
    public void error(String referenceNumber, String message, Throwable throwable) {
        logger.error("[{}] {}", referenceNumber, message, throwable);
    }

    /**
     * Logs a message with PII data masked.
     *
     * @param message The message to log
     * @param piiData The PII data to mask
     */
    public void infoWithMaskedPii(String message, String piiData) {
        logger.info("{}: {}", message, securityUtil.maskPii(piiData));
    }

    /**
     * Logs a message with PII data masked and a reference number.
     *
     * @param referenceNumber The reference number for tracking
     * @param message The message to log
     * @param piiData The PII data to mask
     */
    public void infoWithMaskedPii(String referenceNumber, String message, String piiData) {
        logger.info("[{}] {}: {}", referenceNumber, message, securityUtil.maskPii(piiData));
    }
}
