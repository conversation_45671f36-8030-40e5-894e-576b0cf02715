package com.ekyc.verification.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.UUID;

/**
 * Utility class for security-related operations.
 */
@Component
public class SecurityUtil {

    @Value("${security.pii.hash-algorithm:SHA-256}")
    private String hashAlgorithm;

    private final SecureRandom secureRandom = new SecureRandom();

    /**
     * Generates a unique reference number for tracking.
     *
     * @return A unique reference number
     */
    public String generateReferenceNumber() {
        return "EKYC-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    /**
     * Hashes sensitive data using the configured hash algorithm.
     *
     * @param data The data to hash
     * @return The hashed data
     */
    public String hashData(String data) {
        if (data == null || data.isEmpty()) {
            return null;
        }

        try {
            MessageDigest digest = MessageDigest.getInstance(hashAlgorithm);
            byte[] hash = digest.digest(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hash);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Error hashing data: " + e.getMessage(), e);
        }
    }

    /**
     * Masks sensitive data for logging.
     *
     * @param data The data to mask
     * @return The masked data
     */
    public String maskPii(String data) {
        if (data == null || data.isEmpty()) {
            return data;
        }

        int length = data.length();
        if (length <= 4) {
            return "****";
        }

        // Keep first 2 and last 2 characters, mask the rest
        return data.substring(0, 2) + "*".repeat(length - 4) + data.substring(length - 2);
    }

    /**
     * Validates if the provided OTP is in the correct format.
     *
     * @param otp The OTP to validate
     * @return true if the OTP is valid, false otherwise
     */
    public boolean isValidOtp(String otp) {
        return otp != null && otp.matches("\\d{6}");
    }
}
