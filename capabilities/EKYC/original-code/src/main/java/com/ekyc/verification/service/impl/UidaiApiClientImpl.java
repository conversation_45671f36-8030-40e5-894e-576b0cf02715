package com.ekyc.verification.service.impl;

import com.ekyc.verification.model.dto.EkycRequestDto;
import com.ekyc.verification.model.dto.OtpVerificationRequestDto;
import com.ekyc.verification.service.UidaiApiClient;
import com.ekyc.verification.util.LoggingUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * Implementation of the UidaiApiClient interface.
 */
@Service
public class UidaiApiClientImpl implements UidaiApiClient {

    @Value("${uidai.api.base-url}")
    private String baseUrl;

    @Value("${uidai.api.ekyc-endpoint}")
    private String ekycEndpoint;

    @Value("${uidai.api.otp-verification-endpoint}")
    private String otpVerificationEndpoint;

    @Value("${uidai.api.use-mock:false}")
    private boolean useMock;

    private final RestTemplate restTemplate;
    private final LoggingUtil loggingUtil;

    private String lastErrorMessage;

    @Autowired
    public UidaiApiClientImpl(LoggingUtil loggingUtil) {
        this.restTemplate = new RestTemplate();
        this.loggingUtil = loggingUtil;
    }

    @Override
    public boolean initiateEkyc(EkycRequestDto requestDto, String referenceNumber) {
        if (useMock) {
            return mockInitiateEkyc(requestDto, referenceNumber);
        }

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("idValue", requestDto.getIdValue());
            requestMap.put("idType", requestDto.getIdType().toString());
            requestMap.put("referenceNumber", referenceNumber);
            requestMap.put("sessionId", requestDto.getSessionId());
            requestMap.put("identityVerificationConsent", requestDto.getIdentityVerificationConsent().getValue());
            requestMap.put("mobileEmailConsent", requestDto.getMobileEmailConsent().getValue());

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestMap, headers);
            
            String url = baseUrl + ekycEndpoint;
            loggingUtil.info(referenceNumber, "Calling UIDAI API for eKYC initiation: " + url);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                boolean success = Boolean.TRUE.equals(responseBody.get("success"));
                
                if (!success && responseBody.containsKey("errorMessage")) {
                    lastErrorMessage = (String) responseBody.get("errorMessage");
                    loggingUtil.warn(referenceNumber, "UIDAI API returned error: " + lastErrorMessage);
                }
                
                return success;
            }
            
            lastErrorMessage = "Unexpected response from UIDAI API";
            loggingUtil.warn(referenceNumber, lastErrorMessage);
            return false;
            
        } catch (RestClientException e) {
            lastErrorMessage = "Error calling UIDAI API: " + e.getMessage();
            loggingUtil.error(referenceNumber, lastErrorMessage, e);
            return false;
        }
    }

    @Override
    public boolean verifyOtp(OtpVerificationRequestDto requestDto) {
        if (useMock) {
            return mockVerifyOtp(requestDto);
        }

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("otp", requestDto.getOtp());
            requestMap.put("referenceNumber", requestDto.getReferenceNumber());

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestMap, headers);
            
            String url = baseUrl + otpVerificationEndpoint;
            loggingUtil.info(requestDto.getReferenceNumber(), "Calling UIDAI API for OTP verification: " + url);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                boolean success = Boolean.TRUE.equals(responseBody.get("success"));
                
                if (!success && responseBody.containsKey("errorMessage")) {
                    lastErrorMessage = (String) responseBody.get("errorMessage");
                    loggingUtil.warn(requestDto.getReferenceNumber(), "UIDAI API returned error: " + lastErrorMessage);
                }
                
                return success;
            }
            
            lastErrorMessage = "Unexpected response from UIDAI API";
            loggingUtil.warn(requestDto.getReferenceNumber(), lastErrorMessage);
            return false;
            
        } catch (RestClientException e) {
            lastErrorMessage = "Error calling UIDAI API: " + e.getMessage();
            loggingUtil.error(requestDto.getReferenceNumber(), lastErrorMessage, e);
            return false;
        }
    }

    @Override
    public String getLastErrorMessage() {
        return lastErrorMessage;
    }

    /**
     * Mock implementation of the initiateEkyc method for testing.
     *
     * @param requestDto The eKYC request DTO
     * @param referenceNumber The reference number for the request
     * @return true if the request was successful, false otherwise
     */
    private boolean mockInitiateEkyc(EkycRequestDto requestDto, String referenceNumber) {
        loggingUtil.info(referenceNumber, "Using mock implementation for eKYC initiation");
        
        // Simulate API call
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // Always succeed in mock mode
        return true;
    }

    /**
     * Mock implementation of the verifyOtp method for testing.
     *
     * @param requestDto The OTP verification request DTO
     * @return true if the verification was successful, false otherwise
     */
    private boolean mockVerifyOtp(OtpVerificationRequestDto requestDto) {
        loggingUtil.info(requestDto.getReferenceNumber(), "Using mock implementation for OTP verification");
        
        // Simulate API call
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // Succeed if OTP is "123456" (for testing), fail otherwise
        boolean success = "123456".equals(requestDto.getOtp());
        
        if (!success) {
            lastErrorMessage = "Invalid OTP";
            loggingUtil.warn(requestDto.getReferenceNumber(), "Mock API: " + lastErrorMessage);
        }
        
        return success;
    }
}
