package com.ekyc.verification.service.impl;

import com.ekyc.verification.model.entity.AuditLog;
import com.ekyc.verification.repository.AuditLogRepository;
import com.ekyc.verification.service.AuditService;
import com.ekyc.verification.util.LoggingUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Implementation of the AuditService interface.
 */
@Service
public class AuditServiceImpl implements AuditService {

    private static final String EKYC_REQUEST_ENTITY = "EKYC_REQUEST";
    private static final String OTP_VERIFICATION_ENTITY = "OTP_VERIFICATION";

    private final AuditLogRepository auditLogRepository;
    private final LoggingUtil loggingUtil;

    @Autowired
    public AuditServiceImpl(AuditLogRepository auditLogRepository, LoggingUtil loggingUtil) {
        this.auditLogRepository = auditLogRepository;
        this.loggingUtil = loggingUtil;
    }

    @Override
    @Transactional
    public void logAuditEvent(String entityType, Long entityId, String action, String actor, String details) {
        AuditLog auditLog = new AuditLog(entityType, entityId, action, actor, details);
        auditLogRepository.save(auditLog);
        loggingUtil.info("Audit event logged: " + entityType + " - " + action);
    }

    @Override
    @Transactional
    public void logEkycRequestEvent(Long ekycRequestId, String action, String actor, String details) {
        logAuditEvent(EKYC_REQUEST_ENTITY, ekycRequestId, action, actor, details);
    }

    @Override
    @Transactional
    public void logOtpVerificationEvent(Long otpVerificationId, String action, String actor, String details) {
        logAuditEvent(OTP_VERIFICATION_ENTITY, otpVerificationId, action, actor, details);
    }
}
