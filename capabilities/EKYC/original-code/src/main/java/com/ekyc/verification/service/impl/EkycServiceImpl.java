package com.ekyc.verification.service.impl;

import com.ekyc.verification.exception.EkycException;
import com.ekyc.verification.model.dto.EkycRequestDto;
import com.ekyc.verification.model.dto.EkycResponseDto;
import com.ekyc.verification.model.dto.OtpVerificationRequestDto;
import com.ekyc.verification.model.dto.OtpVerificationResponseDto;
import com.ekyc.verification.model.entity.EkycRequest;
import com.ekyc.verification.model.entity.OtpVerification;
import com.ekyc.verification.model.enums.OtpFailureReason;
import com.ekyc.verification.model.enums.VerificationStatus;
import com.ekyc.verification.repository.EkycRequestRepository;
import com.ekyc.verification.repository.OtpVerificationRepository;
import com.ekyc.verification.service.AuditService;
import com.ekyc.verification.service.EkycService;
import com.ekyc.verification.service.UidaiApiClient;
import com.ekyc.verification.util.LoggingUtil;
import com.ekyc.verification.util.SecurityUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Implementation of the EkycService interface.
 */
@Service
public class EkycServiceImpl implements EkycService {

    private static final String SYSTEM_USER = "SYSTEM";
    private static final int MAX_OTP_ATTEMPTS = 3;

    private final EkycRequestRepository ekycRequestRepository;
    private final OtpVerificationRepository otpVerificationRepository;
    private final UidaiApiClient uidaiApiClient;
    private final AuditService auditService;
    private final SecurityUtil securityUtil;
    private final LoggingUtil loggingUtil;

    @Autowired
    public EkycServiceImpl(EkycRequestRepository ekycRequestRepository,
                          OtpVerificationRepository otpVerificationRepository,
                          UidaiApiClient uidaiApiClient,
                          AuditService auditService,
                          SecurityUtil securityUtil,
                          LoggingUtil loggingUtil) {
        this.ekycRequestRepository = ekycRequestRepository;
        this.otpVerificationRepository = otpVerificationRepository;
        this.uidaiApiClient = uidaiApiClient;
        this.auditService = auditService;
        this.securityUtil = securityUtil;
        this.loggingUtil = loggingUtil;
    }

    @Override
    @Transactional
    public EkycResponseDto initiateEkycRequest(EkycRequestDto requestDto) {
        // Validate ID format
        if (!requestDto.getIdType().isValidFormat(requestDto.getIdValue())) {
            throw new EkycException("Invalid ID format for type " + requestDto.getIdType());
        }

        // Validate mandatory consent
        if (!requestDto.getIdentityVerificationConsent().getValue()) {
            throw new EkycException("Identity verification consent is mandatory");
        }

        // Generate reference number
        String referenceNumber = securityUtil.generateReferenceNumber();
        loggingUtil.info(referenceNumber, "Initiating eKYC request");
        loggingUtil.infoWithMaskedPii(referenceNumber, "ID Value", requestDto.getIdValue());

        // Create and persist eKYC request
        EkycRequest ekycRequest = new EkycRequest(
                referenceNumber,
                requestDto.getSessionId(),
                requestDto.getIdValue(),
                requestDto.getIdType(),
                requestDto.getIdentityVerificationConsent().getValue(),
                requestDto.getMobileEmailConsent().getValue()
        );

        if (requestDto.getParentProcessId() != null) {
            ekycRequest.setParentProcessId(requestDto.getParentProcessId());
        }

        ekycRequest = ekycRequestRepository.save(ekycRequest);

        // Log audit event
        auditService.logEkycRequestEvent(
                ekycRequest.getId(),
                "CREATE",
                SYSTEM_USER,
                "eKYC request created with reference number: " + referenceNumber
        );

        // Call UIDAI API
        boolean apiSuccess = uidaiApiClient.initiateEkyc(requestDto, referenceNumber);

        if (apiSuccess) {
            // Update status to IN_PROGRESS
            ekycRequest.setStatus(VerificationStatus.IN_PROGRESS);
            ekycRequestRepository.save(ekycRequest);

            auditService.logEkycRequestEvent(
                    ekycRequest.getId(),
                    "UPDATE",
                    SYSTEM_USER,
                    "eKYC request status updated to IN_PROGRESS"
            );

            loggingUtil.info(referenceNumber, "eKYC request initiated successfully");
            return EkycResponseDto.success(referenceNumber, "eKYC request initiated successfully");
        } else {
            // Update status to FAILED
            ekycRequest.setStatus(VerificationStatus.FAILED);
            ekycRequest.setErrorDetails(uidaiApiClient.getLastErrorMessage());
            ekycRequestRepository.save(ekycRequest);

            auditService.logEkycRequestEvent(
                    ekycRequest.getId(),
                    "UPDATE",
                    SYSTEM_USER,
                    "eKYC request failed: " + uidaiApiClient.getLastErrorMessage()
            );

            loggingUtil.warn(referenceNumber, "eKYC request failed: " + uidaiApiClient.getLastErrorMessage());
            return EkycResponseDto.failure(referenceNumber, uidaiApiClient.getLastErrorMessage());
        }
    }

    @Override
    @Transactional
    public OtpVerificationResponseDto verifyOtp(OtpVerificationRequestDto requestDto) {
        // Validate OTP format
        if (!securityUtil.isValidOtp(requestDto.getOtp())) {
            throw new EkycException("Invalid OTP format");
        }

        // Find eKYC request
        EkycRequest ekycRequest = ekycRequestRepository.findByReferenceNumber(requestDto.getReferenceNumber())
                .orElseThrow(() -> new EkycException("eKYC request not found for reference number: " + requestDto.getReferenceNumber()));

        String referenceNumber = ekycRequest.getReferenceNumber();
        loggingUtil.info(referenceNumber, "Verifying OTP");

        // Check if maximum attempts exceeded
        long attemptCount = otpVerificationRepository.countByEkycRequest(ekycRequest);
        if (attemptCount >= MAX_OTP_ATTEMPTS) {
            loggingUtil.warn(referenceNumber, "Maximum OTP attempts exceeded");
            return OtpVerificationResponseDto.failure(referenceNumber, OtpFailureReason.MAX_ATTEMPTS_EXCEEDED);
        }

        // Create and persist OTP verification
        OtpVerification otpVerification = new OtpVerification(
                ekycRequest,
                securityUtil.hashData(requestDto.getOtp())
        );
        otpVerification = otpVerificationRepository.save(otpVerification);

        // Log audit event
        auditService.logOtpVerificationEvent(
                otpVerification.getId(),
                "CREATE",
                SYSTEM_USER,
                "OTP verification attempt created for eKYC request: " + referenceNumber
        );

        // Call UIDAI API
        boolean apiSuccess = uidaiApiClient.verifyOtp(requestDto);

        if (apiSuccess) {
            // Update OTP verification status
            otpVerification.setStatus(VerificationStatus.VERIFIED);
            otpVerificationRepository.save(otpVerification);

            // Update eKYC request status
            ekycRequest.setStatus(VerificationStatus.VERIFIED);
            ekycRequestRepository.save(ekycRequest);

            // Log audit events
            auditService.logOtpVerificationEvent(
                    otpVerification.getId(),
                    "UPDATE",
                    SYSTEM_USER,
                    "OTP verification successful"
            );

            auditService.logEkycRequestEvent(
                    ekycRequest.getId(),
                    "UPDATE",
                    SYSTEM_USER,
                    "eKYC request verified successfully"
            );

            loggingUtil.info(referenceNumber, "OTP verification successful");
            return OtpVerificationResponseDto.success(referenceNumber);
        } else {
            // Determine failure reason
            OtpFailureReason failureReason = determineFailureReason(uidaiApiClient.getLastErrorMessage());

            // Update OTP verification status
            otpVerification.setStatus(VerificationStatus.FAILED);
            otpVerification.setFailureReason(failureReason);
            otpVerificationRepository.save(otpVerification);

            // Log audit event
            auditService.logOtpVerificationEvent(
                    otpVerification.getId(),
                    "UPDATE",
                    SYSTEM_USER,
                    "OTP verification failed: " + failureReason
            );

            loggingUtil.warn(referenceNumber, "OTP verification failed: " + failureReason);
            return OtpVerificationResponseDto.failure(referenceNumber, failureReason);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public EkycResponseDto getEkycStatus(String referenceNumber) {
        EkycRequest ekycRequest = ekycRequestRepository.findByReferenceNumber(referenceNumber)
                .orElseThrow(() -> new EkycException("eKYC request not found for reference number: " + referenceNumber));

        loggingUtil.info(referenceNumber, "Retrieved eKYC status: " + ekycRequest.getStatus());

        String message = "eKYC request status: " + ekycRequest.getStatus();
        if (ekycRequest.getStatus() == VerificationStatus.FAILED && ekycRequest.getErrorDetails() != null) {
            message += " - " + ekycRequest.getErrorDetails();
        }

        return new EkycResponseDto(referenceNumber, ekycRequest.getStatus(), message);
    }

    /**
     * Determines the OTP failure reason based on the error message.
     *
     * @param errorMessage The error message from the API
     * @return The OTP failure reason
     */
    private OtpFailureReason determineFailureReason(String errorMessage) {
        if (errorMessage == null) {
            return OtpFailureReason.UNKNOWN_ERROR;
        }

        String lowerCaseError = errorMessage.toLowerCase();

        if (lowerCaseError.contains("invalid") && lowerCaseError.contains("otp")) {
            return OtpFailureReason.INVALID_OTP;
        } else if (lowerCaseError.contains("expire")) {
            return OtpFailureReason.EXPIRED_OTP;
        } else if (lowerCaseError.contains("attempt")) {
            return OtpFailureReason.MAX_ATTEMPTS_EXCEEDED;
        } else if (lowerCaseError.contains("request") && lowerCaseError.contains("invalid")) {
            return OtpFailureReason.INVALID_REQUEST;
        } else if (lowerCaseError.contains("api") || lowerCaseError.contains("service")) {
            return OtpFailureReason.API_ERROR;
        } else {
            return OtpFailureReason.UNKNOWN_ERROR;
        }
    }
}
