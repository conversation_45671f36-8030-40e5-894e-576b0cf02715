package com.ekyc.verification.model.dto;

import com.ekyc.verification.model.enums.VerificationStatus;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * DTO for eKYC response.
 */
public class EkycResponseDto {

    private String referenceNumber;
    private VerificationStatus status;
    private String message;
    private LocalDateTime timestamp;

    // Default constructor
    public EkycResponseDto() {
        this.timestamp = LocalDateTime.now();
    }

    // Constructor with required fields
    public EkycResponseDto(String referenceNumber, VerificationStatus status, String message) {
        this.referenceNumber = referenceNumber;
        this.status = status;
        this.message = message;
        this.timestamp = LocalDateTime.now();
    }

    // Static factory methods for common responses
    public static EkycResponseDto success(String referenceNumber, String message) {
        return new EkycResponseDto(referenceNumber, VerificationStatus.IN_PROGRESS, message);
    }

    public static EkycResponseDto failure(String referenceNumber, String message) {
        return new EkycResponseDto(referenceNumber, VerificationStatus.FAILED, message);
    }

    // Getters and Setters
    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public VerificationStatus getStatus() {
        return status;
    }

    public void setStatus(VerificationStatus status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EkycResponseDto that = (EkycResponseDto) o;
        return Objects.equals(referenceNumber, that.referenceNumber) &&
                status == that.status;
    }

    @Override
    public int hashCode() {
        return Objects.hash(referenceNumber, status);
    }

    @Override
    public String toString() {
        return "EkycResponseDto{" +
                "referenceNumber='" + referenceNumber + '\'' +
                ", status=" + status +
                ", message='" + message + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}
