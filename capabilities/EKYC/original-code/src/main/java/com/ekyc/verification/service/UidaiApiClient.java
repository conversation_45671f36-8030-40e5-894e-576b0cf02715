package com.ekyc.verification.service;

import com.ekyc.verification.model.dto.EkycRequestDto;
import com.ekyc.verification.model.dto.OtpVerificationRequestDto;

/**
 * Interface for the UIDAI API client.
 */
public interface UidaiApiClient {

    /**
     * Initiates an eKYC request with the UIDAI API.
     *
     * @param requestDto The eKYC request DTO
     * @param referenceNumber The reference number for the request
     * @return true if the request was successful, false otherwise
     */
    boolean initiateEkyc(EkycRequestDto requestDto, String referenceNumber);

    /**
     * Verifies an OTP with the UIDAI API.
     *
     * @param requestDto The OTP verification request DTO
     * @return true if the verification was successful, false otherwise
     */
    boolean verifyOtp(OtpVerificationRequestDto requestDto);

    /**
     * Gets the error message from the last API call.
     *
     * @return The error message
     */
    String getLastErrorMessage();
}
