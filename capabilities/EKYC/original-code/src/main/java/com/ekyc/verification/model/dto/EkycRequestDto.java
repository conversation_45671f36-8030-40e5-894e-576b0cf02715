package com.ekyc.verification.model.dto;

import com.ekyc.verification.model.enums.ConsentType;
import com.ekyc.verification.model.enums.IdType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.util.Objects;

/**
 * DTO for eKYC request initiation.
 */
public class EkycRequestDto {

    @NotBlank(message = "ID value is required")
    @Size(min = 12, max = 12, message = "ID must be exactly 12 digits")
    @Pattern(regexp = "\\d{12}", message = "ID must contain only digits")
    private String idValue;

    @NotNull(message = "ID type is required")
    private IdType idType;

    @NotNull(message = "Identity verification consent is required")
    private ConsentType identityVerificationConsent;

    @NotNull(message = "Mobile/email consent is required")
    private ConsentType mobileEmailConsent;

    @NotBlank(message = "Session ID is required")
    @Size(max = 50, message = "Session ID cannot exceed 50 characters")
    private String sessionId;

    @Size(max = 50, message = "Parent process ID cannot exceed 50 characters")
    private String parentProcessId;

    // Default constructor
    public EkycRequestDto() {
    }

    // Constructor with all fields
    public EkycRequestDto(String idValue, IdType idType, ConsentType identityVerificationConsent,
                         ConsentType mobileEmailConsent, String sessionId, String parentProcessId) {
        this.idValue = idValue;
        this.idType = idType;
        this.identityVerificationConsent = identityVerificationConsent;
        this.mobileEmailConsent = mobileEmailConsent;
        this.sessionId = sessionId;
        this.parentProcessId = parentProcessId;
    }

    // Getters and Setters
    public String getIdValue() {
        return idValue;
    }

    public void setIdValue(String idValue) {
        this.idValue = idValue;
    }

    public IdType getIdType() {
        return idType;
    }

    public void setIdType(IdType idType) {
        this.idType = idType;
    }

    public ConsentType getIdentityVerificationConsent() {
        return identityVerificationConsent;
    }

    public void setIdentityVerificationConsent(ConsentType identityVerificationConsent) {
        this.identityVerificationConsent = identityVerificationConsent;
    }

    public ConsentType getMobileEmailConsent() {
        return mobileEmailConsent;
    }

    public void setMobileEmailConsent(ConsentType mobileEmailConsent) {
        this.mobileEmailConsent = mobileEmailConsent;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getParentProcessId() {
        return parentProcessId;
    }

    public void setParentProcessId(String parentProcessId) {
        this.parentProcessId = parentProcessId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EkycRequestDto that = (EkycRequestDto) o;
        return Objects.equals(idValue, that.idValue) &&
                idType == that.idType &&
                Objects.equals(sessionId, that.sessionId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(idValue, idType, sessionId);
    }

    @Override
    public String toString() {
        return "EkycRequestDto{" +
                "idValue='" + idValue + '\'' +
                ", idType=" + idType +
                ", identityVerificationConsent=" + identityVerificationConsent +
                ", mobileEmailConsent=" + mobileEmailConsent +
                ", sessionId='" + sessionId + '\'' +
                '}';
    }
}
