package com.ekyc.verification.model.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.util.Objects;

/**
 * DTO for OTP verification request.
 */
public class OtpVerificationRequestDto {

    @NotBlank(message = "OTP is required")
    @Size(min = 6, max = 6, message = "OTP must be exactly 6 digits")
    @Pattern(regexp = "\\d{6}", message = "OTP must contain only digits")
    private String otp;

    @NotBlank(message = "eKYC reference number is required")
    @Size(max = 50, message = "Reference number cannot exceed 50 characters")
    private String referenceNumber;

    // Default constructor
    public OtpVerificationRequestDto() {
    }

    // Constructor with all fields
    public OtpVerificationRequestDto(String otp, String referenceNumber) {
        this.otp = otp;
        this.referenceNumber = referenceNumber;
    }

    // Getters and Setters
    public String getOtp() {
        return otp;
    }

    public void setOtp(String otp) {
        this.otp = otp;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OtpVerificationRequestDto that = (OtpVerificationRequestDto) o;
        return Objects.equals(otp, that.otp) &&
                Objects.equals(referenceNumber, that.referenceNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(otp, referenceNumber);
    }

    @Override
    public String toString() {
        return "OtpVerificationRequestDto{" +
                "otp='******'" +
                ", referenceNumber='" + referenceNumber + '\'' +
                '}';
    }
}
