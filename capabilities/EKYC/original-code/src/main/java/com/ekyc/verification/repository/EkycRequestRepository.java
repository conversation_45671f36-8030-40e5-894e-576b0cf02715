package com.ekyc.verification.repository;

import com.ekyc.verification.model.entity.EkycRequest;
import com.ekyc.verification.model.enums.VerificationStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for EkycRequest entities.
 */
@Repository
public interface EkycRequestRepository extends JpaRepository<EkycRequest, Long> {

    /**
     * Find an eKYC request by its reference number.
     *
     * @param referenceNumber The reference number to search for
     * @return An Optional containing the eKYC request if found, or empty if not found
     */
    Optional<EkycRequest> findByReferenceNumber(String referenceNumber);

    /**
     * Find all eKYC requests with a specific status.
     *
     * @param status The status to search for
     * @return A list of eKYC requests with the specified status
     */
    List<EkycRequest> findByStatus(VerificationStatus status);

    /**
     * Find all eKYC requests for a specific session ID.
     *
     * @param sessionId The session ID to search for
     * @return A list of eKYC requests for the specified session ID
     */
    List<EkycRequest> findBySessionId(String sessionId);

    /**
     * Find all eKYC requests for a specific parent process ID.
     *
     * @param parentProcessId The parent process ID to search for
     * @return A list of eKYC requests for the specified parent process ID
     */
    List<EkycRequest> findByParentProcessId(String parentProcessId);
}
