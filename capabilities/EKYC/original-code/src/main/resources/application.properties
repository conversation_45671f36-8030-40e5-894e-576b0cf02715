# Application
spring.application.name=ekyc-verification
server.port=${SERVER_PORT:8101}
server.servlet.context-path=/api/ekyc

# Database Configuration
spring.datasource.url=*************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true

# Flyway
spring.flyway.enabled=true
spring.flyway.baseline-on-migrate=true
spring.flyway.locations=classpath:db/migration

# External API Configuration (configurable)
uidai.api.base-url=http://localhost:8201/api/ekyc/internal/uidai/internal/v1
uidai.api.ekyc-endpoint=/ekyc/initiate
uidai.api.otp-verification-endpoint=/ekyc/verify
uidai.api.use-mock=true

# Logging
logging.level.root=INFO
logging.level.com.ekyc=DEBUG
logging.level.org.springframework=INFO
logging.level.org.hibernate=INFO

# Security
security.pii.hash-algorithm=SHA-256

# OpenAPI Documentation
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.swagger-ui.filter=true
