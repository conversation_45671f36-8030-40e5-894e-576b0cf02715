-- Create ekyc_request table
CREATE TABLE ekyc_request (
    id BIGSERIAL PRIMARY KEY,
    reference_number VARCHAR(50) NOT NULL UNIQUE,
    session_id VARCHAR(50) NOT NULL,
    parent_process_id VARCHAR(50),
    id_value VARCHAR(12) NOT NULL,
    id_type VARCHAR(10) NOT NULL,
    identity_verification_consent BOOLEAN NOT NULL,
    mobile_email_consent BOOLEAN NOT NULL,
    status VARCHAR(20) NOT NULL,
    error_details TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create otp_verification table
CREATE TABLE otp_verification (
    id BIGSERIAL PRIMARY KEY,
    ekyc_request_id BIGINT NOT NULL,
    otp_hash VARCHAR(255) NOT NULL,
    status VARCHAR(20) NOT NULL,
    failure_reason VARCHAR(50),
    response_hash VARCHAR(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_ekyc_request FOREIGN KEY (ekyc_request_id) REFERENCES ekyc_request(id)
);

-- Create audit_log table
CREATE TABLE audit_log (
    id BIGSERIAL PRIMARY KEY,
    entity_type VARCHAR(50) NOT NULL,
    entity_id BIGINT NOT NULL,
    action VARCHAR(20) NOT NULL,
    actor VARCHAR(50) NOT NULL,
    details TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_ekyc_request_reference_number ON ekyc_request(reference_number);
CREATE INDEX idx_ekyc_request_session_id ON ekyc_request(session_id);
CREATE INDEX idx_otp_verification_ekyc_request_id ON otp_verification(ekyc_request_id);
CREATE INDEX idx_audit_log_entity ON audit_log(entity_type, entity_id);
