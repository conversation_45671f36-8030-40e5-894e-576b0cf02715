openapi: 3.0.3
info:
  title: Mock UIDAI API Service
  description: |
    Mock implementation of UIDAI API for Aadhaar-based OTP and eKYC flows in development and testing environments.
    
    This mock service simulates:
    - UIDAI eKYC and OTP flows for application development and QA
    - Success, failure, expired OTP, and system error scenarios
    - Realistic API structure, behavior, and timing simulation
    - PII masking and audit logging capabilities
    
    **Test Scenarios**:
    - Aadhaar/VID ending with even digit → OTP initiation succeeds
    - Aadhaar/VID ending with odd digit → OTP initiation fails
    - Aadhaar/VID starting with "999" → System error (HTTP 500)
    - OTP "123456" → Verification success
    - Other OTPs → Invalid OTP error
    - Reference ID "REFEXPIRED001" → Expired OTP error
    
    **Traceability**: All responses include X-Trace-Id header for traceability.
  version: 1.0.0
  contact:
    name: Mock UIDAI Team
    email: <EMAIL>
    url: https://company.com/mock-uidai
  license:
    name: Proprietary
    url: https://company.com/license

servers:
  - url: http://localhost:8201/api/ekyc/internal
    description: Development server
  - url: https://mock-uidai.company.com/api/ekyc/internal
    description: Testing server



paths:
  /uidai/internal/v1/ekyc/initiate:
    post:
      tags:
        - UIDAI eKYC
      summary: Initiate OTP for eKYC
      description: |
        Initiates OTP generation for Aadhaar-based eKYC verification.
        
        **Mock Behavior**:
        - Even-ending Aadhaar/VID: Success (OTP_SENT)
        - Odd-ending Aadhaar/VID: Failure (OTP_GENERATION_FAILED)
        - Starting with "999": System error (UIDAI_SERVICE_FAILURE)
      operationId: initiateOtp
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OtpInitiateRequest'
            examples:
              success_case:
                summary: Success case (even-ending Aadhaar)
                value:
                  aadhaarOrVid: "123456789012"
                  transactionId: "TXN100001"
              failure_case:
                summary: Failure case (odd-ending Aadhaar)
                value:
                  aadhaarOrVid: "123456789013"
                  transactionId: "TXN100002"
              system_error:
                summary: System error case
                value:
                  aadhaarOrVid: "************"
                  transactionId: "TXN100003"
      responses:
        '200':
          description: OTP initiation successful
          headers:
            X-Trace-Id:
              description: Unique trace identifier for request tracking
              schema:
                type: string
                format: uuid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OtpInitiateResponse'
              examples:
                success:
                  summary: OTP sent successfully
                  value:
                    status: "OTP_SENT"
                    referenceId: "REF1234567890"
                    timestamp: "2025-06-24T10:30:00Z"
        '400':
          description: OTP generation failed
          headers:
            X-Trace-Id:
              description: Unique trace identifier for request tracking
              schema:
                type: string
                format: uuid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                otp_failed:
                  summary: OTP generation failed
                  value:
                    errorCode: "OTP_GENERATION_FAILED"
                    errorMessage: "Unable to generate OTP for the provided Aadhaar/VID"
                    timestamp: "2025-06-24T10:30:00Z"
        '500':
          description: System error
          headers:
            X-Trace-Id:
              description: Unique trace identifier for request tracking
              schema:
                type: string
                format: uuid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                system_error:
                  summary: UIDAI service failure
                  value:
                    errorCode: "UIDAI_SERVICE_FAILURE"
                    errorMessage: "UIDAI service is temporarily unavailable"
                    timestamp: "2025-06-24T10:30:00Z"

  /uidai/internal/v1/ekyc/verify:
    post:
      tags:
        - UIDAI eKYC
      summary: Verify OTP and retrieve KYC data
      description: |
        Verifies the OTP and returns KYC data upon successful verification.
        
        **Mock Behavior**:
        - OTP "123456": Success with KYC data
        - Other OTPs: Invalid OTP error
        - Reference ID "REFEXPIRED001": Expired OTP error
      operationId: verifyOtp
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OtpVerifyRequest'
            examples:
              success_case:
                summary: Valid OTP
                value:
                  referenceId: "REF1234567890"
                  otp: "123456"
              invalid_otp:
                summary: Invalid OTP
                value:
                  referenceId: "REF1234567890"
                  otp: "654321"
              expired_otp:
                summary: Expired OTP scenario
                value:
                  referenceId: "REFEXPIRED001"
                  otp: "123456"
      responses:
        '200':
          description: OTP verification successful
          headers:
            X-Trace-Id:
              description: Unique trace identifier for request tracking
              schema:
                type: string
                format: uuid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OtpVerifyResponse'
              examples:
                success:
                  summary: Verification successful with KYC data
                  value:
                    status: "VERIFIED"
                    kycData:
                      name: "Ravi Kumar"
                      dob: "1987-01-01"
                      gender: "M"
                    timestamp: "2025-06-24T10:35:00Z"
        '400':
          description: OTP verification failed
          headers:
            X-Trace-Id:
              description: Unique trace identifier for request tracking
              schema:
                type: string
                format: uuid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                invalid_otp:
                  summary: Invalid OTP
                  value:
                    errorCode: "INVALID_OTP"
                    errorMessage: "The provided OTP is invalid"
                    timestamp: "2025-06-24T10:35:00Z"
                expired_otp:
                  summary: Expired OTP
                  value:
                    errorCode: "EXPIRED_OTP"
                    errorMessage: "The OTP has expired"
                    timestamp: "2025-06-24T10:35:00Z"
        '500':
          description: System error
          headers:
            X-Trace-Id:
              description: Unique trace identifier for request tracking
              schema:
                type: string
                format: uuid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /uidai/internal/v1/admin/requests:
    get:
      tags:
        - Admin
      summary: Get request/response history
      description: |
        Retrieves the audit log of all API requests and responses with PII masking.
        Useful for debugging and monitoring mock service usage.
      operationId: getRequestHistory
      responses:
        '200':
          description: Request history retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ApiLogEntry'
              examples:
                history:
                  summary: Sample request history
                  value:
                    - traceId: "550e8400-e29b-41d4-a716-************"
                      endpoint: "/uidai/internal/v1/ekyc/initiate"
                      method: "POST"
                      requestBody: "{\"aadhaarOrVid\":\"12XXXXXXXX12\",\"transactionId\":\"TXN100001\"}"
                      responseBody: "{\"status\":\"OTP_SENT\",\"referenceId\":\"REF1234567890\"}"
                      statusCode: 200
                      timestamp: "2025-06-24T10:30:00Z"

  /uidai/internal/v1/admin/config:
    post:
      tags:
        - Admin
      summary: Configure mock behavior
      description: |
        Updates the mock service configuration to override default behavior.
        Allows forcing specific responses for testing scenarios.
      operationId: updateConfig
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MockConfigRequest'
            examples:
              force_success:
                summary: Force OTP success
                value:
                  forceOtpSuccess: true
                  latencyMs: 200
              force_failure:
                summary: Force OTP failure
                value:
                  forceOtpFailure: true
              custom_kyc:
                summary: Custom KYC data
                value:
                  customKycData:
                    name: "Test User"
                    dob: "1990-01-01"
                    gender: "F"
      responses:
        '200':
          description: Configuration updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Configuration updated successfully"

  /uidai/internal/v1/admin/reset:
    post:
      tags:
        - Admin
      summary: Reset mock service
      description: |
        Clears all in-memory logs and resets configuration to default values.
        Useful for starting fresh test scenarios.
      operationId: resetService
      responses:
        '200':
          description: Service reset successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Service reset successfully"

  /v1/ekyc/mock/health:
    get:
      tags:
        - Health Check
      summary: Health check endpoint
      description: Returns the health status of the Mock UIDAI service
      operationId: healthCheck
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "UP"
                  service:
                    type: string
                    example: "EKYC Mock"
                  timestamp:
                    type: string
                    format: date-time
                    example: "2025-06-24T10:30:00Z"
                  version:
                    type: string
                    example: "1.0.0"
              example:
                status: "UP"
                service: "EKYC Mock"
                timestamp: "2025-06-24T10:30:00Z"
                version: "1.0.0"

components:
  schemas:
    OtpInitiateRequest:
      type: object
      required:
        - aadhaarOrVid
        - transactionId
      properties:
        aadhaarOrVid:
          type: string
          pattern: '^[0-9]{12}$'
          description: 12-digit Aadhaar number or VID
          example: "123456789012"
        transactionId:
          type: string
          description: Unique transaction identifier
          example: "TXN100001"

    OtpInitiateResponse:
      type: object
      properties:
        status:
          type: string
          enum:
            - OTP_SENT
            - OTP_GENERATION_FAILED
          description: Status of OTP initiation
          example: "OTP_SENT"
        referenceId:
          type: string
          description: Reference ID for OTP verification (null on failure)
          example: "REF1234567890"
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the response
          example: "2025-06-24T10:30:00Z"

    OtpVerifyRequest:
      type: object
      required:
        - referenceId
        - otp
      properties:
        referenceId:
          type: string
          description: Reference ID from OTP initiation
          example: "REF1234567890"
        otp:
          type: string
          pattern: '^[0-9]{6}$'
          description: 6-digit OTP
          example: "123456"

    OtpVerifyResponse:
      type: object
      properties:
        status:
          type: string
          enum:
            - VERIFIED
            - INVALID_OTP
            - EXPIRED_OTP
          description: Status of OTP verification
          example: "VERIFIED"
        kycData:
          $ref: '#/components/schemas/KycData'
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the response
          example: "2025-06-24T10:35:00Z"

    KycData:
      type: object
      properties:
        name:
          type: string
          description: Full name from Aadhaar
          example: "Ravi Kumar"
        dob:
          type: string
          format: date
          description: Date of birth (YYYY-MM-DD)
          example: "1987-01-01"
        gender:
          type: string
          enum:
            - M
            - F
            - O
          description: Gender (M/F/O)
          example: "M"

    ErrorResponse:
      type: object
      properties:
        errorCode:
          type: string
          description: Error code identifying the type of error
          example: "INVALID_OTP"
        errorMessage:
          type: string
          description: Human-readable error message
          example: "The provided OTP is invalid"
        timestamp:
          type: string
          format: date-time
          description: Timestamp when the error occurred
          example: "2025-06-24T10:30:00Z"

    ApiLogEntry:
      type: object
      properties:
        traceId:
          type: string
          format: uuid
          description: Unique trace identifier
          example: "550e8400-e29b-41d4-a716-************"
        endpoint:
          type: string
          description: API endpoint called
          example: "/uidai/internal/v1/ekyc/initiate"
        method:
          type: string
          description: HTTP method
          example: "POST"
        requestBody:
          type: string
          description: Request body (PII masked)
          example: "{\"aadhaarOrVid\":\"12XXXXXXXX12\",\"transactionId\":\"TXN100001\"}"
        responseBody:
          type: string
          description: Response body
          example: "{\"status\":\"OTP_SENT\",\"referenceId\":\"REF1234567890\"}"
        statusCode:
          type: integer
          description: HTTP status code
          example: 200
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the request
          example: "2025-06-24T10:30:00Z"

    MockConfigRequest:
      type: object
      properties:
        forceOtpSuccess:
          type: boolean
          description: Force all OTP operations to succeed
          example: true
        forceOtpFailure:
          type: boolean
          description: Force all OTP operations to fail
          example: false
        forceSystemError:
          type: boolean
          description: Force system errors
          example: false
        latencyMs:
          type: integer
          minimum: 0
          maximum: 10000
          description: Custom latency in milliseconds
          example: 200
        customKycData:
          $ref: '#/components/schemas/KycData'
