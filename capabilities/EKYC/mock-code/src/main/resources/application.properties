# Application Configuration
spring.application.name=mock-uidai-service
server.port=${SERVER_PORT:8201}
server.servlet.context-path=/api/ekyc/internal



# Logging Configuration
logging.level.root=INFO
logging.level.com.uidai.mock=DEBUG
logging.level.org.springframework=INFO

# Mock Service Configuration
mock.uidai.latency.min-ms=100
mock.uidai.latency.max-ms=500

# OpenAPI Documentation
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.swagger-ui.filter=true
