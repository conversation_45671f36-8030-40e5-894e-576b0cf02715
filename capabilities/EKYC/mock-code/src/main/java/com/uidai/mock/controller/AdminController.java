package com.uidai.mock.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.uidai.mock.model.ApiLogEntry;
import com.uidai.mock.model.MockConfigRequest;
import com.uidai.mock.service.MockConfigService;
import com.uidai.mock.service.TraceLoggerService;


/**
 * Controller for admin operations.
 */
@RestController
@RequestMapping("/uidai/internal/v1/admin")

public class AdminController {

    private final TraceLoggerService traceLoggerService;
    private final MockConfigService mockConfigService;

    @Autowired
    public AdminController(TraceLoggerService traceLoggerService, MockConfigService mockConfigService) {
        this.traceLoggerService = traceLoggerService;
        this.mockConfigService = mockConfigService;
    }

    /**
     * Gets the request/response history.
     *
     * @return The list of API log entries
     */
    @GetMapping("/requests")

    public ResponseEntity<List<ApiLogEntry>> getRequestHistory() {
        return ResponseEntity.ok(traceLoggerService.getApiLogs());
    }

    /**
     * Updates the mock configuration.
     *
     * @param configRequest The configuration request
     * @return A success message
     */
    @PostMapping("/config")

    public ResponseEntity<Map<String, String>> updateConfig(@RequestBody MockConfigRequest configRequest) {
        mockConfigService.updateConfig(configRequest);
        return ResponseEntity.ok(Map.of("message", "Configuration updated successfully"));
    }

    /**
     * Resets the mock service.
     *
     * @return A success message
     */
    @PostMapping("/reset")

    public ResponseEntity<Map<String, String>> resetService() {
        mockConfigService.resetConfig();
        traceLoggerService.clearLogs();
        return ResponseEntity.ok(Map.of("message", "Service reset successfully"));
    }
}
