package com.uidai.mock.model;

import java.time.Instant;

/**
 * Response model for OTP initiation.
 */
public class OtpInitiateResponse {

    private String status;
    private String referenceId;
    private Instant timestamp;

    // Default constructor
    public OtpInitiateResponse() {
        this.timestamp = Instant.now();
    }

    // Constructor with required fields
    public OtpInitiateResponse(String status, String referenceId) {
        this.status = status;
        this.referenceId = referenceId;
        this.timestamp = Instant.now();
    }

    // Static factory methods for common responses
    public static OtpInitiateResponse success(String referenceId) {
        return new OtpInitiateResponse("OTP_SENT", referenceId);
    }

    public static OtpInitiateResponse failure() {
        return new OtpInitiateResponse("OTP_GENERATION_FAILED", null);
    }

    // Getters and Setters
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public Instant getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Instant timestamp) {
        this.timestamp = timestamp;
    }
}
