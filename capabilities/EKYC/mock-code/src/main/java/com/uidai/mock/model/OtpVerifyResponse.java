package com.uidai.mock.model;

import java.time.Instant;

/**
 * Response model for OTP verification.
 */
public class OtpVerifyResponse {

    private String status;
    private KycData kycData;
    private Instant timestamp;

    // Default constructor
    public OtpVerifyResponse() {
        this.timestamp = Instant.now();
    }

    // Constructor with required fields
    public OtpVerifyResponse(String status) {
        this.status = status;
        this.timestamp = Instant.now();
    }

    // Constructor with all fields
    public OtpVerifyResponse(String status, KycData kycData) {
        this.status = status;
        this.kycData = kycData;
        this.timestamp = Instant.now();
    }

    // Static factory methods for common responses
    public static OtpVerifyResponse success(KycData kycData) {
        return new OtpVerifyResponse("VERIFIED", kycData);
    }

    public static OtpVerifyResponse invalidOtp() {
        return new OtpVerifyResponse("INVALID_OTP");
    }

    public static OtpVerifyResponse expiredOtp() {
        return new OtpVerifyResponse("EXPIRED_OTP");
    }

    // Getters and Setters
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public KycData getKycData() {
        return kycData;
    }

    public void setKycData(KycData kycData) {
        this.kycData = kycData;
    }

    public Instant getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Instant timestamp) {
        this.timestamp = timestamp;
    }
}
