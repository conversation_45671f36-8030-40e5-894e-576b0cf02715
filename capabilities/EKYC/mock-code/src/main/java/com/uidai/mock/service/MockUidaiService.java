package com.uidai.mock.service;

import com.uidai.mock.model.KycData;
import com.uidai.mock.model.OtpInitiateRequest;
import com.uidai.mock.model.OtpInitiateResponse;
import com.uidai.mock.model.OtpVerifyRequest;
import com.uidai.mock.model.OtpVerifyResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * Service for simulating UIDAI API behavior.
 */
@Service
public class MockUidaiService {

    private static final Logger logger = LoggerFactory.getLogger(MockUidaiService.class);
    private static final String EXPIRED_REFERENCE_ID = "REFEXPIRED001";
    private static final String VALID_OTP = "123456";

    private final MockConfigService configService;

    @Autowired
    public MockUidaiService(MockConfigService configService) {
        this.configService = configService;
    }

    /**
     * Simulates latency in API responses.
     */
    private void simulateLatency() {
        try {
            Thread.sleep(configService.getLatencyMs());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Initiates an OTP request.
     *
     * @param request The OTP initiate request
     * @return The OTP initiate response
     * @throws RuntimeException if a system error is forced
     */
    public OtpInitiateResponse initiateOtp(OtpInitiateRequest request) {
        simulateLatency();
        
        // Check for forced behaviors
        if (configService.isForceSystemError()) {
            logger.info("Forcing system error for OTP initiation");
            throw new RuntimeException("UIDAI_SERVICE_FAILURE");
        }
        
        if (configService.isForceOtpSuccess()) {
            logger.info("Forcing OTP initiation success");
            return OtpInitiateResponse.success(generateReferenceId());
        }
        
        if (configService.isForceOtpFailure()) {
            logger.info("Forcing OTP initiation failure");
            return OtpInitiateResponse.failure();
        }
        
        // Check if Aadhaar/VID starts with "999" for system error
        if (request.getAadhaarOrVid().startsWith("999")) {
            logger.info("Aadhaar/VID starts with 999, triggering system error");
            throw new RuntimeException("UIDAI_SERVICE_FAILURE");
        }
        
        // Check if Aadhaar/VID ends with an even digit for success
        char lastDigit = request.getAadhaarOrVid().charAt(request.getAadhaarOrVid().length() - 1);
        boolean isEven = (lastDigit - '0') % 2 == 0;
        
        if (isEven) {
            logger.info("Aadhaar/VID ends with even digit, OTP initiation successful");
            return OtpInitiateResponse.success(generateReferenceId());
        } else {
            logger.info("Aadhaar/VID ends with odd digit, OTP initiation failed");
            return OtpInitiateResponse.failure();
        }
    }

    /**
     * Verifies an OTP.
     *
     * @param request The OTP verify request
     * @return The OTP verify response
     * @throws RuntimeException if a system error is forced
     */
    public OtpVerifyResponse verifyOtp(OtpVerifyRequest request) {
        simulateLatency();
        
        // Check for forced behaviors
        if (configService.isForceSystemError()) {
            logger.info("Forcing system error for OTP verification");
            throw new RuntimeException("UIDAI_SERVICE_FAILURE");
        }
        
        if (configService.isForceOtpSuccess()) {
            logger.info("Forcing OTP verification success");
            return OtpVerifyResponse.success(getKycData());
        }
        
        if (configService.isForceOtpFailure()) {
            logger.info("Forcing OTP verification failure");
            return OtpVerifyResponse.invalidOtp();
        }
        
        // Check for expired reference ID
        if (EXPIRED_REFERENCE_ID.equals(request.getReferenceId())) {
            logger.info("Reference ID matches expired pattern, returning EXPIRED_OTP");
            return OtpVerifyResponse.expiredOtp();
        }
        
        // Check if OTP is valid
        if (VALID_OTP.equals(request.getOtp())) {
            logger.info("Valid OTP provided, verification successful");
            return OtpVerifyResponse.success(getKycData());
        } else {
            logger.info("Invalid OTP provided, verification failed");
            return OtpVerifyResponse.invalidOtp();
        }
    }

    /**
     * Generates a reference ID.
     *
     * @return A new reference ID
     */
    private String generateReferenceId() {
        return "REF" + UUID.randomUUID().toString().substring(0, 10).toUpperCase();
    }

    /**
     * Gets KYC data for successful verifications.
     *
     * @return The KYC data
     */
    private KycData getKycData() {
        // Use custom KYC data if available
        if (configService.getCustomKycData() != null) {
            return configService.getCustomKycData();
        }
        
        // Default KYC data
        return new KycData("Ravi Kumar", "1987-01-01", "M");
    }
}
