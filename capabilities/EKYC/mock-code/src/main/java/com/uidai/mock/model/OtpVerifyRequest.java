package com.uidai.mock.model;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * Request model for OTP verification.
 */
public class OtpVerifyRequest {

    @NotBlank(message = "Reference ID is required")
    private String referenceId;

    @NotBlank(message = "OTP is required")
    @Size(min = 6, max = 6, message = "OTP must be exactly 6 digits")
    @Pattern(regexp = "\\d{6}", message = "OTP must contain only digits")
    private String otp;

    // Default constructor
    public OtpVerifyRequest() {
    }

    // Constructor with all fields
    public OtpVerifyRequest(String referenceId, String otp) {
        this.referenceId = referenceId;
        this.otp = otp;
    }

    // Getters and Setters
    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public String getOtp() {
        return otp;
    }

    public void setOtp(String otp) {
        this.otp = otp;
    }

    @Override
    public String toString() {
        return "OtpVerifyRequest{" +
                "referenceId='" + referenceId + '\'' +
                ", otp='******'" +
                '}';
    }
}
