package com.uidai.mock.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.uidai.mock.model.ApiLogEntry;
import com.uidai.mock.model.ErrorResponse;
import com.uidai.mock.model.OtpInitiateRequest;
import com.uidai.mock.model.OtpInitiateResponse;
import com.uidai.mock.model.OtpVerifyRequest;
import com.uidai.mock.model.OtpVerifyResponse;
import com.uidai.mock.service.MockUidaiService;
import com.uidai.mock.service.TraceLoggerService;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * Controller for mock UIDAI API endpoints.
 */
@RestController
@RequestMapping("/uidai/internal/v1/ekyc")
public class MockUidaiController {

    private static final Logger logger = LoggerFactory.getLogger(MockUidaiController.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private final MockUidaiService mockUidaiService;
    private final TraceLoggerService traceLoggerService;

    @Autowired
    public MockUidaiController(MockUidaiService mockUidaiService, TraceLoggerService traceLoggerService) {
        this.mockUidaiService = mockUidaiService;
        this.traceLoggerService = traceLoggerService;
    }

    /**
     * Initiates an OTP request.
     *
     * @param request The OTP initiate request
     * @param httpRequest The HTTP request
     * @return The OTP initiate response
     */
    @PostMapping("/initiate")
    public ResponseEntity<?> initiateOtp(@Valid @RequestBody OtpInitiateRequest request, HttpServletRequest httpRequest) {
        String traceId = traceLoggerService.generateTraceId();
        
        // Log request
        ApiLogEntry logEntry = null;
        try {
            logEntry = traceLoggerService.logRequest(
                traceId, 
                httpRequest.getRequestURI(), 
                httpRequest.getMethod(), 
                objectMapper.writeValueAsString(request)
            );
        } catch (JsonProcessingException e) {
            logger.error("Error serializing request", e);
        }
        
        try {
            // Process request
            OtpInitiateResponse response = mockUidaiService.initiateOtp(request);
            
            // Log response
            if (logEntry != null) {
                try {
                    traceLoggerService.logResponse(
                        logEntry, 
                        objectMapper.writeValueAsString(response), 
                        HttpStatus.OK.value()
                    );
                } catch (JsonProcessingException e) {
                    logger.error("Error serializing response", e);
                }
            }
            
            return ResponseEntity.ok()
                    .header("X-Trace-Id", traceId)
                    .body(response);
        } catch (Exception e) {
            // Handle system errors
            ErrorResponse errorResponse = new ErrorResponse("UIDAI_SERVICE_FAILURE", e.getMessage());
            
            // Log error response
            if (logEntry != null) {
                try {
                    traceLoggerService.logResponse(
                        logEntry, 
                        objectMapper.writeValueAsString(errorResponse), 
                        HttpStatus.INTERNAL_SERVER_ERROR.value()
                    );
                } catch (JsonProcessingException ex) {
                    logger.error("Error serializing error response", ex);
                }
            }
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header("X-Trace-Id", traceId)
                    .body(errorResponse);
        }
    }

    /**
     * Verifies an OTP.
     *
     * @param request The OTP verify request
     * @param httpRequest The HTTP request
     * @return The OTP verify response
     */
    @PostMapping("/verify")
    public ResponseEntity<?> verifyOtp(@Valid @RequestBody OtpVerifyRequest request, HttpServletRequest httpRequest) {
        String traceId = traceLoggerService.generateTraceId();
        
        // Log request
        ApiLogEntry logEntry = null;
        try {
            logEntry = traceLoggerService.logRequest(
                traceId, 
                httpRequest.getRequestURI(), 
                httpRequest.getMethod(), 
                objectMapper.writeValueAsString(request)
            );
        } catch (JsonProcessingException e) {
            logger.error("Error serializing request", e);
        }
        
        try {
            // Process request
            OtpVerifyResponse response = mockUidaiService.verifyOtp(request);
            
            // Determine status code based on response
            HttpStatus status = HttpStatus.OK;
            if ("INVALID_OTP".equals(response.getStatus()) || "EXPIRED_OTP".equals(response.getStatus())) {
                status = HttpStatus.BAD_REQUEST;
            }
            
            // Log response
            if (logEntry != null) {
                try {
                    traceLoggerService.logResponse(
                        logEntry, 
                        objectMapper.writeValueAsString(response), 
                        status.value()
                    );
                } catch (JsonProcessingException e) {
                    logger.error("Error serializing response", e);
                }
            }
            
            return ResponseEntity.status(status)
                    .header("X-Trace-Id", traceId)
                    .body(response);
        } catch (Exception e) {
            // Handle system errors
            ErrorResponse errorResponse = new ErrorResponse("UIDAI_SERVICE_FAILURE", e.getMessage());
            
            // Log error response
            if (logEntry != null) {
                try {
                    traceLoggerService.logResponse(
                        logEntry, 
                        objectMapper.writeValueAsString(errorResponse), 
                        HttpStatus.INTERNAL_SERVER_ERROR.value()
                    );
                } catch (JsonProcessingException ex) {
                    logger.error("Error serializing error response", ex);
                }
            }
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header("X-Trace-Id", traceId)
                    .body(errorResponse);
        }
    }

    /**
     * Handles validation exceptions.
     *
     * @param e The exception
     * @param request The HTTP request
     * @return The error response
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleException(Exception e, HttpServletRequest request) {
        String traceId = traceLoggerService.generateTraceId();
        logger.error("[{}] Error processing request: {}", traceId, e.getMessage(), e);
        
        ErrorResponse errorResponse = new ErrorResponse("VALIDATION_ERROR", e.getMessage());
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .header("X-Trace-Id", traceId)
                .body(errorResponse);
    }
}
