package com.uidai.mock.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;

/**
 * OpenAPI configuration for Mock UIDAI Service.
 */
@Configuration
public class OpenApiConfig implements WebMvcConfigurer {

    @Value("${server.port:8201}")
    private String serverPort;

    @Bean
    public OpenAPI mockUidaiOpenAPI() {
        OpenAPI openAPI = new OpenAPI()
                .info(new Info()
                        .title("Mock UIDAI API Service")
                        .description("Mock implementation of UIDAI API for Aadhaar-based OTP and eKYC flows in development and testing environments")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Mock UIDAI Team")
                                .email("<EMAIL>")
                                .url("https://company.com/mock-uidai"))
                        .license(new License()
                                .name("Proprietary")
                                .url("https://company.com/license")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + "/api/ekyc/internal")
                                .description("Development server"),
                        new Server()
                                .url("https://mock-uidai.company.com/api/ekyc/internal")
                                .description("Testing server")
                ));

        return openAPI;
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .exposedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
}
