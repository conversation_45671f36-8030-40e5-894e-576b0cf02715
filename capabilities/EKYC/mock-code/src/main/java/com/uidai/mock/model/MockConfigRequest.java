package com.uidai.mock.model;

/**
 * Request model for configuring mock behavior.
 */
public class MockConfigRequest {

    private Boolean forceOtpSuccess;
    private Boolean forceOtpFailure;
    private Boolean forceSystemError;
    private Integer latencyMs;
    private KycData customKycData;

    // Default constructor
    public MockConfigRequest() {
    }

    // Getters and Setters
    public Boolean getForceOtpSuccess() {
        return forceOtpSuccess;
    }

    public void setForceOtpSuccess(Boolean forceOtpSuccess) {
        this.forceOtpSuccess = forceOtpSuccess;
    }

    public Boolean getForceOtpFailure() {
        return forceOtpFailure;
    }

    public void setForceOtpFailure(Boolean forceOtpFailure) {
        this.forceOtpFailure = forceOtpFailure;
    }

    public Boolean getForceSystemError() {
        return forceSystemError;
    }

    public void setForceSystemError(Boolean forceSystemError) {
        this.forceSystemError = forceSystemError;
    }

    public Integer getLatencyMs() {
        return latencyMs;
    }

    public void setLatencyMs(Integer latencyMs) {
        this.latencyMs = latencyMs;
    }

    public KycData getCustomKycData() {
        return customKycData;
    }

    public void setCustomKycData(KycData customKycData) {
        this.customKycData = customKycData;
    }
}
