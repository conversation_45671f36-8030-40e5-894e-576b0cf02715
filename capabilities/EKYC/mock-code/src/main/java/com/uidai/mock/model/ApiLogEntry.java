package com.uidai.mock.model;

import java.time.Instant;

/**
 * Model for API log entries.
 */
public class ApiLogEntry {

    private String traceId;
    private String endpoint;
    private String method;
    private String requestBody;
    private String responseBody;
    private int statusCode;
    private Instant timestamp;

    // Default constructor
    public ApiLogEntry() {
        this.timestamp = Instant.now();
    }

    // Constructor with required fields
    public ApiLogEntry(String traceId, String endpoint, String method, String requestBody) {
        this.traceId = traceId;
        this.endpoint = endpoint;
        this.method = method;
        this.requestBody = requestBody;
        this.timestamp = Instant.now();
    }

    // Getters and Setters
    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(String requestBody) {
        this.requestBody = requestBody;
    }

    public String getResponseBody() {
        return responseBody;
    }

    public void setResponseBody(String responseBody) {
        this.responseBody = responseBody;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public Instant getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Instant timestamp) {
        this.timestamp = timestamp;
    }
}
