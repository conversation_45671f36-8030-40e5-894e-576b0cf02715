package com.uidai.mock.service;

import com.uidai.mock.model.KycData;
import com.uidai.mock.model.MockConfigRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * Service for configuring mock behavior.
 */
@Service
public class MockConfigService {

    private static final Logger logger = LoggerFactory.getLogger(MockConfigService.class);

    @Value("${mock.uidai.latency.min-ms:100}")
    private int minLatencyMs;

    @Value("${mock.uidai.latency.max-ms:500}")
    private int maxLatencyMs;

    private Boolean forceOtpSuccess;
    private Boolean forceOtpFailure;
    private Boolean forceSystemError;
    private Integer customLatencyMs;
    private KycData customKycData;

    /**
     * Updates the mock configuration.
     *
     * @param configRequest The configuration request
     */
    public void updateConfig(MockConfigRequest configRequest) {
        if (configRequest.getForceOtpSuccess() != null) {
            this.forceOtpSuccess = configRequest.getForceOtpSuccess();
            logger.info("Updated forceOtpSuccess to: {}", this.forceOtpSuccess);
        }
        
        if (configRequest.getForceOtpFailure() != null) {
            this.forceOtpFailure = configRequest.getForceOtpFailure();
            logger.info("Updated forceOtpFailure to: {}", this.forceOtpFailure);
        }
        
        if (configRequest.getForceSystemError() != null) {
            this.forceSystemError = configRequest.getForceSystemError();
            logger.info("Updated forceSystemError to: {}", this.forceSystemError);
        }
        
        if (configRequest.getLatencyMs() != null) {
            this.customLatencyMs = configRequest.getLatencyMs();
            logger.info("Updated customLatencyMs to: {}", this.customLatencyMs);
        }
        
        if (configRequest.getCustomKycData() != null) {
            this.customKycData = configRequest.getCustomKycData();
            logger.info("Updated customKycData to: {}", this.customKycData);
        }
    }

    /**
     * Resets the mock configuration to default values.
     */
    public void resetConfig() {
        this.forceOtpSuccess = null;
        this.forceOtpFailure = null;
        this.forceSystemError = null;
        this.customLatencyMs = null;
        this.customKycData = null;
        logger.info("Mock configuration reset to defaults");
    }

    /**
     * Checks if OTP success should be forced.
     *
     * @return true if OTP success should be forced, false otherwise
     */
    public boolean isForceOtpSuccess() {
        return Boolean.TRUE.equals(forceOtpSuccess);
    }

    /**
     * Checks if OTP failure should be forced.
     *
     * @return true if OTP failure should be forced, false otherwise
     */
    public boolean isForceOtpFailure() {
        return Boolean.TRUE.equals(forceOtpFailure);
    }

    /**
     * Checks if system error should be forced.
     *
     * @return true if system error should be forced, false otherwise
     */
    public boolean isForceSystemError() {
        return Boolean.TRUE.equals(forceSystemError);
    }

    /**
     * Gets the custom KYC data.
     *
     * @return The custom KYC data, or null if not set
     */
    public KycData getCustomKycData() {
        return customKycData;
    }

    /**
     * Gets the latency to simulate.
     *
     * @return The latency in milliseconds
     */
    public int getLatencyMs() {
        if (customLatencyMs != null) {
            return customLatencyMs;
        }
        
        // Random latency between min and max
        return minLatencyMs + (int) (Math.random() * (maxLatencyMs - minLatencyMs));
    }
}
