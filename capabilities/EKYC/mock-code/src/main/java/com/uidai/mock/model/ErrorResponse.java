package com.uidai.mock.model;

import java.time.Instant;

/**
 * Model for error responses.
 */
public class ErrorResponse {

    private String errorCode;
    private String errorMessage;
    private Instant timestamp;

    // Default constructor
    public ErrorResponse() {
        this.timestamp = Instant.now();
    }

    // Constructor with required fields
    public ErrorResponse(String errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.timestamp = Instant.now();
    }

    // Getters and Setters
    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Instant getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Instant timestamp) {
        this.timestamp = timestamp;
    }
}
