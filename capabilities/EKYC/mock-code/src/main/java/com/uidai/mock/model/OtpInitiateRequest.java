package com.uidai.mock.model;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * Request model for OTP initiation.
 */
public class OtpInitiateRequest {

    @NotBlank(message = "Aadhaar/VID is required")
    @Size(min = 12, max = 12, message = "Aadhaar/VID must be exactly 12 digits")
    @Pattern(regexp = "\\d{12}", message = "Aadhaar/VID must contain only digits")
    private String aadhaarOrVid;

    @NotBlank(message = "Transaction ID is required")
    private String transactionId;

    // Default constructor
    public OtpInitiateRequest() {
    }

    // Constructor with all fields
    public OtpInitiateRequest(String aadhaarOrVid, String transactionId) {
        this.aadhaarOrVid = aadhaarOrVid;
        this.transactionId = transactionId;
    }

    // Getters and Setters
    public String getAadhaarOrVid() {
        return aadhaarOrVid;
    }

    public void setAadhaarOrVid(String aadhaarOrVid) {
        this.aadhaarOrVid = aadhaarOrVid;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    @Override
    public String toString() {
        // Mask Aadhaar/VID for security
        String maskedAadhaar = aadhaarOrVid != null ? 
            aadhaarOrVid.substring(0, 2) + "XXXXXXXX" + aadhaarOrVid.substring(10) : null;
        
        return "OtpInitiateRequest{" +
                "aadhaarOrVid='" + maskedAadhaar + '\'' +
                ", transactionId='" + transactionId + '\'' +
                '}';
    }
}
