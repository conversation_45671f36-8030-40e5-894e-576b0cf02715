package com.uidai.mock.service;

import com.uidai.mock.model.ApiLogEntry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Service for trace logging and PII masking.
 */
@Service
public class TraceLoggerService {

    private static final Logger logger = LoggerFactory.getLogger(TraceLoggerService.class);
    
    // Patterns for masking PII
    private static final Pattern AADHAAR_PATTERN = Pattern.compile("\\d{12}");
    private static final Pattern OTP_PATTERN = Pattern.compile("\"otp\"\\s*:\\s*\"(\\d{6})\"");
    
    // In-memory log storage
    private final List<ApiLogEntry> apiLogs = Collections.synchronizedList(new ArrayList<>());

    /**
     * Generates a new trace ID.
     *
     * @return A new trace ID
     */
    public String generateTraceId() {
        return UUID.randomUUID().toString();
    }

    /**
     * Logs an API request.
     *
     * @param traceId The trace ID
     * @param endpoint The API endpoint
     * @param method The HTTP method
     * @param requestBody The request body
     * @return The API log entry
     */
    public ApiLogEntry logRequest(String traceId, String endpoint, String method, String requestBody) {
        String maskedRequest = maskPii(requestBody);
        logger.info("[{}] {} {} - Request: {}", traceId, method, endpoint, maskedRequest);
        
        ApiLogEntry logEntry = new ApiLogEntry(traceId, endpoint, method, maskedRequest);
        apiLogs.add(logEntry);
        
        return logEntry;
    }

    /**
     * Updates an API log entry with response information.
     *
     * @param logEntry The API log entry
     * @param responseBody The response body
     * @param statusCode The HTTP status code
     */
    public void logResponse(ApiLogEntry logEntry, String responseBody, int statusCode) {
        String maskedResponse = maskPii(responseBody);
        logger.info("[{}] Response: {} - Status: {}", logEntry.getTraceId(), maskedResponse, statusCode);
        
        logEntry.setResponseBody(maskedResponse);
        logEntry.setStatusCode(statusCode);
    }

    /**
     * Gets all API log entries.
     *
     * @return The list of API log entries
     */
    public List<ApiLogEntry> getApiLogs() {
        return Collections.unmodifiableList(apiLogs);
    }

    /**
     * Clears all API log entries.
     */
    public void clearLogs() {
        apiLogs.clear();
        logger.info("API logs cleared");
    }

    /**
     * Masks PII in a string.
     *
     * @param input The input string
     * @return The masked string
     */
    public String maskPii(String input) {
        if (input == null) {
            return null;
        }
        
        // Mask Aadhaar numbers
        Matcher aadhaarMatcher = AADHAAR_PATTERN.matcher(input);
        StringBuffer sb = new StringBuffer();
        while (aadhaarMatcher.find()) {
            String aadhaar = aadhaarMatcher.group();
            String masked = aadhaar.substring(0, 2) + "XXXXXXXX" + aadhaar.substring(10);
            aadhaarMatcher.appendReplacement(sb, masked);
        }
        aadhaarMatcher.appendTail(sb);
        
        // Mask OTP values
        String result = sb.toString();
        Matcher otpMatcher = OTP_PATTERN.matcher(result);
        sb = new StringBuffer();
        while (otpMatcher.find()) {
            otpMatcher.appendReplacement(sb, "\"otp\":\"******\"");
        }
        otpMatcher.appendTail(sb);
        
        return sb.toString();
    }
}
