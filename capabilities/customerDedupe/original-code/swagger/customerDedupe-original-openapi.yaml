openapi: 3.0.3
info:
  title: Customer Deduplication Service API
  description: |
    Banking Customer Deduplication Service for RBI Compliance.
    
    This service provides customer deduplication capabilities by integrating with third-party vendors 
    to identify duplicate customer records. It ensures compliance with RBI guidelines for customer 
    onboarding and maintains comprehensive audit trails.
    
    ## Key Features
    - Integration with third-party deduplication vendors
    - Comprehensive audit logging for RBI compliance
    - Real-time duplicate detection
    - Secure PII handling with masking capabilities
    - Retry mechanisms for vendor failures
    
  version: 1.0.0
  contact:
    name: Banking Technology Team
    email: <EMAIL>
    url: https://banking.com/contact
  license:
    name: Proprietary
    url: https://banking.com/license

servers:
  - url: http://localhost:8082/api/customer-dedupe
    description: Local Development Server
  - url: https://api.banking.com/customer-dedupe
    description: Production Server
  - url: https://staging-api.banking.com/customer-dedupe
    description: Staging Server

paths:
  /customer/dedupe:
    post:
      tags:
        - Customer Deduplication
      summary: Process customer deduplication request
      description: |
        Processes a customer deduplication request by validating customer data and 
        checking for duplicate records using third-party vendor services.
        
        The service will:
        1. Validate the incoming customer data
        2. Call the configured third-party vendor API
        3. Process the vendor response
        4. Return standardized deduplication results
        5. Log all activities for audit compliance
        
      operationId: processDedupeRequest
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerDedupeRequest'
            examples:
              validRequest:
                summary: Valid customer deduplication request
                value:
                  customerIdentifier: "CUST123456"
                  customerData:
                    fullName: "Rahul Sharma"
                    dateOfBirth: "1990-01-15"
                    pan: "**********"
                    aadhaar: "1234-5678-9012"
                    mobileNumber: "9876543210"
                    emailAddress: "<EMAIL>"
                    address:
                      line1: "123 MG Road"
                      city: "Bangalore"
                      state: "Karnataka"
                      pincode: "560001"
                  upstreamApplication: "LoanOrigination"
                  upstreamRequestId: "LO-REQ-789"
      responses:
        '200':
          description: Deduplication completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerDedupeResponse'
              examples:
                matchFound:
                  summary: Duplicate customer found
                  value:
                    referenceNumber: "DEDUPE123456789"
                    customerIdentifier: "CUST123456"
                    dedupeStatus: "MATCH_FOUND"
                    message: "Duplicate customer records found"
                    duplicates:
                      - customerId: "EXISTING_CUST_001"
                        matchScore: 95
                        matchedFields: ["pan", "mobileNumber", "fullName"]
                        customerStatus: "ACTIVE"
                        createdDate: "2024-01-15T10:30:00Z"
                    vendorDetails:
                      vendorName: "Karza"
                      processingTimestamp: "2025-06-27T10:15:30Z"
                    auditInfo:
                      requestTimestamp: "2025-06-27T10:15:25Z"
                      responseTimestamp: "2025-06-27T10:15:30Z"
                      processingTimeMs: 5000
                noMatch:
                  summary: No duplicate customer found
                  value:
                    referenceNumber: "DEDUPE123456790"
                    customerIdentifier: "CUST123457"
                    dedupeStatus: "NO_MATCH_FOUND"
                    message: "No duplicate customer records found"
                    duplicates: []
                    vendorDetails:
                      vendorName: "Karza"
                      processingTimestamp: "2025-06-27T10:15:30Z"
                    auditInfo:
                      requestTimestamp: "2025-06-27T10:15:25Z"
                      responseTimestamp: "2025-06-27T10:15:30Z"
                      processingTimeMs: 3000
        '400':
          description: Bad request - Invalid input data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                validationError:
                  summary: Validation error
                  value:
                    referenceNumber: "DEDUPE123456791"
                    customerIdentifier: "CUST123458"
                    dedupeStatus: "ERROR"
                    message: "Validation failed for customer data"
                    vendorDetails:
                      vendorName: "System"
                      processingTimestamp: "2025-06-27T10:15:30Z"
                    auditInfo:
                      requestTimestamp: "2025-06-27T10:15:25Z"
                      responseTimestamp: "2025-06-27T10:15:30Z"
                      processingTimeMs: 100
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                systemError:
                  summary: System error
                  value:
                    referenceNumber: "DEDUPE123456792"
                    customerIdentifier: "CUST123459"
                    dedupeStatus: "ERROR"
                    message: "System error occurred while processing request"
                    vendorDetails:
                      vendorName: "System"
                      processingTimestamp: "2025-06-27T10:15:30Z"
                    auditInfo:
                      requestTimestamp: "2025-06-27T10:15:25Z"
                      responseTimestamp: "2025-06-27T10:15:30Z"
                      processingTimeMs: 500
        '502':
          description: Vendor service error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                vendorError:
                  summary: Vendor service failure
                  value:
                    referenceNumber: "DEDUPE123456793"
                    customerIdentifier: "CUST123460"
                    dedupeStatus: "VENDOR_SERVICE_FAILURE"
                    message: "Third-party vendor service is unavailable"
                    vendorDetails:
                      vendorName: "Karza"
                      processingTimestamp: "2025-06-27T10:15:30Z"
                    auditInfo:
                      requestTimestamp: "2025-06-27T10:15:25Z"
                      responseTimestamp: "2025-06-27T10:15:30Z"
                      processingTimeMs: 30000
        '504':
          description: Vendor service timeout
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                timeoutError:
                  summary: Vendor service timeout
                  value:
                    referenceNumber: "DEDUPE123456794"
                    customerIdentifier: "CUST123461"
                    dedupeStatus: "TIMEOUT"
                    message: "Third-party vendor service call timed out"
                    vendorDetails:
                      vendorName: "Karza"
                      processingTimestamp: "2025-06-27T10:15:30Z"
                    auditInfo:
                      requestTimestamp: "2025-06-27T10:15:25Z"
                      responseTimestamp: "2025-06-27T10:15:30Z"
                      processingTimeMs: 45000

  /v1/customer-dedupe/original/health:
    get:
      tags:
        - Health Check
      summary: Health check endpoint
      description: |
        Returns the health status of the Customer Deduplication Service.
        Used for monitoring and load balancer health checks.
      operationId: healthCheck
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
              examples:
                healthy:
                  summary: Healthy service
                  value:
                    status: "UP"
                    service: "Customer Dedupe Original"
                    timestamp: "2025-06-27T10:15:30Z"
                    version: "1.0.0"

components:
  schemas:
    CustomerDedupeRequest:
      type: object
      required:
        - customerIdentifier
        - customerData
      properties:
        customerIdentifier:
          type: string
          maxLength: 100
          description: Unique identifier for the customer
          example: "CUST123456"
        customerData:
          $ref: '#/components/schemas/CustomerData'
        upstreamApplication:
          type: string
          maxLength: 100
          description: Name of the upstream application making the request
          example: "LoanOrigination"
        upstreamRequestId:
          type: string
          maxLength: 100
          description: Request ID from the upstream application
          example: "LO-REQ-789"

    CustomerData:
      type: object
      required:
        - fullName
        - dateOfBirth
        - pan
        - mobileNumber
      properties:
        fullName:
          type: string
          maxLength: 200
          description: Full name of the customer
          example: "Rahul Sharma"
        dateOfBirth:
          type: string
          format: date
          description: Date of birth in YYYY-MM-DD format
          example: "1990-01-15"
        pan:
          type: string
          pattern: "^[A-Z]{5}[0-9]{4}[A-Z]{1}$"
          description: PAN number in standard format
          example: "**********"
        aadhaar:
          type: string
          pattern: "^[0-9]{4}-[0-9]{4}-[0-9]{4}$"
          description: Aadhaar number in XXXX-XXXX-XXXX format
          example: "1234-5678-9012"
        mobileNumber:
          type: string
          pattern: "^[6-9][0-9]{9}$"
          description: Mobile number (10 digits starting with 6-9)
          example: "9876543210"
        emailAddress:
          type: string
          format: email
          maxLength: 100
          description: Email address
          example: "<EMAIL>"
        address:
          $ref: '#/components/schemas/Address'

    Address:
      type: object
      properties:
        line1:
          type: string
          maxLength: 200
          description: Address line 1
          example: "123 MG Road"
        city:
          type: string
          maxLength: 100
          description: City name
          example: "Bangalore"
        state:
          type: string
          maxLength: 100
          description: State name
          example: "Karnataka"
        pincode:
          type: string
          pattern: "^[0-9]{6}$"
          description: 6-digit pincode
          example: "560001"

    CustomerDedupeResponse:
      type: object
      required:
        - referenceNumber
        - customerIdentifier
        - dedupeStatus
        - message
      properties:
        referenceNumber:
          type: string
          description: Unique reference number for this deduplication request
          example: "DEDUPE123456789"
        customerIdentifier:
          type: string
          description: Customer identifier from the request
          example: "CUST123456"
        dedupeStatus:
          $ref: '#/components/schemas/VendorStatus'
        message:
          type: string
          description: Human-readable message describing the result
          example: "Duplicate customer records found"
        duplicates:
          type: array
          items:
            $ref: '#/components/schemas/DuplicateCustomer'
          description: List of duplicate customer records found
        vendorDetails:
          $ref: '#/components/schemas/VendorDetails'
        auditInfo:
          $ref: '#/components/schemas/AuditInfo'

    DuplicateCustomer:
      type: object
      properties:
        customerId:
          type: string
          description: ID of the duplicate customer
          example: "EXISTING_CUST_001"
        matchScore:
          type: integer
          minimum: 0
          maximum: 100
          description: Match score percentage (0-100)
          example: 95
        matchedFields:
          type: array
          items:
            type: string
          description: List of fields that matched
          example: ["pan", "mobileNumber", "fullName"]
        customerStatus:
          type: string
          description: Status of the duplicate customer
          example: "ACTIVE"
        createdDate:
          type: string
          format: date-time
          description: Date when the duplicate customer was created
          example: "2024-01-15T10:30:00Z"

    VendorDetails:
      type: object
      properties:
        vendorName:
          type: string
          description: Name of the vendor that processed the request
          example: "Karza"
        processingTimestamp:
          type: string
          format: date-time
          description: Timestamp when vendor processed the request
          example: "2025-06-27T10:15:30Z"

    AuditInfo:
      type: object
      properties:
        requestTimestamp:
          type: string
          format: date-time
          description: Timestamp when request was received
          example: "2025-06-27T10:15:25Z"
        responseTimestamp:
          type: string
          format: date-time
          description: Timestamp when response was sent
          example: "2025-06-27T10:15:30Z"
        processingTimeMs:
          type: integer
          description: Processing time in milliseconds
          example: 5000

    VendorStatus:
      type: string
      enum:
        - MATCH_FOUND
        - NO_MATCH_FOUND
        - ERROR
        - TIMEOUT
        - VENDOR_SERVICE_FAILURE
      description: |
        Status of the deduplication process:
        - MATCH_FOUND: Duplicate customer records were found
        - NO_MATCH_FOUND: No duplicate customer records were found
        - ERROR: An error occurred during processing
        - TIMEOUT: Vendor service call timed out
        - VENDOR_SERVICE_FAILURE: Vendor service is unavailable
      example: "MATCH_FOUND"

    ErrorResponse:
      type: object
      required:
        - referenceNumber
        - customerIdentifier
        - dedupeStatus
        - message
      properties:
        referenceNumber:
          type: string
          description: Unique reference number for this request
          example: "DEDUPE123456791"
        customerIdentifier:
          type: string
          description: Customer identifier from the request
          example: "CUST123458"
        dedupeStatus:
          $ref: '#/components/schemas/VendorStatus'
        message:
          type: string
          description: Error message describing what went wrong
          example: "Validation failed for customer data"
        vendorDetails:
          $ref: '#/components/schemas/VendorDetails'
        auditInfo:
          $ref: '#/components/schemas/AuditInfo'

    HealthResponse:
      type: object
      required:
        - status
        - service
        - timestamp
        - version
      properties:
        status:
          type: string
          description: Health status of the service
          example: "UP"
        service:
          type: string
          description: Name of the service
          example: "Customer Dedupe Original"
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the health check
          example: "2025-06-27T10:15:30Z"
        version:
          type: string
          description: Version of the service
          example: "1.0.0"
