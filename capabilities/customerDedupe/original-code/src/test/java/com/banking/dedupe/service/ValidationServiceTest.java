package com.banking.dedupe.service;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import com.banking.dedupe.dto.CustomerDedupeRequestDto;

/**
 * Unit tests for ValidationService
 *
 * Tests validation logic for customer deduplication requests
 * including field validation, format checks, and business rules.
 *
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@SpringBootTest
@ActiveProfiles("test")
class ValidationServiceTest {

    @Autowired
    private ValidationService validationService;

    @BeforeEach
    void setUp() {
        // ValidationService is now injected by Spring with proper @Value annotations
        validationService.initializePatterns();
    }

    @Test
    void testValidRequest() {
        // Given
        CustomerDedupeRequestDto request = createValidRequest();

        // When
        ValidationService.ValidationResult result = validationService.validateRequest(request);

        // Then
        assertTrue(result.isValid());
        assertTrue(result.getErrors().isEmpty());
    }

    @Test
    void testInvalidPanFormat() {
        // Given
        CustomerDedupeRequestDto request = createValidRequest();
        request.getCustomerData().setPan("INVALID_PAN");

        // When
        ValidationService.ValidationResult result = validationService.validateRequest(request);

        // Then
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream()
            .anyMatch(error -> error.contains("Invalid PAN format")));
    }

    @Test
    void testInvalidFormats() {
        // Test multiple validation failures in one test
        CustomerDedupeRequestDto request = createValidRequest();
        request.getCustomerData().setMobileNumber("123456789"); // Invalid mobile
        request.getCustomerData().setDateOfBirth(LocalDate.now().minusYears(17)); // Underage
        request.getCustomerData().setEmailAddress("invalid-email"); // Invalid email

        ValidationService.ValidationResult result = validationService.validateRequest(request);

        assertFalse(result.isValid());
        assertTrue(result.getErrors().size() >= 3); // Should have multiple errors
    }

    @Test
    void testMissingRequiredFields() {
        // Test missing customer data
        CustomerDedupeRequestDto request = new CustomerDedupeRequestDto();
        request.setCustomerIdentifier("CUST123");

        ValidationService.ValidationResult result = validationService.validateRequest(request);

        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream()
            .anyMatch(error -> error.contains("Customer data is required")));
    }

    /**
     * Create a valid request for testing
     */
    private CustomerDedupeRequestDto createValidRequest() {
        CustomerDedupeRequestDto request = new CustomerDedupeRequestDto();
        request.setCustomerIdentifier("CUST123456");
        request.setUpstreamApplication("TEST_APP");
        request.setUpstreamRequestId("REQ123");

        CustomerDedupeRequestDto.CustomerDataDto customerData = new CustomerDedupeRequestDto.CustomerDataDto();
        customerData.setFullName("John Doe");
        customerData.setDateOfBirth(LocalDate.of(1990, 1, 15));
        customerData.setPan("**********");
        customerData.setMobileNumber("9876543210");
        customerData.setEmailAddress("<EMAIL>");

        request.setCustomerData(customerData);

        return request;
    }
}
