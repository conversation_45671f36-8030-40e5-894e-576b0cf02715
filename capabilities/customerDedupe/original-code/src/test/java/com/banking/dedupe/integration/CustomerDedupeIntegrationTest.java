package com.banking.dedupe.integration;

import java.time.LocalDate;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.banking.dedupe.dto.CustomerDedupeRequestDto;
import com.banking.dedupe.dto.CustomerDedupeResponseDto;
import com.banking.dedupe.entity.DedupeRequest;
import com.banking.dedupe.enums.VendorStatus;
import com.banking.dedupe.repository.DedupeRequestRepository;
import com.banking.dedupe.service.CustomerDedupeService;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Integration tests for Customer Deduplication Service
 * 
 * Tests the complete flow from API request to database persistence
 * including validation, processing, and response generation.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@Transactional
class CustomerDedupeIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @LocalServerPort
    private int port;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CustomerDedupeService customerDedupeService;

    @Autowired
    private DedupeRequestRepository dedupeRequestRepository;

    @Test
    void testServiceLayerDirectly() {
        // Test service layer without HTTP overhead
        CustomerDedupeRequestDto request = createValidRequest();

        CustomerDedupeResponseDto response = customerDedupeService.processDedupeRequest(
            request, "127.0.0.1", "Test-Agent");

        assertNotNull(response);
        assertNotNull(response.getReferenceNumber());
        assertEquals("CUST123456", response.getCustomerIdentifier());
        assertNotNull(response.getDedupeStatus());
        assertNotNull(response.getAuditInfo());

        // Verify database persistence
        Optional<DedupeRequest> savedRequest = dedupeRequestRepository
            .findByReferenceNumber(response.getReferenceNumber());
        assertTrue(savedRequest.isPresent());
        assertEquals("CUST123456", savedRequest.get().getCustomerIdentifier());
    }

    @Test
    void testValidationFailure() {
        // Test validation error handling
        CustomerDedupeRequestDto request = createValidRequest();
        request.getCustomerData().setPan("INVALID_PAN"); // Invalid PAN format

        CustomerDedupeResponseDto response = customerDedupeService.processDedupeRequest(
            request, "127.0.0.1", "Test-Agent");

        assertNotNull(response);
        assertEquals(VendorStatus.ERROR, response.getDedupeStatus());
        assertTrue(response.getMessage().contains("validation"));
    }

    /**
     * Create a valid request for testing
     */
    private CustomerDedupeRequestDto createValidRequest() {
        CustomerDedupeRequestDto request = new CustomerDedupeRequestDto();
        request.setCustomerIdentifier("CUST123456");
        request.setUpstreamApplication("TEST_APP");
        request.setUpstreamRequestId("REQ123");

        CustomerDedupeRequestDto.CustomerDataDto customerData = 
            new CustomerDedupeRequestDto.CustomerDataDto();
        customerData.setFullName("John Doe");
        customerData.setDateOfBirth(LocalDate.of(1990, 1, 15));
        customerData.setPan("**********");
        customerData.setAadhaar("1234-5678-9012");
        customerData.setMobileNumber("9876543210");
        customerData.setEmailAddress("<EMAIL>");

        CustomerDedupeRequestDto.AddressDto address = 
            new CustomerDedupeRequestDto.AddressDto();
        address.setLine1("123 Test Street");
        address.setCity("Bangalore");
        address.setState("Karnataka");
        address.setPincode("560001");
        customerData.setAddress(address);

        request.setCustomerData(customerData);

        return request;
    }

    /**
     * Create an invalid request for testing
     */
    private CustomerDedupeRequestDto createInvalidRequest() {
        CustomerDedupeRequestDto request = new CustomerDedupeRequestDto();
        request.setCustomerIdentifier("CUST123456");

        CustomerDedupeRequestDto.CustomerDataDto customerData = 
            new CustomerDedupeRequestDto.CustomerDataDto();
        customerData.setFullName("John Doe");
        customerData.setDateOfBirth(LocalDate.of(1990, 1, 15));
        customerData.setPan("INVALID_PAN"); // Invalid PAN format
        customerData.setMobileNumber("123456789"); // Invalid mobile format

        request.setCustomerData(customerData);

        return request;
    }
}
