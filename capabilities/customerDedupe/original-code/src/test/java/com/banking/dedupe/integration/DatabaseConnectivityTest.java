package com.banking.dedupe.integration;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;

import javax.sql.DataSource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.banking.dedupe.entity.DedupeRequest;
import com.banking.dedupe.enums.RequestStatus;
import com.banking.dedupe.repository.DedupeRequestRepository;

/**
 * Database connectivity and schema verification tests
 *
 * Verifies that the database is properly configured,
 * schema is created correctly, and basic CRUD operations work.
 * Uses H2 for tests and PostgreSQL for production.
 *
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class DatabaseConnectivityTest {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private DedupeRequestRepository dedupeRequestRepository;

    @Test
    void testDatabaseConnection() throws Exception {
        // Test basic database connectivity
        try (Connection connection = dataSource.getConnection()) {
            assertNotNull(connection);
            assertTrue(connection.isValid(5));
            
            DatabaseMetaData metaData = connection.getMetaData();
            String databaseProductName = metaData.getDatabaseProductName();
            // Accept both H2 (for tests) and PostgreSQL (for production)
            assertTrue(databaseProductName.equals("H2") || databaseProductName.equals("PostgreSQL"),
                      "Expected H2 or PostgreSQL, but got: " + databaseProductName);
            
            System.out.println("Database Product: " + metaData.getDatabaseProductName());
            System.out.println("Database Version: " + metaData.getDatabaseProductVersion());
            System.out.println("Driver Name: " + metaData.getDriverName());
            System.out.println("Driver Version: " + metaData.getDriverVersion());
        }
    }

    @Test
    void testBasicDatabaseOperations() {
        // Combined test for basic CRUD operations
        String uniqueRef = "TEST-" + System.currentTimeMillis();
        DedupeRequest request = new DedupeRequest();
        request.setReferenceNumber(uniqueRef);
        request.setCustomerIdentifier("CUST123456");
        request.setFullName("John Doe");
        request.setDateOfBirth(LocalDate.of(1990, 1, 15));
        request.setPan("**********");
        request.setMobileNumber("9876543210");
        request.setEmailAddress("<EMAIL>");
        request.setRequestStatus(RequestStatus.RECEIVED);
        request.setUpstreamApplication("TEST_APP");
        request.setCreatedAt(LocalDateTime.now());
        request.setUpdatedAt(LocalDateTime.now());

        // Test Create and Read
        DedupeRequest savedRequest = dedupeRequestRepository.save(request);
        assertNotNull(savedRequest.getId());
        assertEquals(uniqueRef, savedRequest.getReferenceNumber());

        Optional<DedupeRequest> foundRequest = dedupeRequestRepository
            .findByReferenceNumber(uniqueRef);
        assertTrue(foundRequest.isPresent());
        assertEquals("CUST123456", foundRequest.get().getCustomerIdentifier());
    }

    /**
     * Helper method to create test request
     */
    private DedupeRequest createTestRequest(String refNumber, String customerId, String pan, String mobile) {
        DedupeRequest request = new DedupeRequest();
        request.setReferenceNumber(refNumber);
        request.setCustomerIdentifier(customerId);
        request.setFullName("Test Customer");
        request.setDateOfBirth(LocalDate.of(1990, 1, 15));
        request.setPan(pan);
        request.setMobileNumber(mobile);
        request.setEmailAddress("<EMAIL>");
        request.setRequestStatus(RequestStatus.RECEIVED);
        request.setUpstreamApplication("TEST_APP");
        request.setCreatedAt(LocalDateTime.now());
        request.setUpdatedAt(LocalDateTime.now());
        return request;
    }
}
