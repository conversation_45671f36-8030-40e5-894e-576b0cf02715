spring:
  application:
    name: customer-dedupe-service-test

  # H2 In-Memory Database for Tests
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
    driver-class-name: org.h2.Driver
    hikari:
      maximum-pool-size: 5
      minimum-idle: 1
      connection-timeout: 10000
      idle-timeout: 300000
      max-lifetime: 600000

  jpa:
    hibernate:
      ddl-auto: create-drop  # Create schema for tests
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 10

  h2:
    console:
      enabled: true  # Enable H2 console for debugging tests

  flyway:
    enabled: false  # Disable Flyway for H2 tests, use JPA schema generation

# Vendor Configuration for Testing
vendor:
  dedupe:
    base-url: http://localhost:8222/api/customer-dedupe/internal/customer/dedupe
    timeout: 5000
    retry:
      max-attempts: 1  # Reduced for faster tests
      delay: 100
      backoff-multiplier: 1.0


# Audit Configuration
audit:
  retention:
    days: 30  # Shorter for tests
  pii-masking:
    enabled: true

# Logging Configuration
logging:
  level:
    com.banking.dedupe: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"

# Application Specific Configuration
app:
  reference-number:
    prefix: ""
    length: 12
  validation:
    pan-pattern: "[A-Z]{5}[0-9]{4}[A-Z]{1}"
    aadhaar-pattern: "[0-9]{4}-[0-9]{4}-[0-9]{4}"
    mobile-pattern: "[6-9][0-9]{9}"
    email-pattern: "^[A-Za-z0-9+_.-]+@(.+)$"
