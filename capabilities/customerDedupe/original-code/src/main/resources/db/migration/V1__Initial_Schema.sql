-- Customer Deduplication Service Database Schema
-- Version: 1.0.0
-- Description: Initial schema for customer deduplication service with audit trail

-- Create sequences
CREATE SEQUENCE IF NOT EXISTS dedupe_request_seq START 1 INCREMENT 1;
CREATE SEQUENCE IF NOT EXISTS dedupe_response_seq START 1 INCREMENT 1;
CREATE SEQUENCE IF NOT EXISTS vendor_response_seq START 1 INCREMENT 1;
CREATE SEQUENCE IF NOT EXISTS audit_log_seq START 1 INCREMENT 1;

-- Using VARCHAR for enum-like fields for better compatibility

-- Dedupe Request Table
CREATE TABLE dedupe_request (
    id BIGINT PRIMARY KEY DEFAULT nextval('dedupe_request_seq'),
    reference_number VARCHAR(50) NOT NULL UNIQUE,
    customer_identifier VARCHAR(100) NOT NULL,
    
    -- Customer Data (PII will be masked in logs)
    full_name VARCHAR(200) NOT NULL,
    date_of_birth DATE NOT NULL,
    pan VARCHAR(10) NOT NULL,
    a<PERSON><PERSON><PERSON> VARCHAR(14), -- Format: XXXX-XXXX-XXXX
    mobile_number VARCHAR(15) NOT NULL,
    email_address VARCHAR(100),
    
    -- Address Information
    address_line1 VARCHAR(200),
    address_city VARCHAR(100),
    address_state VARCHAR(100),
    address_pincode VARCHAR(10),
    
    -- Request Metadata
    request_status VARCHAR(50) NOT NULL DEFAULT 'RECEIVED',
    upstream_application VARCHAR(100),
    upstream_request_id VARCHAR(100),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Audit Fields
    created_by VARCHAR(100) DEFAULT 'SYSTEM',
    updated_by VARCHAR(100) DEFAULT 'SYSTEM',
    version INTEGER NOT NULL DEFAULT 1
);

-- Vendor Response Table (Raw vendor responses)
CREATE TABLE vendor_response (
    id BIGINT PRIMARY KEY DEFAULT nextval('vendor_response_seq'),
    dedupe_request_id BIGINT NOT NULL,
    
    -- Vendor Details
    vendor_name VARCHAR(50) NOT NULL,
    vendor_request_id VARCHAR(100),
    vendor_response_id VARCHAR(100),
    
    -- Response Data
    vendor_status VARCHAR(50) NOT NULL,
    raw_response TEXT, -- JSON response from vendor
    error_code VARCHAR(50),
    error_message TEXT,
    
    -- Processing Details
    request_sent_at TIMESTAMP WITH TIME ZONE,
    response_received_at TIMESTAMP WITH TIME ZONE,
    processing_time_ms INTEGER,
    retry_attempt INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_vendor_response_request FOREIGN KEY (dedupe_request_id) REFERENCES dedupe_request(id)
);

-- Dedupe Response Table (Interpreted responses)
CREATE TABLE dedupe_response (
    id BIGINT PRIMARY KEY DEFAULT nextval('dedupe_response_seq'),
    dedupe_request_id BIGINT NOT NULL,
    vendor_response_id BIGINT,
    
    -- Final Dedupe Result
    dedupe_status VARCHAR(50) NOT NULL,
    final_status VARCHAR(50) NOT NULL,
    message TEXT,
    
    -- Duplicate Information (JSON format)
    duplicates_found INTEGER DEFAULT 0,
    duplicate_details TEXT, -- JSON array of duplicate records
    
    -- Vendor Information
    vendor_name VARCHAR(50),
    processing_timestamp TIMESTAMP WITH TIME ZONE,
    
    -- Response Metadata
    response_sent_to_upstream BOOLEAN DEFAULT FALSE,
    upstream_response_status VARCHAR(20),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_dedupe_response_request FOREIGN KEY (dedupe_request_id) REFERENCES dedupe_request(id),
    CONSTRAINT fk_dedupe_response_vendor FOREIGN KEY (vendor_response_id) REFERENCES vendor_response(id)
);

-- Audit Log Table (Comprehensive audit trail)
CREATE TABLE audit_log (
    id BIGINT PRIMARY KEY DEFAULT nextval('audit_log_seq'),
    
    -- Reference Information
    reference_number VARCHAR(50) NOT NULL,
    dedupe_request_id BIGINT,
    
    -- Audit Details
    event_type VARCHAR(50) NOT NULL, -- REQUEST_RECEIVED, VALIDATION_COMPLETED, VENDOR_CALL_INITIATED, etc.
    event_description TEXT,
    event_data TEXT, -- JSON format with masked PII
    
    -- Status Information
    previous_status VARCHAR(50),
    current_status VARCHAR(50),
    
    -- Error Information
    error_code VARCHAR(50),
    error_message TEXT,
    
    -- Processing Information
    processing_time_ms INTEGER,
    retry_attempt INTEGER DEFAULT 0,
    
    -- User/System Information
    performed_by VARCHAR(100) DEFAULT 'SYSTEM',
    client_ip VARCHAR(45),
    user_agent TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_audit_log_request FOREIGN KEY (dedupe_request_id) REFERENCES dedupe_request(id)
);

-- Create indexes for performance
CREATE INDEX idx_dedupe_request_reference ON dedupe_request(reference_number);
CREATE INDEX idx_dedupe_request_customer_id ON dedupe_request(customer_identifier);
CREATE INDEX idx_dedupe_request_pan ON dedupe_request(pan);
CREATE INDEX idx_dedupe_request_mobile ON dedupe_request(mobile_number);
CREATE INDEX idx_dedupe_request_status ON dedupe_request(request_status);
CREATE INDEX idx_dedupe_request_created_at ON dedupe_request(created_at);

CREATE INDEX idx_vendor_response_request_id ON vendor_response(dedupe_request_id);
CREATE INDEX idx_vendor_response_vendor_name ON vendor_response(vendor_name);
CREATE INDEX idx_vendor_response_status ON vendor_response(vendor_status);
CREATE INDEX idx_vendor_response_created_at ON vendor_response(created_at);

CREATE INDEX idx_dedupe_response_request_id ON dedupe_response(dedupe_request_id);
CREATE INDEX idx_dedupe_response_status ON dedupe_response(dedupe_status);
CREATE INDEX idx_dedupe_response_created_at ON dedupe_response(created_at);

CREATE INDEX idx_audit_log_reference ON audit_log(reference_number);
CREATE INDEX idx_audit_log_request_id ON audit_log(dedupe_request_id);
CREATE INDEX idx_audit_log_event_type ON audit_log(event_type);
CREATE INDEX idx_audit_log_created_at ON audit_log(created_at);

-- Create trigger for updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_dedupe_request_updated_at BEFORE UPDATE ON dedupe_request FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vendor_response_updated_at BEFORE UPDATE ON vendor_response FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_dedupe_response_updated_at BEFORE UPDATE ON dedupe_response FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert initial configuration data
INSERT INTO audit_log (reference_number, event_type, event_description, performed_by, created_at)
VALUES ('SYSTEM-INIT', 'SCHEMA_CREATED', 'Initial database schema created successfully', 'FLYWAY', CURRENT_TIMESTAMP);
