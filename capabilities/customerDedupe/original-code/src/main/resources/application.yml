server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /api/customer-dedupe

spring:
  application:
    name: customer-dedupe-service
  
  datasource:
    url: ************************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
  
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
    default-property-inclusion: non_null

# Vendor Configuration
vendor:
  dedupe:
    base-url: http://localhost:8222/api/customer-dedupe/internal/customer/dedupe
    timeout: 30000
    retry:
      max-attempts: 3
      delay: 1000
      backoff-multiplier: 2.0


# Audit Configuration
audit:
  retention:
    days: 2555  # 7 years for RBI compliance
  pii-masking:
    enabled: true
    pan-mask-pattern: "XX****XXXX"
    aadhaar-mask-pattern: "XXXX-XXXX-****"
    mobile-mask-pattern: "****XX****"

# Logging Configuration
logging:
  level:
    com.banking.dedupe: INFO
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: logs/customer-dedupe-service.log
    max-size: 100MB
    max-history: 30

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true



# Application Specific Configuration
app:
  reference-number:
    prefix: "DEDUPE"
    length: 12
  validation:
    pan-pattern: "[A-Z]{5}[0-9]{4}[A-Z]{1}"
    aadhaar-pattern: "[0-9]{4}-[0-9]{4}-[0-9]{4}"
    mobile-pattern: "[6-9][0-9]{9}"
    email-pattern: "^[A-Za-z0-9+_.-]+@(.+)$"
  processing:
    max-concurrent-requests: 100
    request-timeout: 45000
