package com.banking.dedupe.service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.banking.dedupe.dto.AuditTrailDto;
import com.banking.dedupe.entity.AuditLog;
import com.banking.dedupe.entity.DedupeRequest;
import com.banking.dedupe.repository.AuditLogRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Service for managing audit logs and compliance tracking
 * 
 * Provides comprehensive audit trail functionality for all deduplication
 * operations with PII masking and RBI compliance features.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Service
@Transactional
public class AuditService {

    private static final Logger logger = LoggerFactory.getLogger(AuditService.class);

    private final AuditLogRepository auditLogRepository;
    private final PiiMaskingService piiMaskingService;
    private final ObjectMapper objectMapper;

    @Autowired
    public AuditService(AuditLogRepository auditLogRepository, 
                       PiiMaskingService piiMaskingService,
                       ObjectMapper objectMapper) {
        this.auditLogRepository = auditLogRepository;
        this.piiMaskingService = piiMaskingService;
        this.objectMapper = objectMapper;
    }

    /**
     * Log request initiation
     * 
     * @param request The dedupe request
     * @param clientIp The client IP address
     * @param userAgent The user agent
     */
    public void logRequestInitiation(DedupeRequest request, String clientIp, String userAgent) {
        try {
            AuditLog auditLog = new AuditLog(request, "REQUEST_RECEIVED", 
                "Customer deduplication request received from upstream application");
            
            auditLog.setCurrentStatus(request.getRequestStatus().name());
            auditLog.setClientIp(clientIp);
            auditLog.setUserAgent(userAgent);
            
            // Create masked event data
            Map<String, Object> eventData = createMaskedRequestData(request);
            auditLog.setEventData(convertToJson(eventData));
            
            auditLogRepository.save(auditLog);
            
            logger.info("Request initiation logged for reference: {}", request.getReferenceNumber());
            
        } catch (Exception e) {
            logger.error("Failed to log request initiation for reference: {}", 
                request.getReferenceNumber(), e);
        }
    }

    /**
     * Log validation completion
     * 
     * @param request The dedupe request
     * @param validationResult The validation result
     */
    public void logValidationCompletion(DedupeRequest request, boolean validationResult) {
        try {
            String eventType = validationResult ? "VALIDATION_SUCCESS" : "VALIDATION_FAILURE";
            String description = validationResult ? 
                "Request validation completed successfully" : 
                "Request validation failed";
            
            AuditLog auditLog = new AuditLog(request, eventType, description);
            auditLog.setPreviousStatus("RECEIVED");
            auditLog.setCurrentStatus(request.getRequestStatus().name());
            
            if (!validationResult) {
                auditLog.setErrorCode("VALIDATION_ERROR");
                auditLog.setErrorMessage("One or more validation rules failed");
            }
            
            auditLogRepository.save(auditLog);
            
            logger.info("Validation completion logged for reference: {} - Result: {}", 
                request.getReferenceNumber(), validationResult);
            
        } catch (Exception e) {
            logger.error("Failed to log validation completion for reference: {}", 
                request.getReferenceNumber(), e);
        }
    }

    /**
     * Log vendor call initiation
     * 
     * @param request The dedupe request
     * @param vendorName The vendor name
     * @param retryAttempt The retry attempt number
     */
    public void logVendorCallInitiation(DedupeRequest request, String vendorName, int retryAttempt) {
        try {
            String description = retryAttempt > 0 ? 
                String.format("Vendor API call initiated (retry attempt %d)", retryAttempt) :
                "Vendor API call initiated";
            
            AuditLog auditLog = new AuditLog(request, "VENDOR_CALL_INITIATED", description);
            auditLog.setPreviousStatus("VALIDATED");
            auditLog.setCurrentStatus("VENDOR_CALL_INITIATED");
            auditLog.setRetryAttempt(retryAttempt);
            
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("vendorName", vendorName);
            eventData.put("retryAttempt", retryAttempt);
            eventData.put("timestamp", LocalDateTime.now());
            
            auditLog.setEventData(convertToJson(eventData));
            
            auditLogRepository.save(auditLog);
            
            logger.info("Vendor call initiation logged for reference: {} - Vendor: {} - Retry: {}", 
                request.getReferenceNumber(), vendorName, retryAttempt);
            
        } catch (Exception e) {
            logger.error("Failed to log vendor call initiation for reference: {}", 
                request.getReferenceNumber(), e);
        }
    }

    /**
     * Log vendor call completion
     * 
     * @param request The dedupe request
     * @param vendorName The vendor name
     * @param success Whether the call was successful
     * @param processingTimeMs The processing time in milliseconds
     * @param errorCode The error code if failed
     * @param errorMessage The error message if failed
     */
    public void logVendorCallCompletion(DedupeRequest request, String vendorName, boolean success, 
                                      Integer processingTimeMs, String errorCode, String errorMessage) {
        try {
            String eventType = success ? "VENDOR_CALL_SUCCESS" : "VENDOR_CALL_FAILURE";
            String description = success ? 
                "Vendor API call completed successfully" : 
                "Vendor API call failed";
            
            AuditLog auditLog = new AuditLog(request, eventType, description);
            auditLog.setPreviousStatus("VENDOR_CALL_INITIATED");
            auditLog.setCurrentStatus(success ? "VENDOR_CALL_SUCCESS" : "VENDOR_CALL_FAILED");
            auditLog.setProcessingTimeMs(processingTimeMs);
            
            if (!success) {
                auditLog.setErrorCode(errorCode);
                auditLog.setErrorMessage(errorMessage);
            }
            
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("vendorName", vendorName);
            eventData.put("success", success);
            eventData.put("processingTimeMs", processingTimeMs);
            if (!success) {
                eventData.put("errorCode", errorCode);
                eventData.put("errorMessage", errorMessage);
            }
            
            auditLog.setEventData(convertToJson(eventData));
            
            auditLogRepository.save(auditLog);
            
            logger.info("Vendor call completion logged for reference: {} - Success: {} - Time: {}ms", 
                request.getReferenceNumber(), success, processingTimeMs);
            
        } catch (Exception e) {
            logger.error("Failed to log vendor call completion for reference: {}", 
                request.getReferenceNumber(), e);
        }
    }

    /**
     * Log response interpretation
     * 
     * @param request The dedupe request
     * @param finalStatus The final interpreted status
     * @param duplicatesFound The number of duplicates found
     */
    public void logResponseInterpretation(DedupeRequest request, String finalStatus, int duplicatesFound) {
        try {
            AuditLog auditLog = new AuditLog(request, "RESPONSE_INTERPRETED", 
                "Vendor response interpreted and final status determined");
            
            auditLog.setPreviousStatus("VENDOR_CALL_SUCCESS");
            auditLog.setCurrentStatus("RESPONSE_INTERPRETED");
            
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("finalStatus", finalStatus);
            eventData.put("duplicatesFound", duplicatesFound);
            eventData.put("timestamp", LocalDateTime.now());
            
            auditLog.setEventData(convertToJson(eventData));
            
            auditLogRepository.save(auditLog);
            
            logger.info("Response interpretation logged for reference: {} - Status: {} - Duplicates: {}", 
                request.getReferenceNumber(), finalStatus, duplicatesFound);
            
        } catch (Exception e) {
            logger.error("Failed to log response interpretation for reference: {}", 
                request.getReferenceNumber(), e);
        }
    }

    /**
     * Log final completion
     * 
     * @param request The dedupe request
     * @param totalProcessingTimeMs The total processing time
     * @param upstreamResponseStatus The upstream response status
     */
    public void logFinalCompletion(DedupeRequest request, Long totalProcessingTimeMs, String upstreamResponseStatus) {
        try {
            AuditLog auditLog = new AuditLog(request, "REQUEST_COMPLETED", 
                "Customer deduplication request processing completed");
            
            auditLog.setPreviousStatus("RESPONSE_INTERPRETED");
            auditLog.setCurrentStatus("COMPLETED");
            auditLog.setProcessingTimeMs(totalProcessingTimeMs != null ? totalProcessingTimeMs.intValue() : null);
            
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("totalProcessingTimeMs", totalProcessingTimeMs);
            eventData.put("upstreamResponseStatus", upstreamResponseStatus);
            eventData.put("completedAt", LocalDateTime.now());
            
            auditLog.setEventData(convertToJson(eventData));
            
            auditLogRepository.save(auditLog);
            
            logger.info("Final completion logged for reference: {} - Total time: {}ms", 
                request.getReferenceNumber(), totalProcessingTimeMs);
            
        } catch (Exception e) {
            logger.error("Failed to log final completion for reference: {}", 
                request.getReferenceNumber(), e);
        }
    }

    /**
     * Log error occurrence
     * 
     * @param request The dedupe request
     * @param errorCode The error code
     * @param errorMessage The error message
     * @param exception The exception if available
     */
    public void logError(DedupeRequest request, String errorCode, String errorMessage, Exception exception) {
        try {
            AuditLog auditLog = new AuditLog(request, "ERROR", 
                "Error occurred during request processing");
            
            auditLog.setCurrentStatus("FAILED");
            auditLog.setErrorCode(errorCode);
            auditLog.setErrorMessage(errorMessage);
            
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("errorCode", errorCode);
            eventData.put("errorMessage", errorMessage);
            if (exception != null) {
                eventData.put("exceptionType", exception.getClass().getSimpleName());
                eventData.put("exceptionMessage", exception.getMessage());
            }
            eventData.put("timestamp", LocalDateTime.now());
            
            auditLog.setEventData(convertToJson(eventData));
            
            auditLogRepository.save(auditLog);
            
            logger.error("Error logged for reference: {} - Code: {} - Message: {}", 
                request.getReferenceNumber(), errorCode, errorMessage);
            
        } catch (Exception e) {
            logger.error("Failed to log error for reference: {}", 
                request.getReferenceNumber(), e);
        }
    }

    /**
     * Get audit trail for a request
     *
     * @param referenceNumber The reference number
     * @return List of audit logs
     */
    @Transactional(readOnly = true)
    public List<AuditLog> getAuditTrail(String referenceNumber) {
        return auditLogRepository.findByReferenceNumberOrderByCreatedAt(referenceNumber);
    }

    /**
     * Get audit trail DTOs for a request
     *
     * @param referenceNumber The reference number
     * @return List of audit trail DTOs
     */
    @Transactional(readOnly = true)
    public List<AuditTrailDto> getAuditTrailDtos(String referenceNumber) {
        List<AuditLog> auditLogs = auditLogRepository.findByReferenceNumberOrderByCreatedAt(referenceNumber);
        return auditLogs.stream()
                .map(AuditTrailDto::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * Create masked request data for audit logging
     * 
     * @param request The dedupe request
     * @return Masked request data
     */
    private Map<String, Object> createMaskedRequestData(DedupeRequest request) {
        Map<String, Object> maskedData = new HashMap<>();
        
        maskedData.put("customerIdentifier", 
            piiMaskingService.maskCustomerIdentifier(request.getCustomerIdentifier()));
        maskedData.put("fullName", 
            piiMaskingService.maskFullName(request.getFullName()));
        maskedData.put("pan", 
            piiMaskingService.maskPan(request.getPan()));
        maskedData.put("aadhaar", 
            piiMaskingService.maskAadhaar(request.getAadhaar()));
        maskedData.put("mobileNumber", 
            piiMaskingService.maskMobile(request.getMobileNumber()));
        maskedData.put("emailAddress", 
            piiMaskingService.maskEmail(request.getEmailAddress()));
        maskedData.put("upstreamApplication", request.getUpstreamApplication());
        maskedData.put("dateOfBirth", request.getDateOfBirth());
        
        return maskedData;
    }

    /**
     * Convert object to JSON string
     * 
     * @param object The object to convert
     * @return JSON string
     */
    private String convertToJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            logger.warn("Failed to convert object to JSON: {}", e.getMessage());
            return "{}";
        }
    }

    /**
     * Set trace ID in MDC for logging correlation
     * 
     * @param traceId The trace ID
     */
    public void setTraceId(String traceId) {
        MDC.put("traceId", traceId);
    }

    /**
     * Clear trace ID from MDC
     */
    public void clearTraceId() {
        MDC.remove("traceId");
    }
}
