package com.banking.dedupe.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDate;

/**
 * Data Transfer Object for Vendor API Request
 * 
 * Represents the request payload sent to third-party deduplication vendors.
 * This DTO maps internal customer data to the format expected by vendor APIs.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class VendorRequestDto {

    @JsonProperty("customerIdentifier")
    private String customerIdentifier;

    @JsonProperty("customerData")
    private VendorCustomerDataDto customerData;

    /**
     * Default constructor
     */
    public VendorRequestDto() {
    }

    /**
     * Constructor with all fields
     */
    public VendorRequestDto(String customerIdentifier, VendorCustomerDataDto customerData) {
        this.customerIdentifier = customerIdentifier;
        this.customerData = customerData;
    }

    // Getters and Setters

    public String getCustomerIdentifier() {
        return customerIdentifier;
    }

    public void setCustomerIdentifier(String customerIdentifier) {
        this.customerIdentifier = customerIdentifier;
    }

    public VendorCustomerDataDto getCustomerData() {
        return customerData;
    }

    public void setCustomerData(VendorCustomerDataDto customerData) {
        this.customerData = customerData;
    }

    /**
     * Nested DTO for Vendor Customer Data
     */
    public static class VendorCustomerDataDto {

        @JsonProperty("fullName")
        private String fullName;

        @JsonProperty("dateOfBirth")
        private LocalDate dateOfBirth;

        @JsonProperty("pan")
        private String pan;

        @JsonProperty("aadhaar")
        private String aadhaar;

        @JsonProperty("mobileNumber")
        private String mobileNumber;

        @JsonProperty("emailAddress")
        private String emailAddress;

        @JsonProperty("address")
        private VendorAddressDto address;

        /**
         * Default constructor
         */
        public VendorCustomerDataDto() {
        }

        /**
         * Constructor with all fields
         */
        public VendorCustomerDataDto(String fullName, LocalDate dateOfBirth, String pan, String aadhaar,
                                   String mobileNumber, String emailAddress, VendorAddressDto address) {
            this.fullName = fullName;
            this.dateOfBirth = dateOfBirth;
            this.pan = pan;
            this.aadhaar = aadhaar;
            this.mobileNumber = mobileNumber;
            this.emailAddress = emailAddress;
            this.address = address;
        }

        // Getters and Setters

        public String getFullName() {
            return fullName;
        }

        public void setFullName(String fullName) {
            this.fullName = fullName;
        }

        public LocalDate getDateOfBirth() {
            return dateOfBirth;
        }

        public void setDateOfBirth(LocalDate dateOfBirth) {
            this.dateOfBirth = dateOfBirth;
        }

        public String getPan() {
            return pan;
        }

        public void setPan(String pan) {
            this.pan = pan;
        }

        public String getAadhaar() {
            return aadhaar;
        }

        public void setAadhaar(String aadhaar) {
            this.aadhaar = aadhaar;
        }

        public String getMobileNumber() {
            return mobileNumber;
        }

        public void setMobileNumber(String mobileNumber) {
            this.mobileNumber = mobileNumber;
        }

        public String getEmailAddress() {
            return emailAddress;
        }

        public void setEmailAddress(String emailAddress) {
            this.emailAddress = emailAddress;
        }

        public VendorAddressDto getAddress() {
            return address;
        }

        public void setAddress(VendorAddressDto address) {
            this.address = address;
        }
    }

    /**
     * Nested DTO for Vendor Address Information
     */
    public static class VendorAddressDto {

        @JsonProperty("line1")
        private String line1;

        @JsonProperty("city")
        private String city;

        @JsonProperty("state")
        private String state;

        @JsonProperty("pincode")
        private String pincode;

        /**
         * Default constructor
         */
        public VendorAddressDto() {
        }

        /**
         * Constructor with all fields
         */
        public VendorAddressDto(String line1, String city, String state, String pincode) {
            this.line1 = line1;
            this.city = city;
            this.state = state;
            this.pincode = pincode;
        }

        // Getters and Setters

        public String getLine1() {
            return line1;
        }

        public void setLine1(String line1) {
            this.line1 = line1;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getPincode() {
            return pincode;
        }

        public void setPincode(String pincode) {
            this.pincode = pincode;
        }
    }
}
