package com.banking.dedupe.dto;

import java.time.LocalDateTime;
import java.util.List;

import com.banking.dedupe.enums.VendorStatus;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Data Transfer Object for Customer Deduplication Response
 * 
 * Represents the response sent back to upstream applications after deduplication processing.
 * Contains the final deduplication result and any duplicate customer information found.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class CustomerDedupeResponseDto {

    @JsonProperty("referenceNumber")
    private String referenceNumber;

    @JsonProperty("customerIdentifier")
    private String customerIdentifier;

    @JsonProperty("dedupeStatus")
    private VendorStatus dedupeStatus;

    @JsonProperty("message")
    private String message;

    @JsonProperty("duplicates")
    private List<DuplicateCustomerDto> duplicates;

    @JsonProperty("vendorDetails")
    private VendorDetailsDto vendorDetails;

    @JsonProperty("auditInfo")
    private AuditInfoDto auditInfo;

    /**
     * Default constructor
     */
    public CustomerDedupeResponseDto() {
    }

    /**
     * Constructor with all fields
     */
    public CustomerDedupeResponseDto(String referenceNumber, String customerIdentifier, 
                                   VendorStatus dedupeStatus, String message,
                                   List<DuplicateCustomerDto> duplicates, 
                                   VendorDetailsDto vendorDetails, AuditInfoDto auditInfo) {
        this.referenceNumber = referenceNumber;
        this.customerIdentifier = customerIdentifier;
        this.dedupeStatus = dedupeStatus;
        this.message = message;
        this.duplicates = duplicates;
        this.vendorDetails = vendorDetails;
        this.auditInfo = auditInfo;
    }

    // Getters and Setters

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getCustomerIdentifier() {
        return customerIdentifier;
    }

    public void setCustomerIdentifier(String customerIdentifier) {
        this.customerIdentifier = customerIdentifier;
    }

    public VendorStatus getDedupeStatus() {
        return dedupeStatus;
    }

    public void setDedupeStatus(VendorStatus dedupeStatus) {
        this.dedupeStatus = dedupeStatus;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<DuplicateCustomerDto> getDuplicates() {
        return duplicates;
    }

    public void setDuplicates(List<DuplicateCustomerDto> duplicates) {
        this.duplicates = duplicates;
    }

    public VendorDetailsDto getVendorDetails() {
        return vendorDetails;
    }

    public void setVendorDetails(VendorDetailsDto vendorDetails) {
        this.vendorDetails = vendorDetails;
    }

    public AuditInfoDto getAuditInfo() {
        return auditInfo;
    }

    public void setAuditInfo(AuditInfoDto auditInfo) {
        this.auditInfo = auditInfo;
    }

    /**
     * Nested DTO for Duplicate Customer Information
     */
    public static class DuplicateCustomerDto {

        @JsonProperty("customerId")
        private String customerId;

        @JsonProperty("matchScore")
        private Integer matchScore;

        @JsonProperty("matchedFields")
        private List<String> matchedFields;

        @JsonProperty("customerStatus")
        private String customerStatus;

        @JsonProperty("createdDate")
        private LocalDateTime createdDate;

        /**
         * Default constructor
         */
        public DuplicateCustomerDto() {
        }

        /**
         * Constructor with all fields
         */
        public DuplicateCustomerDto(String customerId, Integer matchScore, List<String> matchedFields,
                                   String customerStatus, LocalDateTime createdDate) {
            this.customerId = customerId;
            this.matchScore = matchScore;
            this.matchedFields = matchedFields;
            this.customerStatus = customerStatus;
            this.createdDate = createdDate;
        }

        // Getters and Setters

        public String getCustomerId() {
            return customerId;
        }

        public void setCustomerId(String customerId) {
            this.customerId = customerId;
        }

        public Integer getMatchScore() {
            return matchScore;
        }

        public void setMatchScore(Integer matchScore) {
            this.matchScore = matchScore;
        }

        public List<String> getMatchedFields() {
            return matchedFields;
        }

        public void setMatchedFields(List<String> matchedFields) {
            this.matchedFields = matchedFields;
        }

        public String getCustomerStatus() {
            return customerStatus;
        }

        public void setCustomerStatus(String customerStatus) {
            this.customerStatus = customerStatus;
        }

        public LocalDateTime getCreatedDate() {
            return createdDate;
        }

        public void setCreatedDate(LocalDateTime createdDate) {
            this.createdDate = createdDate;
        }
    }

    /**
     * Nested DTO for Vendor Details
     */
    public static class VendorDetailsDto {

        @JsonProperty("vendorName")
        private String vendorName;



        @JsonProperty("processingTimestamp")
        private LocalDateTime processingTimestamp;

        /**
         * Default constructor
         */
        public VendorDetailsDto() {
        }

        /**
         * Constructor with all fields
         */
        public VendorDetailsDto(String vendorName, LocalDateTime processingTimestamp) {
            this.vendorName = vendorName;
            this.processingTimestamp = processingTimestamp;
        }

        // Getters and Setters

        public String getVendorName() {
            return vendorName;
        }

        public void setVendorName(String vendorName) {
            this.vendorName = vendorName;
        }



        public LocalDateTime getProcessingTimestamp() {
            return processingTimestamp;
        }

        public void setProcessingTimestamp(LocalDateTime processingTimestamp) {
            this.processingTimestamp = processingTimestamp;
        }
    }

    /**
     * Nested DTO for Audit Information
     */
    public static class AuditInfoDto {

        @JsonProperty("requestTimestamp")
        private LocalDateTime requestTimestamp;

        @JsonProperty("responseTimestamp")
        private LocalDateTime responseTimestamp;

        @JsonProperty("processingTimeMs")
        private Long processingTimeMs;

        /**
         * Default constructor
         */
        public AuditInfoDto() {
        }

        /**
         * Constructor with all fields
         */
        public AuditInfoDto(LocalDateTime requestTimestamp, LocalDateTime responseTimestamp, Long processingTimeMs) {
            this.requestTimestamp = requestTimestamp;
            this.responseTimestamp = responseTimestamp;
            this.processingTimeMs = processingTimeMs;
        }

        // Getters and Setters

        public LocalDateTime getRequestTimestamp() {
            return requestTimestamp;
        }

        public void setRequestTimestamp(LocalDateTime requestTimestamp) {
            this.requestTimestamp = requestTimestamp;
        }

        public LocalDateTime getResponseTimestamp() {
            return responseTimestamp;
        }

        public void setResponseTimestamp(LocalDateTime responseTimestamp) {
            this.responseTimestamp = responseTimestamp;
        }

        public Long getProcessingTimeMs() {
            return processingTimeMs;
        }

        public void setProcessingTimeMs(Long processingTimeMs) {
            this.processingTimeMs = processingTimeMs;
        }
    }
}
