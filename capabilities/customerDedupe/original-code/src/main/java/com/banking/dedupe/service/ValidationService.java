package com.banking.dedupe.service;

import java.time.LocalDate;
import java.time.Period;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.banking.dedupe.dto.CustomerDedupeRequestDto;

/**
 * Service for validating customer deduplication requests
 * 
 * Provides comprehensive validation of incoming requests including
 * format validation, business rule validation, and data integrity checks.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Service
public class ValidationService {

    private static final Logger logger = LoggerFactory.getLogger(ValidationService.class);

    @Value("${app.validation.pan-pattern:[A-Z]{5}[0-9]{4}[A-Z]{1}}")
    private String panPattern;

    @Value("${app.validation.aadhaar-pattern:[0-9]{4}-[0-9]{4}-[0-9]{4}}")
    private String aadhaarPattern;

    @Value("${app.validation.mobile-pattern:[6-9][0-9]{9}}")
    private String mobilePattern;

    @Value("${app.validation.email-pattern:^[A-Za-z0-9+_.-]+@(.+)$}")
    private String emailPattern;

    private Pattern panRegex;
    private Pattern aadhaarRegex;
    private Pattern mobileRegex;
    private Pattern emailRegex;

    /**
     * Initialize regex patterns
     */
    public void initializePatterns() {
        // Use default patterns if @Value injection failed (e.g., in tests)
        String defaultPanPattern = "[A-Z]{5}[0-9]{4}[A-Z]{1}";
        String defaultAadhaarPattern = "[0-9]{4}-[0-9]{4}-[0-9]{4}";
        String defaultMobilePattern = "[6-9][0-9]{9}";
        String defaultEmailPattern = "^[A-Za-z0-9+_.-]+@(.+)$";

        this.panRegex = Pattern.compile(panPattern != null ? panPattern : defaultPanPattern);
        this.aadhaarRegex = Pattern.compile(aadhaarPattern != null ? aadhaarPattern : defaultAadhaarPattern);
        this.mobileRegex = Pattern.compile(mobilePattern != null ? mobilePattern : defaultMobilePattern);
        this.emailRegex = Pattern.compile(emailPattern != null ? emailPattern : defaultEmailPattern);
    }

    /**
     * Validate customer deduplication request
     * 
     * @param request The request to validate
     * @return ValidationResult containing validation status and errors
     */
    public ValidationResult validateRequest(CustomerDedupeRequestDto request) {
        if (panRegex == null) {
            initializePatterns();
        }

        List<String> errors = new ArrayList<>();

        try {
            // Validate basic request structure
            if (request == null) {
                errors.add("Request cannot be null");
                return new ValidationResult(false, errors);
            }

            // Validate customer identifier
            validateCustomerIdentifier(request.getCustomerIdentifier(), errors);

            // Validate customer data
            if (request.getCustomerData() == null) {
                errors.add("Customer data is required");
                return new ValidationResult(false, errors);
            }

            validateCustomerData(request.getCustomerData(), errors);

            // Validate upstream application info
            validateUpstreamInfo(request.getUpstreamApplication(), request.getUpstreamRequestId(), errors);

            boolean isValid = errors.isEmpty();
            
            logger.info("Request validation completed - Valid: {} - Errors: {}", isValid, errors.size());
            
            return new ValidationResult(isValid, errors);

        } catch (Exception e) {
            logger.error("Unexpected error during validation", e);
            errors.add("Validation failed due to system error");
            return new ValidationResult(false, errors);
        }
    }

    /**
     * Validate customer identifier
     * 
     * @param customerIdentifier The customer identifier
     * @param errors The list to add errors to
     */
    private void validateCustomerIdentifier(String customerIdentifier, List<String> errors) {
        if (customerIdentifier == null || customerIdentifier.trim().isEmpty()) {
            errors.add("Customer identifier is required");
            return;
        }

        if (customerIdentifier.length() > 100) {
            errors.add("Customer identifier must not exceed 100 characters");
        }

        // Check for valid characters (alphanumeric and common separators)
        if (!customerIdentifier.matches("^[A-Za-z0-9_-]+$")) {
            errors.add("Customer identifier contains invalid characters");
        }
    }

    /**
     * Validate customer data
     * 
     * @param customerData The customer data
     * @param errors The list to add errors to
     */
    private void validateCustomerData(CustomerDedupeRequestDto.CustomerDataDto customerData, List<String> errors) {
        // Validate full name
        validateFullName(customerData.getFullName(), errors);

        // Validate date of birth
        validateDateOfBirth(customerData.getDateOfBirth(), errors);

        // Validate PAN
        validatePan(customerData.getPan(), errors);

        // Validate Aadhaar (optional)
        if (customerData.getAadhaar() != null) {
            validateAadhaar(customerData.getAadhaar(), errors);
        }

        // Validate mobile number
        validateMobileNumber(customerData.getMobileNumber(), errors);

        // Validate email (optional)
        if (customerData.getEmailAddress() != null) {
            validateEmail(customerData.getEmailAddress(), errors);
        }

        // Validate address (optional)
        if (customerData.getAddress() != null) {
            validateAddress(customerData.getAddress(), errors);
        }
    }

    /**
     * Validate full name
     * 
     * @param fullName The full name
     * @param errors The list to add errors to
     */
    private void validateFullName(String fullName, List<String> errors) {
        if (fullName == null || fullName.trim().isEmpty()) {
            errors.add("Full name is required");
            return;
        }

        if (fullName.length() > 200) {
            errors.add("Full name must not exceed 200 characters");
        }

        // Check for valid name characters (letters, spaces, common punctuation)
        if (!fullName.matches("^[A-Za-z\\s.',-]+$")) {
            errors.add("Full name contains invalid characters");
        }

        // Check minimum length
        if (fullName.trim().length() < 2) {
            errors.add("Full name must be at least 2 characters long");
        }
    }

    /**
     * Validate date of birth
     * 
     * @param dateOfBirth The date of birth
     * @param errors The list to add errors to
     */
    private void validateDateOfBirth(LocalDate dateOfBirth, List<String> errors) {
        if (dateOfBirth == null) {
            errors.add("Date of birth is required");
            return;
        }

        LocalDate today = LocalDate.now();
        
        // Check if date is in the future
        if (dateOfBirth.isAfter(today)) {
            errors.add("Date of birth cannot be in the future");
        }

        // Check minimum age (18 years)
        Period age = Period.between(dateOfBirth, today);
        if (age.getYears() < 18) {
            errors.add("Customer must be at least 18 years old");
        }

        // Check maximum age (120 years)
        if (age.getYears() > 120) {
            errors.add("Invalid date of birth - age exceeds 120 years");
        }
    }

    /**
     * Validate PAN
     * 
     * @param pan The PAN number
     * @param errors The list to add errors to
     */
    private void validatePan(String pan, List<String> errors) {
        if (pan == null || pan.trim().isEmpty()) {
            errors.add("PAN is required");
            return;
        }

        if (!panRegex.matcher(pan).matches()) {
            errors.add("Invalid PAN format. Expected format: **********");
        }

        // Additional PAN validation rules
        if (pan.length() != 10) {
            errors.add("PAN must be exactly 10 characters long");
        }
    }

    /**
     * Validate Aadhaar
     * 
     * @param aadhaar The Aadhaar number
     * @param errors The list to add errors to
     */
    private void validateAadhaar(String aadhaar, List<String> errors) {
        if (aadhaar == null || aadhaar.trim().isEmpty()) {
            return; // Aadhaar is optional
        }

        if (!aadhaarRegex.matcher(aadhaar).matches()) {
            errors.add("Invalid Aadhaar format. Expected format: 1234-5678-9012");
        }

        // Check for all zeros or repeated digits
        String digitsOnly = aadhaar.replaceAll("-", "");
        if (digitsOnly.matches("0{12}") || digitsOnly.matches("(\\d)\\1{11}")) {
            errors.add("Invalid Aadhaar number - cannot be all zeros or repeated digits");
        }
    }

    /**
     * Validate mobile number
     * 
     * @param mobileNumber The mobile number
     * @param errors The list to add errors to
     */
    private void validateMobileNumber(String mobileNumber, List<String> errors) {
        if (mobileNumber == null || mobileNumber.trim().isEmpty()) {
            errors.add("Mobile number is required");
            return;
        }

        if (!mobileRegex.matcher(mobileNumber).matches()) {
            errors.add("Invalid mobile number format. Must start with 6-9 and be 10 digits long");
        }

        // Check for repeated digits
        if (mobileNumber.matches("(\\d)\\1{9}")) {
            errors.add("Invalid mobile number - cannot be all same digits");
        }
    }

    /**
     * Validate email
     * 
     * @param email The email address
     * @param errors The list to add errors to
     */
    private void validateEmail(String email, List<String> errors) {
        if (email == null || email.trim().isEmpty()) {
            return; // Email is optional
        }

        if (email.length() > 100) {
            errors.add("Email address must not exceed 100 characters");
        }

        if (!emailRegex.matcher(email).matches()) {
            errors.add("Invalid email format");
        }
    }

    /**
     * Validate address
     * 
     * @param address The address
     * @param errors The list to add errors to
     */
    private void validateAddress(CustomerDedupeRequestDto.AddressDto address, List<String> errors) {
        if (address.getLine1() != null && address.getLine1().length() > 200) {
            errors.add("Address line 1 must not exceed 200 characters");
        }

        if (address.getCity() != null && address.getCity().length() > 100) {
            errors.add("City must not exceed 100 characters");
        }

        if (address.getState() != null && address.getState().length() > 100) {
            errors.add("State must not exceed 100 characters");
        }

        if (address.getPincode() != null) {
            if (!address.getPincode().matches("^[0-9]{6}$")) {
                errors.add("Invalid pincode format. Must be 6 digits");
            }
        }
    }

    /**
     * Validate upstream information
     * 
     * @param upstreamApplication The upstream application
     * @param upstreamRequestId The upstream request ID
     * @param errors The list to add errors to
     */
    private void validateUpstreamInfo(String upstreamApplication, String upstreamRequestId, List<String> errors) {
        if (upstreamApplication != null && upstreamApplication.length() > 100) {
            errors.add("Upstream application name must not exceed 100 characters");
        }

        if (upstreamRequestId != null && upstreamRequestId.length() > 100) {
            errors.add("Upstream request ID must not exceed 100 characters");
        }
    }

    /**
     * Validation result class
     */
    public static class ValidationResult {
        private final boolean valid;
        private final List<String> errors;

        public ValidationResult(boolean valid, List<String> errors) {
            this.valid = valid;
            this.errors = errors != null ? new ArrayList<>(errors) : new ArrayList<>();
        }

        public boolean isValid() {
            return valid;
        }

        public List<String> getErrors() {
            return new ArrayList<>(errors);
        }

        public String getErrorMessage() {
            return String.join("; ", errors);
        }

        public boolean hasErrors() {
            return !errors.isEmpty();
        }
    }
}
