package com.banking.dedupe.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * Configuration for RestTemplate used in vendor integration
 * 
 * Configures HTTP client settings for calling third-party vendor APIs
 * with appropriate timeouts and connection pooling.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Configuration
public class RestTemplateConfig {

    @Value("${vendor.dedupe.timeout:30000}")
    private int vendorTimeout;

    @Value("${vendor.dedupe.connection-timeout:10000}")
    private int connectionTimeout;

    /**
     * Configure RestTemplate for vendor API calls
     * 
     * @param builder RestTemplate builder
     * @return Configured RestTemplate
     */
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        return builder
            .setConnectTimeout(Duration.ofMillis(connectionTimeout))
            .setReadTimeout(Duration.ofMillis(vendorTimeout))
            .requestFactory(this::clientHttpRequestFactory)
            .build();
    }

    /**
     * Configure HTTP request factory
     * 
     * @return ClientHttpRequestFactory with custom settings
     */
    private ClientHttpRequestFactory clientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectionTimeout);
        factory.setReadTimeout(vendorTimeout);
        return factory;
    }
}
