package com.banking.dedupe.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * Service for masking Personally Identifiable Information (PII)
 * 
 * Provides methods to mask sensitive customer data for logging and audit purposes
 * in compliance with data privacy regulations and RBI guidelines.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Service
public class PiiMaskingService {

    @Value("${audit.pii-masking.pan-mask-pattern:XX****XXXX}")
    private String panMaskPattern;

    @Value("${audit.pii-masking.aadhaar-mask-pattern:XXXX-XXXX-****}")
    private String aadhaarMaskPattern;

    @Value("${audit.pii-masking.mobile-mask-pattern:****XX****}")
    private String mobileMaskPattern;

    /**
     * Mask PAN number for logging
     * 
     * @param pan The PAN number to mask
     * @return Masked PAN number
     */
    public String maskPan(String pan) {
        if (pan == null || pan.length() < 10) {
            return "INVALID_PAN";
        }
        
        // Show first 2 and last 1 characters, mask the rest
        return pan.substring(0, 2) + "****" + pan.substring(6, 9) + "*" + pan.substring(9);
    }

    /**
     * Mask Aadhaar number for logging
     * 
     * @param aadhaar The Aadhaar number to mask
     * @return Masked Aadhaar number
     */
    public String maskAadhaar(String aadhaar) {
        if (aadhaar == null || aadhaar.length() < 14) {
            return "INVALID_AADHAAR";
        }
        
        // Show only last 4 digits, mask the rest
        return "XXXX-XXXX-" + aadhaar.substring(10);
    }

    /**
     * Mask mobile number for logging
     * 
     * @param mobile The mobile number to mask
     * @return Masked mobile number
     */
    public String maskMobile(String mobile) {
        if (mobile == null || mobile.length() < 10) {
            return "INVALID_MOBILE";
        }
        
        // Show first 2 and last 2 digits, mask the rest
        return mobile.substring(0, 2) + "****" + mobile.substring(mobile.length() - 2);
    }

    /**
     * Mask email address for logging
     * 
     * @param email The email address to mask
     * @return Masked email address
     */
    public String maskEmail(String email) {
        if (email == null || !email.contains("@")) {
            return "INVALID_EMAIL";
        }
        
        String[] parts = email.split("@");
        String localPart = parts[0];
        String domain = parts[1];
        
        if (localPart.length() <= 2) {
            return "**@" + domain;
        }
        
        return localPart.substring(0, 2) + "****@" + domain;
    }

    /**
     * Mask full name for logging
     * 
     * @param fullName The full name to mask
     * @return Masked full name
     */
    public String maskFullName(String fullName) {
        if (fullName == null || fullName.trim().isEmpty()) {
            return "INVALID_NAME";
        }
        
        String[] nameParts = fullName.trim().split("\\s+");
        if (nameParts.length == 1) {
            return maskSingleName(nameParts[0]);
        }
        
        StringBuilder maskedName = new StringBuilder();
        for (int i = 0; i < nameParts.length; i++) {
            if (i > 0) {
                maskedName.append(" ");
            }
            maskedName.append(maskSingleName(nameParts[i]));
        }
        
        return maskedName.toString();
    }

    /**
     * Mask a single name part
     * 
     * @param name The name to mask
     * @return Masked name
     */
    private String maskSingleName(String name) {
        if (name.length() <= 2) {
            return "**";
        }
        return name.substring(0, 1) + "*".repeat(name.length() - 2) + name.substring(name.length() - 1);
    }

    /**
     * Mask address for logging
     * 
     * @param address The address to mask
     * @return Masked address
     */
    public String maskAddress(String address) {
        if (address == null || address.trim().isEmpty()) {
            return "INVALID_ADDRESS";
        }
        
        // Keep first few characters and mask the rest
        if (address.length() <= 10) {
            return "****";
        }
        
        return address.substring(0, 3) + "****" + address.substring(address.length() - 3);
    }

    /**
     * Mask customer identifier for logging
     * 
     * @param customerIdentifier The customer identifier to mask
     * @return Masked customer identifier
     */
    public String maskCustomerIdentifier(String customerIdentifier) {
        if (customerIdentifier == null || customerIdentifier.trim().isEmpty()) {
            return "INVALID_CUSTOMER_ID";
        }
        
        if (customerIdentifier.length() <= 4) {
            return "****";
        }
        
        return customerIdentifier.substring(0, 2) + "****" + 
               customerIdentifier.substring(customerIdentifier.length() - 2);
    }

    /**
     * Create a masked version of customer data for logging
     * 
     * @param fullName The full name
     * @param pan The PAN number
     * @param aadhaar The Aadhaar number
     * @param mobile The mobile number
     * @param email The email address
     * @return Masked customer data string
     */
    public String createMaskedCustomerData(String fullName, String pan, String aadhaar, 
                                         String mobile, String email) {
        StringBuilder maskedData = new StringBuilder();
        maskedData.append("Name: ").append(maskFullName(fullName));
        maskedData.append(", PAN: ").append(maskPan(pan));
        
        if (aadhaar != null) {
            maskedData.append(", Aadhaar: ").append(maskAadhaar(aadhaar));
        }
        
        maskedData.append(", Mobile: ").append(maskMobile(mobile));
        
        if (email != null) {
            maskedData.append(", Email: ").append(maskEmail(email));
        }
        
        return maskedData.toString();
    }

    /**
     * Check if PII masking is enabled
     * 
     * @return true if PII masking is enabled
     */
    public boolean isPiiMaskingEnabled() {
        return true; // Always enabled for compliance
    }

    /**
     * Mask any string that might contain sensitive information
     * 
     * @param sensitiveData The sensitive data to mask
     * @param showLength The number of characters to show at the beginning
     * @return Masked string
     */
    public String maskSensitiveData(String sensitiveData, int showLength) {
        if (sensitiveData == null || sensitiveData.isEmpty()) {
            return "EMPTY_DATA";
        }
        
        if (sensitiveData.length() <= showLength) {
            return "*".repeat(sensitiveData.length());
        }
        
        return sensitiveData.substring(0, showLength) + "*".repeat(sensitiveData.length() - showLength);
    }

    /**
     * Mask JSON string containing sensitive data
     * 
     * @param jsonData The JSON string to mask
     * @return Masked JSON string
     */
    public String maskJsonData(String jsonData) {
        if (jsonData == null || jsonData.trim().isEmpty()) {
            return "EMPTY_JSON";
        }
        
        // Simple masking for JSON - replace values but keep structure
        String maskedJson = jsonData;
        
        // Mask common PII fields in JSON
        maskedJson = maskedJson.replaceAll("(\"pan\"\\s*:\\s*\")([^\"]+)(\")", "$1****$3");
        maskedJson = maskedJson.replaceAll("(\"aadhaar\"\\s*:\\s*\")([^\"]+)(\")", "$1****$3");
        maskedJson = maskedJson.replaceAll("(\"mobileNumber\"\\s*:\\s*\")([^\"]+)(\")", "$1****$3");
        maskedJson = maskedJson.replaceAll("(\"emailAddress\"\\s*:\\s*\")([^\"]+)(\")", "$1****$3");
        maskedJson = maskedJson.replaceAll("(\"fullName\"\\s*:\\s*\")([^\"]+)(\")", "$1****$3");
        
        return maskedJson;
    }
}
