package com.banking.dedupe.entity;

import java.time.LocalDateTime;

import com.banking.dedupe.enums.DedupeStatus;
import com.banking.dedupe.enums.VendorStatus;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

/**
 * Entity representing a Dedupe Response
 * 
 * Stores the interpreted deduplication response that will be sent back
 * to upstream applications, including final status and duplicate details.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Entity
@Table(name = "dedupe_response")
public class DedupeResponse {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "dedupe_response_seq")
    @SequenceGenerator(name = "dedupe_response_seq", sequenceName = "dedupe_response_seq", allocationSize = 1)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dedupe_request_id", nullable = false)
    private DedupeRequest dedupeRequest;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vendor_response_id")
    private VendorResponse vendorResponse;

    // Final Dedupe Result
    @Enumerated(EnumType.STRING)
    @Column(name = "dedupe_status", nullable = false)
    private DedupeStatus dedupeStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "final_status", nullable = false)
    private VendorStatus finalStatus;

    @Column(name = "message", columnDefinition = "TEXT")
    private String message;

    // Duplicate Information
    @Column(name = "duplicates_found")
    private Integer duplicatesFound = 0;

    @Column(name = "duplicate_details", columnDefinition = "TEXT")
    private String duplicateDetails; // JSON format

    // Vendor Information
    @Column(name = "vendor_name", length = 50)
    private String vendorName;



    @Column(name = "processing_timestamp")
    private LocalDateTime processingTimestamp;

    // Response Metadata
    @Column(name = "response_sent_to_upstream")
    private Boolean responseSentToUpstream = false;

    @Column(name = "upstream_response_status", length = 20)
    private String upstreamResponseStatus;

    // Timestamps
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * Default constructor
     */
    public DedupeResponse() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Constructor with essential fields
     */
    public DedupeResponse(DedupeRequest dedupeRequest, DedupeStatus dedupeStatus, VendorStatus finalStatus) {
        this();
        this.dedupeRequest = dedupeRequest;
        this.dedupeStatus = dedupeStatus;
        this.finalStatus = finalStatus;
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public DedupeRequest getDedupeRequest() {
        return dedupeRequest;
    }

    public void setDedupeRequest(DedupeRequest dedupeRequest) {
        this.dedupeRequest = dedupeRequest;
    }

    public VendorResponse getVendorResponse() {
        return vendorResponse;
    }

    public void setVendorResponse(VendorResponse vendorResponse) {
        this.vendorResponse = vendorResponse;
    }

    public DedupeStatus getDedupeStatus() {
        return dedupeStatus;
    }

    public void setDedupeStatus(DedupeStatus dedupeStatus) {
        this.dedupeStatus = dedupeStatus;
    }

    public VendorStatus getFinalStatus() {
        return finalStatus;
    }

    public void setFinalStatus(VendorStatus finalStatus) {
        this.finalStatus = finalStatus;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getDuplicatesFound() {
        return duplicatesFound;
    }

    public void setDuplicatesFound(Integer duplicatesFound) {
        this.duplicatesFound = duplicatesFound;
    }

    public String getDuplicateDetails() {
        return duplicateDetails;
    }

    public void setDuplicateDetails(String duplicateDetails) {
        this.duplicateDetails = duplicateDetails;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }



    public LocalDateTime getProcessingTimestamp() {
        return processingTimestamp;
    }

    public void setProcessingTimestamp(LocalDateTime processingTimestamp) {
        this.processingTimestamp = processingTimestamp;
    }

    public Boolean getResponseSentToUpstream() {
        return responseSentToUpstream;
    }

    public void setResponseSentToUpstream(Boolean responseSentToUpstream) {
        this.responseSentToUpstream = responseSentToUpstream;
    }

    public String getUpstreamResponseStatus() {
        return upstreamResponseStatus;
    }

    public void setUpstreamResponseStatus(String upstreamResponseStatus) {
        this.upstreamResponseStatus = upstreamResponseStatus;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * Mark response as sent to upstream
     */
    public void markResponseSent(String status) {
        this.responseSentToUpstream = true;
        this.upstreamResponseStatus = status;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Check if duplicates were found
     */
    public boolean hasDuplicates() {
        return duplicatesFound != null && duplicatesFound > 0;
    }

    /**
     * Check if the response indicates success
     */
    public boolean isSuccessful() {
        return dedupeStatus == DedupeStatus.COMPLETED && finalStatus != null && finalStatus.isSuccessful();
    }
}
