package com.banking.dedupe.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.banking.dedupe.dto.CustomerDedupeResponseDto;
import com.banking.dedupe.dto.VendorResponseDto;
import com.banking.dedupe.entity.DedupeRequest;
import com.banking.dedupe.entity.DedupeResponse;
import com.banking.dedupe.entity.VendorResponse;
import com.banking.dedupe.enums.DedupeStatus;
import com.banking.dedupe.enums.VendorStatus;
import com.banking.dedupe.repository.DedupeResponseRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Service for interpreting vendor responses and mapping to standardized format
 * 
 * Handles Phase 2 of the customer deduplication capability:
 * - Interprets raw vendor responses
 * - Maps to internal standardized format
 * - Creates final response for upstream applications
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Service
@Transactional
public class ResponseInterpreterService {

    private static final Logger logger = LoggerFactory.getLogger(ResponseInterpreterService.class);

    private final DedupeResponseRepository dedupeResponseRepository;
    private final AuditService auditService;
    private final ObjectMapper objectMapper;

    public ResponseInterpreterService(DedupeResponseRepository dedupeResponseRepository,
                                    AuditService auditService, ObjectMapper objectMapper) {
        this.dedupeResponseRepository = dedupeResponseRepository;
        this.auditService = auditService;
        this.objectMapper = objectMapper;
    }

    /**
     * Interpret vendor response and create standardized dedupe response
     * 
     * @param request The original dedupe request
     * @param vendorResponse The vendor response to interpret
     * @return Standardized dedupe response
     */
    public DedupeResponse interpretVendorResponse(DedupeRequest request, VendorResponse vendorResponse) {
        logger.info("Interpreting vendor response for reference: {} - Vendor status: {}", 
            request.getReferenceNumber(), vendorResponse.getVendorStatus());

        try {
            // Create dedupe response entity
            DedupeResponse dedupeResponse = new DedupeResponse();
            dedupeResponse.setDedupeRequest(request);
            dedupeResponse.setVendorResponse(vendorResponse);
            dedupeResponse.setVendorName(vendorResponse.getVendorName());
            dedupeResponse.setProcessingTimestamp(LocalDateTime.now());

            // Interpret based on vendor status
            if (vendorResponse.isSuccessful()) {
                interpretSuccessfulResponse(dedupeResponse, vendorResponse);
            } else {
                interpretErrorResponse(dedupeResponse, vendorResponse);
            }

            // Save to database
            dedupeResponse = dedupeResponseRepository.save(dedupeResponse);

            // Log interpretation completion
            auditService.logResponseInterpretation(request, 
                dedupeResponse.getFinalStatus().name(), 
                dedupeResponse.getDuplicatesFound());

            logger.info("Vendor response interpreted for reference: {} - Final status: {} - Duplicates: {}", 
                request.getReferenceNumber(), dedupeResponse.getFinalStatus(), dedupeResponse.getDuplicatesFound());

            return dedupeResponse;

        } catch (Exception e) {
            logger.error("Failed to interpret vendor response for reference: {}", 
                request.getReferenceNumber(), e);

            // Create error response
            DedupeResponse errorResponse = createErrorResponse(request, vendorResponse, 
                "INTERPRETATION_ERROR", "Failed to interpret vendor response: " + e.getMessage());
            
            return dedupeResponseRepository.save(errorResponse);
        }
    }

    /**
     * Interpret successful vendor response
     */
    private void interpretSuccessfulResponse(DedupeResponse dedupeResponse, VendorResponse vendorResponse) {
        VendorStatus vendorStatus = vendorResponse.getVendorStatus();
        
        dedupeResponse.setDedupeStatus(DedupeStatus.COMPLETED);
        dedupeResponse.setFinalStatus(vendorStatus);

        if (vendorStatus == VendorStatus.MATCH_FOUND) {
            interpretMatchFoundResponse(dedupeResponse, vendorResponse);
        } else if (vendorStatus == VendorStatus.NO_MATCH_FOUND) {
            interpretNoMatchResponse(dedupeResponse, vendorResponse);
        }
    }

    /**
     * Interpret MATCH_FOUND response
     */
    private void interpretMatchFoundResponse(DedupeResponse dedupeResponse, VendorResponse vendorResponse) {
        dedupeResponse.setMessage("Duplicate customer records found");
        
        try {
            // Parse raw response to extract duplicate details
            if (vendorResponse.getRawResponse() != null) {
                VendorResponseDto vendorResponseDto = objectMapper.readValue(
                    vendorResponse.getRawResponse(), VendorResponseDto.class);
                
                if (vendorResponseDto.getDuplicates() != null && !vendorResponseDto.getDuplicates().isEmpty()) {
                    dedupeResponse.setDuplicatesFound(vendorResponseDto.getDuplicates().size());
                    
                    // Convert vendor duplicates to internal format
                    List<CustomerDedupeResponseDto.DuplicateCustomerDto> duplicates = 
                        convertVendorDuplicates(vendorResponseDto.getDuplicates());
                    
                    // Store as JSON
                    String duplicateDetailsJson = objectMapper.writeValueAsString(duplicates);
                    dedupeResponse.setDuplicateDetails(duplicateDetailsJson);
                } else {
                    dedupeResponse.setDuplicatesFound(0);
                    dedupeResponse.setDuplicateDetails("[]");
                }
                
                // Extract vendor details - lookback period removed as per requirement
            } else {
                // Fallback if raw response is not available
                dedupeResponse.setDuplicatesFound(1); // Assume at least one duplicate
                dedupeResponse.setDuplicateDetails("[]");
            }
            
        } catch (JsonProcessingException e) {
            logger.warn("Failed to parse vendor response JSON for reference: {} - Error: {}", 
                dedupeResponse.getDedupeRequest().getReferenceNumber(), e.getMessage());
            
            // Fallback values
            dedupeResponse.setDuplicatesFound(1);
            dedupeResponse.setDuplicateDetails("[]");
        }
        
        // Lookback period removed as per requirement
    }

    /**
     * Interpret NO_MATCH_FOUND response
     */
    private void interpretNoMatchResponse(DedupeResponse dedupeResponse, VendorResponse vendorResponse) {
        dedupeResponse.setDuplicatesFound(0);
        dedupeResponse.setDuplicateDetails("[]");

        // Lookback period removed as per requirement
        dedupeResponse.setMessage("No duplicate records found");
    }

    /**
     * Interpret error response
     */
    private void interpretErrorResponse(DedupeResponse dedupeResponse, VendorResponse vendorResponse) {
        dedupeResponse.setDedupeStatus(DedupeStatus.FAILED);
        dedupeResponse.setFinalStatus(vendorResponse.getVendorStatus());
        dedupeResponse.setDuplicatesFound(0);
        dedupeResponse.setDuplicateDetails("[]");
        
        // Set appropriate error message based on vendor status
        String message = switch (vendorResponse.getVendorStatus()) {
            case TIMEOUT -> "Dedupe service request timed out, manual review required";
            case VENDOR_SERVICE_FAILURE -> "Dedupe service temporarily unavailable, manual review required";
            case ERROR -> "Dedupe service returned an error, manual review required";
            default -> "Dedupe service unavailable, manual review required";
        };
        
        dedupeResponse.setMessage(message);
        
        // Include error details if available
        if (vendorResponse.getErrorMessage() != null) {
            dedupeResponse.setMessage(message + " - " + vendorResponse.getErrorMessage());
        }
    }

    /**
     * Create error response for interpretation failures
     */
    private DedupeResponse createErrorResponse(DedupeRequest request, VendorResponse vendorResponse,
                                             String errorCode, String errorMessage) {
        DedupeResponse errorResponse = new DedupeResponse();
        errorResponse.setDedupeRequest(request);
        errorResponse.setVendorResponse(vendorResponse);
        errorResponse.setDedupeStatus(DedupeStatus.FAILED);
        errorResponse.setFinalStatus(VendorStatus.ERROR);
        errorResponse.setMessage(errorMessage);
        errorResponse.setDuplicatesFound(0);
        errorResponse.setDuplicateDetails("[]");
        errorResponse.setVendorName(vendorResponse != null ? vendorResponse.getVendorName() : "UNKNOWN");
        errorResponse.setProcessingTimestamp(LocalDateTime.now());
        
        return errorResponse;
    }

    /**
     * Convert vendor duplicates to internal format
     */
    private List<CustomerDedupeResponseDto.DuplicateCustomerDto> convertVendorDuplicates(
            List<VendorResponseDto.VendorDuplicateDto> vendorDuplicates) {
        
        return vendorDuplicates.stream()
            .map(this::convertVendorDuplicate)
            .collect(Collectors.toList());
    }

    /**
     * Convert single vendor duplicate to internal format
     */
    private CustomerDedupeResponseDto.DuplicateCustomerDto convertVendorDuplicate(
            VendorResponseDto.VendorDuplicateDto vendorDuplicate) {
        
        CustomerDedupeResponseDto.DuplicateCustomerDto duplicate = 
            new CustomerDedupeResponseDto.DuplicateCustomerDto();
        
        duplicate.setCustomerId(vendorDuplicate.getCustomerId());
        duplicate.setMatchScore(vendorDuplicate.getMatchScore());
        duplicate.setMatchedFields(vendorDuplicate.getMatchedFields() != null ? 
            new ArrayList<>(vendorDuplicate.getMatchedFields()) : new ArrayList<>());
        duplicate.setCustomerStatus(vendorDuplicate.getCustomerStatus());
        duplicate.setCreatedDate(vendorDuplicate.getCreatedDate());
        
        return duplicate;
    }

    /**
     * Create final response DTO for upstream applications
     * 
     * @param dedupeResponse The interpreted dedupe response
     * @return Customer dedupe response DTO
     */
    public CustomerDedupeResponseDto createFinalResponseDto(DedupeResponse dedupeResponse) {
        logger.info("Creating final response DTO for reference: {}", 
            dedupeResponse.getDedupeRequest().getReferenceNumber());

        CustomerDedupeResponseDto responseDto = new CustomerDedupeResponseDto();
        
        // Basic information
        responseDto.setReferenceNumber(dedupeResponse.getDedupeRequest().getReferenceNumber());
        responseDto.setCustomerIdentifier(dedupeResponse.getDedupeRequest().getCustomerIdentifier());
        responseDto.setDedupeStatus(dedupeResponse.getFinalStatus());
        responseDto.setMessage(dedupeResponse.getMessage());
        
        // Duplicate information
        List<CustomerDedupeResponseDto.DuplicateCustomerDto> duplicates = new ArrayList<>();
        if (dedupeResponse.getDuplicateDetails() != null && !dedupeResponse.getDuplicateDetails().equals("[]")) {
            try {
                duplicates = objectMapper.readValue(dedupeResponse.getDuplicateDetails(), 
                    objectMapper.getTypeFactory().constructCollectionType(List.class, 
                        CustomerDedupeResponseDto.DuplicateCustomerDto.class));
            } catch (JsonProcessingException e) {
                logger.warn("Failed to parse duplicate details JSON: {}", e.getMessage());
            }
        }
        responseDto.setDuplicates(duplicates);
        
        // Vendor details
        CustomerDedupeResponseDto.VendorDetailsDto vendorDetails =
            new CustomerDedupeResponseDto.VendorDetailsDto();
        vendorDetails.setVendorName(dedupeResponse.getVendorName());
        vendorDetails.setProcessingTimestamp(dedupeResponse.getProcessingTimestamp());
        responseDto.setVendorDetails(vendorDetails);
        
        // Audit information
        CustomerDedupeResponseDto.AuditInfoDto auditInfo = 
            new CustomerDedupeResponseDto.AuditInfoDto();
        auditInfo.setRequestTimestamp(dedupeResponse.getDedupeRequest().getCreatedAt());
        auditInfo.setResponseTimestamp(LocalDateTime.now());
        
        // Calculate total processing time
        long processingTimeMs = java.time.Duration.between(
            dedupeResponse.getDedupeRequest().getCreatedAt(), 
            LocalDateTime.now()).toMillis();
        auditInfo.setProcessingTimeMs(processingTimeMs);
        
        responseDto.setAuditInfo(auditInfo);
        
        return responseDto;
    }

    /**
     * Check if response indicates duplicates found
     * 
     * @param dedupeResponse The dedupe response
     * @return true if duplicates were found
     */
    public boolean hasDuplicatesFound(DedupeResponse dedupeResponse) {
        return dedupeResponse.getFinalStatus() == VendorStatus.MATCH_FOUND && 
               dedupeResponse.getDuplicatesFound() != null && 
               dedupeResponse.getDuplicatesFound() > 0;
    }

    /**
     * Get duplicate count from response
     * 
     * @param dedupeResponse The dedupe response
     * @return Number of duplicates found
     */
    public int getDuplicateCount(DedupeResponse dedupeResponse) {
        return dedupeResponse.getDuplicatesFound() != null ? dedupeResponse.getDuplicatesFound() : 0;
    }
}
