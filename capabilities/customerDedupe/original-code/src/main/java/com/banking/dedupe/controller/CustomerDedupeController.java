package com.banking.dedupe.controller;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.banking.dedupe.dto.CustomerDedupeRequestDto;
import com.banking.dedupe.dto.CustomerDedupeResponseDto;
import com.banking.dedupe.service.AuditService;
import com.banking.dedupe.service.CustomerDedupeService;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * REST Controller for Customer Deduplication API
 * 
 * Provides endpoints for customer deduplication requests and status tracking.
 * Implements comprehensive error handling and audit logging for RBI compliance.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@RestController
@RequestMapping("/customer/dedupe")
@Validated
public class CustomerDedupeController {

    private static final Logger logger = LoggerFactory.getLogger(CustomerDedupeController.class);

    private final CustomerDedupeService customerDedupeService;
    private final AuditService auditService;

    public CustomerDedupeController(CustomerDedupeService customerDedupeService, AuditService auditService) {
        this.customerDedupeService = customerDedupeService;
        this.auditService = auditService;
    }

    /**
     * Process customer deduplication request
     * 
     * @param request The deduplication request
     * @param httpRequest The HTTP servlet request
     * @return Deduplication response
     */
    @PostMapping
    public ResponseEntity<CustomerDedupeResponseDto> processDedupeRequest(
            @Valid @RequestBody CustomerDedupeRequestDto request,
            HttpServletRequest httpRequest) {

        String clientIp = getClientIpAddress(httpRequest);
        String userAgent = httpRequest.getHeader("User-Agent");

        logger.info("Received deduplication request for customer: {} from IP: {}", 
            request.getCustomerIdentifier(), clientIp);

        try {
            CustomerDedupeResponseDto response = customerDedupeService.processDedupeRequest(
                request, clientIp, userAgent);

            HttpStatus status = determineHttpStatus(response);

            logger.info("Deduplication request completed - Reference: {} - Status: {} - HTTP: {}", 
                response.getReferenceNumber(), response.getDedupeStatus(), status.value());

            return ResponseEntity.status(status).body(response);

        } catch (Exception e) {
            logger.error("Unexpected error processing deduplication request for customer: {}", 
                request.getCustomerIdentifier(), e);

            CustomerDedupeResponseDto errorResponse = createSystemErrorResponse(request.getCustomerIdentifier());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Get request status by reference number
     * 
     * @param referenceNumber The reference number
     * @return Request status information
     */
    @GetMapping("/status/{referenceNumber}")
    public ResponseEntity<Map<String, Object>> getRequestStatus(
            @PathVariable String referenceNumber) {

        logger.info("Status check requested for reference: {}", referenceNumber);

        try {
            CustomerDedupeService.RequestStatusInfo statusInfo = 
                customerDedupeService.getRequestStatus(referenceNumber);

            Map<String, Object> response = new HashMap<>();
            response.put("referenceNumber", statusInfo.getReferenceNumber());
            response.put("status", statusInfo.getStatus());
            response.put("description", statusInfo.getDescription());
            response.put("lastUpdated", statusInfo.getLastUpdated());
            response.put("timestamp", LocalDateTime.now());

            if ("NOT_FOUND".equals(statusInfo.getStatus())) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Error retrieving status for reference: {}", referenceNumber, e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("referenceNumber", referenceNumber);
            errorResponse.put("error", "Unable to retrieve status");
            errorResponse.put("timestamp", LocalDateTime.now());

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Get audit trail for a request
     * 
     * @param referenceNumber The reference number
     * @return Audit trail information
     */
    @GetMapping("/audit/{referenceNumber}")
    public ResponseEntity<Map<String, Object>> getAuditTrail(
            @PathVariable String referenceNumber) {

        logger.info("Audit trail requested for reference: {}", referenceNumber);

        try {
            List<com.banking.dedupe.dto.AuditTrailDto> auditTrail = auditService.getAuditTrailDtos(referenceNumber);

            Map<String, Object> response = new HashMap<>();
            response.put("referenceNumber", referenceNumber);
            response.put("auditTrail", auditTrail);
            response.put("totalEvents", auditTrail.size());
            response.put("timestamp", LocalDateTime.now());

            if (auditTrail.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Error retrieving audit trail for reference: {}", referenceNumber, e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("referenceNumber", referenceNumber);
            errorResponse.put("error", "Unable to retrieve audit trail");
            errorResponse.put("timestamp", LocalDateTime.now());

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Health check endpoint
     * 
     * @return Health status
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "Customer Deduplication Service");
        health.put("timestamp", LocalDateTime.now());
        health.put("version", "1.0.0");

        return ResponseEntity.ok(health);
    }

    /**
     * Get client IP address from request
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * Determine HTTP status based on response
     */
    private HttpStatus determineHttpStatus(CustomerDedupeResponseDto response) {
        if (response.getDedupeStatus() == null) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }

        return switch (response.getDedupeStatus()) {
            case MATCH_FOUND, NO_MATCH_FOUND -> HttpStatus.OK;
            case ERROR, TIMEOUT, VENDOR_SERVICE_FAILURE -> HttpStatus.SERVICE_UNAVAILABLE;
        };
    }

    /**
     * Create system error response
     */
    private CustomerDedupeResponseDto createSystemErrorResponse(String customerIdentifier) {
        CustomerDedupeResponseDto errorResponse = new CustomerDedupeResponseDto();
        errorResponse.setReferenceNumber("ERROR-" + System.currentTimeMillis());
        errorResponse.setCustomerIdentifier(customerIdentifier);
        errorResponse.setDedupeStatus(com.banking.dedupe.enums.VendorStatus.ERROR);
        errorResponse.setMessage("System error occurred, manual review required");
        errorResponse.setDuplicates(java.util.Collections.emptyList());

        // Vendor details
        CustomerDedupeResponseDto.VendorDetailsDto vendorDetails = 
            new CustomerDedupeResponseDto.VendorDetailsDto();
        vendorDetails.setVendorName("SYSTEM");
        vendorDetails.setProcessingTimestamp(LocalDateTime.now());
        errorResponse.setVendorDetails(vendorDetails);

        // Audit info
        CustomerDedupeResponseDto.AuditInfoDto auditInfo = 
            new CustomerDedupeResponseDto.AuditInfoDto();
        auditInfo.setRequestTimestamp(LocalDateTime.now());
        auditInfo.setResponseTimestamp(LocalDateTime.now());
        auditInfo.setProcessingTimeMs(0L);
        errorResponse.setAuditInfo(auditInfo);

        return errorResponse;
    }
}
