package com.banking.dedupe.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;

/**
 * DTO for Audit Trail Entry
 * 
 * Represents a single audit log entry in the audit trail response.
 * Contains masked PII data for compliance and security.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class AuditTrailDto {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("referenceNumber")
    private String referenceNumber;

    @JsonProperty("eventType")
    private String eventType;

    @JsonProperty("eventDescription")
    private String eventDescription;

    @JsonProperty("eventData")
    private String eventData; // JSON format with masked PII

    @JsonProperty("previousStatus")
    private String previousStatus;

    @JsonProperty("currentStatus")
    private String currentStatus;

    @JsonProperty("errorCode")
    private String errorCode;

    @JsonProperty("errorMessage")
    private String errorMessage;

    @JsonProperty("processingTimeMs")
    private Integer processingTimeMs;

    @JsonProperty("retryAttempt")
    private Integer retryAttempt;

    @JsonProperty("performedBy")
    private String performedBy;

    @JsonProperty("clientIp")
    private String clientIp;

    @JsonProperty("userAgent")
    private String userAgent;

    @JsonProperty("createdAt")
    private LocalDateTime createdAt;

    /**
     * Default constructor
     */
    public AuditTrailDto() {
    }

    /**
     * Constructor with all fields
     */
    public AuditTrailDto(Long id, String referenceNumber, String eventType, String eventDescription,
                        String eventData, String previousStatus, String currentStatus, String errorCode,
                        String errorMessage, Integer processingTimeMs, Integer retryAttempt,
                        String performedBy, String clientIp, String userAgent, LocalDateTime createdAt) {
        this.id = id;
        this.referenceNumber = referenceNumber;
        this.eventType = eventType;
        this.eventDescription = eventDescription;
        this.eventData = eventData;
        this.previousStatus = previousStatus;
        this.currentStatus = currentStatus;
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.processingTimeMs = processingTimeMs;
        this.retryAttempt = retryAttempt;
        this.performedBy = performedBy;
        this.clientIp = clientIp;
        this.userAgent = userAgent;
        this.createdAt = createdAt;
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getEventDescription() {
        return eventDescription;
    }

    public void setEventDescription(String eventDescription) {
        this.eventDescription = eventDescription;
    }

    public String getEventData() {
        return eventData;
    }

    public void setEventData(String eventData) {
        this.eventData = eventData;
    }

    public String getPreviousStatus() {
        return previousStatus;
    }

    public void setPreviousStatus(String previousStatus) {
        this.previousStatus = previousStatus;
    }

    public String getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(String currentStatus) {
        this.currentStatus = currentStatus;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Integer getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(Integer processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }

    public Integer getRetryAttempt() {
        return retryAttempt;
    }

    public void setRetryAttempt(Integer retryAttempt) {
        this.retryAttempt = retryAttempt;
    }

    public String getPerformedBy() {
        return performedBy;
    }

    public void setPerformedBy(String performedBy) {
        this.performedBy = performedBy;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * Create DTO from AuditLog entity
     */
    public static AuditTrailDto fromEntity(com.banking.dedupe.entity.AuditLog auditLog) {
        return new AuditTrailDto(
            auditLog.getId(),
            auditLog.getReferenceNumber(),
            auditLog.getEventType(),
            auditLog.getEventDescription(),
            auditLog.getEventData(),
            auditLog.getPreviousStatus(),
            auditLog.getCurrentStatus(),
            auditLog.getErrorCode(),
            auditLog.getErrorMessage(),
            auditLog.getProcessingTimeMs(),
            auditLog.getRetryAttempt(),
            auditLog.getPerformedBy(),
            auditLog.getClientIp(),
            auditLog.getUserAgent(),
            auditLog.getCreatedAt()
        );
    }
}
