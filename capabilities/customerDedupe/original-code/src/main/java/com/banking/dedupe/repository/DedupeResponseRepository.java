package com.banking.dedupe.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.banking.dedupe.entity.DedupeResponse;
import com.banking.dedupe.enums.DedupeStatus;
import com.banking.dedupe.enums.VendorStatus;

/**
 * Repository interface for DedupeResponse entity
 * 
 * Provides data access methods for deduplication responses
 * including queries for reporting and audit purposes.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Repository
public interface DedupeResponseRepository extends JpaRepository<DedupeResponse, Long> {

    /**
     * Find dedupe response by dedupe request ID
     * 
     * @param dedupeRequestId The dedupe request ID
     * @return Optional containing the dedupe response
     */
    Optional<DedupeResponse> findByDedupeRequestId(Long dedupeRequestId);

    /**
     * Find dedupe responses by status
     * 
     * @param dedupeStatus The dedupe status
     * @return List of responses with the specified status
     */
    List<DedupeResponse> findByDedupeStatus(DedupeStatus dedupeStatus);

    /**
     * Find dedupe responses by final status
     * 
     * @param finalStatus The final vendor status
     * @return List of responses with the specified final status
     */
    List<DedupeResponse> findByFinalStatus(VendorStatus finalStatus);

    /**
     * Find dedupe responses by vendor name
     * 
     * @param vendorName The vendor name
     * @return List of responses from the specified vendor
     */
    List<DedupeResponse> findByVendorName(String vendorName);

    /**
     * Find dedupe responses created within a date range
     * 
     * @param startDate The start date
     * @param endDate The end date
     * @return List of responses created within the date range
     */
    List<DedupeResponse> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find responses that have not been sent to upstream
     * 
     * @return List of responses not sent to upstream
     */
    List<DedupeResponse> findByResponseSentToUpstreamFalse();

    /**
     * Find responses with duplicates found
     * 
     * @return List of responses where duplicates were found
     */
    @Query("SELECT dr FROM DedupeResponse dr WHERE dr.duplicatesFound > 0")
    List<DedupeResponse> findResponsesWithDuplicates();

    /**
     * Count responses by final status
     * 
     * @param finalStatus The final status
     * @return Count of responses with the specified final status
     */
    long countByFinalStatus(VendorStatus finalStatus);

    /**
     * Count responses with duplicates found today
     * 
     * @param startOfDay The start of the current day
     * @return Count of responses with duplicates found today
     */
    @Query("SELECT COUNT(dr) FROM DedupeResponse dr WHERE dr.duplicatesFound > 0 AND dr.createdAt >= :startOfDay")
    long countResponsesWithDuplicatesToday(@Param("startOfDay") LocalDateTime startOfDay);

    /**
     * Get duplicate detection statistics
     * 
     * @param startDate The start date
     * @param endDate The end date
     * @return List of duplicate detection statistics
     */
    @Query("SELECT dr.finalStatus, COUNT(dr), SUM(dr.duplicatesFound) FROM DedupeResponse dr " +
           "WHERE dr.createdAt BETWEEN :startDate AND :endDate " +
           "GROUP BY dr.finalStatus")
    List<Object[]> getDuplicateDetectionStatistics(@Param("startDate") LocalDateTime startDate,
                                                   @Param("endDate") LocalDateTime endDate);

    /**
     * Find responses for data retention cleanup
     * 
     * @param retentionCutoff The retention cutoff date
     * @return List of responses older than retention period
     */
    @Query("SELECT dr FROM DedupeResponse dr WHERE dr.createdAt < :retentionCutoff")
    List<DedupeResponse> findResponsesForRetentionCleanup(@Param("retentionCutoff") LocalDateTime retentionCutoff);

    /**
     * Get vendor performance statistics
     * 
     * @param vendorName The vendor name
     * @param startDate The start date
     * @param endDate The end date
     * @return List of vendor performance statistics
     */
    @Query("SELECT dr.finalStatus, COUNT(dr), AVG(CASE WHEN dr.duplicatesFound > 0 THEN dr.duplicatesFound ELSE 0 END) " +
           "FROM DedupeResponse dr WHERE dr.vendorName = :vendorName " +
           "AND dr.createdAt BETWEEN :startDate AND :endDate GROUP BY dr.finalStatus")
    List<Object[]> getVendorPerformanceStatistics(@Param("vendorName") String vendorName,
                                                  @Param("startDate") LocalDateTime startDate,
                                                  @Param("endDate") LocalDateTime endDate);

    /**
     * Find high-risk responses (multiple duplicates found)
     * 
     * @param duplicateThreshold The minimum number of duplicates to be considered high-risk
     * @param startDate The start date
     * @param endDate The end date
     * @return List of high-risk responses
     */
    @Query("SELECT dr FROM DedupeResponse dr WHERE dr.duplicatesFound >= :duplicateThreshold " +
           "AND dr.createdAt BETWEEN :startDate AND :endDate ORDER BY dr.duplicatesFound DESC")
    List<DedupeResponse> findHighRiskResponses(@Param("duplicateThreshold") Integer duplicateThreshold,
                                              @Param("startDate") LocalDateTime startDate,
                                              @Param("endDate") LocalDateTime endDate);

    /**
     * Get response time distribution
     *
     * @param startDate The start date
     * @param endDate The end date
     * @return List of response time statistics
     */
    @Query("SELECT COUNT(dr) FROM DedupeResponse dr WHERE dr.createdAt BETWEEN :startDate AND :endDate")
    Long getResponseTimeDistribution(@Param("startDate") LocalDateTime startDate,
                                    @Param("endDate") LocalDateTime endDate);

    /**
     * Find responses by upstream response status
     * 
     * @param upstreamResponseStatus The upstream response status
     * @return List of responses with the specified upstream status
     */
    List<DedupeResponse> findByUpstreamResponseStatus(String upstreamResponseStatus);

    /**
     * Get daily response summary
     * 
     * @param date The date for summary
     * @return List of daily response summary
     */
    @Query("SELECT dr.finalStatus, COUNT(dr), SUM(dr.duplicatesFound) FROM DedupeResponse dr " +
           "WHERE DATE(dr.createdAt) = DATE(:date) GROUP BY dr.finalStatus")
    List<Object[]> getDailyResponseSummary(@Param("date") LocalDateTime date);
}
