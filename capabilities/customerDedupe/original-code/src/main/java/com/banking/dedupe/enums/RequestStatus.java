package com.banking.dedupe.enums;

/**
 * Enumeration for Request Processing Status
 * 
 * Represents the detailed processing status of a deduplication request
 * throughout its lifecycle from reception to completion.
 * Used for granular tracking and audit trail purposes.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public enum RequestStatus {
    
    /**
     * Request has been received from upstream application
     */
    RECEIVED("Request received from upstream application"),
    
    /**
     * Request has been validated successfully
     */
    VALIDATED("Request validation completed successfully"),
    
    /**
     * Vendor API call has been initiated
     */
    VENDOR_CALL_INITIATED("Vendor API call initiated"),
    
    /**
     * Vendor API call completed successfully
     */
    VENDOR_CALL_SUCCESS("Vendor API call completed successfully"),
    
    /**
     * Vendor API call failed
     */
    VENDOR_CALL_FAILED("Vendor API call failed"),
    
    /**
     * Vendor response has been interpreted
     */
    RESPONSE_INTERPRETED("Vendor response interpreted successfully"),
    
    /**
     * Request processing completed successfully
     */
    COMPLETED("Request processing completed"),
    
    /**
     * Request processing failed
     */
    FAILED("Request processing failed");

    private final String description;

    /**
     * Constructor for RequestStatus enum
     * 
     * @param description Human-readable description of the status
     */
    RequestStatus(String description) {
        this.description = description;
    }

    /**
     * Get the description of the status
     * 
     * @return String description of the status
     */
    public String getDescription() {
        return description;
    }

    /**
     * Check if the status indicates completion (either success or failure)
     * 
     * @return true if the status is terminal (COMPLETED or FAILED)
     */
    public boolean isTerminal() {
        return this == COMPLETED || this == FAILED;
    }

    /**
     * Check if the status indicates successful completion
     * 
     * @return true if the status is COMPLETED
     */
    public boolean isSuccessful() {
        return this == COMPLETED;
    }

    /**
     * Check if the status indicates a failure
     * 
     * @return true if the status is FAILED or VENDOR_CALL_FAILED
     */
    public boolean isFailure() {
        return this == FAILED || this == VENDOR_CALL_FAILED;
    }

    /**
     * Check if the status indicates the request is in progress
     * 
     * @return true if the status is not terminal
     */
    public boolean isInProgress() {
        return !isTerminal();
    }

    /**
     * Get the next expected status in the processing flow
     * 
     * @return The next RequestStatus in the normal flow, or null if terminal
     */
    public RequestStatus getNextStatus() {
        return switch (this) {
            case RECEIVED -> VALIDATED;
            case VALIDATED -> VENDOR_CALL_INITIATED;
            case VENDOR_CALL_INITIATED -> VENDOR_CALL_SUCCESS;
            case VENDOR_CALL_SUCCESS -> RESPONSE_INTERPRETED;
            case RESPONSE_INTERPRETED -> COMPLETED;
            case VENDOR_CALL_FAILED, COMPLETED, FAILED -> null;
        };
    }
}
