package com.banking.dedupe.repository;

import com.banking.dedupe.entity.VendorResponse;
import com.banking.dedupe.enums.VendorStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for VendorResponse entity
 * 
 * Provides data access methods for vendor responses
 * including queries for monitoring and audit purposes.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Repository
public interface VendorResponseRepository extends JpaRepository<VendorResponse, Long> {

    /**
     * Find vendor responses by dedupe request ID
     * 
     * @param dedupeRequestId The dedupe request ID
     * @return List of vendor responses for the request
     */
    List<VendorResponse> findByDedupeRequestId(Long dedupeRequestId);

    /**
     * Find the latest vendor response for a dedupe request
     * 
     * @param dedupeRequestId The dedupe request ID
     * @return Optional containing the latest vendor response
     */
    @Query("SELECT vr FROM VendorResponse vr WHERE vr.dedupeRequest.id = :dedupeRequestId " +
           "ORDER BY vr.createdAt DESC LIMIT 1")
    Optional<VendorResponse> findLatestByDedupeRequestId(@Param("dedupeRequestId") Long dedupeRequestId);

    /**
     * Find vendor responses by vendor name
     * 
     * @param vendorName The vendor name
     * @return List of responses from the specified vendor
     */
    List<VendorResponse> findByVendorName(String vendorName);

    /**
     * Find vendor responses by status
     * 
     * @param vendorStatus The vendor status
     * @return List of responses with the specified status
     */
    List<VendorResponse> findByVendorStatus(VendorStatus vendorStatus);

    /**
     * Find vendor responses by vendor request ID
     * 
     * @param vendorRequestId The vendor request ID
     * @return Optional containing the vendor response
     */
    Optional<VendorResponse> findByVendorRequestId(String vendorRequestId);

    /**
     * Find vendor responses created within a date range
     * 
     * @param startDate The start date
     * @param endDate The end date
     * @return List of responses created within the date range
     */
    List<VendorResponse> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find failed vendor responses for retry
     * 
     * @param maxRetryAttempts The maximum retry attempts
     * @param cutoffTime The cutoff time for retry
     * @return List of failed responses eligible for retry
     */
    @Query("SELECT vr FROM VendorResponse vr WHERE vr.vendorStatus IN :failureStatuses " +
           "AND vr.retryAttempt < :maxRetryAttempts AND vr.createdAt >= :cutoffTime")
    List<VendorResponse> findFailedResponsesForRetry(@Param("failureStatuses") List<VendorStatus> failureStatuses,
                                                    @Param("maxRetryAttempts") Integer maxRetryAttempts,
                                                    @Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * Count responses by vendor and status
     * 
     * @param vendorName The vendor name
     * @param vendorStatus The vendor status
     * @return Count of responses
     */
    long countByVendorNameAndVendorStatus(String vendorName, VendorStatus vendorStatus);

    /**
     * Get average processing time by vendor
     * 
     * @param vendorName The vendor name
     * @param startDate The start date
     * @param endDate The end date
     * @return Average processing time in milliseconds
     */
    @Query("SELECT AVG(vr.processingTimeMs) FROM VendorResponse vr " +
           "WHERE vr.vendorName = :vendorName AND vr.processingTimeMs IS NOT NULL " +
           "AND vr.createdAt BETWEEN :startDate AND :endDate")
    Double getAverageProcessingTime(@Param("vendorName") String vendorName,
                                   @Param("startDate") LocalDateTime startDate,
                                   @Param("endDate") LocalDateTime endDate);

    /**
     * Find responses with processing time exceeding threshold
     * 
     * @param thresholdMs The processing time threshold in milliseconds
     * @param startDate The start date
     * @param endDate The end date
     * @return List of slow responses
     */
    @Query("SELECT vr FROM VendorResponse vr WHERE vr.processingTimeMs > :thresholdMs " +
           "AND vr.createdAt BETWEEN :startDate AND :endDate ORDER BY vr.processingTimeMs DESC")
    List<VendorResponse> findSlowResponses(@Param("thresholdMs") Integer thresholdMs,
                                          @Param("startDate") LocalDateTime startDate,
                                          @Param("endDate") LocalDateTime endDate);

    /**
     * Get vendor response statistics
     * 
     * @param vendorName The vendor name
     * @param startDate The start date
     * @param endDate The end date
     * @return List of vendor statistics
     */
    @Query("SELECT vr.vendorStatus, COUNT(vr), AVG(vr.processingTimeMs) FROM VendorResponse vr " +
           "WHERE vr.vendorName = :vendorName AND vr.createdAt BETWEEN :startDate AND :endDate " +
           "GROUP BY vr.vendorStatus")
    List<Object[]> getVendorStatistics(@Param("vendorName") String vendorName,
                                      @Param("startDate") LocalDateTime startDate,
                                      @Param("endDate") LocalDateTime endDate);

    /**
     * Find responses for data retention cleanup
     * 
     * @param retentionCutoff The retention cutoff date
     * @return List of responses older than retention period
     */
    @Query("SELECT vr FROM VendorResponse vr WHERE vr.createdAt < :retentionCutoff")
    List<VendorResponse> findResponsesForRetentionCleanup(@Param("retentionCutoff") LocalDateTime retentionCutoff);

    /**
     * Count successful responses today
     * 
     * @param startOfDay The start of the current day
     * @return Count of successful responses today
     */
    @Query("SELECT COUNT(vr) FROM VendorResponse vr WHERE vr.vendorStatus IN ('MATCH_FOUND', 'NO_MATCH_FOUND') " +
           "AND vr.createdAt >= :startOfDay")
    long countSuccessfulResponsesToday(@Param("startOfDay") LocalDateTime startOfDay);

    /**
     * Find responses with errors for monitoring
     * 
     * @param startDate The start date
     * @param endDate The end date
     * @return List of error responses
     */
    @Query("SELECT vr FROM VendorResponse vr WHERE vr.vendorStatus IN ('ERROR', 'TIMEOUT', 'VENDOR_SERVICE_FAILURE') " +
           "AND vr.createdAt BETWEEN :startDate AND :endDate ORDER BY vr.createdAt DESC")
    List<VendorResponse> findErrorResponses(@Param("startDate") LocalDateTime startDate,
                                           @Param("endDate") LocalDateTime endDate);
}
