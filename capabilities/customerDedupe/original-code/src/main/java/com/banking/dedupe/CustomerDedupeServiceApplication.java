package com.banking.dedupe;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Customer Deduplication Service Application
 * 
 * Main entry point for the Banking Customer Deduplication Service.
 * This service provides customer deduplication capabilities for RBI compliance
 * by integrating with third-party vendors and maintaining comprehensive audit trails.
 * 
 * Features:
 * - Customer deduplication request processing
 * - Third-party vendor integration
 * - Comprehensive audit logging with PII masking
 * - RBI compliance data retention
 * - Standardized error handling
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@SpringBootApplication
@EnableConfigurationProperties
@EnableTransactionManagement
@EnableAsync
public class CustomerDedupeServiceApplication {

    /**
     * Main method to start the Customer Deduplication Service
     * 
     * @param args Command line arguments
     */
    public static void main(String[] args) {
        SpringApplication.run(CustomerDedupeServiceApplication.class, args);
    }
}
