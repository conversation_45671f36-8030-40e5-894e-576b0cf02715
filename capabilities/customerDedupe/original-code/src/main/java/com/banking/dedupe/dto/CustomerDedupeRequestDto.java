package com.banking.dedupe.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

import java.time.LocalDate;

/**
 * Data Transfer Object for Customer Deduplication Request
 * 
 * Represents the incoming request from upstream applications for customer deduplication.
 * Contains all necessary customer information required for deduplication processing.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class CustomerDedupeRequestDto {

    @NotBlank(message = "Customer identifier is required")
    @Size(max = 100, message = "Customer identifier must not exceed 100 characters")
    @JsonProperty("customerIdentifier")
    private String customerIdentifier;

    @NotNull(message = "Customer data is required")
    @Valid
    @JsonProperty("customerData")
    private CustomerDataDto customerData;

    @Size(max = 100, message = "Upstream application name must not exceed 100 characters")
    @JsonProperty("upstreamApplication")
    private String upstreamApplication;

    @Size(max = 100, message = "Upstream request ID must not exceed 100 characters")
    @JsonProperty("upstreamRequestId")
    private String upstreamRequestId;

    /**
     * Default constructor
     */
    public CustomerDedupeRequestDto() {
    }

    /**
     * Constructor with all fields
     */
    public CustomerDedupeRequestDto(String customerIdentifier, CustomerDataDto customerData, 
                                   String upstreamApplication, String upstreamRequestId) {
        this.customerIdentifier = customerIdentifier;
        this.customerData = customerData;
        this.upstreamApplication = upstreamApplication;
        this.upstreamRequestId = upstreamRequestId;
    }

    // Getters and Setters

    public String getCustomerIdentifier() {
        return customerIdentifier;
    }

    public void setCustomerIdentifier(String customerIdentifier) {
        this.customerIdentifier = customerIdentifier;
    }

    public CustomerDataDto getCustomerData() {
        return customerData;
    }

    public void setCustomerData(CustomerDataDto customerData) {
        this.customerData = customerData;
    }

    public String getUpstreamApplication() {
        return upstreamApplication;
    }

    public void setUpstreamApplication(String upstreamApplication) {
        this.upstreamApplication = upstreamApplication;
    }

    public String getUpstreamRequestId() {
        return upstreamRequestId;
    }

    public void setUpstreamRequestId(String upstreamRequestId) {
        this.upstreamRequestId = upstreamRequestId;
    }

    /**
     * Nested DTO for Customer Data
     */
    public static class CustomerDataDto {

        @NotBlank(message = "Full name is required")
        @Size(max = 200, message = "Full name must not exceed 200 characters")
        @JsonProperty("fullName")
        private String fullName;

        @NotNull(message = "Date of birth is required")
        @JsonProperty("dateOfBirth")
        private LocalDate dateOfBirth;

        @NotBlank(message = "PAN is required")
        @Pattern(regexp = "[A-Z]{5}[0-9]{4}[A-Z]{1}", message = "Invalid PAN format")
        @JsonProperty("pan")
        private String pan;

        @Pattern(regexp = "[0-9]{4}-[0-9]{4}-[0-9]{4}", message = "Invalid Aadhaar format")
        @JsonProperty("aadhaar")
        private String aadhaar;

        @NotBlank(message = "Mobile number is required")
        @Pattern(regexp = "[6-9][0-9]{9}", message = "Invalid mobile number format")
        @JsonProperty("mobileNumber")
        private String mobileNumber;

        @Email(message = "Invalid email format")
        @Size(max = 100, message = "Email address must not exceed 100 characters")
        @JsonProperty("emailAddress")
        private String emailAddress;

        @Valid
        @JsonProperty("address")
        private AddressDto address;

        /**
         * Default constructor
         */
        public CustomerDataDto() {
        }

        /**
         * Constructor with all fields
         */
        public CustomerDataDto(String fullName, LocalDate dateOfBirth, String pan, String aadhaar,
                              String mobileNumber, String emailAddress, AddressDto address) {
            this.fullName = fullName;
            this.dateOfBirth = dateOfBirth;
            this.pan = pan;
            this.aadhaar = aadhaar;
            this.mobileNumber = mobileNumber;
            this.emailAddress = emailAddress;
            this.address = address;
        }

        // Getters and Setters

        public String getFullName() {
            return fullName;
        }

        public void setFullName(String fullName) {
            this.fullName = fullName;
        }

        public LocalDate getDateOfBirth() {
            return dateOfBirth;
        }

        public void setDateOfBirth(LocalDate dateOfBirth) {
            this.dateOfBirth = dateOfBirth;
        }

        public String getPan() {
            return pan;
        }

        public void setPan(String pan) {
            this.pan = pan;
        }

        public String getAadhaar() {
            return aadhaar;
        }

        public void setAadhaar(String aadhaar) {
            this.aadhaar = aadhaar;
        }

        public String getMobileNumber() {
            return mobileNumber;
        }

        public void setMobileNumber(String mobileNumber) {
            this.mobileNumber = mobileNumber;
        }

        public String getEmailAddress() {
            return emailAddress;
        }

        public void setEmailAddress(String emailAddress) {
            this.emailAddress = emailAddress;
        }

        public AddressDto getAddress() {
            return address;
        }

        public void setAddress(AddressDto address) {
            this.address = address;
        }
    }

    /**
     * Nested DTO for Address Information
     */
    public static class AddressDto {

        @Size(max = 200, message = "Address line 1 must not exceed 200 characters")
        @JsonProperty("line1")
        private String line1;

        @Size(max = 100, message = "City must not exceed 100 characters")
        @JsonProperty("city")
        private String city;

        @Size(max = 100, message = "State must not exceed 100 characters")
        @JsonProperty("state")
        private String state;

        @Pattern(regexp = "[0-9]{6}", message = "Invalid pincode format")
        @JsonProperty("pincode")
        private String pincode;

        /**
         * Default constructor
         */
        public AddressDto() {
        }

        /**
         * Constructor with all fields
         */
        public AddressDto(String line1, String city, String state, String pincode) {
            this.line1 = line1;
            this.city = city;
            this.state = state;
            this.pincode = pincode;
        }

        // Getters and Setters

        public String getLine1() {
            return line1;
        }

        public void setLine1(String line1) {
            this.line1 = line1;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getPincode() {
            return pincode;
        }

        public void setPincode(String pincode) {
            this.pincode = pincode;
        }
    }
}
