package com.banking.dedupe.enums;

/**
 * Enumeration for Vendor Response Status
 * 
 * Represents the status returned by third-party deduplication vendors.
 * This enum maps vendor-specific responses to standardized internal statuses.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public enum VendorStatus {
    
    /**
     * Vendor found duplicate customer records
     */
    MATCH_FOUND("Duplicate customer records found"),
    
    /**
     * Vendor did not find any duplicate customer records
     */
    NO_MATCH_FOUND("No duplicate customer records found"),
    
    /**
     * Vendor returned an error response
     */
    ERROR("Vendor service returned an error"),
    
    /**
     * Vendor call timed out
     */
    TIMEOUT("Vendor service call timed out"),
    
    /**
     * Vendor service is unavailable or failed
     */
    VENDOR_SERVICE_FAILURE("Vendor service is unavailable");

    private final String description;

    /**
     * Constructor for VendorStatus enum
     * 
     * @param description Human-readable description of the status
     */
    VendorStatus(String description) {
        this.description = description;
    }

    /**
     * Get the description of the status
     * 
     * @return String description of the status
     */
    public String getDescription() {
        return description;
    }

    /**
     * Check if the status indicates a successful vendor response
     * 
     * @return true if the status is MATCH_FOUND or NO_MATCH_FOUND
     */
    public boolean isSuccessful() {
        return this == MATCH_FOUND || this == NO_MATCH_FOUND;
    }

    /**
     * Check if the status indicates an error condition
     * 
     * @return true if the status is ERROR, TIMEOUT, or VENDOR_SERVICE_FAILURE
     */
    public boolean isError() {
        return this == ERROR || this == TIMEOUT || this == VENDOR_SERVICE_FAILURE;
    }

    /**
     * Check if the status indicates a match was found
     * 
     * @return true if the status is MATCH_FOUND
     */
    public boolean isMatch() {
        return this == MATCH_FOUND;
    }

    /**
     * Check if the status indicates no match was found
     * 
     * @return true if the status is NO_MATCH_FOUND
     */
    public boolean isNoMatch() {
        return this == NO_MATCH_FOUND;
    }
}
