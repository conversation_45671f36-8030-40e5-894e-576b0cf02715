package com.banking.dedupe.service;

import com.banking.dedupe.dto.CustomerDedupeRequestDto;
import com.banking.dedupe.dto.CustomerDedupeResponseDto;
import com.banking.dedupe.dto.VendorRequestDto;
import com.banking.dedupe.entity.DedupeRequest;
import com.banking.dedupe.entity.DedupeResponse;
import com.banking.dedupe.entity.VendorResponse;
import com.banking.dedupe.enums.RequestStatus;
import com.banking.dedupe.repository.DedupeRequestRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Main Customer Deduplication Service
 * 
 * Orchestrates the complete customer deduplication process including:
 * - Phase 1: Request reception, validation, vendor interaction
 * - Phase 2: Response interpretation, standardization, upstream reporting
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Service
@Transactional
public class CustomerDedupeService {

    private static final Logger logger = LoggerFactory.getLogger(CustomerDedupeService.class);

    private final DedupeRequestRepository dedupeRequestRepository;
    private final ValidationService validationService;
    private final VendorIntegrationService vendorIntegrationService;
    private final ResponseInterpreterService responseInterpreterService;
    private final AuditService auditService;
    private final ReferenceNumberService referenceNumberService;

    public CustomerDedupeService(DedupeRequestRepository dedupeRequestRepository,
                               ValidationService validationService,
                               VendorIntegrationService vendorIntegrationService,
                               ResponseInterpreterService responseInterpreterService,
                               AuditService auditService,
                               ReferenceNumberService referenceNumberService) {
        this.dedupeRequestRepository = dedupeRequestRepository;
        this.validationService = validationService;
        this.vendorIntegrationService = vendorIntegrationService;
        this.responseInterpreterService = responseInterpreterService;
        this.auditService = auditService;
        this.referenceNumberService = referenceNumberService;
    }

    /**
     * Process customer deduplication request
     * 
     * @param requestDto The incoming deduplication request
     * @param clientIp The client IP address
     * @param userAgent The user agent
     * @return Customer deduplication response
     */
    public CustomerDedupeResponseDto processDedupeRequest(CustomerDedupeRequestDto requestDto, 
                                                         String clientIp, String userAgent) {
        
        // Generate unique reference number
        String referenceNumber = referenceNumberService.generateReferenceNumber();
        auditService.setTraceId(referenceNumber);
        
        LocalDateTime startTime = LocalDateTime.now();
        
        logger.info("Starting deduplication process for reference: {} - Customer: {}", 
            referenceNumber, requestDto.getCustomerIdentifier());

        try {
            // PHASE 1: REQUEST RECEPTION & VENDOR INTERACTION
            
            // Step 1: Create and save initial request
            DedupeRequest request = createDedupeRequest(requestDto, referenceNumber);
            request = dedupeRequestRepository.save(request);
            
            // Step 2: Log request initiation
            auditService.logRequestInitiation(request, clientIp, userAgent);
            
            // Step 3: Validate request
            ValidationService.ValidationResult validationResult = validationService.validateRequest(requestDto);
            
            if (!validationResult.isValid()) {
                return handleValidationFailure(request, validationResult);
            }
            
            // Update status after successful validation
            request.updateStatus(RequestStatus.VALIDATED);
            request = dedupeRequestRepository.save(request);
            auditService.logValidationCompletion(request, true);
            
            // Step 4: Transform to vendor request format
            VendorRequestDto vendorRequest = vendorIntegrationService.transformToVendorRequest(request);
            
            // Step 5: Call vendor API with retry logic
            request.updateStatus(RequestStatus.VENDOR_CALL_INITIATED);
            request = dedupeRequestRepository.save(request);
            
            VendorResponse vendorResponse = vendorIntegrationService.callVendorApi(request, vendorRequest);
            
            // Update status based on vendor call result
            if (vendorResponse.isSuccessful()) {
                request.updateStatus(RequestStatus.VENDOR_CALL_SUCCESS);
            } else {
                request.updateStatus(RequestStatus.VENDOR_CALL_FAILED);
                request = dedupeRequestRepository.save(request);
                return handleVendorFailure(request, vendorResponse);
            }
            request = dedupeRequestRepository.save(request);
            
            // PHASE 2: RESPONSE INTERPRETATION & REPORTING
            
            // Step 6: Interpret vendor response
            DedupeResponse dedupeResponse = responseInterpreterService.interpretVendorResponse(request, vendorResponse);
            
            request.updateStatus(RequestStatus.RESPONSE_INTERPRETED);
            request = dedupeRequestRepository.save(request);
            
            // Step 7: Create final response
            CustomerDedupeResponseDto finalResponse = responseInterpreterService.createFinalResponseDto(dedupeResponse);
            
            // Step 8: Mark as completed
            request.updateStatus(RequestStatus.COMPLETED);
            request = dedupeRequestRepository.save(request);
            
            dedupeResponse.markResponseSent("SUCCESS");
            
            // Step 9: Log final completion
            long totalProcessingTime = java.time.Duration.between(startTime, LocalDateTime.now()).toMillis();
            auditService.logFinalCompletion(request, totalProcessingTime, "SUCCESS");
            
            logger.info("Deduplication process completed for reference: {} - Status: {} - Duplicates: {} - Time: {}ms", 
                referenceNumber, finalResponse.getDedupeStatus(), 
                finalResponse.getDuplicates().size(), totalProcessingTime);
            
            return finalResponse;
            
        } catch (Exception e) {
            logger.error("Unexpected error in deduplication process for reference: {}", referenceNumber, e);
            return handleUnexpectedError(referenceNumber, requestDto, e);
            
        } finally {
            auditService.clearTraceId();
        }
    }

    /**
     * Create dedupe request entity from DTO
     */
    private DedupeRequest createDedupeRequest(CustomerDedupeRequestDto requestDto, String referenceNumber) {
        DedupeRequest request = new DedupeRequest();
        
        // Basic information
        request.setReferenceNumber(referenceNumber);
        request.setCustomerIdentifier(requestDto.getCustomerIdentifier());
        request.setUpstreamApplication(requestDto.getUpstreamApplication());
        request.setUpstreamRequestId(requestDto.getUpstreamRequestId());
        
        // Customer data
        CustomerDedupeRequestDto.CustomerDataDto customerData = requestDto.getCustomerData();
        request.setFullName(customerData.getFullName());
        request.setDateOfBirth(customerData.getDateOfBirth());
        request.setPan(customerData.getPan());
        request.setAadhaar(customerData.getAadhaar());
        request.setMobileNumber(customerData.getMobileNumber());
        request.setEmailAddress(customerData.getEmailAddress());
        
        // Address data
        if (customerData.getAddress() != null) {
            request.setAddressLine1(customerData.getAddress().getLine1());
            request.setAddressCity(customerData.getAddress().getCity());
            request.setAddressState(customerData.getAddress().getState());
            request.setAddressPincode(customerData.getAddress().getPincode());
        }
        
        return request;
    }

    /**
     * Handle validation failure
     */
    private CustomerDedupeResponseDto handleValidationFailure(DedupeRequest request, 
                                                            ValidationService.ValidationResult validationResult) {
        
        request.updateStatus(RequestStatus.FAILED);
        dedupeRequestRepository.save(request);
        
        auditService.logValidationCompletion(request, false);
        auditService.logError(request, "VALIDATION_ERROR", validationResult.getErrorMessage(), null);
        
        logger.warn("Validation failed for reference: {} - Errors: {}", 
            request.getReferenceNumber(), validationResult.getErrors());
        
        return createErrorResponse(request.getReferenceNumber(), request.getCustomerIdentifier(),
            "VALIDATION_ERROR", "Request validation failed: " + validationResult.getErrorMessage());
    }

    /**
     * Handle vendor failure
     */
    private CustomerDedupeResponseDto handleVendorFailure(DedupeRequest request, VendorResponse vendorResponse) {
        
        request.updateStatus(RequestStatus.FAILED);
        dedupeRequestRepository.save(request);
        
        auditService.logError(request, vendorResponse.getErrorCode(), vendorResponse.getErrorMessage(), null);
        
        logger.error("Vendor call failed for reference: {} - Error: {} - Message: {}", 
            request.getReferenceNumber(), vendorResponse.getErrorCode(), vendorResponse.getErrorMessage());
        
        String errorMessage = switch (vendorResponse.getVendorStatus()) {
            case TIMEOUT -> "Dedupe service request timed out, manual review required";
            case VENDOR_SERVICE_FAILURE -> "Dedupe service temporarily unavailable, manual review required";
            default -> "Dedupe service error, manual review required";
        };
        
        return createErrorResponse(request.getReferenceNumber(), request.getCustomerIdentifier(),
            vendorResponse.getErrorCode(), errorMessage);
    }

    /**
     * Handle unexpected errors
     */
    private CustomerDedupeResponseDto handleUnexpectedError(String referenceNumber, 
                                                          CustomerDedupeRequestDto requestDto, Exception e) {
        
        auditService.logError(null, "UNEXPECTED_ERROR", e.getMessage(), e);
        
        logger.error("Unexpected error for reference: {}", referenceNumber, e);
        
        return createErrorResponse(referenceNumber, 
            requestDto != null ? requestDto.getCustomerIdentifier() : "UNKNOWN",
            "SYSTEM_ERROR", "System error occurred, manual review required");
    }

    /**
     * Create error response DTO
     */
    private CustomerDedupeResponseDto createErrorResponse(String referenceNumber, String customerIdentifier,
                                                        String errorCode, String errorMessage) {
        
        CustomerDedupeResponseDto errorResponse = new CustomerDedupeResponseDto();
        errorResponse.setReferenceNumber(referenceNumber);
        errorResponse.setCustomerIdentifier(customerIdentifier);
        errorResponse.setDedupeStatus(com.banking.dedupe.enums.VendorStatus.ERROR);
        errorResponse.setMessage(errorMessage);
        errorResponse.setDuplicates(java.util.Collections.emptyList());
        
        // Vendor details
        CustomerDedupeResponseDto.VendorDetailsDto vendorDetails = 
            new CustomerDedupeResponseDto.VendorDetailsDto();
        vendorDetails.setVendorName("SYSTEM");
        vendorDetails.setProcessingTimestamp(LocalDateTime.now());
        errorResponse.setVendorDetails(vendorDetails);
        
        // Audit info
        CustomerDedupeResponseDto.AuditInfoDto auditInfo = 
            new CustomerDedupeResponseDto.AuditInfoDto();
        auditInfo.setRequestTimestamp(LocalDateTime.now());
        auditInfo.setResponseTimestamp(LocalDateTime.now());
        auditInfo.setProcessingTimeMs(0L);
        errorResponse.setAuditInfo(auditInfo);
        
        return errorResponse;
    }

    /**
     * Get deduplication request by reference number
     * 
     * @param referenceNumber The reference number
     * @return Dedupe request if found
     */
    @Transactional(readOnly = true)
    public DedupeRequest getRequestByReferenceNumber(String referenceNumber) {
        return dedupeRequestRepository.findByReferenceNumber(referenceNumber).orElse(null);
    }

    /**
     * Check request status
     * 
     * @param referenceNumber The reference number
     * @return Request status information
     */
    @Transactional(readOnly = true)
    public RequestStatusInfo getRequestStatus(String referenceNumber) {
        DedupeRequest request = getRequestByReferenceNumber(referenceNumber);
        
        if (request == null) {
            return new RequestStatusInfo(referenceNumber, "NOT_FOUND", "Request not found", null);
        }
        
        return new RequestStatusInfo(referenceNumber, request.getRequestStatus().name(),
            request.getRequestStatus().getDescription(), request.getUpdatedAt());
    }

    /**
     * Request status information class
     */
    public static class RequestStatusInfo {
        private final String referenceNumber;
        private final String status;
        private final String description;
        private final LocalDateTime lastUpdated;

        public RequestStatusInfo(String referenceNumber, String status, String description, LocalDateTime lastUpdated) {
            this.referenceNumber = referenceNumber;
            this.status = status;
            this.description = description;
            this.lastUpdated = lastUpdated;
        }

        public String getReferenceNumber() { return referenceNumber; }
        public String getStatus() { return status; }
        public String getDescription() { return description; }
        public LocalDateTime getLastUpdated() { return lastUpdated; }
    }
}
