package com.banking.dedupe.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;

import com.banking.dedupe.enums.RequestStatus;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

/**
 * Entity representing a Customer Deduplication Request
 * 
 * Stores all information related to a customer deduplication request
 * including customer data, processing status, and audit information.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Entity
@Table(name = "dedupe_request")
public class DedupeRequest {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "dedupe_request_seq")
    @SequenceGenerator(name = "dedupe_request_seq", sequenceName = "dedupe_request_seq", allocationSize = 1)
    private Long id;

    @Column(name = "reference_number", nullable = false, unique = true, length = 20)
    private String referenceNumber;

    @Column(name = "customer_identifier", nullable = false, length = 100)
    private String customerIdentifier;

    // Customer Data
    @Column(name = "full_name", nullable = false, length = 200)
    private String fullName;

    @Column(name = "date_of_birth", nullable = false)
    private LocalDate dateOfBirth;

    @Column(name = "pan", nullable = false, length = 10)
    private String pan;

    @Column(name = "aadhaar", length = 14)
    private String aadhaar;

    @Column(name = "mobile_number", nullable = false, length = 15)
    private String mobileNumber;

    @Column(name = "email_address", length = 100)
    private String emailAddress;

    // Address Information
    @Column(name = "address_line1", length = 200)
    private String addressLine1;

    @Column(name = "address_city", length = 100)
    private String addressCity;

    @Column(name = "address_state", length = 100)
    private String addressState;

    @Column(name = "address_pincode", length = 10)
    private String addressPincode;

    // Request Metadata
    @Column(name = "request_status", nullable = false)
    private String requestStatus = "RECEIVED";

    @Column(name = "upstream_application", length = 100)
    private String upstreamApplication;

    @Column(name = "upstream_request_id", length = 100)
    private String upstreamRequestId;

    // Timestamps
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Audit Fields
    @Column(name = "created_by", length = 100)
    private String createdBy = "SYSTEM";

    @Column(name = "updated_by", length = 100)
    private String updatedBy = "SYSTEM";

    @Version
    @Column(name = "version", nullable = false)
    private Integer version = 1;

    /**
     * Default constructor
     */
    public DedupeRequest() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Constructor with essential fields
     */
    public DedupeRequest(String referenceNumber, String customerIdentifier, String fullName, 
                        LocalDate dateOfBirth, String pan, String mobileNumber) {
        this();
        this.referenceNumber = referenceNumber;
        this.customerIdentifier = customerIdentifier;
        this.fullName = fullName;
        this.dateOfBirth = dateOfBirth;
        this.pan = pan;
        this.mobileNumber = mobileNumber;
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getCustomerIdentifier() {
        return customerIdentifier;
    }

    public void setCustomerIdentifier(String customerIdentifier) {
        this.customerIdentifier = customerIdentifier;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getAadhaar() {
        return aadhaar;
    }

    public void setAadhaar(String aadhaar) {
        this.aadhaar = aadhaar;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getAddressLine1() {
        return addressLine1;
    }

    public void setAddressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    public String getAddressCity() {
        return addressCity;
    }

    public void setAddressCity(String addressCity) {
        this.addressCity = addressCity;
    }

    public String getAddressState() {
        return addressState;
    }

    public void setAddressState(String addressState) {
        this.addressState = addressState;
    }

    public String getAddressPincode() {
        return addressPincode;
    }

    public void setAddressPincode(String addressPincode) {
        this.addressPincode = addressPincode;
    }

    public RequestStatus getRequestStatus() {
        return RequestStatus.valueOf(requestStatus);
    }

    public void setRequestStatus(RequestStatus requestStatus) {
        this.requestStatus = requestStatus.name();
        this.updatedAt = LocalDateTime.now();
    }

    public String getUpstreamApplication() {
        return upstreamApplication;
    }

    public void setUpstreamApplication(String upstreamApplication) {
        this.upstreamApplication = upstreamApplication;
    }

    public String getUpstreamRequestId() {
        return upstreamRequestId;
    }

    public void setUpstreamRequestId(String upstreamRequestId) {
        this.upstreamRequestId = upstreamRequestId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    /**
     * Update the status and timestamp
     */
    public void updateStatus(RequestStatus newStatus) {
        this.requestStatus = newStatus.name();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Check if the request is in a terminal state
     */
    public boolean isTerminal() {
        return requestStatus != null && RequestStatus.valueOf(requestStatus).isTerminal();
    }
}
