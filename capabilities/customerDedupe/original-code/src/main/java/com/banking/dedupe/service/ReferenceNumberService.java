package com.banking.dedupe.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Service for generating unique reference numbers
 * 
 * Generates unique, traceable reference numbers for customer deduplication requests
 * following banking industry standards for transaction tracking.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Service
public class ReferenceNumberService {

    @Value("${app.reference-number.prefix:DEDUPE}")
    private String referencePrefix;

    @Value("${app.reference-number.length:12}")
    private int referenceLength;

    private final AtomicLong sequenceCounter = new AtomicLong(1);
    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    private final DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HHmmss");

    /**
     * Generate unique reference number
     * 
     * Format: PREFIX-YYYYMMDD-HHMMSS-SEQUENCE
     * Example: DEDUPE-********-143022-001
     * 
     * @return Unique reference number
     */
    public String generateReferenceNumber() {
        LocalDateTime now = LocalDateTime.now();
        String datePart = now.format(dateFormatter);
        String timePart = now.format(timeFormatter);
        long sequence = sequenceCounter.getAndIncrement();
        
        // Reset sequence daily to keep numbers manageable
        if (sequence > 999999) {
            sequenceCounter.set(1);
            sequence = 1;
        }
        
        return String.format("%s-%s-%s-%03d", 
            referencePrefix, datePart, timePart, sequence % 1000);
    }

    /**
     * Validate reference number format
     * 
     * @param referenceNumber The reference number to validate
     * @return true if valid format
     */
    public boolean isValidReferenceNumber(String referenceNumber) {
        if (referenceNumber == null || referenceNumber.trim().isEmpty()) {
            return false;
        }
        
        // Check basic format: PREFIX-YYYYMMDD-HHMMSS-NNN
        String pattern = referencePrefix + "-\\d{8}-\\d{6}-\\d{3}";
        return referenceNumber.matches(pattern);
    }

    /**
     * Extract date from reference number
     * 
     * @param referenceNumber The reference number
     * @return LocalDateTime if extractable, null otherwise
     */
    public LocalDateTime extractDateFromReference(String referenceNumber) {
        if (!isValidReferenceNumber(referenceNumber)) {
            return null;
        }
        
        try {
            String[] parts = referenceNumber.split("-");
            if (parts.length >= 3) {
                String datePart = parts[1];
                String timePart = parts[2];
                String dateTimeString = datePart + timePart;
                
                return LocalDateTime.parse(dateTimeString, 
                    DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            }
        } catch (Exception e) {
            // Invalid format
        }
        
        return null;
    }

    /**
     * Get current sequence number (for monitoring)
     * 
     * @return Current sequence number
     */
    public long getCurrentSequence() {
        return sequenceCounter.get();
    }

    /**
     * Reset sequence counter (for testing or daily reset)
     */
    public void resetSequence() {
        sequenceCounter.set(1);
    }
}
