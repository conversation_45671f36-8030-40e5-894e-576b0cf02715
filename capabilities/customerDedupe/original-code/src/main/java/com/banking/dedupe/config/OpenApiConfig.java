package com.banking.dedupe.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;

/**
 * OpenAPI Configuration for Customer Deduplication Service
 * 
 * Configures OpenAPI 3.0 documentation and Swagger UI for the customer deduplication service.
 * Includes CORS configuration to ensure Swagger UI works properly.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-27
 */
@Configuration
public class OpenApiConfig implements WebMvcConfigurer {

    @Value("${server.port:8082}")
    private String serverPort;

    @Value("${server.servlet.context-path:/api/customer-dedupe}")
    private String contextPath;

    /**
     * Configure OpenAPI documentation
     */
    @Bean
    public OpenAPI customerDedupeOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Customer Deduplication Service API")
                        .description("Banking Customer Deduplication Service for RBI Compliance. " +
                                   "This service provides customer deduplication capabilities by integrating " +
                                   "with third-party vendors to identify duplicate customer records.")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Banking Technology Team")
                                .email("<EMAIL>")
                                .url("https://banking.com/contact"))
                        .license(new License()
                                .name("Proprietary")
                                .url("https://banking.com/license")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + contextPath)
                                .description("Local Development Server"),
                        new Server()
                                .url("https://api.banking.com/customer-dedupe")
                                .description("Production Server"),
                        new Server()
                                .url("https://staging-api.banking.com/customer-dedupe")
                                .description("Staging Server")
                ));
    }

    /**
     * Configure CORS to allow Swagger UI to function properly
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
}
