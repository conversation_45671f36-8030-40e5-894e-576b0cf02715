package com.banking.dedupe.entity;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonIgnore;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

/**
 * Entity representing an Audit Log Entry
 * 
 * Stores comprehensive audit trail information for all deduplication
 * operations including events, status changes, and error details.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Entity
@Table(name = "audit_log")
public class AuditLog {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "audit_log_seq")
    @SequenceGenerator(name = "audit_log_seq", sequenceName = "audit_log_seq", allocationSize = 1)
    private Long id;

    // Reference Information
    @Column(name = "reference_number", nullable = false, length = 20)
    private String referenceNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dedupe_request_id")
    @JsonIgnore
    private DedupeRequest dedupeRequest;

    // Audit Details
    @Column(name = "event_type", nullable = false, length = 50)
    private String eventType;

    @Column(name = "event_description", columnDefinition = "TEXT")
    private String eventDescription;

    @Column(name = "event_data", columnDefinition = "TEXT")
    private String eventData; // JSON format with masked PII

    // Status Information
    @Column(name = "previous_status", length = 50)
    private String previousStatus;

    @Column(name = "current_status", length = 50)
    private String currentStatus;

    // Error Information
    @Column(name = "error_code", length = 50)
    private String errorCode;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    // Processing Information
    @Column(name = "processing_time_ms")
    private Integer processingTimeMs;

    @Column(name = "retry_attempt")
    private Integer retryAttempt = 0;

    // User/System Information
    @Column(name = "performed_by", length = 100)
    private String performedBy = "SYSTEM";

    @Column(name = "client_ip", length = 45)
    private String clientIp;

    @Column(name = "user_agent", columnDefinition = "TEXT")
    private String userAgent;

    // Timestamp
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    /**
     * Default constructor
     */
    public AuditLog() {
        this.createdAt = LocalDateTime.now();
    }

    /**
     * Constructor with essential fields
     */
    public AuditLog(String referenceNumber, String eventType, String eventDescription) {
        this();
        this.referenceNumber = referenceNumber;
        this.eventType = eventType;
        this.eventDescription = eventDescription;
    }

    /**
     * Constructor with request reference
     */
    public AuditLog(DedupeRequest dedupeRequest, String eventType, String eventDescription) {
        this();
        this.dedupeRequest = dedupeRequest;
        this.referenceNumber = dedupeRequest.getReferenceNumber();
        this.eventType = eventType;
        this.eventDescription = eventDescription;
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    @JsonIgnore
    public DedupeRequest getDedupeRequest() {
        return dedupeRequest;
    }

    public void setDedupeRequest(DedupeRequest dedupeRequest) {
        this.dedupeRequest = dedupeRequest;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getEventDescription() {
        return eventDescription;
    }

    public void setEventDescription(String eventDescription) {
        this.eventDescription = eventDescription;
    }

    public String getEventData() {
        return eventData;
    }

    public void setEventData(String eventData) {
        this.eventData = eventData;
    }

    public String getPreviousStatus() {
        return previousStatus;
    }

    public void setPreviousStatus(String previousStatus) {
        this.previousStatus = previousStatus;
    }

    public String getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(String currentStatus) {
        this.currentStatus = currentStatus;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Integer getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(Integer processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }

    public Integer getRetryAttempt() {
        return retryAttempt;
    }

    public void setRetryAttempt(Integer retryAttempt) {
        this.retryAttempt = retryAttempt;
    }

    public String getPerformedBy() {
        return performedBy;
    }

    public void setPerformedBy(String performedBy) {
        this.performedBy = performedBy;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * Create audit log for status change
     */
    public static AuditLog forStatusChange(DedupeRequest request, String previousStatus, 
                                         String currentStatus, String description) {
        AuditLog auditLog = new AuditLog(request, "STATUS_CHANGE", description);
        auditLog.setPreviousStatus(previousStatus);
        auditLog.setCurrentStatus(currentStatus);
        return auditLog;
    }

    /**
     * Create audit log for error
     */
    public static AuditLog forError(DedupeRequest request, String errorCode, 
                                  String errorMessage, String description) {
        AuditLog auditLog = new AuditLog(request, "ERROR", description);
        auditLog.setErrorCode(errorCode);
        auditLog.setErrorMessage(errorMessage);
        return auditLog;
    }

    /**
     * Create audit log for vendor call
     */
    public static AuditLog forVendorCall(DedupeRequest request, String eventType, 
                                       String description, Integer processingTime) {
        AuditLog auditLog = new AuditLog(request, eventType, description);
        auditLog.setProcessingTimeMs(processingTime);
        return auditLog;
    }
}
