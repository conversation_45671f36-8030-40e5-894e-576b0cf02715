package com.banking.dedupe.repository;

import com.banking.dedupe.entity.AuditLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository interface for AuditLog entity
 * 
 * Provides data access methods for audit logs
 * including queries for compliance and monitoring purposes.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Repository
public interface AuditLogRepository extends JpaRepository<AuditLog, Long> {

    /**
     * Find audit logs by reference number
     * 
     * @param referenceNumber The reference number
     * @return List of audit logs for the reference number
     */
    List<AuditLog> findByReferenceNumber(String referenceNumber);

    /**
     * Find audit logs by dedupe request ID
     * 
     * @param dedupeRequestId The dedupe request ID
     * @return List of audit logs for the request
     */
    List<AuditLog> findByDedupeRequestId(Long dedupeRequestId);

    /**
     * Find audit logs by event type
     * 
     * @param eventType The event type
     * @return List of audit logs for the event type
     */
    List<AuditLog> findByEventType(String eventType);

    /**
     * Find audit logs created within a date range
     * 
     * @param startDate The start date
     * @param endDate The end date
     * @return List of audit logs created within the date range
     */
    List<AuditLog> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find audit logs by performed by
     * 
     * @param performedBy The user/system that performed the action
     * @return List of audit logs performed by the specified user/system
     */
    List<AuditLog> findByPerformedBy(String performedBy);

    /**
     * Find error audit logs
     * 
     * @return List of audit logs with error events
     */
    @Query("SELECT al FROM AuditLog al WHERE al.errorCode IS NOT NULL OR al.eventType = 'ERROR'")
    List<AuditLog> findErrorLogs();

    /**
     * Find audit logs with retry attempts
     * 
     * @return List of audit logs with retry attempts
     */
    @Query("SELECT al FROM AuditLog al WHERE al.retryAttempt > 0")
    List<AuditLog> findLogsWithRetries();

    /**
     * Find audit logs for a specific reference number ordered by creation time
     * 
     * @param referenceNumber The reference number
     * @return List of audit logs ordered by creation time
     */
    @Query("SELECT al FROM AuditLog al WHERE al.referenceNumber = :referenceNumber ORDER BY al.createdAt ASC")
    List<AuditLog> findByReferenceNumberOrderByCreatedAt(@Param("referenceNumber") String referenceNumber);

    /**
     * Count audit logs by event type
     * 
     * @param eventType The event type
     * @return Count of audit logs for the event type
     */
    long countByEventType(String eventType);

    /**
     * Count error logs today
     * 
     * @param startOfDay The start of the current day
     * @return Count of error logs today
     */
    @Query("SELECT COUNT(al) FROM AuditLog al WHERE (al.errorCode IS NOT NULL OR al.eventType = 'ERROR') " +
           "AND al.createdAt >= :startOfDay")
    long countErrorLogsToday(@Param("startOfDay") LocalDateTime startOfDay);

    /**
     * Get event type statistics
     * 
     * @param startDate The start date
     * @param endDate The end date
     * @return List of event type statistics
     */
    @Query("SELECT al.eventType, COUNT(al) FROM AuditLog al " +
           "WHERE al.createdAt BETWEEN :startDate AND :endDate " +
           "GROUP BY al.eventType ORDER BY COUNT(al) DESC")
    List<Object[]> getEventTypeStatistics(@Param("startDate") LocalDateTime startDate,
                                         @Param("endDate") LocalDateTime endDate);

    /**
     * Find audit logs for data retention cleanup
     * 
     * @param retentionCutoff The retention cutoff date
     * @return List of audit logs older than retention period
     */
    @Query("SELECT al FROM AuditLog al WHERE al.createdAt < :retentionCutoff")
    List<AuditLog> findLogsForRetentionCleanup(@Param("retentionCutoff") LocalDateTime retentionCutoff);

    /**
     * Find audit logs with long processing times
     * 
     * @param thresholdMs The processing time threshold in milliseconds
     * @param startDate The start date
     * @param endDate The end date
     * @return List of audit logs with long processing times
     */
    @Query("SELECT al FROM AuditLog al WHERE al.processingTimeMs > :thresholdMs " +
           "AND al.createdAt BETWEEN :startDate AND :endDate ORDER BY al.processingTimeMs DESC")
    List<AuditLog> findLogsWithLongProcessingTimes(@Param("thresholdMs") Integer thresholdMs,
                                                  @Param("startDate") LocalDateTime startDate,
                                                  @Param("endDate") LocalDateTime endDate);

    /**
     * Get processing time statistics by event type
     * 
     * @param eventType The event type
     * @param startDate The start date
     * @param endDate The end date
     * @return Processing time statistics
     */
    @Query("SELECT AVG(al.processingTimeMs), MIN(al.processingTimeMs), MAX(al.processingTimeMs) " +
           "FROM AuditLog al WHERE al.eventType = :eventType AND al.processingTimeMs IS NOT NULL " +
           "AND al.createdAt BETWEEN :startDate AND :endDate")
    Object[] getProcessingTimeStatistics(@Param("eventType") String eventType,
                                        @Param("startDate") LocalDateTime startDate,
                                        @Param("endDate") LocalDateTime endDate);

    /**
     * Find audit logs by client IP
     * 
     * @param clientIp The client IP address
     * @return List of audit logs from the specified IP
     */
    List<AuditLog> findByClientIp(String clientIp);

    /**
     * Find recent audit logs for monitoring
     * 
     * @param since The time threshold
     * @param limit The maximum number of logs to return
     * @return List of recent audit logs
     */
    @Query("SELECT al FROM AuditLog al WHERE al.createdAt >= :since " +
           "ORDER BY al.createdAt DESC LIMIT :limit")
    List<AuditLog> findRecentLogs(@Param("since") LocalDateTime since, @Param("limit") Integer limit);

    /**
     * Get daily audit log summary
     * 
     * @param date The date for summary
     * @return List of daily audit log summary
     */
    @Query("SELECT al.eventType, COUNT(al), " +
           "COUNT(CASE WHEN al.errorCode IS NOT NULL THEN 1 END) as errorCount " +
           "FROM AuditLog al WHERE DATE(al.createdAt) = DATE(:date) " +
           "GROUP BY al.eventType ORDER BY COUNT(al) DESC")
    List<Object[]> getDailyAuditSummary(@Param("date") LocalDateTime date);

    /**
     * Find audit logs for compliance reporting
     * 
     * @param startDate The start date
     * @param endDate The end date
     * @param eventTypes The list of event types to include
     * @return List of audit logs for compliance reporting
     */
    @Query("SELECT al FROM AuditLog al WHERE al.createdAt BETWEEN :startDate AND :endDate " +
           "AND al.eventType IN :eventTypes ORDER BY al.createdAt ASC")
    List<AuditLog> findLogsForComplianceReporting(@Param("startDate") LocalDateTime startDate,
                                                 @Param("endDate") LocalDateTime endDate,
                                                 @Param("eventTypes") List<String> eventTypes);
}
