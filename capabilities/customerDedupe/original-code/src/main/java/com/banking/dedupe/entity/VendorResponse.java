package com.banking.dedupe.entity;

import com.banking.dedupe.enums.VendorStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

import java.time.LocalDateTime;

/**
 * Entity representing a Vendor Response
 * 
 * Stores raw responses received from third-party deduplication vendors
 * including response data, timing information, and error details.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Entity
@Table(name = "vendor_response")
public class VendorResponse {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "vendor_response_seq")
    @SequenceGenerator(name = "vendor_response_seq", sequenceName = "vendor_response_seq", allocationSize = 1)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dedupe_request_id", nullable = false)
    private DedupeRequest dedupeRequest;

    // Vendor Details
    @Column(name = "vendor_name", nullable = false, length = 50)
    private String vendorName;

    @Column(name = "vendor_request_id", length = 100)
    private String vendorRequestId;

    @Column(name = "vendor_response_id", length = 100)
    private String vendorResponseId;

    // Response Data
    @Enumerated(EnumType.STRING)
    @Column(name = "vendor_status", nullable = false)
    private VendorStatus vendorStatus;

    @Column(name = "raw_response", columnDefinition = "TEXT")
    private String rawResponse;

    @Column(name = "error_code", length = 50)
    private String errorCode;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    // Processing Details
    @Column(name = "request_sent_at")
    private LocalDateTime requestSentAt;

    @Column(name = "response_received_at")
    private LocalDateTime responseReceivedAt;

    @Column(name = "processing_time_ms")
    private Integer processingTimeMs;

    @Column(name = "retry_attempt")
    private Integer retryAttempt = 0;

    // Timestamps
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * Default constructor
     */
    public VendorResponse() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Constructor with essential fields
     */
    public VendorResponse(DedupeRequest dedupeRequest, String vendorName, VendorStatus vendorStatus) {
        this();
        this.dedupeRequest = dedupeRequest;
        this.vendorName = vendorName;
        this.vendorStatus = vendorStatus;
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public DedupeRequest getDedupeRequest() {
        return dedupeRequest;
    }

    public void setDedupeRequest(DedupeRequest dedupeRequest) {
        this.dedupeRequest = dedupeRequest;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getVendorRequestId() {
        return vendorRequestId;
    }

    public void setVendorRequestId(String vendorRequestId) {
        this.vendorRequestId = vendorRequestId;
    }

    public String getVendorResponseId() {
        return vendorResponseId;
    }

    public void setVendorResponseId(String vendorResponseId) {
        this.vendorResponseId = vendorResponseId;
    }

    public VendorStatus getVendorStatus() {
        return vendorStatus;
    }

    public void setVendorStatus(VendorStatus vendorStatus) {
        this.vendorStatus = vendorStatus;
    }

    public String getRawResponse() {
        return rawResponse;
    }

    public void setRawResponse(String rawResponse) {
        this.rawResponse = rawResponse;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public LocalDateTime getRequestSentAt() {
        return requestSentAt;
    }

    public void setRequestSentAt(LocalDateTime requestSentAt) {
        this.requestSentAt = requestSentAt;
    }

    public LocalDateTime getResponseReceivedAt() {
        return responseReceivedAt;
    }

    public void setResponseReceivedAt(LocalDateTime responseReceivedAt) {
        this.responseReceivedAt = responseReceivedAt;
    }

    public Integer getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(Integer processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }

    public Integer getRetryAttempt() {
        return retryAttempt;
    }

    public void setRetryAttempt(Integer retryAttempt) {
        this.retryAttempt = retryAttempt;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * Calculate processing time based on request and response timestamps
     */
    public void calculateProcessingTime() {
        if (requestSentAt != null && responseReceivedAt != null) {
            this.processingTimeMs = (int) java.time.Duration.between(requestSentAt, responseReceivedAt).toMillis();
        }
    }

    /**
     * Mark response as received and calculate processing time
     */
    public void markResponseReceived() {
        this.responseReceivedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        calculateProcessingTime();
    }

    /**
     * Check if the vendor response indicates success
     */
    public boolean isSuccessful() {
        return vendorStatus != null && vendorStatus.isSuccessful();
    }

    /**
     * Check if the vendor response indicates an error
     */
    public boolean isError() {
        return vendorStatus != null && vendorStatus.isError();
    }
}
