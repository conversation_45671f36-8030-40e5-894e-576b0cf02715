package com.banking.dedupe.service;

import com.banking.dedupe.dto.VendorRequestDto;
import com.banking.dedupe.dto.VendorResponseDto;
import com.banking.dedupe.entity.DedupeRequest;
import com.banking.dedupe.entity.VendorResponse;
import com.banking.dedupe.enums.VendorStatus;
import com.banking.dedupe.repository.VendorResponseRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Service for integrating with third-party deduplication vendors
 * 
 * Handles vendor API calls, authentication, retry logic, and response processing
 * for Phase 1 of the customer deduplication capability.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Service
@Transactional
public class VendorIntegrationService {

    private static final Logger logger = LoggerFactory.getLogger(VendorIntegrationService.class);

    @Value("${vendor.dedupe.base-url}")
    private String vendorBaseUrl;

    @Value("${vendor.dedupe.timeout:30000}")
    private int vendorTimeout;

    @Value("${vendor.dedupe.retry.max-attempts:3}")
    private int maxRetryAttempts;

    @Value("${vendor.dedupe.retry.delay:1000}")
    private long retryDelay;

    @Value("${vendor.dedupe.retry.backoff-multiplier:2.0}")
    private double backoffMultiplier;



    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final VendorResponseRepository vendorResponseRepository;
    private final AuditService auditService;
    private final PiiMaskingService piiMaskingService;

    public VendorIntegrationService(RestTemplate restTemplate, ObjectMapper objectMapper,
                                   VendorResponseRepository vendorResponseRepository,
                                   AuditService auditService, PiiMaskingService piiMaskingService) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
        this.vendorResponseRepository = vendorResponseRepository;
        this.auditService = auditService;
        this.piiMaskingService = piiMaskingService;
    }

    /**
     * Call vendor deduplication API with retry logic
     * 
     * @param request The dedupe request
     * @param vendorRequest The vendor-specific request payload
     * @return VendorResponse entity with call results
     */
    public VendorResponse callVendorApi(DedupeRequest request, VendorRequestDto vendorRequest) {
        String vendorName = "Mock Dedupe Service"; // In real implementation, this would be configurable
        VendorResponse vendorResponse = new VendorResponse(request, vendorName, VendorStatus.ERROR);
        
        int attempt = 0;
        long currentDelay = retryDelay;
        
        while (attempt < maxRetryAttempts) {
            attempt++;
            vendorResponse.setRetryAttempt(attempt - 1);
            
            try {
                logger.info("Calling vendor API - Attempt {} for reference: {}", 
                    attempt, request.getReferenceNumber());
                
                auditService.logVendorCallInitiation(request, vendorName, attempt - 1);
                
                // Make the actual vendor API call
                VendorResponseDto apiResponse = makeVendorApiCall(vendorRequest);
                
                // Process successful response
                vendorResponse = processSuccessfulResponse(request, vendorName, apiResponse, attempt - 1);
                
                auditService.logVendorCallCompletion(request, vendorName, true, 
                    vendorResponse.getProcessingTimeMs(), null, null);
                
                logger.info("Vendor API call successful for reference: {} - Status: {}", 
                    request.getReferenceNumber(), vendorResponse.getVendorStatus());
                
                break; // Success, exit retry loop
                
            } catch (HttpClientErrorException e) {
                // 4xx errors - don't retry
                vendorResponse = processClientError(request, vendorName, e, attempt - 1);
                
                auditService.logVendorCallCompletion(request, vendorName, false, 
                    null, "CLIENT_ERROR", e.getMessage());
                
                logger.error("Vendor API client error for reference: {} - Status: {} - Message: {}", 
                    request.getReferenceNumber(), e.getStatusCode(), e.getMessage());
                
                break; // Don't retry client errors
                
            } catch (HttpServerErrorException e) {
                // 5xx errors - retry
                vendorResponse = processServerError(request, vendorName, e, attempt - 1);
                
                auditService.logVendorCallCompletion(request, vendorName, false, 
                    null, "SERVER_ERROR", e.getMessage());
                
                logger.warn("Vendor API server error for reference: {} - Attempt {} - Status: {} - Message: {}", 
                    request.getReferenceNumber(), attempt, e.getStatusCode(), e.getMessage());
                
                if (attempt < maxRetryAttempts) {
                    waitBeforeRetry(currentDelay);
                    currentDelay = (long) (currentDelay * backoffMultiplier);
                }
                
            } catch (ResourceAccessException e) {
                // Timeout or connection errors - retry
                vendorResponse = processTimeoutError(request, vendorName, e, attempt - 1);
                
                auditService.logVendorCallCompletion(request, vendorName, false, 
                    null, "TIMEOUT", e.getMessage());
                
                logger.warn("Vendor API timeout for reference: {} - Attempt {} - Message: {}", 
                    request.getReferenceNumber(), attempt, e.getMessage());
                
                if (attempt < maxRetryAttempts) {
                    waitBeforeRetry(currentDelay);
                    currentDelay = (long) (currentDelay * backoffMultiplier);
                }
                
            } catch (Exception e) {
                // Unexpected errors
                vendorResponse = processUnexpectedError(request, vendorName, e, attempt - 1);
                
                auditService.logVendorCallCompletion(request, vendorName, false, 
                    null, "UNEXPECTED_ERROR", e.getMessage());
                
                logger.error("Unexpected error calling vendor API for reference: {} - Attempt {}", 
                    request.getReferenceNumber(), attempt, e);
                
                break; // Don't retry unexpected errors
            }
        }
        
        // Save vendor response to database
        vendorResponse = vendorResponseRepository.save(vendorResponse);
        
        logger.info("Vendor response saved for reference: {} - Final status: {}", 
            request.getReferenceNumber(), vendorResponse.getVendorStatus());
        
        return vendorResponse;
    }

    /**
     * Make the actual vendor API call
     * 
     * @param vendorRequest The vendor request payload
     * @return Vendor response DTO
     */
    private VendorResponseDto makeVendorApiCall(VendorRequestDto vendorRequest) {
        // Prepare headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("X-Trace-Id", UUID.randomUUID().toString());
        

        
        // Create request entity
        HttpEntity<VendorRequestDto> requestEntity = new HttpEntity<>(vendorRequest, headers);
        
        // Make the API call
        ResponseEntity<VendorResponseDto> responseEntity = restTemplate.exchange(
            vendorBaseUrl, HttpMethod.POST, requestEntity, VendorResponseDto.class);
        
        if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
            return responseEntity.getBody();
        } else {
            throw new RuntimeException("Invalid response from vendor API");
        }
    }

    /**
     * Process successful vendor response
     */
    private VendorResponse processSuccessfulResponse(DedupeRequest request, String vendorName, 
                                                   VendorResponseDto apiResponse, int retryAttempt) {
        VendorResponse vendorResponse = new VendorResponse(request, vendorName, 
            mapVendorStatus(apiResponse.getDedupeStatus()));
        
        vendorResponse.setVendorRequestId(apiResponse.getRequestId());
        vendorResponse.setVendorResponseId(apiResponse.getRequestId());
        vendorResponse.setRetryAttempt(retryAttempt);
        vendorResponse.setRequestSentAt(LocalDateTime.now().minusSeconds(2)); // Approximate
        vendorResponse.markResponseReceived();
        
        try {
            // Store raw response with PII masking
            String rawResponse = objectMapper.writeValueAsString(apiResponse);
            vendorResponse.setRawResponse(rawResponse); // Store original for processing, mask in logs
        } catch (JsonProcessingException e) {
            logger.warn("Failed to serialize vendor response: {}", e.getMessage());
            vendorResponse.setRawResponse("SERIALIZATION_ERROR");
        }
        
        return vendorResponse;
    }

    /**
     * Process client error (4xx)
     */
    private VendorResponse processClientError(DedupeRequest request, String vendorName, 
                                            HttpClientErrorException e, int retryAttempt) {
        VendorResponse vendorResponse = new VendorResponse(request, vendorName, VendorStatus.ERROR);
        vendorResponse.setRetryAttempt(retryAttempt);
        vendorResponse.setErrorCode("CLIENT_ERROR_" + e.getStatusCode().value());
        vendorResponse.setErrorMessage(e.getMessage());
        vendorResponse.setRequestSentAt(LocalDateTime.now().minusSeconds(1));
        vendorResponse.markResponseReceived();
        
        return vendorResponse;
    }

    /**
     * Process server error (5xx)
     */
    private VendorResponse processServerError(DedupeRequest request, String vendorName, 
                                            HttpServerErrorException e, int retryAttempt) {
        VendorResponse vendorResponse = new VendorResponse(request, vendorName, VendorStatus.VENDOR_SERVICE_FAILURE);
        vendorResponse.setRetryAttempt(retryAttempt);
        vendorResponse.setErrorCode("SERVER_ERROR_" + e.getStatusCode().value());
        vendorResponse.setErrorMessage(e.getMessage());
        vendorResponse.setRequestSentAt(LocalDateTime.now().minusSeconds(1));
        vendorResponse.markResponseReceived();
        
        return vendorResponse;
    }

    /**
     * Process timeout error
     */
    private VendorResponse processTimeoutError(DedupeRequest request, String vendorName, 
                                             ResourceAccessException e, int retryAttempt) {
        VendorResponse vendorResponse = new VendorResponse(request, vendorName, VendorStatus.TIMEOUT);
        vendorResponse.setRetryAttempt(retryAttempt);
        vendorResponse.setErrorCode("TIMEOUT");
        vendorResponse.setErrorMessage(e.getMessage());
        vendorResponse.setRequestSentAt(LocalDateTime.now().minusSeconds(vendorTimeout / 1000));
        vendorResponse.markResponseReceived();
        
        return vendorResponse;
    }

    /**
     * Process unexpected error
     */
    private VendorResponse processUnexpectedError(DedupeRequest request, String vendorName, 
                                                Exception e, int retryAttempt) {
        VendorResponse vendorResponse = new VendorResponse(request, vendorName, VendorStatus.ERROR);
        vendorResponse.setRetryAttempt(retryAttempt);
        vendorResponse.setErrorCode("UNEXPECTED_ERROR");
        vendorResponse.setErrorMessage(e.getMessage());
        vendorResponse.setRequestSentAt(LocalDateTime.now().minusSeconds(1));
        vendorResponse.markResponseReceived();
        
        return vendorResponse;
    }

    /**
     * Map vendor status string to enum
     */
    private VendorStatus mapVendorStatus(String vendorStatus) {
        if (vendorStatus == null) {
            return VendorStatus.ERROR;
        }
        
        return switch (vendorStatus.toUpperCase()) {
            case "MATCH_FOUND" -> VendorStatus.MATCH_FOUND;
            case "NO_MATCH_FOUND" -> VendorStatus.NO_MATCH_FOUND;
            case "ERROR" -> VendorStatus.ERROR;
            case "TIMEOUT" -> VendorStatus.TIMEOUT;
            default -> VendorStatus.ERROR;
        };
    }

    /**
     * Wait before retry with exponential backoff
     */
    private void waitBeforeRetry(long delay) {
        try {
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Retry delay interrupted");
        }
    }

    /**
     * Transform internal request to vendor-specific format
     * 
     * @param request The internal dedupe request
     * @return Vendor-specific request DTO
     */
    public VendorRequestDto transformToVendorRequest(DedupeRequest request) {
        VendorRequestDto vendorRequest = new VendorRequestDto();
        vendorRequest.setCustomerIdentifier(request.getCustomerIdentifier());
        
        VendorRequestDto.VendorCustomerDataDto customerData = new VendorRequestDto.VendorCustomerDataDto();
        customerData.setFullName(request.getFullName());
        customerData.setDateOfBirth(request.getDateOfBirth());
        customerData.setPan(request.getPan());
        customerData.setAadhaar(request.getAadhaar());
        customerData.setMobileNumber(request.getMobileNumber());
        customerData.setEmailAddress(request.getEmailAddress());
        
        // Map address if available
        if (request.getAddressLine1() != null || request.getAddressCity() != null) {
            VendorRequestDto.VendorAddressDto address = new VendorRequestDto.VendorAddressDto();
            address.setLine1(request.getAddressLine1());
            address.setCity(request.getAddressCity());
            address.setState(request.getAddressState());
            address.setPincode(request.getAddressPincode());
            customerData.setAddress(address);
        }
        
        vendorRequest.setCustomerData(customerData);
        
        return vendorRequest;
    }
}
