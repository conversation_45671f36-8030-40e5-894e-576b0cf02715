package com.banking.dedupe.repository;

import com.banking.dedupe.entity.DedupeRequest;
import com.banking.dedupe.enums.RequestStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for DedupeRequest entity
 * 
 * Provides data access methods for customer deduplication requests
 * including queries for status tracking and audit purposes.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Repository
public interface DedupeRequestRepository extends JpaRepository<DedupeRequest, Long> {

    /**
     * Find a deduplication request by its reference number
     * 
     * @param referenceNumber The unique reference number
     * @return Optional containing the request if found
     */
    Optional<DedupeRequest> findByReferenceNumber(String referenceNumber);

    /**
     * Find deduplication requests by customer identifier
     * 
     * @param customerIdentifier The customer identifier
     * @return List of requests for the customer
     */
    List<DedupeRequest> findByCustomerIdentifier(String customerIdentifier);

    /**
     * Find deduplication requests by PAN
     * 
     * @param pan The PAN number
     * @return List of requests with the same PAN
     */
    List<DedupeRequest> findByPan(String pan);

    /**
     * Find deduplication requests by mobile number
     * 
     * @param mobileNumber The mobile number
     * @return List of requests with the same mobile number
     */
    List<DedupeRequest> findByMobileNumber(String mobileNumber);

    /**
     * Find deduplication requests by status
     * 
     * @param status The request status
     * @return List of requests with the specified status
     */
    List<DedupeRequest> findByRequestStatus(RequestStatus status);

    /**
     * Find deduplication requests by upstream application
     * 
     * @param upstreamApplication The upstream application name
     * @return List of requests from the specified application
     */
    List<DedupeRequest> findByUpstreamApplication(String upstreamApplication);

    /**
     * Find deduplication requests created within a date range
     * 
     * @param startDate The start date
     * @param endDate The end date
     * @return List of requests created within the date range
     */
    List<DedupeRequest> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find pending requests older than specified time
     * 
     * @param cutoffTime The cutoff time
     * @return List of pending requests older than cutoff time
     */
    @Query("SELECT dr FROM DedupeRequest dr WHERE dr.requestStatus IN :statuses AND dr.createdAt < :cutoffTime")
    List<DedupeRequest> findPendingRequestsOlderThan(@Param("statuses") List<RequestStatus> statuses, 
                                                     @Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * Count requests by status
     * 
     * @param status The request status
     * @return Count of requests with the specified status
     */
    long countByRequestStatus(RequestStatus status);

    /**
     * Count requests created today
     * 
     * @param startOfDay The start of the current day
     * @return Count of requests created today
     */
    @Query("SELECT COUNT(dr) FROM DedupeRequest dr WHERE dr.createdAt >= :startOfDay")
    long countRequestsCreatedToday(@Param("startOfDay") LocalDateTime startOfDay);

    /**
     * Find requests for data retention cleanup
     * 
     * @param retentionCutoff The retention cutoff date
     * @return List of requests older than retention period
     */
    @Query("SELECT dr FROM DedupeRequest dr WHERE dr.createdAt < :retentionCutoff AND dr.requestStatus IN ('COMPLETED', 'FAILED')")
    List<DedupeRequest> findRequestsForRetentionCleanup(@Param("retentionCutoff") LocalDateTime retentionCutoff);

    /**
     * Check if a customer has any recent requests
     * 
     * @param customerIdentifier The customer identifier
     * @param since The time threshold
     * @return true if customer has recent requests
     */
    @Query("SELECT CASE WHEN COUNT(dr) > 0 THEN true ELSE false END FROM DedupeRequest dr " +
           "WHERE dr.customerIdentifier = :customerIdentifier AND dr.createdAt >= :since")
    boolean hasRecentRequests(@Param("customerIdentifier") String customerIdentifier, 
                             @Param("since") LocalDateTime since);

    /**
     * Find duplicate requests based on customer data
     * 
     * @param pan The PAN number
     * @param mobileNumber The mobile number
     * @param excludeId The ID to exclude from results
     * @return List of potential duplicate requests
     */
    @Query("SELECT dr FROM DedupeRequest dr WHERE (dr.pan = :pan OR dr.mobileNumber = :mobileNumber) " +
           "AND dr.id != :excludeId ORDER BY dr.createdAt DESC")
    List<DedupeRequest> findPotentialDuplicates(@Param("pan") String pan, 
                                               @Param("mobileNumber") String mobileNumber,
                                               @Param("excludeId") Long excludeId);

    /**
     * Get request statistics for a date range
     * 
     * @param startDate The start date
     * @param endDate The end date
     * @return List of request statistics
     */
    @Query("SELECT dr.requestStatus, COUNT(dr) FROM DedupeRequest dr " +
           "WHERE dr.createdAt BETWEEN :startDate AND :endDate " +
           "GROUP BY dr.requestStatus")
    List<Object[]> getRequestStatistics(@Param("startDate") LocalDateTime startDate, 
                                       @Param("endDate") LocalDateTime endDate);
}
