package com.banking.dedupe.dto;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Data Transfer Object for Vendor API Response
 * 
 * Represents the response received from third-party deduplication vendors.
 * This DTO captures the raw vendor response for processing and interpretation.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class VendorResponseDto {

    @JsonProperty("requestId")
    private String requestId;

    @JsonProperty("customerIdentifier")
    private String customerIdentifier;

    @JsonProperty("dedupeStatus")
    private String dedupeStatus;

    @JsonProperty("message")
    private String message;

    @JsonProperty("duplicates")
    private List<VendorDuplicateDto> duplicates;

    @JsonProperty("vendorDetails")
    private VendorDetailsDto vendorDetails;

    @JsonProperty("auditInfo")
    private VendorAuditInfoDto auditInfo;

    @JsonProperty("errorCode")
    private String errorCode;

    @JsonProperty("errorMessage")
    private String errorMessage;

    /**
     * Default constructor
     */
    public VendorResponseDto() {
    }

    /**
     * Constructor with all fields
     */
    public VendorResponseDto(String requestId, String customerIdentifier, String dedupeStatus, 
                           String message, List<VendorDuplicateDto> duplicates, 
                           VendorDetailsDto vendorDetails, VendorAuditInfoDto auditInfo,
                           String errorCode, String errorMessage) {
        this.requestId = requestId;
        this.customerIdentifier = customerIdentifier;
        this.dedupeStatus = dedupeStatus;
        this.message = message;
        this.duplicates = duplicates;
        this.vendorDetails = vendorDetails;
        this.auditInfo = auditInfo;
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    // Getters and Setters

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getCustomerIdentifier() {
        return customerIdentifier;
    }

    public void setCustomerIdentifier(String customerIdentifier) {
        this.customerIdentifier = customerIdentifier;
    }

    public String getDedupeStatus() {
        return dedupeStatus;
    }

    public void setDedupeStatus(String dedupeStatus) {
        this.dedupeStatus = dedupeStatus;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<VendorDuplicateDto> getDuplicates() {
        return duplicates;
    }

    public void setDuplicates(List<VendorDuplicateDto> duplicates) {
        this.duplicates = duplicates;
    }

    public VendorDetailsDto getVendorDetails() {
        return vendorDetails;
    }

    public void setVendorDetails(VendorDetailsDto vendorDetails) {
        this.vendorDetails = vendorDetails;
    }

    public VendorAuditInfoDto getAuditInfo() {
        return auditInfo;
    }

    public void setAuditInfo(VendorAuditInfoDto auditInfo) {
        this.auditInfo = auditInfo;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * Nested DTO for Vendor Duplicate Information
     */
    public static class VendorDuplicateDto {

        @JsonProperty("customerId")
        private String customerId;

        @JsonProperty("matchScore")
        private Integer matchScore;

        @JsonProperty("matchedFields")
        private List<String> matchedFields;

        @JsonProperty("customerStatus")
        private String customerStatus;

        @JsonProperty("createdDate")
        private LocalDateTime createdDate;

        /**
         * Default constructor
         */
        public VendorDuplicateDto() {
        }

        /**
         * Constructor with all fields
         */
        public VendorDuplicateDto(String customerId, Integer matchScore, List<String> matchedFields,
                                String customerStatus, LocalDateTime createdDate) {
            this.customerId = customerId;
            this.matchScore = matchScore;
            this.matchedFields = matchedFields;
            this.customerStatus = customerStatus;
            this.createdDate = createdDate;
        }

        // Getters and Setters

        public String getCustomerId() {
            return customerId;
        }

        public void setCustomerId(String customerId) {
            this.customerId = customerId;
        }

        public Integer getMatchScore() {
            return matchScore;
        }

        public void setMatchScore(Integer matchScore) {
            this.matchScore = matchScore;
        }

        public List<String> getMatchedFields() {
            return matchedFields;
        }

        public void setMatchedFields(List<String> matchedFields) {
            this.matchedFields = matchedFields;
        }

        public String getCustomerStatus() {
            return customerStatus;
        }

        public void setCustomerStatus(String customerStatus) {
            this.customerStatus = customerStatus;
        }

        public LocalDateTime getCreatedDate() {
            return createdDate;
        }

        public void setCreatedDate(LocalDateTime createdDate) {
            this.createdDate = createdDate;
        }
    }

    /**
     * Nested DTO for Vendor Details
     */
    public static class VendorDetailsDto {

        @JsonProperty("vendorName")
        private String vendorName;



        @JsonProperty("processingTimestamp")
        private LocalDateTime processingTimestamp;

        /**
         * Default constructor
         */
        public VendorDetailsDto() {
        }

        /**
         * Constructor with all fields
         */
        public VendorDetailsDto(String vendorName, LocalDateTime processingTimestamp) {
            this.vendorName = vendorName;
            this.processingTimestamp = processingTimestamp;
        }

        // Getters and Setters

        public String getVendorName() {
            return vendorName;
        }

        public void setVendorName(String vendorName) {
            this.vendorName = vendorName;
        }



        public LocalDateTime getProcessingTimestamp() {
            return processingTimestamp;
        }

        public void setProcessingTimestamp(LocalDateTime processingTimestamp) {
            this.processingTimestamp = processingTimestamp;
        }
    }

    /**
     * Nested DTO for Vendor Audit Information
     */
    public static class VendorAuditInfoDto {

        @JsonProperty("requestTimestamp")
        private LocalDateTime requestTimestamp;

        @JsonProperty("responseTimestamp")
        private LocalDateTime responseTimestamp;

        /**
         * Default constructor
         */
        public VendorAuditInfoDto() {
        }

        /**
         * Constructor with all fields
         */
        public VendorAuditInfoDto(LocalDateTime requestTimestamp, LocalDateTime responseTimestamp) {
            this.requestTimestamp = requestTimestamp;
            this.responseTimestamp = responseTimestamp;
        }

        // Getters and Setters

        public LocalDateTime getRequestTimestamp() {
            return requestTimestamp;
        }

        public void setRequestTimestamp(LocalDateTime requestTimestamp) {
            this.requestTimestamp = requestTimestamp;
        }

        public LocalDateTime getResponseTimestamp() {
            return responseTimestamp;
        }

        public void setResponseTimestamp(LocalDateTime responseTimestamp) {
            this.responseTimestamp = responseTimestamp;
        }
    }
}
