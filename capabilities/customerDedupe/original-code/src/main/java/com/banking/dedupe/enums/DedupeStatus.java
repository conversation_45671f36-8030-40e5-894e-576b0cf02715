package com.banking.dedupe.enums;

/**
 * Enumeration for Customer Deduplication Status
 * 
 * Represents the overall status of a deduplication request throughout its lifecycle.
 * Used for tracking the progress of deduplication requests from initiation to completion.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public enum DedupeStatus {
    
    /**
     * Request has been received but processing has not started
     */
    PENDING("Request received and queued for processing"),
    
    /**
     * Request is currently being processed
     */
    IN_PROGRESS("Request is being processed"),
    
    /**
     * Request has been successfully completed
     */
    COMPLETED("Request processing completed successfully"),
    
    /**
     * Request processing failed due to an error
     */
    FAILED("Request processing failed"),
    
    /**
     * Request processing timed out
     */
    TIMEOUT("Request processing timed out");

    private final String description;

    /**
     * Constructor for DedupeStatus enum
     * 
     * @param description Human-readable description of the status
     */
    DedupeStatus(String description) {
        this.description = description;
    }

    /**
     * Get the description of the status
     * 
     * @return String description of the status
     */
    public String getDescription() {
        return description;
    }

    /**
     * Check if the status indicates completion (either success or failure)
     * 
     * @return true if the status is terminal (COMPLETED, FAILED, TIMEOUT)
     */
    public boolean isTerminal() {
        return this == COMPLETED || this == FAILED || this == TIMEOUT;
    }

    /**
     * Check if the status indicates successful completion
     * 
     * @return true if the status is COMPLETED
     */
    public boolean isSuccessful() {
        return this == COMPLETED;
    }

    /**
     * Check if the status indicates an error state
     * 
     * @return true if the status is FAILED or TIMEOUT
     */
    public boolean isError() {
        return this == FAILED || this == TIMEOUT;
    }
}
