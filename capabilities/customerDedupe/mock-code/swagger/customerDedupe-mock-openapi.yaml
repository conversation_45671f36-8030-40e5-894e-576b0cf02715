openapi: 3.0.3
info:
  title: Mock Customer Deduplication Service API
  description: |
    Mock service for simulating third-party customer deduplication vendors (<PERSON><PERSON><PERSON>, Perfios, Signzy) 
    for development and testing environments.
    
    This service helps validate application behavior with various deduplication scenarios without 
    requiring access to real vendor services. It provides realistic API structure, behavior, 
    and timing simulation.
    
    ## Key Features
    - Simulates realistic vendor response patterns
    - Configurable match rates and response times
    - Support for error scenarios and timeouts
    - PII masking for audit compliance
    - Request/response history tracking
    
  version: 1.0.0
  contact:
    name: Banking Technology Team
    email: <EMAIL>
    url: https://banking.com/contact
  license:
    name: Proprietary
    url: https://banking.com/license

servers:
  - url: http://localhost:8222/api/customer-dedupe/internal
    description: Local Development Server
  - url: https://mock-api.banking.com/customer-dedupe/internal
    description: Development Mock Server
  - url: https://staging-mock-api.banking.com/customer-dedupe/internal
    description: Staging Mock Server

paths:
  /customer/dedupe:
    post:
      tags:
        - Mock Deduplication
      summary: Mock customer deduplication request
      description: |
        Simulates third-party vendor customer deduplication processing.
        
        The mock service will:
        1. Validate the incoming customer data
        2. Simulate vendor processing with configurable delays
        3. Return realistic deduplication results based on configuration
        4. Log requests for testing and debugging
        
        Response behavior is configurable through the admin endpoints.
        
      operationId: mockDedupeRequest
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MockDedupeRequest'
            examples:
              validRequest:
                summary: Valid mock deduplication request
                value:
                  customerIdentifier: "CUST123456"
                  customerData:
                    fullName: "Rahul Sharma"
                    dateOfBirth: "1990-01-15"
                    pan: "**********"
                    aadhaar: "1234-5678-9012"
                    mobileNumber: "9876543210"
                    emailAddress: "<EMAIL>"
                    address:
                      line1: "123 MG Road"
                      city: "Bangalore"
                      state: "Karnataka"
                      pincode: "560001"
      responses:
        '200':
          description: Mock deduplication completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockDedupeResponse'
              examples:
                matchFound:
                  summary: Mock duplicate customer found
                  value:
                    requestId: "MOCK_REQ_123456"
                    customerIdentifier: "CUST123456"
                    dedupeStatus: "MATCH_FOUND"
                    message: "Duplicate customer records found (simulated)"
                    duplicates:
                      - customerId: "MOCK_CUST_001"
                        matchScore: 92
                        matchedFields: ["pan", "mobileNumber"]
                        customerStatus: "ACTIVE"
                        createdDate: "2024-01-15T10:30:00Z"
                    vendorDetails:
                      vendorName: "Mock Dedupe Service"
                      processingTimestamp: "2025-06-27T10:15:30Z"
                    auditInfo:
                      requestTimestamp: "2025-06-27T10:15:25Z"
                      responseTimestamp: "2025-06-27T10:15:30Z"
                      processingTimeMs: 1500
                noMatch:
                  summary: Mock no duplicate found
                  value:
                    requestId: "MOCK_REQ_123457"
                    customerIdentifier: "CUST123457"
                    dedupeStatus: "NO_MATCH_FOUND"
                    message: "No duplicate customer records found (simulated)"
                    duplicates: []
                    vendorDetails:
                      vendorName: "Mock Dedupe Service"
                      processingTimestamp: "2025-06-27T10:15:30Z"
                    auditInfo:
                      requestTimestamp: "2025-06-27T10:15:25Z"
                      responseTimestamp: "2025-06-27T10:15:30Z"
                      processingTimeMs: 800
        '400':
          description: Bad request - Invalid input data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockErrorResponse'
              examples:
                validationError:
                  summary: Mock validation error
                  value:
                    requestId: "MOCK_REQ_123458"
                    customerIdentifier: "CUST123458"
                    dedupeStatus: "ERROR"
                    message: "Invalid customer data format (simulated)"
                    errorCode: "VALIDATION_ERROR"
                    errorMessage: "PAN format is invalid"
                    vendorDetails:
                      vendorName: "Mock Dedupe Service"
                      processingTimestamp: "2025-06-27T10:15:30Z"
                    auditInfo:
                      requestTimestamp: "2025-06-27T10:15:25Z"
                      responseTimestamp: "2025-06-27T10:15:30Z"
                      processingTimeMs: 100
        '500':
          description: Internal server error (simulated)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockErrorResponse'
              examples:
                systemError:
                  summary: Mock system error
                  value:
                    requestId: "MOCK_REQ_123459"
                    customerIdentifier: "CUST123459"
                    dedupeStatus: "ERROR"
                    message: "Mock vendor system error (simulated)"
                    errorCode: "SYSTEM_ERROR"
                    errorMessage: "Database connection failed"
                    vendorDetails:
                      vendorName: "Mock Dedupe Service"
                      processingTimestamp: "2025-06-27T10:15:30Z"
                    auditInfo:
                      requestTimestamp: "2025-06-27T10:15:25Z"
                      responseTimestamp: "2025-06-27T10:15:30Z"
                      processingTimeMs: 500
        '504':
          description: Gateway timeout (simulated)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockErrorResponse'
              examples:
                timeoutError:
                  summary: Mock timeout error
                  value:
                    requestId: "MOCK_REQ_123460"
                    customerIdentifier: "CUST123460"
                    dedupeStatus: "TIMEOUT"
                    message: "Mock vendor service timeout (simulated)"
                    errorCode: "TIMEOUT_ERROR"
                    errorMessage: "Request processing timed out"
                    vendorDetails:
                      vendorName: "Mock Dedupe Service"
                      processingTimestamp: "2025-06-27T10:15:30Z"
                    auditInfo:
                      requestTimestamp: "2025-06-27T10:15:25Z"
                      responseTimestamp: "2025-06-27T10:15:30Z"
                      processingTimeMs: 30000

  /customer/dedupe/health:
    get:
      tags:
        - Health Check
      summary: Mock service health check
      description: |
        Returns the health status of the Mock Customer Deduplication Service.
        Used for monitoring and testing connectivity.
      operationId: mockHealthCheck
      responses:
        '200':
          description: Mock service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockHealthResponse'
              examples:
                healthy:
                  summary: Healthy mock service
                  value:
                    status: "UP"
                    vendor: "Mock Dedupe Service"
                    timestamp: "2025-06-27T10:15:30Z"
                    version: "1.0.0"

  /v1/customer-dedupe/mock/health:
    get:
      tags:
        - Health Check
      summary: Alternative health check endpoint
      description: |
        Alternative health check endpoint following the standard pattern.
        Returns the health status of the Mock Customer Deduplication Service.
      operationId: alternativeHealthCheck
      responses:
        '200':
          description: Mock service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StandardHealthResponse'
              examples:
                healthy:
                  summary: Healthy service (standard format)
                  value:
                    status: "UP"
                    service: "Customer Dedupe Mock"
                    timestamp: "2025-06-27T10:15:30Z"
                    version: "1.0.0"

components:
  schemas:
    MockDedupeRequest:
      type: object
      required:
        - customerIdentifier
        - customerData
      properties:
        customerIdentifier:
          type: string
          description: Unique identifier for the customer
          example: "CUST123456"
        customerData:
          $ref: '#/components/schemas/CustomerData'

    CustomerData:
      type: object
      required:
        - fullName
        - dateOfBirth
        - pan
        - mobileNumber
      properties:
        fullName:
          type: string
          description: Full name of the customer
          example: "Rahul Sharma"
        dateOfBirth:
          type: string
          format: date
          description: Date of birth in YYYY-MM-DD format
          example: "1990-01-15"
        pan:
          type: string
          description: PAN number
          example: "**********"
        aadhaar:
          type: string
          description: Aadhaar number (masked for privacy)
          example: "XXXX-XXXX-9012"
        mobileNumber:
          type: string
          description: Mobile number
          example: "9876543210"
        emailAddress:
          type: string
          format: email
          description: Email address
          example: "<EMAIL>"
        address:
          $ref: '#/components/schemas/Address'

    Address:
      type: object
      properties:
        line1:
          type: string
          description: Address line 1
          example: "123 MG Road"
        city:
          type: string
          description: City name
          example: "Bangalore"
        state:
          type: string
          description: State name
          example: "Karnataka"
        pincode:
          type: string
          description: 6-digit pincode
          example: "560001"

    MockDedupeResponse:
      type: object
      required:
        - requestId
        - customerIdentifier
        - dedupeStatus
        - message
      properties:
        requestId:
          type: string
          description: Unique request ID generated by mock service
          example: "MOCK_REQ_123456"
        customerIdentifier:
          type: string
          description: Customer identifier from the request
          example: "CUST123456"
        dedupeStatus:
          type: string
          enum:
            - MATCH_FOUND
            - NO_MATCH_FOUND
            - ERROR
            - TIMEOUT
          description: Status of the mock deduplication process
          example: "MATCH_FOUND"
        message:
          type: string
          description: Human-readable message describing the result
          example: "Duplicate customer records found (simulated)"
        duplicates:
          type: array
          items:
            $ref: '#/components/schemas/MockDuplicateCustomer'
          description: List of mock duplicate customer records
        vendorDetails:
          $ref: '#/components/schemas/MockVendorDetails'
        auditInfo:
          $ref: '#/components/schemas/MockAuditInfo'

    MockDuplicateCustomer:
      type: object
      properties:
        customerId:
          type: string
          description: Mock ID of the duplicate customer
          example: "MOCK_CUST_001"
        matchScore:
          type: integer
          minimum: 0
          maximum: 100
          description: Simulated match score percentage (0-100)
          example: 92
        matchedFields:
          type: array
          items:
            type: string
          description: List of fields that matched (simulated)
          example: ["pan", "mobileNumber"]
        customerStatus:
          type: string
          description: Simulated status of the duplicate customer
          example: "ACTIVE"
        createdDate:
          type: string
          format: date-time
          description: Simulated date when the duplicate customer was created
          example: "2024-01-15T10:30:00Z"

    MockVendorDetails:
      type: object
      properties:
        vendorName:
          type: string
          description: Name of the mock vendor service
          example: "Mock Dedupe Service"
        processingTimestamp:
          type: string
          format: date-time
          description: Timestamp when mock processing completed
          example: "2025-06-27T10:15:30Z"

    MockAuditInfo:
      type: object
      properties:
        requestTimestamp:
          type: string
          format: date-time
          description: Timestamp when request was received
          example: "2025-06-27T10:15:25Z"
        responseTimestamp:
          type: string
          format: date-time
          description: Timestamp when response was sent
          example: "2025-06-27T10:15:30Z"
        processingTimeMs:
          type: integer
          description: Simulated processing time in milliseconds
          example: 1500

    MockErrorResponse:
      type: object
      required:
        - requestId
        - customerIdentifier
        - dedupeStatus
        - message
      properties:
        requestId:
          type: string
          description: Unique request ID generated by mock service
          example: "MOCK_REQ_123458"
        customerIdentifier:
          type: string
          description: Customer identifier from the request
          example: "CUST123458"
        dedupeStatus:
          type: string
          enum:
            - ERROR
            - TIMEOUT
          description: Error status of the mock deduplication process
          example: "ERROR"
        message:
          type: string
          description: Human-readable error message
          example: "Invalid customer data format (simulated)"
        errorCode:
          type: string
          description: Mock error code
          example: "VALIDATION_ERROR"
        errorMessage:
          type: string
          description: Detailed error message
          example: "PAN format is invalid"
        vendorDetails:
          $ref: '#/components/schemas/MockVendorDetails'
        auditInfo:
          $ref: '#/components/schemas/MockAuditInfo'

    MockHealthResponse:
      type: object
      required:
        - status
        - vendor
        - timestamp
        - version
      properties:
        status:
          type: string
          description: Health status of the mock service
          example: "UP"
        vendor:
          type: string
          description: Name of the mock vendor
          example: "Mock Dedupe Service"
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the health check
          example: "2025-06-27T10:15:30Z"
        version:
          type: string
          description: Version of the mock service
          example: "1.0.0"

    StandardHealthResponse:
      type: object
      required:
        - status
        - service
        - timestamp
        - version
      properties:
        status:
          type: string
          description: Health status of the service
          example: "UP"
        service:
          type: string
          description: Name of the service
          example: "Customer Dedupe Mock"
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the health check
          example: "2025-06-27T10:15:30Z"
        version:
          type: string
          description: Version of the service
          example: "1.0.0"
