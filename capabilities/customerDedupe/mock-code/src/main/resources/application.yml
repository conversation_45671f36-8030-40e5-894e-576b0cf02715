server:
  port: 8222
  servlet:
    context-path: /api/customer-dedupe/internal

spring:
  application:
    name: mock-dedupe-service
  
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
    default-property-inclusion: non_null



# Mock Service Configuration
mock:
  vendor:
    name: "Mock Dedupe Service"
    version: "1.0.0"
  
  simulation:
    latency:
      min-ms: 500
      max-ms: 2000
      timeout-ms: 30000
    
    response-rates:
      match-found-percentage: 30
      no-match-percentage: 65
      error-percentage: 5
    
    match-scores:
      high-min: 90
      high-max: 98
      medium-min: 70
      medium-max: 89
      low-min: 50
      low-max: 69

# Audit Configuration
audit:
  retention:
    days: 30
  pii-masking:
    enabled: true
    pan-mask-pattern: "XX****XXXX"
    aadhaar-mask-pattern: "XXXX-XXXX-****"
    mobile-mask-pattern: "****XX****"

# Logging Configuration
logging:
  level:
    com.banking.mock: INFO
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: logs/mock-dedupe-service.log
    max-size: 100MB
    max-history: 30

# OpenAPI Configuration
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
    tagsSorter: alpha
  show-actuator: true

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true



# Rate Limiting
rate-limiting:
  enabled: true
  requests-per-minute: 100
  burst-capacity: 20

# Admin Configuration
admin:
  enabled: true
  max-request-history: 1000
  cleanup-interval-hours: 24
