package com.banking.mock.dto;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Mock Customer Deduplication Response DTO
 * 
 * Represents the response format returned by the mock vendor API.
 * Mirrors the structure returned by real third-party vendors.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class MockDedupeResponseDto {

    @JsonProperty("requestId")
    private String requestId;

    @JsonProperty("customerIdentifier")
    private String customerIdentifier;

    @JsonProperty("dedupeStatus")
    private String dedupeStatus;

    @JsonProperty("message")
    private String message;

    @JsonProperty("duplicates")
    private List<DuplicateCustomerDto> duplicates;

    @JsonProperty("vendorDetails")
    private VendorDetailsDto vendorDetails;

    @JsonProperty("auditInfo")
    private AuditInfoDto auditInfo;

    @JsonProperty("errorCode")
    private String errorCode;

    @JsonProperty("errorMessage")
    private String errorMessage;

    /**
     * Default constructor
     */
    public MockDedupeResponseDto() {
    }

    // Getters and Setters

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getCustomerIdentifier() {
        return customerIdentifier;
    }

    public void setCustomerIdentifier(String customerIdentifier) {
        this.customerIdentifier = customerIdentifier;
    }

    public String getDedupeStatus() {
        return dedupeStatus;
    }

    public void setDedupeStatus(String dedupeStatus) {
        this.dedupeStatus = dedupeStatus;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<DuplicateCustomerDto> getDuplicates() {
        return duplicates;
    }

    public void setDuplicates(List<DuplicateCustomerDto> duplicates) {
        this.duplicates = duplicates;
    }

    public VendorDetailsDto getVendorDetails() {
        return vendorDetails;
    }

    public void setVendorDetails(VendorDetailsDto vendorDetails) {
        this.vendorDetails = vendorDetails;
    }

    public AuditInfoDto getAuditInfo() {
        return auditInfo;
    }

    public void setAuditInfo(AuditInfoDto auditInfo) {
        this.auditInfo = auditInfo;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * Nested DTO for Duplicate Customer Information
     */
    public static class DuplicateCustomerDto {

        @JsonProperty("customerId")
        private String customerId;

        @JsonProperty("matchScore")
        private Integer matchScore;

        @JsonProperty("matchedFields")
        private List<String> matchedFields;

        @JsonProperty("customerStatus")
        private String customerStatus;

        @JsonProperty("createdDate")
        private LocalDateTime createdDate;

        /**
         * Default constructor
         */
        public DuplicateCustomerDto() {
        }

        // Getters and Setters

        public String getCustomerId() {
            return customerId;
        }

        public void setCustomerId(String customerId) {
            this.customerId = customerId;
        }

        public Integer getMatchScore() {
            return matchScore;
        }

        public void setMatchScore(Integer matchScore) {
            this.matchScore = matchScore;
        }

        public List<String> getMatchedFields() {
            return matchedFields;
        }

        public void setMatchedFields(List<String> matchedFields) {
            this.matchedFields = matchedFields;
        }

        public String getCustomerStatus() {
            return customerStatus;
        }

        public void setCustomerStatus(String customerStatus) {
            this.customerStatus = customerStatus;
        }

        public LocalDateTime getCreatedDate() {
            return createdDate;
        }

        public void setCreatedDate(LocalDateTime createdDate) {
            this.createdDate = createdDate;
        }
    }

    /**
     * Nested DTO for Vendor Details
     */
    public static class VendorDetailsDto {

        @JsonProperty("vendorName")
        private String vendorName;



        @JsonProperty("processingTimestamp")
        private LocalDateTime processingTimestamp;

        /**
         * Default constructor
         */
        public VendorDetailsDto() {
        }

        // Getters and Setters

        public String getVendorName() {
            return vendorName;
        }

        public void setVendorName(String vendorName) {
            this.vendorName = vendorName;
        }



        public LocalDateTime getProcessingTimestamp() {
            return processingTimestamp;
        }

        public void setProcessingTimestamp(LocalDateTime processingTimestamp) {
            this.processingTimestamp = processingTimestamp;
        }
    }

    /**
     * Nested DTO for Audit Information
     */
    public static class AuditInfoDto {

        @JsonProperty("requestTimestamp")
        private LocalDateTime requestTimestamp;

        @JsonProperty("responseTimestamp")
        private LocalDateTime responseTimestamp;

        /**
         * Default constructor
         */
        public AuditInfoDto() {
        }

        // Getters and Setters

        public LocalDateTime getRequestTimestamp() {
            return requestTimestamp;
        }

        public void setRequestTimestamp(LocalDateTime requestTimestamp) {
            this.requestTimestamp = requestTimestamp;
        }

        public LocalDateTime getResponseTimestamp() {
            return responseTimestamp;
        }

        public void setResponseTimestamp(LocalDateTime responseTimestamp) {
            this.responseTimestamp = responseTimestamp;
        }
    }
}
