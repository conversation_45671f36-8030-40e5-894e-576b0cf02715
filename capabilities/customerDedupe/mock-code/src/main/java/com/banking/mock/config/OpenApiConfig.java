package com.banking.mock.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * OpenAPI Configuration for Mock Customer Deduplication Service
 * 
 * Configures OpenAPI 3.0 documentation and Swagger UI for the mock customer deduplication service.
 * Includes CORS configuration to ensure Swagger UI works properly.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-27
 */
@Configuration
public class OpenApiConfig implements WebMvcConfigurer {

    @Value("${server.port:8222}")
    private String serverPort;

    @Value("${server.servlet.context-path:/api/customer-dedupe/internal}")
    private String contextPath;

    /**
     * Configure OpenAPI documentation
     */
    @Bean
    public OpenAPI mockDedupeOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Mock Customer Deduplication Service API")
                        .description("Mock service for simulating third-party customer deduplication vendors " +
                                   "(Karza, Perfios, Signzy) for development and testing environments. " +
                                   "This service helps validate application behavior with various deduplication scenarios.")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Banking Technology Team")
                                .email("<EMAIL>")
                                .url("https://banking.com/contact"))
                        .license(new License()
                                .name("Proprietary")
                                .url("https://banking.com/license")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + contextPath)
                                .description("Local Development Server"),
                        new Server()
                                .url("https://mock-api.banking.com/customer-dedupe/internal")
                                .description("Development Mock Server"),
                        new Server()
                                .url("https://staging-mock-api.banking.com/customer-dedupe/internal")
                                .description("Staging Mock Server")
                ));
    }

    /**
     * Configure CORS to allow Swagger UI to function properly
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
}
