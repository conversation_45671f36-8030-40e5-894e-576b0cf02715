package com.banking.mock;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Mock Customer Deduplication Service Application
 * 
 * Main entry point for the Mock Customer Deduplication Service.
 * This service simulates third-party vendor APIs for development and testing.
 * 
 * Features:
 * - Realistic vendor API simulation
 * - Configurable response scenarios
 * - PII masking and audit trails
 * - Admin interface for testing
 * - Rate limiting
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@SpringBootApplication
@EnableConfigurationProperties
@EnableAsync
@EnableScheduling
public class MockDedupeServiceApplication {

    /**
     * Main method to start the Mock Deduplication Service
     * 
     * @param args Command line arguments
     */
    public static void main(String[] args) {
        SpringApplication.run(MockDedupeServiceApplication.class, args);
    }
}
