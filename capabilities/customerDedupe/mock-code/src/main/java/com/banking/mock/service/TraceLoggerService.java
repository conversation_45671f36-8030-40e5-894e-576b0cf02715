package com.banking.mock.service;

import org.slf4j.MDC;
import org.springframework.stereotype.Service;

/**
 * Service for managing trace IDs and correlation logging
 * 
 * Provides trace ID management for request correlation and audit trails
 * in the mock deduplication service.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Service
public class TraceLoggerService {

    private static final String TRACE_ID_KEY = "traceId";

    /**
     * Set trace ID in MDC for logging correlation
     * 
     * @param traceId The trace ID
     */
    public void setTraceId(String traceId) {
        MDC.put(TRACE_ID_KEY, traceId);
    }

    /**
     * Get current trace ID from MDC
     * 
     * @return Current trace ID or null if not set
     */
    public String getTraceId() {
        return MDC.get(TRACE_ID_KEY);
    }

    /**
     * Clear trace ID from MDC
     */
    public void clearTraceId() {
        MDC.remove(TRACE_ID_KEY);
    }

    /**
     * Check if trace ID is set
     * 
     * @return true if trace ID is set
     */
    public boolean hasTraceId() {
        return MDC.get(TRACE_ID_KEY) != null;
    }
}
