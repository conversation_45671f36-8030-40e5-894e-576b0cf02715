package com.banking.mock.controller;

import com.banking.mock.dto.MockDedupeRequestDto;
import com.banking.mock.dto.MockDedupeResponseDto;
import com.banking.mock.service.MockDedupeService;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Mock Customer Deduplication Controller
 * 
 * Provides mock vendor API endpoints for customer deduplication testing.
 * Simulates realistic vendor behavior with configurable scenarios.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@RestController
@RequestMapping("/customer/dedupe")
@Validated

public class MockDedupeController {

    private static final Logger logger = LoggerFactory.getLogger(MockDedupeController.class);

    private final MockDedupeService mockDedupeService;

    public MockDedupeController(MockDedupeService mockDedupeService) {
        this.mockDedupeService = mockDedupeService;
    }

    /**
     * Process customer deduplication request (mock vendor endpoint)
     * 
     * @param request The deduplication request
     * @param traceId The trace ID header
     * @param httpRequest The HTTP servlet request
     * @return Mock deduplication response
     */
    @PostMapping

    public ResponseEntity<MockDedupeResponseDto> processDedupeRequest(
            @Valid @RequestBody MockDedupeRequestDto request,
            @RequestHeader(value = "X-Trace-Id", required = false) String traceId,
            HttpServletRequest httpRequest) {

        // Set trace ID if provided
        if (traceId == null) {
            traceId = UUID.randomUUID().toString();
        }

        String clientIp = getClientIpAddress(httpRequest);
        
        logger.info("Mock vendor received deduplication request - TraceId: {} - Customer: {} - IP: {}", 
            traceId, request.getCustomerIdentifier(), clientIp);

        try {
            // Check for special test scenarios that should return errors
            HttpStatus errorStatus = checkForErrorScenarios(request);
            if (errorStatus != null) {
                MockDedupeResponseDto errorResponse = createErrorResponse(request, errorStatus, traceId);
                return ResponseEntity.status(errorStatus).body(errorResponse);
            }

            // Process normal request
            MockDedupeResponseDto response = mockDedupeService.processDedupeRequest(request);
            
            // Add trace ID to response
            response.setRequestId(traceId);

            logger.info("Mock vendor response generated - TraceId: {} - Status: {} - Duplicates: {}", 
                traceId, response.getDedupeStatus(), 
                response.getDuplicates() != null ? response.getDuplicates().size() : 0);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Mock vendor error processing request - TraceId: {}", traceId, e);
            
            MockDedupeResponseDto errorResponse = createSystemErrorResponse(request, traceId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Health check endpoint
     * 
     * @return Health status
     */
    @GetMapping("/health")

    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("vendor", "Mock Dedupe Service");
        health.put("timestamp", LocalDateTime.now());
        health.put("version", "1.0.0");

        return ResponseEntity.ok(health);
    }

    /**
     * Check for special error scenarios based on request data
     */
    private HttpStatus checkForErrorScenarios(MockDedupeRequestDto request) {
        String pan = request.getCustomerData().getPan();
        String fullName = request.getCustomerData().getFullName();

        // PAN-based error scenarios
        if (pan != null) {
            if (pan.startsWith("TEST")) {
                return HttpStatus.INTERNAL_SERVER_ERROR; // 500
            }
        }



        // Name-based error scenarios
        if (fullName != null) {
            if (fullName.toLowerCase().contains("error")) {
                return HttpStatus.BAD_REQUEST; // 400 - Validation error
            }
        }

        return null; // No error scenario
    }

    /**
     * Create error response for specific scenarios
     */
    private MockDedupeResponseDto createErrorResponse(MockDedupeRequestDto request, 
                                                     HttpStatus status, String traceId) {
        MockDedupeResponseDto errorResponse = new MockDedupeResponseDto();
        errorResponse.setRequestId(traceId);
        errorResponse.setCustomerIdentifier(request.getCustomerIdentifier());
        errorResponse.setDedupeStatus("ERROR");
        
        String errorCode;
        String errorMessage;
        
        switch (status) {
            case BAD_REQUEST:
                errorCode = "VALIDATION_ERROR";
                errorMessage = "Invalid customer data provided";
                break;
            case INTERNAL_SERVER_ERROR:
                errorCode = "VENDOR_SERVICE_FAILURE";
                errorMessage = "Internal service error occurred";
                break;
            default:
                errorCode = "UNKNOWN_ERROR";
                errorMessage = "Unknown error occurred";
        }
        
        errorResponse.setErrorCode(errorCode);
        errorResponse.setErrorMessage(errorMessage);
        errorResponse.setMessage(errorMessage);
        errorResponse.setDuplicates(java.util.Collections.emptyList());
        
        // Set vendor details
        MockDedupeResponseDto.VendorDetailsDto vendorDetails = new MockDedupeResponseDto.VendorDetailsDto();
        vendorDetails.setVendorName("Mock Dedupe Service");
        vendorDetails.setProcessingTimestamp(LocalDateTime.now());
        errorResponse.setVendorDetails(vendorDetails);
        
        // Set audit info
        MockDedupeResponseDto.AuditInfoDto auditInfo = new MockDedupeResponseDto.AuditInfoDto();
        auditInfo.setRequestTimestamp(LocalDateTime.now());
        auditInfo.setResponseTimestamp(LocalDateTime.now());
        errorResponse.setAuditInfo(auditInfo);
        
        return errorResponse;
    }

    /**
     * Create system error response
     */
    private MockDedupeResponseDto createSystemErrorResponse(MockDedupeRequestDto request, String traceId) {
        MockDedupeResponseDto errorResponse = new MockDedupeResponseDto();
        errorResponse.setRequestId(traceId);
        errorResponse.setCustomerIdentifier(request.getCustomerIdentifier());
        errorResponse.setDedupeStatus("ERROR");
        errorResponse.setErrorCode("SYSTEM_ERROR");
        errorResponse.setErrorMessage("System error occurred");
        errorResponse.setMessage("System error occurred");
        errorResponse.setDuplicates(java.util.Collections.emptyList());
        
        // Set vendor details
        MockDedupeResponseDto.VendorDetailsDto vendorDetails = new MockDedupeResponseDto.VendorDetailsDto();
        vendorDetails.setVendorName("Mock Dedupe Service");
        vendorDetails.setProcessingTimestamp(LocalDateTime.now());
        errorResponse.setVendorDetails(vendorDetails);
        
        // Set audit info
        MockDedupeResponseDto.AuditInfoDto auditInfo = new MockDedupeResponseDto.AuditInfoDto();
        auditInfo.setRequestTimestamp(LocalDateTime.now());
        auditInfo.setResponseTimestamp(LocalDateTime.now());
        errorResponse.setAuditInfo(auditInfo);
        
        return errorResponse;
    }

    /**
     * Get client IP address from request
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}
