package com.banking.mock.service;

import org.springframework.stereotype.Service;

/**
 * Service for masking Personally Identifiable Information (PII) in mock service
 * 
 * Provides methods to mask sensitive customer data for logging and audit purposes
 * in compliance with data privacy regulations.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Service
public class PiiMaskingService {

    /**
     * Mask customer identifier for logging
     * 
     * @param customerIdentifier The customer identifier to mask
     * @return Masked customer identifier
     */
    public String maskCustomerIdentifier(String customerIdentifier) {
        if (customerIdentifier == null || customerIdentifier.trim().isEmpty()) {
            return "INVALID_CUSTOMER_ID";
        }
        
        if (customerIdentifier.length() <= 4) {
            return "****";
        }
        
        return customerIdentifier.substring(0, 2) + "****" + 
               customerIdentifier.substring(customerIdentifier.length() - 2);
    }

    /**
     * Mask PAN number for logging
     * 
     * @param pan The PAN number to mask
     * @return Masked PAN number
     */
    public String maskPan(String pan) {
        if (pan == null || pan.length() < 10) {
            return "INVALID_PAN";
        }
        
        return pan.substring(0, 2) + "****" + pan.substring(6, 9) + "*" + pan.substring(9);
    }

    /**
     * Mask mobile number for logging
     * 
     * @param mobile The mobile number to mask
     * @return Masked mobile number
     */
    public String maskMobile(String mobile) {
        if (mobile == null || mobile.length() < 10) {
            return "INVALID_MOBILE";
        }
        
        return mobile.substring(0, 2) + "****" + mobile.substring(mobile.length() - 2);
    }

    /**
     * Mask full name for logging
     * 
     * @param fullName The full name to mask
     * @return Masked full name
     */
    public String maskFullName(String fullName) {
        if (fullName == null || fullName.trim().isEmpty()) {
            return "INVALID_NAME";
        }
        
        String[] nameParts = fullName.trim().split("\\s+");
        if (nameParts.length == 1) {
            return maskSingleName(nameParts[0]);
        }
        
        StringBuilder maskedName = new StringBuilder();
        for (int i = 0; i < nameParts.length; i++) {
            if (i > 0) {
                maskedName.append(" ");
            }
            maskedName.append(maskSingleName(nameParts[i]));
        }
        
        return maskedName.toString();
    }

    /**
     * Mask a single name part
     * 
     * @param name The name to mask
     * @return Masked name
     */
    private String maskSingleName(String name) {
        if (name.length() <= 2) {
            return "**";
        }
        return name.substring(0, 1) + "*".repeat(name.length() - 2) + name.substring(name.length() - 1);
    }

    /**
     * Create a masked version of customer data for logging
     * 
     * @param fullName The full name
     * @param pan The PAN number
     * @param mobile The mobile number
     * @return Masked customer data string
     */
    public String createMaskedCustomerData(String fullName, String pan, String mobile) {
        StringBuilder maskedData = new StringBuilder();
        maskedData.append("Name: ").append(maskFullName(fullName));
        maskedData.append(", PAN: ").append(maskPan(pan));
        maskedData.append(", Mobile: ").append(maskMobile(mobile));
        
        return maskedData.toString();
    }
}
