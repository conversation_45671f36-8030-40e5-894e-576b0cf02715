package com.banking.mock.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.banking.mock.dto.MockDedupeRequestDto;
import com.banking.mock.dto.MockDedupeResponseDto;

/**
 * Mock Customer Deduplication Service
 * 
 * Simulates third-party vendor deduplication APIs with realistic behavior
 * based on customer data patterns and configurable scenarios.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
@Service
public class MockDedupeService {

    private static final Logger logger = LoggerFactory.getLogger(MockDedupeService.class);

    @Value("${mock.vendor.name:Mock Dedupe Service}")
    private String vendorName;



    @Value("${mock.simulation.latency.min-ms:500}")
    private int minLatencyMs;

    @Value("${mock.simulation.latency.max-ms:2000}")
    private int maxLatencyMs;

    private final Random random = new Random();
    private final TraceLoggerService traceLoggerService;
    private final PiiMaskingService piiMaskingService;

    public MockDedupeService(TraceLoggerService traceLoggerService, PiiMaskingService piiMaskingService) {
        this.traceLoggerService = traceLoggerService;
        this.piiMaskingService = piiMaskingService;
    }

    /**
     * Process customer deduplication request
     * 
     * @param request The deduplication request
     * @return Mock deduplication response
     */
    public MockDedupeResponseDto processDedupeRequest(MockDedupeRequestDto request) {
        String traceId = UUID.randomUUID().toString();
        traceLoggerService.setTraceId(traceId);
        
        LocalDateTime requestTimestamp = LocalDateTime.now();
        
        try {
            logger.info("Processing dedupe request for customer: {}", 
                piiMaskingService.maskCustomerIdentifier(request.getCustomerIdentifier()));

            // Simulate processing latency
            simulateProcessingLatency();

            // Determine response based on business rules
            MockDedupeResponseDto response = generateResponse(request, requestTimestamp, traceId);

            logger.info("Dedupe request processed - Status: {} - Duplicates: {}", 
                response.getDedupeStatus(), response.getDuplicates().size());

            return response;

        } catch (Exception e) {
            logger.error("Error processing dedupe request", e);
            return createErrorResponse(request, requestTimestamp, traceId, "VENDOR_SERVICE_FAILURE", 
                "Internal service error occurred");
        } finally {
            traceLoggerService.clearTraceId();
        }
    }

    /**
     * Generate response based on business rules
     * 
     * @param request The request
     * @param requestTimestamp The request timestamp
     * @param traceId The trace ID
     * @return Generated response
     */
    private MockDedupeResponseDto generateResponse(MockDedupeRequestDto request, 
                                                  LocalDateTime requestTimestamp, String traceId) {
        
        String pan = request.getCustomerData().getPan();
        String mobile = request.getCustomerData().getMobileNumber();
        String fullName = request.getCustomerData().getFullName();

        // Apply business rules based on PAN
        if (pan != null) {
            // PAN-based logic
            if (pan.startsWith("TEST")) {
                return createErrorResponse(request, requestTimestamp, traceId, 
                    "VENDOR_SERVICE_FAILURE", "Test PAN triggered service failure");
            }
            
            if (pan.startsWith("TIMEOUT")) {
                simulateTimeout();
                return createErrorResponse(request, requestTimestamp, traceId, 
                    "TIMEOUT", "Request timed out");
            }
            
            // Even 4th digit (last digit before final letter) = MATCH_FOUND
            // PAN format: ********** - check the '4' (4th digit)
            if (pan.length() >= 10) {
                char fourthDigit = pan.charAt(pan.length() - 2); // Second to last character
                if (Character.isDigit(fourthDigit) && (fourthDigit - '0') % 2 == 0) {
                    return createMatchFoundResponse(request, requestTimestamp, traceId);
                }
            }
        }

        // Mobile number logic
        if (mobile != null) {
            if (mobile.startsWith("999")) {
                return createMatchFoundResponse(request, requestTimestamp, traceId);
            }
            
            if (mobile.startsWith("888")) {
                return createNoMatchResponse(request, requestTimestamp, traceId);
            }
            
            if (mobile.startsWith("777")) {
                return createErrorResponse(request, requestTimestamp, traceId, 
                    "AUTHENTICATION_ERROR", "Authentication failed");
            }
        }

        // Name-based logic
        if (fullName != null) {
            if (fullName.toLowerCase().contains("duplicate")) {
                return createMatchFoundResponse(request, requestTimestamp, traceId);
            }
            
            if (fullName.toLowerCase().contains("unique")) {
                return createNoMatchResponse(request, requestTimestamp, traceId);
            }
            
            if (fullName.toLowerCase().contains("error")) {
                return createErrorResponse(request, requestTimestamp, traceId, 
                    "VALIDATION_ERROR", "Invalid customer data");
            }
        }

        // Default: NO_MATCH_FOUND for odd 4th digit or invalid PAN format
        return createNoMatchResponse(request, requestTimestamp, traceId);
    }

    /**
     * Create MATCH_FOUND response
     */
    private MockDedupeResponseDto createMatchFoundResponse(MockDedupeRequestDto request, 
                                                          LocalDateTime requestTimestamp, String traceId) {
        
        List<MockDedupeResponseDto.DuplicateCustomerDto> duplicates = generateDuplicates();
        
        MockDedupeResponseDto response = new MockDedupeResponseDto();
        response.setRequestId(UUID.randomUUID().toString());
        response.setCustomerIdentifier(request.getCustomerIdentifier());
        response.setDedupeStatus("MATCH_FOUND");
        response.setMessage("Duplicate customer records found");
        response.setDuplicates(duplicates);
        
        // Set vendor details
        MockDedupeResponseDto.VendorDetailsDto vendorDetails = new MockDedupeResponseDto.VendorDetailsDto();
        vendorDetails.setVendorName(vendorName);
        vendorDetails.setProcessingTimestamp(LocalDateTime.now());
        response.setVendorDetails(vendorDetails);
        
        // Set audit info
        MockDedupeResponseDto.AuditInfoDto auditInfo = new MockDedupeResponseDto.AuditInfoDto();
        auditInfo.setRequestTimestamp(requestTimestamp);
        auditInfo.setResponseTimestamp(LocalDateTime.now());
        response.setAuditInfo(auditInfo);
        
        return response;
    }

    /**
     * Create NO_MATCH_FOUND response
     */
    private MockDedupeResponseDto createNoMatchResponse(MockDedupeRequestDto request, 
                                                       LocalDateTime requestTimestamp, String traceId) {
        
        MockDedupeResponseDto response = new MockDedupeResponseDto();
        response.setRequestId(UUID.randomUUID().toString());
        response.setCustomerIdentifier(request.getCustomerIdentifier());
        response.setDedupeStatus("NO_MATCH_FOUND");
        response.setMessage("No duplicate records found");
        response.setDuplicates(new ArrayList<>());
        
        // Set vendor details
        MockDedupeResponseDto.VendorDetailsDto vendorDetails = new MockDedupeResponseDto.VendorDetailsDto();
        vendorDetails.setVendorName(vendorName);
        vendorDetails.setProcessingTimestamp(LocalDateTime.now());
        response.setVendorDetails(vendorDetails);
        
        // Set audit info
        MockDedupeResponseDto.AuditInfoDto auditInfo = new MockDedupeResponseDto.AuditInfoDto();
        auditInfo.setRequestTimestamp(requestTimestamp);
        auditInfo.setResponseTimestamp(LocalDateTime.now());
        response.setAuditInfo(auditInfo);
        
        return response;
    }

    /**
     * Create ERROR response
     */
    private MockDedupeResponseDto createErrorResponse(MockDedupeRequestDto request, 
                                                     LocalDateTime requestTimestamp, String traceId,
                                                     String errorCode, String errorMessage) {
        
        MockDedupeResponseDto response = new MockDedupeResponseDto();
        response.setRequestId(UUID.randomUUID().toString());
        response.setCustomerIdentifier(request.getCustomerIdentifier());
        response.setDedupeStatus("ERROR");
        response.setMessage(errorMessage);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        response.setDuplicates(new ArrayList<>());
        
        // Set vendor details
        MockDedupeResponseDto.VendorDetailsDto vendorDetails = new MockDedupeResponseDto.VendorDetailsDto();
        vendorDetails.setVendorName(vendorName);
        vendorDetails.setProcessingTimestamp(LocalDateTime.now());
        response.setVendorDetails(vendorDetails);
        
        // Set audit info
        MockDedupeResponseDto.AuditInfoDto auditInfo = new MockDedupeResponseDto.AuditInfoDto();
        auditInfo.setRequestTimestamp(requestTimestamp);
        auditInfo.setResponseTimestamp(LocalDateTime.now());
        response.setAuditInfo(auditInfo);
        
        return response;
    }

    /**
     * Generate mock duplicate customers
     */
    private List<MockDedupeResponseDto.DuplicateCustomerDto> generateDuplicates() {
        List<MockDedupeResponseDto.DuplicateCustomerDto> duplicates = new ArrayList<>();
        
        int numDuplicates = random.nextInt(2) + 1; // 1-2 duplicates
        
        for (int i = 0; i < numDuplicates; i++) {
            MockDedupeResponseDto.DuplicateCustomerDto duplicate = new MockDedupeResponseDto.DuplicateCustomerDto();
            duplicate.setCustomerId("CUST" + (100000 + random.nextInt(900000)));
            duplicate.setMatchScore(90 + random.nextInt(9)); // 90-98%
            duplicate.setMatchedFields(generateMatchedFields());
            duplicate.setCustomerStatus(random.nextBoolean() ? "ACTIVE" : "INACTIVE");
            duplicate.setCreatedDate(LocalDateTime.now().minusDays(random.nextInt(180)));
            
            duplicates.add(duplicate);
        }
        
        return duplicates;
    }

    /**
     * Generate matched fields
     */
    private List<String> generateMatchedFields() {
        List<String> allFields = Arrays.asList("fullName", "pan", "mobileNumber", "dateOfBirth", "emailAddress");
        List<String> matchedFields = new ArrayList<>();
        
        int numMatches = random.nextInt(3) + 2; // 2-4 matches
        
        for (int i = 0; i < numMatches && i < allFields.size(); i++) {
            matchedFields.add(allFields.get(i));
        }
        
        return matchedFields;
    }

    /**
     * Simulate processing latency
     */
    private void simulateProcessingLatency() {
        try {
            int latency = minLatencyMs + random.nextInt(maxLatencyMs - minLatencyMs);
            Thread.sleep(latency);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Processing latency simulation interrupted");
        }
    }

    /**
     * Simulate timeout
     */
    private void simulateTimeout() {
        try {
            Thread.sleep(30000); // 30 second timeout
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
