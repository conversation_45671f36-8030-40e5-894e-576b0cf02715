package com.banking.mock.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;

/**
 * Mock Customer Deduplication Request DTO
 * 
 * Represents the request format expected by the mock vendor API.
 * Mirrors the structure expected by real third-party vendors.
 * 
 * <AUTHOR> Technology Team
 * @version 1.0.0
 * @since 2025-06-16
 */
public class MockDedupeRequestDto {

    @NotBlank(message = "Customer identifier is required")
    @JsonProperty("customerIdentifier")
    private String customerIdentifier;

    @NotNull(message = "Customer data is required")
    @Valid
    @JsonProperty("customerData")
    private CustomerDataDto customerData;

    /**
     * Default constructor
     */
    public MockDedupeRequestDto() {
    }

    /**
     * Constructor with all fields
     */
    public MockDedupeRequestDto(String customerIdentifier, CustomerDataDto customerData) {
        this.customerIdentifier = customerIdentifier;
        this.customerData = customerData;
    }

    // Getters and Setters

    public String getCustomerIdentifier() {
        return customerIdentifier;
    }

    public void setCustomerIdentifier(String customerIdentifier) {
        this.customerIdentifier = customerIdentifier;
    }

    public CustomerDataDto getCustomerData() {
        return customerData;
    }

    public void setCustomerData(CustomerDataDto customerData) {
        this.customerData = customerData;
    }

    /**
     * Nested DTO for Customer Data
     */
    public static class CustomerDataDto {

        @NotBlank(message = "Full name is required")
        @JsonProperty("fullName")
        private String fullName;

        @NotNull(message = "Date of birth is required")
        @JsonProperty("dateOfBirth")
        private LocalDate dateOfBirth;

        @NotBlank(message = "PAN is required")
        @JsonProperty("pan")
        private String pan;

        @JsonProperty("aadhaar")
        private String aadhaar;

        @NotBlank(message = "Mobile number is required")
        @JsonProperty("mobileNumber")
        private String mobileNumber;

        @JsonProperty("emailAddress")
        private String emailAddress;

        @Valid
        @JsonProperty("address")
        private AddressDto address;

        /**
         * Default constructor
         */
        public CustomerDataDto() {
        }

        /**
         * Constructor with all fields
         */
        public CustomerDataDto(String fullName, LocalDate dateOfBirth, String pan, String aadhaar,
                              String mobileNumber, String emailAddress, AddressDto address) {
            this.fullName = fullName;
            this.dateOfBirth = dateOfBirth;
            this.pan = pan;
            this.aadhaar = aadhaar;
            this.mobileNumber = mobileNumber;
            this.emailAddress = emailAddress;
            this.address = address;
        }

        // Getters and Setters

        public String getFullName() {
            return fullName;
        }

        public void setFullName(String fullName) {
            this.fullName = fullName;
        }

        public LocalDate getDateOfBirth() {
            return dateOfBirth;
        }

        public void setDateOfBirth(LocalDate dateOfBirth) {
            this.dateOfBirth = dateOfBirth;
        }

        public String getPan() {
            return pan;
        }

        public void setPan(String pan) {
            this.pan = pan;
        }

        public String getAadhaar() {
            return aadhaar;
        }

        public void setAadhaar(String aadhaar) {
            this.aadhaar = aadhaar;
        }

        public String getMobileNumber() {
            return mobileNumber;
        }

        public void setMobileNumber(String mobileNumber) {
            this.mobileNumber = mobileNumber;
        }

        public String getEmailAddress() {
            return emailAddress;
        }

        public void setEmailAddress(String emailAddress) {
            this.emailAddress = emailAddress;
        }

        public AddressDto getAddress() {
            return address;
        }

        public void setAddress(AddressDto address) {
            this.address = address;
        }
    }

    /**
     * Nested DTO for Address Information
     */
    public static class AddressDto {

        @JsonProperty("line1")
        private String line1;

        @JsonProperty("city")
        private String city;

        @JsonProperty("state")
        private String state;

        @JsonProperty("pincode")
        private String pincode;

        /**
         * Default constructor
         */
        public AddressDto() {
        }

        /**
         * Constructor with all fields
         */
        public AddressDto(String line1, String city, String state, String pincode) {
            this.line1 = line1;
            this.city = city;
            this.state = state;
            this.pincode = pincode;
        }

        // Getters and Setters

        public String getLine1() {
            return line1;
        }

        public void setLine1(String line1) {
            this.line1 = line1;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getPincode() {
            return pincode;
        }

        public void setPincode(String pincode) {
            this.pincode = pincode;
        }
    }
}
