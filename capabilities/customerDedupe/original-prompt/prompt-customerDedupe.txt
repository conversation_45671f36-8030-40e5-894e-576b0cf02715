# Customer Dedupe Capability

## Business Use Case
**Customer Dedupe (Customer Deduplication)** is the process of identifying, preventing, and resolving duplicate customer records in a database or system. It's an essential part of data quality management and uniquely identifying a customer, where the same customer may interact through multiple channels or touchpoints.

## Overarching Goal
The objective is to identify and eliminate duplicate customer records across the bank's systems to ensure each customer is uniquely and accurately represented. This is done by accepting customer data from upstream applications, seamlessly passing it to chosen third-party dedupe vendor such as Karza, Perfios, or Signzy, receiving and interpreting their deduplication responses, and relaying a clear, actionable outcome back to the upstream applications. This ensures unique customer representation and aids in regulatory compliance (like RBI's guidelines) and efficient credit underwriting. For our capability we'll go ahead with the assumption that there is only one chosen dedupe vendor and managing multiple vendors is not in scope.

---

## Phase 1: Request Reception & Vendor Interaction

### Objective
To accept a customer deduplication request, validate its inputs, prepare and execute a call to the single configured third-party dedupe vendor, and capture its raw response.

### Trigger
An upstream application (e.g., Customer Onboarding, CRM, Loan Origination System) initiates a deduplication check for a new or existing customer record.

### Inputs from Upstream Application

**Customer Identifier (Current):** A unique ID for the customer record being processed (e.g., newCustomerId, loanApplicantId).

**Core Customer Data for Dedupe:**
- Full Name
- Date of Birth (DOB)
- PAN (Permanent Account Number)
- Aadhaar Number (masked/tokenized as per regulations)
- Mobile Number
- Email Address
- Address Details (e.g., Line1, City, State, Pincode)

### Key Activities within Your Capability

**Input Validation & Sanitization:** The capability will first validate the presence and format of all incoming mandatory inputs. Basic sanitization (like trimming whitespace) can be done to ensure data cleanliness for the vendor, but complex data transformations or normalization are avoided, as the external service is expected to handle those for its matching logic.

**Vendor Configuration Retrieval:** The service will access its secure configuration store to retrieve the specific API endpoint, authentication credentials (e.g., API keys, tokens), and any other parameters for the currently configured dedupe vendor.

**Vendor Request Transformation:** It will map and transform the standardized internal customer data into the exact request payload format required by the single configured vendor's API. This ensures the vendor receives data precisely as it expects.

**Establish Secure Connection & Authenticate:** Using the retrieved credentials, the capability will securely authenticate with the vendor's API and establish a connection.

**Call External Dedupe Service:** The constructed request payload is then transmitted to the vendor's API. This will be a synchronous call.

**Receive & Parse Raw Response:** The capability will capture the synchronous response from the vendor. It then parses this raw response to extract the core deduplication result (e.g., a flag indicating match found or no match, a match score, and a list of duplicate details if found). Any error codes or messages provided by the vendor are also captured.

**Error Handling for External Call:** Robust error handling for communication failures (e.g., network timeouts, vendor unavailability, authentication errors) is implemented. This includes retry mechanisms for transient, recoverable errors. All successes and failures are logged, updating the internal request status (e.g., VENDOR_CALL_SUCCESS, VENDOR_CALL_FAILED).

### Outputs from This Capability (to next phase/internal log)
- The raw, parsed response received directly from the single configured dedupe vendor
- Any error details related to the external call
- An internal record of the dedupe request and the outcome of the vendor interaction

---

## Phase 2: Response Interpretation & Reporting

This phase takes the raw response from the vendor, translates it into the bank's standardized dedupe outcome, and then communicates this clear decision back to the upstream application.

### Objective
To interpret the single vendor's deduplication results into a clear "Match Found" or "No Match Found" status with relevant details, and communicate this final decision back to the upstream application.

### Trigger
Successful receipt of the raw response from the external dedupe vendor in Phase 1.

### Key Activities within Your Capability

**Vendor Response Interpretation:** The capability will analyze the parsed raw response from the vendor. It then maps the vendor's specific dedupe status (e.g., their is_duplicate flag, match_strength code, or match reasons) to an internal, standardized Dedupe Status (e.g., MATCH_FOUND, NO_MATCH_FOUND). If MATCH_FOUND, all relevant details of the duplicate applications/customers identified by the vendor (e.g., their customer_id, application_id, matched_fields, match_score, status_of_prev_record) are extracted. If the vendor call failed in Phase 1, or the vendor indicated an internal error, an appropriate internal error status for the dedupe check is generated.

**Determine Final Output Message/Details:**
- **If MATCH_FOUND:** A structured response is prepared including the MATCH_FOUND status and a list of the identified duplicate customer records or applications with their key details.
- **If NO_MATCH_FOUND:** A response is prepared including the NO_MATCH_FOUND status and a message by the vendor (if the vendor provides this detail, otherwise a generic "No match found").
- **If ERROR:** A response is prepared indicating an error in the dedupe process (e.g., "Dedupe service unavailable, manual review required").

**Result Persistence & Audit Trail:** The capability will meticulously persist the complete deduplication outcome in its database. This includes the original request details, the raw vendor response, the interpreted dedupe status, and the final output details. This ensures a robust audit trail crucial for RBI compliance.

**Report to Upstream Application:** Finally, the service constructs and sends the final response payload to the initiating upstream application (typically a synchronous HTTP response). The response is clear, concise, and provides all necessary information for the upstream application to take action (e.g., proceed, send for manual review, reject).

### Outputs from Your Capability
- A definitive Dedupe Status (MATCH_FOUND or NO_MATCH_FOUND or an ERROR status)
- Detailed information about any identified duplicate customers if a match is found
- A clear message indicating no match found
- A robust, persisted audit record of the deduplication check within the system
- A response sent back to the upstream applicat