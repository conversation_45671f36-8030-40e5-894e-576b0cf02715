Mock Customer Dedupe Service - Specification
Overview
A standalone Spring Boot application designed to simulate third-party customer deduplication services (<PERSON><PERSON><PERSON>, Perfios, Signzy) for development and testing environments. This mock service helps validate how applications handle both duplicate detection and no-match scenarios across the customer deduplication lifecycle without requiring live vendor access.
Purpose

Simulate customer dedupe vendor APIs for application development and QA
Enable testing of match found, no match, timeout, and system error scenarios
Provide realistic API structure, behavior, and timing simulation
Isolate development environments from production dedupe vendor services
Support RBI compliance testing and audit trail validation

Technical Requirements

Spring Boot version: 3.2.3 (or compatible)
Java version: 21
Server Port: 8083
OpenAPI/Swagger documentation included
In-memory request audit with PII masking for customer data
UUID-based "X-Trace-Id" tagging on every request and response
Simulated latency for realistic vendor response timing (500ms-2s)

API Endpoints

Customer Deduplication Check

Path: /api/customer-dedupe/internal/customer/dedupe
Method: POST
Request Body:
json{
  "customerIdentifier": "CUST123456",
  "customerData": {
    "fullName": "<PERSON><PERSON>",
    "dateOfBirth": "1990-01-15",
    "pan": "**********",
    "aadhaar": "XXXX-XXXX-7890",
    "mobileNumber": "9876543210",
    "emailAddress": "<EMAIL>",
    "address": {
      "line1": "123 MG Road",
      "city": "Bangalore",
      "state": "Karnataka",
      "pincode": "560001"
    }
  }
}

Response (Match Found):
json{
  "requestId": "REQ987654321",
  "customerIdentifier": "CUST123456",
  "dedupeStatus": "MATCH_FOUND",
  "message": "Duplicate customer records found",
  "duplicates": [
    {
      "customerId": "CUST111222",
      "matchScore": 95,
      "matchedFields": ["fullName", "pan", "mobileNumber"],
      "customerStatus": "ACTIVE",
      "createdDate": "2022-05-10T14:30:00Z"
    }
  ],
  "vendorDetails": {
    "vendorName": "Karza",
    "lookbackPeriod": "6 months",
    "processingTimestamp": "2023-09-15T10:45:22Z"
  },
  "auditInfo": {
    "requestTimestamp": "2023-09-15T10:45:20Z",
    "responseTimestamp": "2023-09-15T10:45:23Z"
  }
}



Health Check

Path: /api/customer-dedupe/internal/customer/dedupe/health
Method: GET
Response:
json{
  "status": "UP",
  "vendor": "Mock Dedupe Service",
  "timestamp": "2025-06-16T10:00:00Z"
}




Simulated Behavior Logic
PAN-based Logic:

PAN ends with even digit (0,2,4,6,8) → MATCH_FOUND with 1-2 duplicates
PAN ends with odd digit (1,3,5,7,9) → NO_MATCH_FOUND
PAN starts with "TEST" → Return HTTP 500 with "VENDOR_SERVICE_FAILURE"
PAN starts with "TIMEOUT" → Simulate 30-second timeout

Mobile Number Logic:

Mobile starts with "999" → Always MATCH_FOUND (high-risk number)
Mobile starts with "888" → Always NO_MATCH_FOUND


Name-based Logic:

Names containing "Duplicate" → MATCH_FOUND with high match score (98%)
Names containing "Unique" → NO_MATCH_FOUND
Names containing "Error" → Return validation error (400)

Match Score Distribution:

High matches: 90-98% (strong duplicates)
Medium matches: 70-89% (potential duplicates)
Low matches: 50-69% (weak matches - not typically returned)

Response Scenarios

MATCH_FOUND Response:
json{
  "dedupeStatus": "MATCH_FOUND",
  "duplicates": [
    {
      "customerId": "CUST555666",
      "matchScore": 92,
      "matchedFields": ["fullName", "pan", "dateOfBirth"],
      "customerStatus": "ACTIVE",
      "createdDate": "2023-01-15T09:30:00Z"
    }
  ]
}

NO_MATCH_FOUND Response:
json{
  "dedupeStatus": "NO_MATCH_FOUND",
  "message": "No duplicate records found in 6 months lookback period",
  "duplicates": []
}

ERROR Response:
json{
  "dedupeStatus": "ERROR",
  "message": "Dedupe service temporarily unavailable, manual review required",
  "errorCode": "VENDOR_SERVICE_FAILURE",
  "duplicates": []
}


Security & Compliance

Mask PAN (show only first 2 and last 1 characters), Aadhaar, mobile numbers in logs
All responses tagged with "X-Trace-Id" UUID for traceability

Request/response audit trail with timestamp and masked PII
Simulate RBI compliance data retention (configurable retention period)

Admin & Configuration Interface

GET  /api/customer-dedupe/internal/admin/dedupe/requests          → View request/response history with PII masking
POST /api/customer-dedupe/internal/admin/dedupe/config            → Configure response overrides and match scenarios
POST /api/customer-dedupe/internal/admin/dedupe/reset             → Clear in-memory logs and reset configuration
GET  /api/customer-dedupe/internal/admin/dedupe/stats             → Get statistics (total requests, match rate, etc.)
POST /api/customer-dedupe/internal/admin/dedupe/simulate-outage   → Simulate vendor outage for testing

Service Layer Components

MockDedupeService: Simulates vendor dedupe logic and response generation
MockConfigService: Enables runtime response manipulation and scenario testing
TraceLoggerService: Adds trace ID and PII-masked audit logging
DedupeRulesEngine: Implements the business logic for different match scenarios
AuditService: Maintains compliance audit trail with data masking

Testing Strategy
PAN Testing:

Test PANs ending 0-9:

Even digits → MATCH_FOUND
Odd digits → NO_MATCH_FOUND
TESTABCD1 → System error
TIMEOUT123A → Timeout simulation



Mobile Testing:

9998887777 → High-risk match
8887776666 → No match


Name Testing:

"Rahul Duplicate Kumar" → Match found
"Priya Unique Sharma" → No match
"Error Test User" → Validation error

Edge Cases:

Missing mandatory fields → 400 Bad Request
Invalid PAN format → 400 Bad Request
Malformed request → 400 Bad Request
System overload simulation → 503 Service Unavailable

Performance Testing

Normal response time: 500ms - 1.5s (configurable)
Timeout scenarios: 30s delay
Rate limiting simulation: 100 requests/minute per client
Bulk testing support with batch endpoints

Mock Data Templates
High Match Customer:
json{
  "customerId": "CUST998877",
  "matchScore": 95,
  "matchedFields": ["fullName", "pan", "mobileNumber", "dateOfBirth"],
  "customerStatus": "ACTIVE",
  "createdDate": "2023-03-20T11:15:00Z"
}
Medium Match Customer:
json{
  "customerId": "CUST776655",
  "matchScore": 78,
  "matchedFields": ["fullName", "mobileNumber"],
  "customerStatus": "INACTIVE",
  "createdDate": "2022-11-10T16:45:00Z"
}