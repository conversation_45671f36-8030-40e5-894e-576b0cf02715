server:
  port: ${SERVER_PORT:8205}
  servlet:
    context-path: /api/application-dedupe/internal

spring:
  application:
    name: mock-external-dedupe-service
  
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# Mock Service Configuration
mock:
  dedupe:
    enable-logging: true
    enable-audit: true
    response-delay-ms: 100
    enable-random-errors: false
    error-rate-percent: 5

# Logging Configuration
logging:
  level:
    com.bank.dedupe.mock: INFO
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: logs/mock-external-dedupe.log

# OpenAPI/Swagger Configuration
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    try-it-out-enabled: true
    operations-sorter: method
    tags-sorter: alpha
    display-request-duration: true
    show-extensions: true
    show-common-extensions: true
  show-actuator: true
  default-consumes-media-type: application/json
  default-produces-media-type: application/json

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always


