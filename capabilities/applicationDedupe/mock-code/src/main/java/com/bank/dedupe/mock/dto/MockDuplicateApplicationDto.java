package com.bank.dedupe.mock.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDate;
import java.util.List;

/**
 * Mock DTO for duplicate application details.
 * Represents individual duplicate applications found by the mock service.
 */
public class MockDuplicateApplicationDto {

    @JsonProperty("applicationId")
    private String applicationId;

    @JsonProperty("submissionDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate submissionDate;

    @JsonProperty("matchedFields")
    private List<String> matchedFields;

    @JsonProperty("matchScore")
    private Double matchScore;

    @JsonProperty("previousApplicationStatus")
    private String previousApplicationStatus;

    @JsonProperty("customerName")
    private String customerName;

    @JsonProperty("matchNotes")
    private String matchNotes;

    // Default constructor
    public MockDuplicateApplicationDto() {
    }

    // Constructor with essential fields
    public MockDuplicateApplicationDto(String applicationId, LocalDate submissionDate, 
                                      List<String> matchedFields, Double matchScore, 
                                      String previousApplicationStatus) {
        this.applicationId = applicationId;
        this.submissionDate = submissionDate;
        this.matchedFields = matchedFields;
        this.matchScore = matchScore;
        this.previousApplicationStatus = previousApplicationStatus;
    }

    // Getters and Setters
    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public LocalDate getSubmissionDate() {
        return submissionDate;
    }

    public void setSubmissionDate(LocalDate submissionDate) {
        this.submissionDate = submissionDate;
    }

    public List<String> getMatchedFields() {
        return matchedFields;
    }

    public void setMatchedFields(List<String> matchedFields) {
        this.matchedFields = matchedFields;
    }

    public Double getMatchScore() {
        return matchScore;
    }

    public void setMatchScore(Double matchScore) {
        this.matchScore = matchScore;
    }

    public String getPreviousApplicationStatus() {
        return previousApplicationStatus;
    }

    public void setPreviousApplicationStatus(String previousApplicationStatus) {
        this.previousApplicationStatus = previousApplicationStatus;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getMatchNotes() {
        return matchNotes;
    }

    public void setMatchNotes(String matchNotes) {
        this.matchNotes = matchNotes;
    }

    @Override
    public String toString() {
        return "MockDuplicateApplicationDto{" +
                "applicationId='" + applicationId + '\'' +
                ", submissionDate=" + submissionDate +
                ", matchedFields=" + matchedFields +
                ", matchScore=" + matchScore +
                ", previousApplicationStatus='" + previousApplicationStatus + '\'' +
                ", customerName='" + customerName + '\'' +
                ", matchNotes='" + matchNotes + '\'' +
                '}';
    }
}
