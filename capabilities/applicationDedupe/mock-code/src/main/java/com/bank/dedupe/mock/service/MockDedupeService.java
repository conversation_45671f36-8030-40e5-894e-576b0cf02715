package com.bank.dedupe.mock.service;

import com.bank.dedupe.mock.dto.MockDedupeRequestDto;
import com.bank.dedupe.mock.dto.MockDedupeResponseDto;
import com.bank.dedupe.mock.dto.MockDuplicateApplicationDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * Mock service that simulates external deduplication service behavior.
 * Implements different response scenarios based on input patterns.
 */
@Service
public class MockDedupeService {

    private static final Logger logger = LoggerFactory.getLogger(MockDedupeService.class);

    /**
     * Processes a deduplication request and returns a mock response.
     * Response behavior is determined by input patterns as specified in requirements.
     * 
     * @param request the deduplication request
     * @return mock deduplication response
     */
    public MockDedupeResponseDto processDeduplicationRequest(MockDedupeRequestDto request) {
        logger.info("Processing mock deduplication request for application: {}", request.getApplicationId());
        
        String transactionId = "TXN" + UUID.randomUUID().toString().substring(0, 8);
        
        // Determine response based on PAN suffix
        String pan = request.getPan();
        if (pan != null && pan.length() > 0) {
            char lastChar = pan.charAt(pan.length() - 1);
            
            switch (lastChar) {
                case 'A':
                    return createMatchFoundResponse(request, transactionId);
                case 'B':
                    return createNoMatchResponse(request, transactionId);
                case 'C':
                    return createPartialMatchResponse(request, transactionId);
                case 'D':
                    throw new RuntimeException("External dedupe service temporarily unavailable");
                default:
                    return createNoMatchResponse(request, transactionId);
            }
        }
        
        // Check mobile number patterns
        String mobile = request.getMobileNumber();
        if (mobile != null && mobile.endsWith("0000")) {
            throw new RuntimeException("Request timeout - dedupe service not responding");
        }
        
        // Check email patterns
        String email = request.getEmailAddress();
        if (email != null && email.contains("invalid")) {
            throw new RuntimeException("Authentication failed with external service");
        }
        
        // Default to no match
        return createNoMatchResponse(request, transactionId);
    }

    /**
     * Creates a match found response with duplicate applications.
     * 
     * @param request the original request
     * @param transactionId the transaction ID
     * @return match found response
     */
    private MockDedupeResponseDto createMatchFoundResponse(MockDedupeRequestDto request, String transactionId) {
        logger.debug("Creating match found response for application: {}", request.getApplicationId());
        
        MockDedupeResponseDto response = new MockDedupeResponseDto(
                "MATCH_FOUND", 
                2, 
                "Duplicate applications found", 
                request.getDeduplicationPeriod()
        );
        
        response.setTransactionId(transactionId);
        
        // Create duplicate applications
        MockDuplicateApplicationDto duplicate1 = new MockDuplicateApplicationDto(
                "APP" + generateRandomNumber(5),
                LocalDate.now().minusDays(15),
                List.of("PAN", "MOBILE", "EMAIL"),
                0.95,
                "APPROVED"
        );
        duplicate1.setCustomerName(request.getDedupedCustomerName());
        duplicate1.setMatchNotes("High confidence match on multiple fields");
        
        MockDuplicateApplicationDto duplicate2 = new MockDuplicateApplicationDto(
                "APP" + generateRandomNumber(5),
                LocalDate.now().minusDays(32),
                List.of("PAN", "AADHAAR"),
                0.87,
                "PENDING"
        );
        duplicate2.setCustomerName(request.getDedupedCustomerName());
        duplicate2.setMatchNotes("Strong match on identity documents");
        
        response.setDuplicateApplications(List.of(duplicate1, duplicate2));
        
        return response;
    }

    /**
     * Creates a no match response.
     * 
     * @param request the original request
     * @param transactionId the transaction ID
     * @return no match response
     */
    private MockDedupeResponseDto createNoMatchResponse(MockDedupeRequestDto request, String transactionId) {
        logger.debug("Creating no match response for application: {}", request.getApplicationId());
        
        String period = request.getDeduplicationPeriod();
        String periodDisplay = getPeriodDisplayName(period);
        
        MockDedupeResponseDto response = new MockDedupeResponseDto(
                "NO_MATCH_FOUND", 
                0, 
                "No duplicates found in the last " + periodDisplay, 
                period
        );
        
        response.setTransactionId(transactionId);
        
        return response;
    }

    /**
     * Creates a partial match response.
     * 
     * @param request the original request
     * @param transactionId the transaction ID
     * @return partial match response
     */
    private MockDedupeResponseDto createPartialMatchResponse(MockDedupeRequestDto request, String transactionId) {
        logger.debug("Creating partial match response for application: {}", request.getApplicationId());
        
        MockDedupeResponseDto response = new MockDedupeResponseDto(
                "PARTIAL_MATCH", 
                1, 
                "Potential duplicate found requiring manual review", 
                request.getDeduplicationPeriod()
        );
        
        response.setTransactionId(transactionId);
        
        // Create partial match duplicate
        MockDuplicateApplicationDto duplicate = new MockDuplicateApplicationDto(
                "APP" + generateRandomNumber(5),
                LocalDate.now().minusDays(25),
                List.of("MOBILE", "EMAIL"),
                0.65,
                "UNDER_REVIEW"
        );
        duplicate.setCustomerName(request.getDedupedCustomerName());
        duplicate.setMatchNotes("Medium confidence match on contact details only");
        
        response.setDuplicateApplications(List.of(duplicate));
        
        return response;
    }

    /**
     * Gets display name for deduplication period.
     * 
     * @param period the period code
     * @return display name
     */
    private String getPeriodDisplayName(String period) {
        return switch (period) {
            case "30_DAYS" -> "30 days";
            case "90_DAYS" -> "90 days";
            case "180_DAYS" -> "180 days";
            case "1_YEAR" -> "1 year";
            case "2_YEARS" -> "2 years";
            default -> "90 days";
        };
    }

    /**
     * Generates a random number string of specified length.
     * 
     * @param length the length of the number string
     * @return random number string
     */
    private String generateRandomNumber(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append((int) (Math.random() * 10));
        }
        return sb.toString();
    }
}
