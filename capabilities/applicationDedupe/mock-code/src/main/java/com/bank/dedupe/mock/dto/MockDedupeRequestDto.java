package com.bank.dedupe.mock.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;

/**
 * Mock DTO for external deduplication requests.
 * Mirrors the expected format of the real external service.
 */
public class MockDedupeRequestDto {

    @JsonProperty("applicationId")
    @NotBlank(message = "Application ID is required")
    private String applicationId;

    @JsonProperty("dedupedCustomerName")
    @NotBlank(message = "Customer name is required")
    private String dedupedCustomerName;

    @JsonProperty("mobileNumber")
    @NotBlank(message = "Mobile number is required")
    private String mobileNumber;

    @JsonProperty("emailAddress")
    @NotBlank(message = "Email address is required")
    @Email(message = "Email must be valid")
    private String emailAddress;

    @JsonProperty("pan")
    @NotBlank(message = "PAN is required")
    private String pan;

    @JsonProperty("aadhaarNumber")
    @NotBlank(message = "Aadhaar number is required")
    private String aadhaarNumber;

    @JsonProperty("dateOfBirth")
    @NotNull(message = "Date of birth is required")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate dateOfBirth;

    @JsonProperty("applicationSubmissionDate")
    @NotNull(message = "Application submission date is required")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate applicationSubmissionDate;

    @JsonProperty("deduplicationPeriod")
    private String deduplicationPeriod;

    // Default constructor
    public MockDedupeRequestDto() {
    }

    // Getters and Setters
    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getDedupedCustomerName() {
        return dedupedCustomerName;
    }

    public void setDedupedCustomerName(String dedupedCustomerName) {
        this.dedupedCustomerName = dedupedCustomerName;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getAadhaarNumber() {
        return aadhaarNumber;
    }

    public void setAadhaarNumber(String aadhaarNumber) {
        this.aadhaarNumber = aadhaarNumber;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public LocalDate getApplicationSubmissionDate() {
        return applicationSubmissionDate;
    }

    public void setApplicationSubmissionDate(LocalDate applicationSubmissionDate) {
        this.applicationSubmissionDate = applicationSubmissionDate;
    }

    public String getDeduplicationPeriod() {
        return deduplicationPeriod;
    }

    public void setDeduplicationPeriod(String deduplicationPeriod) {
        this.deduplicationPeriod = deduplicationPeriod;
    }

    @Override
    public String toString() {
        return "MockDedupeRequestDto{" +
                "applicationId='" + applicationId + '\'' +
                ", dedupedCustomerName='" + dedupedCustomerName + '\'' +
                ", mobileNumber='***MASKED***'" +
                ", emailAddress='***MASKED***'" +
                ", pan='***MASKED***'" +
                ", aadhaarNumber='***MASKED***'" +
                ", dateOfBirth=" + dateOfBirth +
                ", applicationSubmissionDate=" + applicationSubmissionDate +
                ", deduplicationPeriod='" + deduplicationPeriod + '\'' +
                '}';
    }
}
