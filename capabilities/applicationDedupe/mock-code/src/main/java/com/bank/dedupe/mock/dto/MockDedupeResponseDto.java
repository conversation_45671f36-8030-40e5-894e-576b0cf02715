package com.bank.dedupe.mock.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Mock DTO for external deduplication responses.
 * Mirrors the expected format of the real external service.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MockDedupeResponseDto {

    @JsonProperty("dedupeStatus")
    private String dedupeStatus;

    @JsonProperty("matchCount")
    private Integer matchCount;

    @JsonProperty("duplicateApplications")
    private List<MockDuplicateApplicationDto> duplicateApplications;

    @JsonProperty("message")
    private String message;

    @JsonProperty("timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime timestamp;

    @JsonProperty("transactionId")
    private String transactionId;

    @JsonProperty("searchPeriod")
    private String searchPeriod;

    // Default constructor
    public MockDedupeResponseDto() {
        this.timestamp = LocalDateTime.now();
    }

    // Constructor for successful responses
    public MockDedupeResponseDto(String dedupeStatus, Integer matchCount, String message, String searchPeriod) {
        this();
        this.dedupeStatus = dedupeStatus;
        this.matchCount = matchCount;
        this.message = message;
        this.searchPeriod = searchPeriod;
    }

    // Getters and Setters
    public String getDedupeStatus() {
        return dedupeStatus;
    }

    public void setDedupeStatus(String dedupeStatus) {
        this.dedupeStatus = dedupeStatus;
    }

    public Integer getMatchCount() {
        return matchCount;
    }

    public void setMatchCount(Integer matchCount) {
        this.matchCount = matchCount;
    }

    public List<MockDuplicateApplicationDto> getDuplicateApplications() {
        return duplicateApplications;
    }

    public void setDuplicateApplications(List<MockDuplicateApplicationDto> duplicateApplications) {
        this.duplicateApplications = duplicateApplications;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getSearchPeriod() {
        return searchPeriod;
    }

    public void setSearchPeriod(String searchPeriod) {
        this.searchPeriod = searchPeriod;
    }

    @Override
    public String toString() {
        return "MockDedupeResponseDto{" +
                "dedupeStatus='" + dedupeStatus + '\'' +
                ", matchCount=" + matchCount +
                ", message='" + message + '\'' +
                ", timestamp=" + timestamp +
                ", transactionId='" + transactionId + '\'' +
                ", searchPeriod='" + searchPeriod + '\'' +
                ", duplicateApplicationsCount=" + (duplicateApplications != null ? duplicateApplications.size() : 0) +
                '}';
    }
}
