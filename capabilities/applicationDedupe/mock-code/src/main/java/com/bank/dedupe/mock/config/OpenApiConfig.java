package com.bank.dedupe.mock.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * OpenAPI configuration for Mock External Dedupe service.
 * Configures Swagger UI and API documentation with proper CORS settings.
 */
@Configuration
public class OpenApiConfig implements WebMvcConfigurer {

    @Value("${server.port:8205}")
    private String serverPort;

    @Value("${server.servlet.context-path:/api/application-dedupe/internal}")
    private String contextPath;

    @Bean
    public OpenAPI mockExternalDedupeOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Mock External Dedupe Service API")
                        .description("Mock REST API simulating external deduplication service behavior. " +
                                   "This service provides test endpoints to simulate various deduplication " +
                                   "scenarios including matches, partial matches, errors, and timeouts for " +
                                   "development and testing purposes.")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Banking API Team")
                                .email("<EMAIL>")
                                .url("https://bank.com/api-support"))
                        .license(new License()
                                .name("Proprietary")
                                .url("https://bank.com/license")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + contextPath)
                                .description("Local Development Server"),
                        new Server()
                                .url("http://localhost:8205" + contextPath)
                                .description("Docker Development Server"),
                        new Server()
                                .url("https://mock-api.bank.com" + contextPath)
                                .description("Mock Production Server")
                ));
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
}
