package com.bank.dedupe.mock.controller;

import com.bank.dedupe.mock.dto.MockDedupeRequestDto;
import com.bank.dedupe.mock.dto.MockDedupeResponseDto;
import com.bank.dedupe.mock.service.MockDedupeService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Mock REST Controller for external deduplication service.
 * Simulates the behavior of the real external service for testing.
 */
@RestController
@RequestMapping("/v1")
public class MockDedupeController {

    private static final Logger logger = LoggerFactory.getLogger(MockDedupeController.class);

    private final MockDedupeService mockDedupeService;

    public MockDedupeController(MockDedupeService mockDedupeService) {
        this.mockDedupeService = mockDedupeService;
    }

    /**
     * Mock deduplication check endpoint.
     * 
     * @param request the deduplication request
     * @param bindingResult validation results
     * @param traceId trace ID from header
     * @param referenceNumber reference number from header
     * @param httpRequest HTTP request for audit
     * @return mock deduplication response
     */
    @PostMapping("/duplicate-check")
    public ResponseEntity<MockDedupeResponseDto> checkDuplicates(
            @Valid @RequestBody MockDedupeRequestDto request,
            BindingResult bindingResult,
            @RequestHeader(value = "X-Trace-Id", required = false) String traceId,
            @RequestHeader(value = "X-Reference-Number", required = false) String referenceNumber,
            HttpServletRequest httpRequest) {
        
        // Set up MDC for logging
        if (traceId == null) {
            traceId = UUID.randomUUID().toString();
        }
        MDC.put("traceId", traceId);
        MDC.put("referenceNumber", referenceNumber);
        MDC.put("applicationId", request.getApplicationId());

        try {
            logger.info("Received mock deduplication request for application: {}", request.getApplicationId());
            
            // Validate request
            if (bindingResult.hasErrors()) {
                String validationErrors = bindingResult.getFieldErrors().stream()
                        .map(error -> error.getField() + ": " + error.getDefaultMessage())
                        .collect(Collectors.joining(", "));
                
                logger.warn("Validation errors in mock deduplication request: {}", validationErrors);
                return ResponseEntity.badRequest().build();
            }

            // Simulate processing delay
            try {
                Thread.sleep(100); // 100ms delay to simulate processing
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // Check for specific error scenarios based on input patterns
            checkForErrorScenarios(request);

            // Process the request
            MockDedupeResponseDto response = mockDedupeService.processDeduplicationRequest(request);
            
            logger.info("Mock deduplication request completed for application: {}, status: {}", 
                       request.getApplicationId(), response.getDedupeStatus());

            return ResponseEntity.ok(response);

        } catch (RuntimeException e) {
            logger.error("Mock external service error for application: {}, error: {}", 
                        request.getApplicationId(), e.getMessage());
            
            // Return appropriate error response based on error message
            return handleErrorResponse(e);
            
        } finally {
            MDC.clear();
        }
    }

    /**
     * Health check endpoint for the mock service.
     * 
     * @return health status
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", LocalDateTime.now());
        health.put("service", "mock-external-dedupe-service");
        health.put("version", "1.0.0");
        
        return ResponseEntity.ok(health);
    }

    /**
     * Admin endpoint to view service statistics.
     * 
     * @return service statistics
     */
    @GetMapping("/admin/stats")
    public ResponseEntity<Map<String, Object>> getStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("service", "mock-external-dedupe-service");
        stats.put("uptime", "Available");
        stats.put("timestamp", LocalDateTime.now());
        stats.put("scenarios", Map.of(
            "MATCH_FOUND", "PAN ending with 'A'",
            "NO_MATCH_FOUND", "PAN ending with 'B'",
            "PARTIAL_MATCH", "PAN ending with 'C'",
            "ERROR", "PAN ending with 'D'",
            "TIMEOUT", "Mobile ending with '0000'",
            "AUTH_ERROR", "Email containing 'invalid'"
        ));
        
        return ResponseEntity.ok(stats);
    }

    /**
     * Checks for specific error scenarios based on input patterns.
     * 
     * @param request the request to check
     * @throws RuntimeException if error scenario is detected
     */
    private void checkForErrorScenarios(MockDedupeRequestDto request) {
        // Check mobile number for timeout scenario
        if (request.getMobileNumber() != null && request.getMobileNumber().endsWith("0000")) {
            logger.warn("Simulating timeout scenario for mobile: {}", request.getMobileNumber());
            throw new RuntimeException("Request timeout - dedupe service not responding");
        }
        
        // Check email for authentication error scenario
        if (request.getEmailAddress() != null && request.getEmailAddress().contains("invalid")) {
            logger.warn("Simulating authentication error scenario for email: {}", request.getEmailAddress());
            throw new RuntimeException("Authentication failed with external service");
        }
        
        // Check PAN for system error scenario
        if (request.getPan() != null && request.getPan().endsWith("D")) {
            logger.warn("Simulating system error scenario for PAN: {}", request.getPan());
            throw new RuntimeException("External dedupe service temporarily unavailable");
        }
    }

    /**
     * Handles error responses based on the exception message.
     * 
     * @param e the exception
     * @return appropriate error response
     */
    private ResponseEntity<MockDedupeResponseDto> handleErrorResponse(RuntimeException e) {
        String errorMessage = e.getMessage();
        
        if (errorMessage.contains("timeout")) {
            return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT).build();
        } else if (errorMessage.contains("Authentication failed")) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        } else if (errorMessage.contains("temporarily unavailable")) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
