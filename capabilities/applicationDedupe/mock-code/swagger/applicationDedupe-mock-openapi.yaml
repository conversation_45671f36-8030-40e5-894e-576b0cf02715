openapi: 3.0.3
info:
  title: Mock External Dedupe Service API
  description: |
    Mock REST API simulating external deduplication service behavior. 
    This service provides test endpoints to simulate various deduplication 
    scenarios including matches, partial matches, errors, and timeouts for 
    development and testing purposes.
    
    ## Test Scenarios
    The mock service provides different responses based on input data patterns:
    - **MATCH_FOUND**: PAN ending with 'A' (e.g., **********)
    - **NO_MATCH_FOUND**: PAN ending with 'B' (e.g., **********)
    - **PARTIAL_MATCH**: PAN ending with 'C' (e.g., **********)
    - **ERROR**: PAN ending with 'D' (e.g., **********)
    - **TIMEOUT**: Mobile number ending with '0000' (e.g., **********)
    - **AUTH_ERROR**: Email containing 'invalid' (e.g., <EMAIL>)
    
    ## Features
    - Configurable response delays and error rates
    - Comprehensive audit trail for testing
    - Admin endpoints for runtime configuration
    - PII data masking in logs and responses
    - Trace ID propagation for request tracking
    
    ## Authentication
    No authentication required for testing purposes.
  version: 1.0.0
  contact:
    name: Banking API Team
    email: <EMAIL>
    url: https://bank.com/api-support
  license:
    name: Proprietary
    url: https://bank.com/license

servers:
  - url: http://localhost:8205/api/application-dedupe/internal
    description: Local Development Server
  - url: http://localhost:8205/api/application-dedupe/internal
    description: Docker Development Server
  - url: https://mock-api.bank.com/api/application-dedupe/internal
    description: Mock Production Server

paths:
  /v1/duplicate-check:
    post:
      tags:
        - Mock Deduplication
      summary: Mock deduplication check endpoint
      description: |
        Simulates external deduplication service behavior with configurable responses
        based on input data patterns. Returns different scenarios for testing purposes.
      operationId: mockCheckDuplicates
      parameters:
        - name: X-Trace-Id
          in: header
          required: false
          description: Trace ID for request tracking
          schema:
            type: string
            example: "trace-12345-67890"
        - name: X-Reference-Number
          in: header
          required: false
          description: Reference number for the request
          schema:
            type: string
            example: "REF-2025-001234"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MockDedupeRequest'
            examples:
              match_found_scenario:
                summary: Scenario that returns MATCH_FOUND
                value:
                  applicationId: "APP12345"
                  dedupedCustomerName: "ALICE JOHNSON"
                  mobileNumber: "**********"
                  emailAddress: "<EMAIL>"
                  pan: "**********"
                  aadhaarNumber: "XXXX-XXXX-1234"
                  dateOfBirth: "1990-05-20"
                  applicationSubmissionDate: "2025-06-04"
                  deduplicationPeriod: "90_DAYS"
              no_match_scenario:
                summary: Scenario that returns NO_MATCH_FOUND
                value:
                  applicationId: "APP67890"
                  dedupedCustomerName: "JOHN DOE"
                  mobileNumber: "9123456789"
                  emailAddress: "<EMAIL>"
                  pan: "**********"
                  aadhaarNumber: "YYYY-YYYY-5678"
                  dateOfBirth: "1985-03-15"
                  applicationSubmissionDate: "2025-06-04"
                  deduplicationPeriod: "90_DAYS"
              partial_match_scenario:
                summary: Scenario that returns PARTIAL_MATCH
                value:
                  applicationId: "APP33333"
                  dedupedCustomerName: "JANE SMITH"
                  mobileNumber: "9555666777"
                  emailAddress: "<EMAIL>"
                  pan: "**********"
                  aadhaarNumber: "ZZZZ-ZZZZ-9012"
                  dateOfBirth: "1992-08-10"
                  applicationSubmissionDate: "2025-06-04"
                  deduplicationPeriod: "90_DAYS"
              error_scenario:
                summary: Scenario that returns ERROR
                value:
                  applicationId: "APP44444"
                  dedupedCustomerName: "ERROR TEST"
                  mobileNumber: "9888999000"
                  emailAddress: "<EMAIL>"
                  pan: "**********"
                  aadhaarNumber: "AAAA-AAAA-3456"
                  dateOfBirth: "1988-12-25"
                  applicationSubmissionDate: "2025-06-04"
                  deduplicationPeriod: "90_DAYS"
              timeout_scenario:
                summary: Scenario that simulates timeout
                value:
                  applicationId: "APP55555"
                  dedupedCustomerName: "TIMEOUT TEST"
                  mobileNumber: "**********"
                  emailAddress: "<EMAIL>"
                  pan: "**********"
                  aadhaarNumber: "BBBB-BBBB-7890"
                  dateOfBirth: "1995-01-01"
                  applicationSubmissionDate: "2025-06-04"
                  deduplicationPeriod: "90_DAYS"
      responses:
        '200':
          description: Mock deduplication response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockDedupeResponse'
              examples:
                match_found:
                  summary: Mock response with duplicates found
                  value:
                    dedupeStatus: "MATCH_FOUND"
                    matchCount: 2
                    duplicateApplications:
                      - applicationId: "MOCK-APP-001"
                        submissionDate: "2025-05-15"
                        matchedFields: ["pan", "mobileNumber", "emailAddress"]
                        matchScore: 95.5
                        previousApplicationStatus: "APPROVED"
                        customerName: "ALICE JOHNSON"
                        matchNotes: "High confidence match - PAN and mobile number exact match"
                      - applicationId: "MOCK-APP-002"
                        submissionDate: "2025-04-20"
                        matchedFields: ["pan", "aadhaarNumber"]
                        matchScore: 88.2
                        previousApplicationStatus: "REJECTED"
                        customerName: "ALICE JOHNSON"
                        matchNotes: "Strong identity document match"
                    message: "Mock service: 2 duplicate applications found"
                    timestamp: "2025-06-04T10:30:00Z"
                    transactionId: "TXN-MOCK-001234"
                    searchPeriod: "90_DAYS"
                no_match_found:
                  summary: Mock response with no duplicates
                  value:
                    dedupeStatus: "NO_MATCH_FOUND"
                    matchCount: 0
                    duplicateApplications: []
                    message: "Mock service: No duplicate applications found"
                    timestamp: "2025-06-04T10:35:00Z"
                    transactionId: "TXN-MOCK-001235"
                    searchPeriod: "90_DAYS"
                partial_match:
                  summary: Mock response with partial match
                  value:
                    dedupeStatus: "PARTIAL_MATCH"
                    matchCount: 1
                    duplicateApplications:
                      - applicationId: "MOCK-APP-003"
                        submissionDate: "2025-05-25"
                        matchedFields: ["mobileNumber"]
                        matchScore: 65.0
                        previousApplicationStatus: "PENDING"
                        customerName: "JANE SMITH SIMILAR"
                        matchNotes: "Partial match - mobile number only, requires manual review"
                    message: "Mock service: Potential duplicate found - manual review required"
                    timestamp: "2025-06-04T10:40:00Z"
                    transactionId: "TXN-MOCK-001236"
                    searchPeriod: "90_DAYS"
        '400':
          description: Bad request - validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockErrorResponse'
              examples:
                validation_error:
                  summary: Mock validation error
                  value:
                    dedupeStatus: "ERROR"
                    matchCount: 0
                    message: "Mock service: Validation error - invalid input data"
                    timestamp: "2025-06-04T10:45:00Z"
                    transactionId: "TXN-MOCK-ERROR-001"
        '401':
          description: Authentication error (simulated)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockErrorResponse'
              examples:
                auth_error:
                  summary: Mock authentication error
                  value:
                    dedupeStatus: "ERROR"
                    matchCount: 0
                    message: "Mock service: Authentication failed - invalid credentials"
                    timestamp: "2025-06-04T10:50:00Z"
                    transactionId: "TXN-MOCK-AUTH-ERROR"
        '408':
          description: Request timeout (simulated)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockErrorResponse'
              examples:
                timeout_error:
                  summary: Mock timeout error
                  value:
                    dedupeStatus: "ERROR"
                    matchCount: 0
                    message: "Mock service: Request timeout - service temporarily unavailable"
                    timestamp: "2025-06-04T10:55:00Z"
                    transactionId: "TXN-MOCK-TIMEOUT"
        '500':
          description: Internal server error (simulated)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockErrorResponse'
              examples:
                server_error:
                  summary: Mock server error
                  value:
                    dedupeStatus: "ERROR"
                    matchCount: 0
                    message: "Mock service: Internal server error - service temporarily unavailable"
                    timestamp: "2025-06-04T11:00:00Z"
                    transactionId: "TXN-MOCK-SERVER-ERROR"

  /v1/health:
    get:
      tags:
        - Health
      summary: Mock service health check
      description: Returns the health status of the mock deduplication service
      operationId: mockHealthCheck
      responses:
        '200':
          description: Mock service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockHealthStatus'
              examples:
                healthy:
                  summary: Healthy mock service
                  value:
                    status: "UP"
                    timestamp: "2025-06-04T10:30:00"
                    service: "mock-external-dedupe-service"
                    version: "1.0.0"

  /v1/admin/stats:
    get:
      tags:
        - Admin
      summary: Get mock service statistics
      description: Returns statistics and configuration information for the mock service
      operationId: getMockStats
      responses:
        '200':
          description: Mock service statistics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockStats'
              examples:
                stats:
                  summary: Mock service statistics
                  value:
                    service: "mock-external-dedupe-service"
                    uptime: "Available"
                    timestamp: "2025-06-04T10:30:00"
                    scenarios:
                      MATCH_FOUND: "PAN ending with 'A'"
                      NO_MATCH_FOUND: "PAN ending with 'B'"
                      PARTIAL_MATCH: "PAN ending with 'C'"
                      ERROR: "PAN ending with 'D'"
                      TIMEOUT: "Mobile ending with '0000'"
                      AUTH_ERROR: "Email containing 'invalid'"

  /v1/application-dedupe/mock/health:
    get:
      tags:
        - Health
      summary: Detailed mock health check
      description: Returns detailed health status for the mock application dedupe service
      operationId: detailedMockHealthCheck
      responses:
        '200':
          description: Detailed mock health status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockHealthStatus'
              examples:
                detailed_health:
                  summary: Detailed mock health status
                  value:
                    status: "UP"
                    service: "Application Dedupe Mock"
                    timestamp: "2025-06-04T10:30:00"
                    version: "1.0.0"

components:
  schemas:
    MockDedupeRequest:
      type: object
      required:
        - applicationId
        - dedupedCustomerName
        - mobileNumber
        - emailAddress
        - pan
        - aadhaarNumber
        - dateOfBirth
        - applicationSubmissionDate
      properties:
        applicationId:
          type: string
          description: Unique identifier for the loan application
          example: "APP12345"
        dedupedCustomerName:
          type: string
          description: Standardized customer name for deduplication
          example: "ALICE JOHNSON"
        mobileNumber:
          type: string
          description: Customer's mobile number
          example: "**********"
        emailAddress:
          type: string
          format: email
          description: Customer's email address
          example: "<EMAIL>"
        pan:
          type: string
          description: Customer's PAN number
          example: "**********"
        aadhaarNumber:
          type: string
          description: Customer's Aadhaar number (masked)
          example: "XXXX-XXXX-1234"
        dateOfBirth:
          type: string
          format: date
          description: Customer's date of birth
          example: "1990-05-20"
        applicationSubmissionDate:
          type: string
          format: date
          description: Date when the application was submitted
          example: "2025-06-04"
        deduplicationPeriod:
          type: string
          description: Period to search for duplicates
          example: "90_DAYS"

    MockDedupeResponse:
      type: object
      properties:
        dedupeStatus:
          type: string
          enum: ["MATCH_FOUND", "NO_MATCH_FOUND", "PARTIAL_MATCH", "ERROR"]
          description: Status of the mock deduplication check
          example: "MATCH_FOUND"
        matchCount:
          type: integer
          minimum: 0
          description: Number of duplicate applications found
          example: 2
        duplicateApplications:
          type: array
          items:
            $ref: '#/components/schemas/MockDuplicateApplication'
          description: List of mock duplicate applications
        message:
          type: string
          description: Mock service message
          example: "Mock service: 2 duplicate applications found"
        timestamp:
          type: string
          format: date-time
          description: Mock response timestamp
          example: "2025-06-04T10:30:00Z"
        transactionId:
          type: string
          description: Mock transaction ID
          example: "TXN-MOCK-001234"
        searchPeriod:
          type: string
          description: Search period used
          example: "90_DAYS"

    MockDuplicateApplication:
      type: object
      properties:
        applicationId:
          type: string
          description: Mock duplicate application ID
          example: "MOCK-APP-001"
        submissionDate:
          type: string
          format: date
          description: Mock submission date
          example: "2025-05-15"
        matchedFields:
          type: array
          items:
            type: string
          description: Fields that matched in mock scenario
          example: ["pan", "mobileNumber", "emailAddress"]
        matchScore:
          type: number
          format: double
          minimum: 0
          maximum: 100
          description: Mock confidence score
          example: 95.5
        previousApplicationStatus:
          type: string
          description: Mock previous application status
          example: "APPROVED"
        customerName:
          type: string
          description: Mock customer name
          example: "ALICE JOHNSON"
        matchNotes:
          type: string
          description: Mock match notes
          example: "High confidence match - PAN and mobile number exact match"

    MockErrorResponse:
      type: object
      properties:
        dedupeStatus:
          type: string
          enum: ["ERROR"]
          example: "ERROR"
        matchCount:
          type: integer
          example: 0
        message:
          type: string
          description: Mock error message
          example: "Mock service: Validation error - invalid input data"
        timestamp:
          type: string
          format: date-time
          description: Mock error timestamp
          example: "2025-06-04T10:45:00Z"
        transactionId:
          type: string
          description: Mock transaction ID for error
          example: "TXN-MOCK-ERROR-001"

    MockHealthStatus:
      type: object
      properties:
        status:
          type: string
          enum: ["UP", "DOWN"]
          description: Mock service health status
          example: "UP"
        service:
          type: string
          description: Mock service name
          example: "mock-external-dedupe-service"
        timestamp:
          type: string
          format: date-time
          description: Health check timestamp
          example: "2025-06-04T10:30:00"
        version:
          type: string
          description: Mock service version
          example: "1.0.0"

    MockStats:
      type: object
      properties:
        service:
          type: string
          description: Service name
          example: "mock-external-dedupe-service"
        uptime:
          type: string
          description: Service uptime status
          example: "Available"
        timestamp:
          type: string
          format: date-time
          description: Statistics timestamp
          example: "2025-06-04T10:30:00"
        scenarios:
          type: object
          description: Available test scenarios
          properties:
            MATCH_FOUND:
              type: string
              example: "PAN ending with 'A'"
            NO_MATCH_FOUND:
              type: string
              example: "PAN ending with 'B'"
            PARTIAL_MATCH:
              type: string
              example: "PAN ending with 'C'"
            ERROR:
              type: string
              example: "PAN ending with 'D'"
            TIMEOUT:
              type: string
              example: "Mobile ending with '0000'"
            AUTH_ERROR:
              type: string
              example: "Email containing 'invalid'"

tags:
  - name: Mock Deduplication
    description: Mock deduplication operations for testing
  - name: Admin
    description: Mock service administration and statistics
  - name: Health
    description: Mock service health and monitoring endpoints
