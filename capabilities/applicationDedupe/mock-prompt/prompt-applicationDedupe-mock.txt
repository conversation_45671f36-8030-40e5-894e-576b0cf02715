Application Dedupe Mock API Service - Specification
======================================================

Overview
--------
A standalone Spring Boot application designed to simulate the behavior of the real Application Dedupe Service in development and testing environments. This mock service helps verify the main application's response handling for different deduplication scenarios without requiring live connectivity to the real external dedupe service.

Purpose
-------
- Simulate Application Dedupe Service responses for testing duplicate detection logic
- Enable behavior validation under match found, no match, incomplete data, and error scenarios
- Support RBI compliance testing by providing comprehensive audit trails
- Offer traceability, configurability, and isolated deployment from production services

Technical Requirements
----------------------
- Spring Boot version: 3.2.3 (or compatible)
- Java version: 21
- Server Port: 8084
- OpenAPI/Swagger documentation included
- Basic Authentication (optional, for simulating secure integrations)
- In-memory storage of requests for auditing/testing
- Masking of sensitive borrower inputs in logs (PAN, Aadhaar, Mobile, Email)
- Unique trace ID included in all transactions

Implementation Details
----------------------
- DTOs
- Enums (MATCH_FOUND, NO_MATCH_FOUND, ERROR, PARTIAL_MATCH)
- Controllers
- Services (include mock logic as per spec)
- Utility Classes (logging with PII masking)
- Sample application.yml
- Sample Swagger config
- README with setup, test data, and usage

API Endpoint
------------
1. Application Deduplication Check Endpoint
   - Path: /api/application-dedupe/internal/v1/duplicate-check
   - Method: POST
   - Request Body:
     {
       "applicationId": "APP12345",
       "dedupedCustomerName": "ALICE JOHNSON",
       "mobileNumber": "9876543210",
       "emailAddress": "<EMAIL>",
       "pan": "**********",
       "aadhaarNumber": "XXXX-XXXX-1234",
       "dateOfBirth": "1990-05-20",
       "applicationSubmissionDate": "2025-06-04",
       "deduplicationPeriod": "90_DAYS"
     }
   - Response Body (Match Found example):
     {
       "dedupeStatus": "MATCH_FOUND",
       "matchCount": 2,
       "duplicateApplications": [
         {
           "applicationId": "APP11223",
           "submissionDate": "2025-05-15",
           "matchedFields": ["PAN", "MOBILE", "EMAIL"],
           "matchScore": 0.95,
           "previousApplicationStatus": "APPROVED"
         },
         {
           "applicationId": "APP11567",
           "submissionDate": "2025-04-22",
           "matchedFields": ["PAN", "AADHAAR"],
           "matchScore": 0.87,
           "previousApplicationStatus": "PENDING"
         }
       ],
       "timestamp": "2025-06-04T10:00:00Z",
       "transactionId": "TXN789456",
       "searchPeriod": "90_DAYS"
     }
   - Response Body (No Match example):
     {
       "dedupeStatus": "NO_MATCH_FOUND",
       "matchCount": 0,
       "message": "No duplicates found in the last 90 days",
       "timestamp": "2025-06-04T10:00:00Z",
       "transactionId": "TXN789457",
       "searchPeriod": "90_DAYS"
     }

Simulated Behavior Logic
-------------------------
- PAN ends with 'A' → Simulate match found scenario
  - Dedupe Status: "MATCH_FOUND"
  - Match Count: 2
  - Generate 2 duplicate applications with high match scores (0.85-0.95)
  - Matched Fields: ["PAN", "MOBILE", "EMAIL"] for first match, ["PAN", "AADHAAR"] for second

- PAN ends with 'B' → Simulate no match scenario
  - Dedupe Status: "NO_MATCH_FOUND"
  - Match Count: 0
  - Message: "No duplicates found in the last X days"

- PAN ends with 'C' → Simulate partial match scenario
  - Dedupe Status: "PARTIAL_MATCH"
  - Match Count: 1
  - Generate 1 duplicate with medium match score (0.65-0.75)
  - Matched Fields: ["MOBILE", "EMAIL"] only

- PAN ends with 'D' → Simulate system error
  - Respond with HTTP 500
  - Include error message: "External dedupe service temporarily unavailable"

- Mobile ends with '0000' → Simulate timeout scenario
  - Respond with HTTP 408
  - Include error message: "Request timeout - dedupe service not responding"

- Email contains 'invalid' → Simulate authentication error
  - Respond with HTTP 401
  - Include error message: "Authentication failed with external service"

Security
--------
- Mask sensitive fields (PAN, Aadhaar, Mobile, Email) in all logs
- Use a unique "X-Trace-Id" in request/response headers
- Optional basic authentication to mimic production API protections
- Implement request/response encryption simulation flags

Admin & Configuration Interface
-------------------------------
- GET  /api/application-dedupe/internal/v1/admin/requests        → View masked request history
- POST /api/application-dedupe/internal/v1/admin/config          → Modify response behaviors (match scores, duplicate counts)
- POST /api/application-dedupe/internal/v1/admin/reset           → Reset history and configurations
- GET  /api/application-dedupe/internal/v1/admin/stats           → View deduplication statistics
- POST /api/application-dedupe/internal/v1/admin/scenario        → Create custom test scenarios

Service Layer Components
------------------------
- MockDedupeService: Core logic for duplicate detection simulation
- MockConfigService: Runtime control of matching logic and response patterns
- TraceLoggerService: Manages traceable, masked logging with audit trails
- DedupeScenarioService: Handles different test scenarios and edge cases
- AuditTrailService: Maintains comprehensive audit records for compliance

Testing Strategy
----------------
- Test various PAN suffixes ('A', 'B', 'C', 'D') using Swagger/Postman
- Test mobile number patterns (ending with '0000') for timeout scenarios
- Test email patterns (containing 'invalid') for authentication errors
- Validate correctness of:
  - Match found responses with duplicate application details
  - No match responses with appropriate messaging
  - Partial match scenarios with medium confidence scores
  - Error responses with proper HTTP status codes
- Check trace ID propagation and comprehensive log masking
- Verify audit trail creation and data persistence
- Use admin endpoints to override behaviors and create custom scenarios
- Test deduplication period variations (30_DAYS, 90_DAYS, 180_DAYS, 1_YEAR)

Data Models
-----------
- DedupeRequest: Input model with all application details
- DedupeResponse: Output model with status and duplicate details
- DuplicateApplication: Model for individual duplicate records
- AuditRecord: Model for compliance and traceability
- ConfigurationSettings: Model for runtime behavior control

Response Scenarios
------------------
- MATCH_FOUND: High confidence duplicates with detailed match information
- NO_MATCH_FOUND: Clean applications with no duplicates in specified period
- PARTIAL_MATCH: Low-medium confidence matches requiring manual review
- ERROR: Various error conditions (timeout, authentication, service unavailable)
- INCOMPLETE_DATA: Missing required fields in external service response

README Deliverables
-------------------
- Step-by-step setup for running the mock dedupe service
- Sample request and expected response examples for all scenarios
- Admin interface usage guide with configuration options
- Description of all configurable behaviors and test patterns
- Swagger URL for interactive testing
- RBI compliance features and audit trail explanation
- Performance testing guidelines and bulk simulation setup

Extensibility Suggestions
-------------------------
- Add machine learning simulation for dynamic match scoring
- Implement configurable duplicate generation patterns
- Add fraud detection simulation capabilities
- Integrate with test data generators for realistic customer profiles
- Support for batch deduplication requests
- Add real-time duplicate monitoring dashboard
- Implement webhook notifications for match events
- Support for custom matching algorithms configuration