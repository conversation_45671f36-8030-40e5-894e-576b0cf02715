server:
  port: 0

spring:
  application:
    name: application-dedupe-service-test
  
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  flyway:
    enabled: false
  
  jackson:
    serialization:
      write-dates-as-timestamps: false

# External Service Configuration for Testing
external:
  dedupe-service:
    base-url: http://localhost:8084/api/application-dedupe/internal/v1
    endpoint: /duplicate-check
    timeout: 5s
    retry:
      max-attempts: 2
      backoff-delay: 100ms
      max-backoff-delay: 1s
    auth:
      enabled: false

# Application Configuration for Testing
app:
  dedupe:
    default-period: 90_DAYS
    max-period: 1_YEAR
    audit:
      retention-days: 30
      enable-detailed-audit: true
      enable-performance-tracking: true
      enable-compliance-checks: true
    logging:
      mask-sensitive-data: true
      include-request-response: true
      enable-structured-logging: true
      log-level: DEBUG
      enable-metrics: true

# Logging Configuration for Testing
logging:
  level:
    com.bank.dedupe: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
