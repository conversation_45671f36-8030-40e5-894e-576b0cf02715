package com.bank.dedupe.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for ReferenceNumberGenerator.
 * Tests reference number generation and validation functionality.
 */
class ReferenceNumberGeneratorTest {

    private ReferenceNumberGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new ReferenceNumberGenerator();
        generator.resetSequence(); // Reset for consistent testing
    }

    @Test
    void testGenerateReferenceNumber_ValidFormat() {
        // When
        String referenceNumber = generator.generateReferenceNumber();
        
        // Then
        assertNotNull(referenceNumber);
        assertTrue(referenceNumber.startsWith("DDR-"));
        assertTrue(generator.isValidReferenceNumber(referenceNumber));
        
        // Check format: DDR-YYYYMMDD-HHMMSS-SEQUENCE
        String[] parts = referenceNumber.split("-");
        assertEquals(4, parts.length);
        assertEquals("DDR", parts[0]);
        assertEquals(8, parts[1].length()); // Date part
        assertEquals(6, parts[2].length()); // Time part
        assertEquals(6, parts[3].length()); // Sequence part
    }

    @Test
    void testGenerateReferenceNumber_UniqueNumbers() {
        // When
        String ref1 = generator.generateReferenceNumber();
        String ref2 = generator.generateReferenceNumber();
        
        // Then
        assertNotEquals(ref1, ref2);
        assertTrue(generator.isValidReferenceNumber(ref1));
        assertTrue(generator.isValidReferenceNumber(ref2));
    }

    @Test
    void testGenerateReferenceNumber_CustomPrefix() {
        // Given
        String customPrefix = "TEST";
        
        // When
        String referenceNumber = generator.generateReferenceNumber(customPrefix);
        
        // Then
        assertNotNull(referenceNumber);
        assertTrue(referenceNumber.startsWith("TEST-"));
        
        String[] parts = referenceNumber.split("-");
        assertEquals(4, parts.length);
        assertEquals("TEST", parts[0]);
    }

    @Test
    void testIsValidReferenceNumber_ValidFormats_ReturnsTrue() {
        // Given
        String validRef = "DDR-20250604-123456-000001";
        
        // When & Then
        assertTrue(generator.isValidReferenceNumber(validRef));
    }

    @Test
    void testIsValidReferenceNumber_InvalidFormats_ReturnsFalse() {
        // Test various invalid formats
        assertFalse(generator.isValidReferenceNumber(null));
        assertFalse(generator.isValidReferenceNumber(""));
        assertFalse(generator.isValidReferenceNumber("INVALID"));
        assertFalse(generator.isValidReferenceNumber("DDR-20250604-123456")); // Missing sequence
        assertFalse(generator.isValidReferenceNumber("DDR-2025060-123456-000001")); // Invalid date length
        assertFalse(generator.isValidReferenceNumber("DDR-20250604-12345-000001")); // Invalid time length
        assertFalse(generator.isValidReferenceNumber("DDR-20250604-123456-00001")); // Invalid sequence length
        assertFalse(generator.isValidReferenceNumber("XYZ-20250604-123456-000001")); // Wrong prefix
    }

    @Test
    void testExtractDatePart_ValidReference_ReturnsDate() {
        // Given
        String referenceNumber = "DDR-20250604-123456-000001";
        
        // When
        String datePart = generator.extractDatePart(referenceNumber);
        
        // Then
        assertEquals("20250604", datePart);
    }

    @Test
    void testExtractDatePart_InvalidReference_ReturnsNull() {
        // Given
        String invalidReference = "INVALID";
        
        // When
        String datePart = generator.extractDatePart(invalidReference);
        
        // Then
        assertNull(datePart);
    }

    @Test
    void testExtractTimePart_ValidReference_ReturnsTime() {
        // Given
        String referenceNumber = "DDR-20250604-123456-000001";
        
        // When
        String timePart = generator.extractTimePart(referenceNumber);
        
        // Then
        assertEquals("123456", timePart);
    }

    @Test
    void testExtractSequenceNumber_ValidReference_ReturnsSequence() {
        // Given
        String referenceNumber = "DDR-20250604-123456-000001";
        
        // When
        long sequence = generator.extractSequenceNumber(referenceNumber);
        
        // Then
        assertEquals(1, sequence);
    }

    @Test
    void testExtractSequenceNumber_InvalidReference_ReturnsMinusOne() {
        // Given
        String invalidReference = "INVALID";
        
        // When
        long sequence = generator.extractSequenceNumber(invalidReference);
        
        // Then
        assertEquals(-1, sequence);
    }

    @Test
    void testGenerateBatch_ValidCount_ReturnsUniqueReferences() {
        // Given
        int count = 5;
        
        // When
        String[] references = generator.generateBatch(count);
        
        // Then
        assertEquals(count, references.length);
        
        // Check all are unique and valid
        for (int i = 0; i < references.length; i++) {
            assertTrue(generator.isValidReferenceNumber(references[i]));
            
            // Check uniqueness
            for (int j = i + 1; j < references.length; j++) {
                assertNotEquals(references[i], references[j]);
            }
        }
    }

    @Test
    void testGenerateBatch_ZeroCount_ReturnsEmptyArray() {
        // When
        String[] references = generator.generateBatch(0);
        
        // Then
        assertEquals(0, references.length);
    }

    @Test
    void testGenerateBatch_NegativeCount_ReturnsEmptyArray() {
        // When
        String[] references = generator.generateBatch(-1);
        
        // Then
        assertEquals(0, references.length);
    }

    @Test
    void testGenerateTestReferenceNumber_SpecificTimestamp_UsesTimestamp() {
        // Given
        LocalDateTime timestamp = LocalDateTime.of(2025, 6, 4, 10, 30, 45);
        
        // When
        String referenceNumber = generator.generateTestReferenceNumber(timestamp);
        
        // Then
        assertTrue(referenceNumber.contains("20250604"));
        assertTrue(referenceNumber.contains("103045"));
        assertTrue(generator.isValidReferenceNumber(referenceNumber));
    }

    @Test
    void testGetCurrentSequence_AfterGeneration_ReturnsCorrectValue() {
        // Given
        generator.resetSequence();
        long initialSequence = generator.getCurrentSequence();
        
        // When
        generator.generateReferenceNumber();
        generator.generateReferenceNumber();
        
        // Then
        assertEquals(initialSequence + 2, generator.getCurrentSequence());
    }

    @Test
    void testResetSequence_ResetsToOne() {
        // Given
        generator.generateReferenceNumber();
        generator.generateReferenceNumber();
        
        // When
        generator.resetSequence();
        
        // Then
        assertEquals(1, generator.getCurrentSequence());
    }

    @Test
    void testSequenceIncrement_ConsecutiveGeneration_IncrementsCorrectly() {
        // Given
        generator.resetSequence();
        
        // When
        String ref1 = generator.generateReferenceNumber();
        String ref2 = generator.generateReferenceNumber();
        String ref3 = generator.generateReferenceNumber();
        
        // Then
        assertEquals(1, generator.extractSequenceNumber(ref1));
        assertEquals(2, generator.extractSequenceNumber(ref2));
        assertEquals(3, generator.extractSequenceNumber(ref3));
    }
}
