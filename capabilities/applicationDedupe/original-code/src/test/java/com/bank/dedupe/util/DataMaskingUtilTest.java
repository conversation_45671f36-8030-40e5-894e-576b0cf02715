package com.bank.dedupe.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for DataMaskingUtil.
 * Tests data masking functionality for PII compliance.
 */
class DataMaskingUtilTest {

    @Test
    void testMaskPan_ValidPan_MaskedCorrectly() {
        // Given
        String pan = "**********";
        
        // When
        String masked = DataMaskingUtil.maskPan(pan);
        
        // Then
        assertEquals("A********A", masked);
    }

    @Test
    void testMaskPan_ShortPan_ReturnsMaskValue() {
        // Given
        String pan = "AB";
        
        // When
        String masked = DataMaskingUtil.maskPan(pan);
        
        // Then
        assertEquals("***MASKED***", masked);
    }

    @Test
    void testMaskAadhaar_ValidAadhaar_MaskedCorrectly() {
        // Given
        String aadhaar = "************";
        
        // When
        String masked = DataMaskingUtil.maskAadhaar(aadhaar);
        
        // Then
        assertEquals("XXXX-XXXX-9012", masked);
    }

    @Test
    void testMaskAadhaar_FormattedAadhaar_MaskedCorrectly() {
        // Given
        String aadhaar = "1234-5678-9012";
        
        // When
        String masked = DataMaskingUtil.maskAadhaar(aadhaar);
        
        // Then
        assertEquals("XXXX-XXXX-9012", masked);
    }

    @Test
    void testMaskMobileNumber_ValidMobile_MaskedCorrectly() {
        // Given
        String mobile = "9876543210";
        
        // When
        String masked = DataMaskingUtil.maskMobileNumber(mobile);
        
        // Then
        assertEquals("******3210", masked);
    }

    @Test
    void testMaskEmailAddress_ValidEmail_MaskedCorrectly() {
        // Given
        String email = "<EMAIL>";
        
        // When
        String masked = DataMaskingUtil.maskEmailAddress(email);
        
        // Then
        assertEquals("*************@email.com", masked);
    }

    @Test
    void testMaskEmailAddress_InvalidEmail_ReturnsMaskValue() {
        // Given
        String email = "invalid-email";
        
        // When
        String masked = DataMaskingUtil.maskEmailAddress(email);
        
        // Then
        assertEquals("***MASKED***", masked);
    }

    @Test
    void testPartiallyMaskString_ValidString_MaskedCorrectly() {
        // Given
        String value = "APP12345";
        
        // When
        String masked = DataMaskingUtil.partiallyMaskString(value);
        
        // Then
        assertEquals("AP****45", masked);
    }

    @Test
    void testPartiallyMaskString_ShortString_ReturnsMaskValue() {
        // Given
        String value = "ABC";
        
        // When
        String masked = DataMaskingUtil.partiallyMaskString(value);
        
        // Then
        assertEquals("***MASKED***", masked);
    }

    @Test
    void testMaskJsonData_ValidJson_SensitiveFieldsMasked() {
        // Given
        String json = "{\"applicationId\":\"APP123\",\"pan\":\"**********\",\"mobileNumber\":\"9876543210\"}";
        
        // When
        String masked = DataMaskingUtil.maskJsonData(json);
        
        // Then
        assertTrue(masked.contains("***MASKED***"));
        assertTrue(masked.contains("APP123")); // Non-sensitive field should remain
        assertFalse(masked.contains("**********")); // Sensitive field should be masked
        assertFalse(masked.contains("9876543210")); // Sensitive field should be masked
    }

    @Test
    void testMaskJsonData_InvalidJson_ReturnsPlaceholder() {
        // Given
        String invalidJson = "invalid json";
        
        // When
        String masked = DataMaskingUtil.maskJsonData(invalidJson);
        
        // Then
        assertEquals("{\"data\":\"***MASKED***\"}", masked);
    }

    @Test
    void testIsSensitiveField_SensitiveFields_ReturnsTrue() {
        // Test various sensitive fields
        assertTrue(DataMaskingUtil.isSensitiveField("pan"));
        assertTrue(DataMaskingUtil.isSensitiveField("PAN"));
        assertTrue(DataMaskingUtil.isSensitiveField("aadhaarNumber"));
        assertTrue(DataMaskingUtil.isSensitiveField("mobileNumber"));
        assertTrue(DataMaskingUtil.isSensitiveField("emailAddress"));
        assertTrue(DataMaskingUtil.isSensitiveField("password"));
    }

    @Test
    void testIsSensitiveField_NonSensitiveFields_ReturnsFalse() {
        // Test non-sensitive fields
        assertFalse(DataMaskingUtil.isSensitiveField("applicationId"));
        assertFalse(DataMaskingUtil.isSensitiveField("customerName"));
        assertFalse(DataMaskingUtil.isSensitiveField("dateOfBirth"));
        assertFalse(DataMaskingUtil.isSensitiveField("status"));
    }

    @Test
    void testMaskObjectString_ContainsSensitiveData_MaskedCorrectly() {
        // Given
        String objectString = "DedupeRequest{applicationId='APP123', pan='**********', mobileNumber='9876543210'}";
        
        // When
        String masked = DataMaskingUtil.maskObjectString(objectString);
        
        // Then
        assertTrue(masked.contains("***MASKED***"));
        assertTrue(masked.contains("APP123")); // Non-sensitive should remain
        assertFalse(masked.contains("**********")); // Sensitive should be masked
    }

    @Test
    void testCreateMaskedRequest_ValidObject_ReturnsMaskedJson() {
        // Given
        TestRequest request = new TestRequest("APP123", "**********", "9876543210");
        
        // When
        String masked = DataMaskingUtil.createMaskedRequest(request);
        
        // Then
        assertNotNull(masked);
        assertTrue(masked.contains("***MASKED***"));
    }

    @Test
    void testGetMaskValue_ReturnsCorrectValue() {
        // When & Then
        assertEquals("***MASKED***", DataMaskingUtil.getMaskValue());
    }

    // Test helper class
    private static class TestRequest {
        private String applicationId;
        private String pan;
        private String mobileNumber;

        public TestRequest(String applicationId, String pan, String mobileNumber) {
            this.applicationId = applicationId;
            this.pan = pan;
            this.mobileNumber = mobileNumber;
        }

        public String getApplicationId() { return applicationId; }
        public String getPan() { return pan; }
        public String getMobileNumber() { return mobileNumber; }
    }
}
