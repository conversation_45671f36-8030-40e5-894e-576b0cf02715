package com.bank.dedupe.service;

import com.bank.dedupe.dto.DedupeRequestDto;
import com.bank.dedupe.dto.DedupeResponseDto;
import com.bank.dedupe.entity.AuditLogEntity;
import com.bank.dedupe.entity.DedupeRequestEntity;
import com.bank.dedupe.enums.DedupeStatus;
import com.bank.dedupe.repository.AuditLogRepository;
import com.bank.dedupe.repository.DedupeRequestRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive test suite for DedupeService.
 * Tests all business scenarios including success, failure, and edge cases.
 * Uses functional testing approach without mocks.
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "spring.flyway.enabled=false",
    "external.dedupe-service.base-url=http://localhost:8084/api/application-dedupe/internal/v1",
    "external.dedupe-service.timeout=5s"
})
class DedupeServiceTest {

    @Autowired
    private DedupeService dedupeService;

    @Autowired
    private DedupeRequestRepository dedupeRequestRepository;

    @Autowired
    private AuditLogRepository auditLogRepository;

    @Test
    @Transactional
    void testProcessDeduplicationRequest_ValidRequest_CreatesAuditTrail() {
        // Given
        DedupeRequestDto requestDto = createTestRequestDto("**********"); // No match scenario

        // When
        DedupeResponseDto response = dedupeService.processDeduplicationRequest(requestDto);

        // Then
        assertNotNull(response);
        assertNotNull(response.getReferenceNumber());
        assertEquals(requestDto.getApplicationId(), response.getApplicationId());

        // Verify database persistence
        Optional<DedupeRequestEntity> savedEntity = dedupeRequestRepository
                .findByReferenceNumber(response.getReferenceNumber());
        assertTrue(savedEntity.isPresent());
        assertEquals(requestDto.getApplicationId(), savedEntity.get().getApplicationId());
        assertEquals(requestDto.getDedupedCustomerName(), savedEntity.get().getDedupedCustomerName());

        // Verify audit trail creation
        List<AuditLogEntity> auditLogs = auditLogRepository
                .findByReferenceNumber(response.getReferenceNumber());
        assertFalse(auditLogs.isEmpty());

        // Should have at least request initiated and response logs
        boolean hasRequestInitiated = auditLogs.stream()
                .anyMatch(log -> "REQUEST_INITIATED".equals(log.getEventType()));
        assertTrue(hasRequestInitiated, "Should have REQUEST_INITIATED audit log");
    }

    @Test
    @Transactional
    void testProcessDeduplicationRequest_ValidatesInputData() {
        // Given
        DedupeRequestDto requestDto = createTestRequestDto("**********");

        // When
        DedupeResponseDto response = dedupeService.processDeduplicationRequest(requestDto);

        // Then
        assertNotNull(response);
        assertNotNull(response.getReferenceNumber());
        assertEquals(requestDto.getApplicationId(), response.getApplicationId());

        // Verify that the request was properly stored with all fields
        Optional<DedupeRequestEntity> savedEntity = dedupeRequestRepository
                .findByReferenceNumber(response.getReferenceNumber());
        assertTrue(savedEntity.isPresent());

        DedupeRequestEntity entity = savedEntity.get();
        assertEquals(requestDto.getPan(), entity.getPan());
        assertEquals(requestDto.getMobileNumber(), entity.getMobileNumber());
        assertEquals(requestDto.getEmailAddress(), entity.getEmailAddress());
        assertEquals(requestDto.getDateOfBirth(), entity.getDateOfBirth());
        assertEquals(requestDto.getApplicationSubmissionDate(), entity.getApplicationSubmissionDate());
        assertNotNull(entity.getCreatedAt());
        assertNotNull(entity.getUpdatedAt());
    }

    @Test
    @Transactional
    void testProcessDeduplicationRequest_GeneratesUniqueReferenceNumbers() {
        // Given
        DedupeRequestDto requestDto1 = createTestRequestDto("**********");
        DedupeRequestDto requestDto2 = createTestRequestDto("**********");

        // When
        DedupeResponseDto response1 = dedupeService.processDeduplicationRequest(requestDto1);
        DedupeResponseDto response2 = dedupeService.processDeduplicationRequest(requestDto2);

        // Then
        assertNotNull(response1.getReferenceNumber());
        assertNotNull(response2.getReferenceNumber());
        assertNotEquals(response1.getReferenceNumber(), response2.getReferenceNumber());

        // Verify both requests are stored separately
        Optional<DedupeRequestEntity> entity1 = dedupeRequestRepository
                .findByReferenceNumber(response1.getReferenceNumber());
        Optional<DedupeRequestEntity> entity2 = dedupeRequestRepository
                .findByReferenceNumber(response2.getReferenceNumber());

        assertTrue(entity1.isPresent());
        assertTrue(entity2.isPresent());
        assertNotEquals(entity1.get().getId(), entity2.get().getId());
    }

    // Helper method to create test data
    private DedupeRequestDto createTestRequestDto(String pan) {
        return new DedupeRequestDto(
                "APP" + System.currentTimeMillis(),
                "ALICE JOHNSON",
                "9876543210",
                "<EMAIL>",
                pan,
                "XXXX-XXXX-1234",
                LocalDate.of(1990, 5, 20),
                LocalDate.now(),
                "90_DAYS"
        );
    }


}
