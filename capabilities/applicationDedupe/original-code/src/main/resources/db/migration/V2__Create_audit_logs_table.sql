-- Create audit_logs table
CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    reference_number VARCHAR(50) NOT NULL,
    application_id VARCHAR(50),
    event_type VARCHAR(50) NOT NULL,
    event_description VARCHAR(500) NOT NULL,
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_id VARCHAR(50),
    session_id VARCHAR(100),
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    request_data TEXT,
    response_data TEXT,
    error_details TEXT,
    processing_time_ms BIGINT,
    external_service_called VARCHAR(100),
    retry_attempt INTEGER,
    compliance_flag BOOLEAN NOT NULL DEFAULT TRUE
);

-- Create indexes for performance and compliance queries
CREATE INDEX idx_audit_logs_reference_number ON audit_logs(reference_number);
CREATE INDEX idx_audit_logs_application_id ON audit_logs(application_id);
CREATE INDEX idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_compliance_flag ON audit_logs(compliance_flag);
CREATE INDEX idx_audit_logs_external_service ON audit_logs(external_service_called);

-- Composite indexes for common query patterns
CREATE INDEX idx_audit_logs_ref_timestamp ON audit_logs(reference_number, timestamp);
CREATE INDEX idx_audit_logs_event_timestamp ON audit_logs(event_type, timestamp);
CREATE INDEX idx_audit_logs_compliance_timestamp ON audit_logs(compliance_flag, timestamp);

-- Add comments for documentation
COMMENT ON TABLE audit_logs IS 'Comprehensive audit trail for all deduplication activities';
COMMENT ON COLUMN audit_logs.reference_number IS 'Reference number linking to dedupe request';
COMMENT ON COLUMN audit_logs.application_id IS 'Application identifier for cross-reference';
COMMENT ON COLUMN audit_logs.event_type IS 'Type of event being audited';
COMMENT ON COLUMN audit_logs.event_description IS 'Human-readable description of the event';
COMMENT ON COLUMN audit_logs.timestamp IS 'When the event occurred';
COMMENT ON COLUMN audit_logs.user_id IS 'User who initiated the action (if applicable)';
COMMENT ON COLUMN audit_logs.session_id IS 'Session identifier for tracking';
COMMENT ON COLUMN audit_logs.ip_address IS 'IP address of the request origin';
COMMENT ON COLUMN audit_logs.user_agent IS 'User agent string from the request';
COMMENT ON COLUMN audit_logs.request_data IS 'Masked request data for audit';
COMMENT ON COLUMN audit_logs.response_data IS 'Masked response data for audit';
COMMENT ON COLUMN audit_logs.error_details IS 'Error details if the event failed';
COMMENT ON COLUMN audit_logs.processing_time_ms IS 'Processing time for the event';
COMMENT ON COLUMN audit_logs.external_service_called IS 'Name of external service called';
COMMENT ON COLUMN audit_logs.retry_attempt IS 'Retry attempt number if applicable';
COMMENT ON COLUMN audit_logs.compliance_flag IS 'Flag indicating compliance status';
