-- Create dedupe_requests table
CREATE TABLE dedupe_requests (
    id BIGSERIAL PRIMARY KEY,
    reference_number VARCHAR(50) NOT NULL UNIQUE,
    application_id VARCHAR(50) NOT NULL,
    deduped_customer_name VARCHAR(200) NOT NULL,
    mobile_number VARCHAR(15) NOT NULL,
    email_address VARCHAR(100) NOT NULL,
    pan VARCHAR(10) NOT NULL,
    aadhaar_number VARCHAR(20) NOT NULL,
    date_of_birth DATE NOT NULL,
    application_submission_date DATE NOT NULL,
    deduplication_period VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    external_request_payload TEXT,
    external_response_payload TEXT,
    error_details TEXT,
    retry_count INTEGER NOT NULL DEFAULT 0,
    processing_time_ms BIGINT
);

-- Create indexes for performance
CREATE INDEX idx_dedupe_requests_application_id ON dedupe_requests(application_id);
CREATE INDEX idx_dedupe_requests_reference_number ON dedupe_requests(reference_number);
CREATE INDEX idx_dedupe_requests_pan ON dedupe_requests(pan);
CREATE INDEX idx_dedupe_requests_mobile_number ON dedupe_requests(mobile_number);
CREATE INDEX idx_dedupe_requests_created_at ON dedupe_requests(created_at);
CREATE INDEX idx_dedupe_requests_status ON dedupe_requests(status);
CREATE INDEX idx_dedupe_requests_submission_date ON dedupe_requests(application_submission_date);

-- Add comments for documentation
COMMENT ON TABLE dedupe_requests IS 'Stores all deduplication requests for audit and tracking';
COMMENT ON COLUMN dedupe_requests.reference_number IS 'Unique reference number for tracking';
COMMENT ON COLUMN dedupe_requests.application_id IS 'Application identifier from upstream system';
COMMENT ON COLUMN dedupe_requests.deduped_customer_name IS 'Standardized customer name';
COMMENT ON COLUMN dedupe_requests.mobile_number IS 'Customer mobile number';
COMMENT ON COLUMN dedupe_requests.email_address IS 'Customer email address';
COMMENT ON COLUMN dedupe_requests.pan IS 'Customer PAN number';
COMMENT ON COLUMN dedupe_requests.aadhaar_number IS 'Masked/tokenized Aadhaar number';
COMMENT ON COLUMN dedupe_requests.date_of_birth IS 'Customer date of birth';
COMMENT ON COLUMN dedupe_requests.application_submission_date IS 'Date when application was submitted';
COMMENT ON COLUMN dedupe_requests.deduplication_period IS 'Period to search for duplicates';
COMMENT ON COLUMN dedupe_requests.status IS 'Current status of deduplication request';
COMMENT ON COLUMN dedupe_requests.external_request_payload IS 'Masked request sent to external service';
COMMENT ON COLUMN dedupe_requests.external_response_payload IS 'Masked response from external service';
COMMENT ON COLUMN dedupe_requests.error_details IS 'Error details if request failed';
COMMENT ON COLUMN dedupe_requests.retry_count IS 'Number of retry attempts made';
COMMENT ON COLUMN dedupe_requests.processing_time_ms IS 'Total processing time in milliseconds';
