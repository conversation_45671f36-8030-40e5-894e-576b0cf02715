server:
  port: ${SERVER_PORT:8105}
  servlet:
    context-path: /api/application-dedupe

spring:
  application:
    name: application-dedupe-service

  datasource:
    url: ******************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true

  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# External Service Configuration
external:
  dedupe-service:
    base-url: http://localhost:8205/api/application-dedupe/internal/v1
    endpoint: /duplicate-check
    timeout: 30s
    retry:
      max-attempts: 3
      backoff-delay: 1s
      max-backoff-delay: 10s
    auth:
      enabled: false
      username: dedupe_client
      password: dedupe_secret

# Application Configuration
app:
  dedupe:
    default-period: 90_DAYS
    max-period: 1_YEAR
    audit:
      retention-days: 2555  # 7 years for RBI compliance
    logging:
      mask-sensitive-data: true
      include-request-response: true

# Logging Configuration
logging:
  level:
    com.bank.dedupe: INFO
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: logs/application-dedupe.log

# OpenAPI/Swagger Configuration
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    try-it-out-enabled: true
    operations-sorter: method
    tags-sorter: alpha
    display-request-duration: true
    show-extensions: true
    show-common-extensions: true
  show-actuator: true
  default-consumes-media-type: application/json
  default-produces-media-type: application/json

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true


