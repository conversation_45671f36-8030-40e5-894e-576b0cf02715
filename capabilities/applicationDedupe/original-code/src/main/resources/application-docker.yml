server:
  port: 8080
  servlet:
    context-path: /api/application-dedupe

spring:
  application:
    name: application-dedupe-service
  
  datasource:
    url: *****************************************
    username: dedupe_user
    password: dedupe_password
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
        jdbc:
          batch_size: 20
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true

# External Service Configuration for Docker
external:
  dedupe-service:
    base-url: http://localhost:8205/api/application-dedupe/internal/v1
    endpoint: /duplicate-check
    timeout: 30s
    retry:
      max-attempts: 3
      backoff-delay: 1s
      max-backoff-delay: 10s
    auth:
      enabled: false

# Application Configuration
app:
  dedupe:
    default-period: 90_DAYS
    max-period: 1_YEAR
    audit:
      retention-days: 2555
    logging:
      mask-sensitive-data: true
      include-request-response: true

# Logging Configuration for Docker
logging:
  level:
    com.bank.dedupe: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: /app/logs/application-dedupe.log

# OpenAPI/Swagger Configuration for Docker
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    try-it-out-enabled: true
    operations-sorter: method
    tags-sorter: alpha
    display-request-duration: true
    show-extensions: true
    show-common-extensions: true
  show-actuator: true
  default-consumes-media-type: application/json
  default-produces-media-type: application/json

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
