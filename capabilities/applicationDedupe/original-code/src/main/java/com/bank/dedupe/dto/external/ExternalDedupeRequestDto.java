package com.bank.dedupe.dto.external;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDate;

/**
 * Data Transfer Object for requests to the external deduplication service.
 * Maps internal request format to external service expected format.
 */
public class ExternalDedupeRequestDto {

    @JsonProperty("applicationId")
    private String applicationId;

    @JsonProperty("dedupedCustomerName")
    private String dedupedCustomerName;

    @JsonProperty("mobileNumber")
    private String mobileNumber;

    @JsonProperty("emailAddress")
    private String emailAddress;

    @JsonProperty("pan")
    private String pan;

    @JsonProperty("aadhaarNumber")
    private String aadhaarNumber;

    @JsonProperty("dateOfBirth")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate dateOfBirth;

    @JsonProperty("applicationSubmissionDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate applicationSubmissionDate;

    @JsonProperty("deduplicationPeriod")
    private String deduplicationPeriod;

    // Default constructor
    public ExternalDedupeRequestDto() {
    }

    // Constructor with all fields
    public ExternalDedupeRequestDto(String applicationId, String dedupedCustomerName, String mobileNumber,
                                   String emailAddress, String pan, String aadhaarNumber, LocalDate dateOfBirth,
                                   LocalDate applicationSubmissionDate, String deduplicationPeriod) {
        this.applicationId = applicationId;
        this.dedupedCustomerName = dedupedCustomerName;
        this.mobileNumber = mobileNumber;
        this.emailAddress = emailAddress;
        this.pan = pan;
        this.aadhaarNumber = aadhaarNumber;
        this.dateOfBirth = dateOfBirth;
        this.applicationSubmissionDate = applicationSubmissionDate;
        this.deduplicationPeriod = deduplicationPeriod;
    }

    // Getters and Setters
    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getDedupedCustomerName() {
        return dedupedCustomerName;
    }

    public void setDedupedCustomerName(String dedupedCustomerName) {
        this.dedupedCustomerName = dedupedCustomerName;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getAadhaarNumber() {
        return aadhaarNumber;
    }

    public void setAadhaarNumber(String aadhaarNumber) {
        this.aadhaarNumber = aadhaarNumber;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public LocalDate getApplicationSubmissionDate() {
        return applicationSubmissionDate;
    }

    public void setApplicationSubmissionDate(LocalDate applicationSubmissionDate) {
        this.applicationSubmissionDate = applicationSubmissionDate;
    }

    public String getDeduplicationPeriod() {
        return deduplicationPeriod;
    }

    public void setDeduplicationPeriod(String deduplicationPeriod) {
        this.deduplicationPeriod = deduplicationPeriod;
    }

    @Override
    public String toString() {
        return "ExternalDedupeRequestDto{" +
                "applicationId='" + applicationId + '\'' +
                ", dedupedCustomerName='" + dedupedCustomerName + '\'' +
                ", mobileNumber='***MASKED***'" +
                ", emailAddress='***MASKED***'" +
                ", pan='***MASKED***'" +
                ", aadhaarNumber='***MASKED***'" +
                ", dateOfBirth=" + dateOfBirth +
                ", applicationSubmissionDate=" + applicationSubmissionDate +
                ", deduplicationPeriod='" + deduplicationPeriod + '\'' +
                '}';
    }
}
