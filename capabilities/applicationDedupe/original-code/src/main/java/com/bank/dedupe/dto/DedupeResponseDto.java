package com.bank.dedupe.dto;

import com.bank.dedupe.enums.DedupeStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;


import java.time.LocalDateTime;
import java.util.List;

/**
 * Data Transfer Object for deduplication responses to upstream applications.
 * Contains the result of the deduplication check and any duplicate application details.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DedupeResponseDto {

    private String referenceNumber;

    private String applicationId;

    private DedupeStatus dedupeStatus;

    private Integer matchCount;

    private List<DuplicateApplicationDto> duplicateApplications;

    private String message;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime timestamp;

    private String searchPeriod;

    private ErrorDetailsDto errorDetails;

    // Default constructor
    public DedupeResponseDto() {
        this.timestamp = LocalDateTime.now();
    }

    // Constructor for successful responses
    public DedupeResponseDto(String referenceNumber, String applicationId, DedupeStatus dedupeStatus,
                            Integer matchCount, List<DuplicateApplicationDto> duplicateApplications,
                            String message, String searchPeriod) {
        this();
        this.referenceNumber = referenceNumber;
        this.applicationId = applicationId;
        this.dedupeStatus = dedupeStatus;
        this.matchCount = matchCount;
        this.duplicateApplications = duplicateApplications;
        this.message = message;
        this.searchPeriod = searchPeriod;
    }

    // Constructor for error responses
    public DedupeResponseDto(String referenceNumber, String applicationId, DedupeStatus dedupeStatus,
                            String message, ErrorDetailsDto errorDetails) {
        this();
        this.referenceNumber = referenceNumber;
        this.applicationId = applicationId;
        this.dedupeStatus = dedupeStatus;
        this.message = message;
        this.errorDetails = errorDetails;
        this.matchCount = 0;
    }

    // Getters and Setters
    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public DedupeStatus getDedupeStatus() {
        return dedupeStatus;
    }

    public void setDedupeStatus(DedupeStatus dedupeStatus) {
        this.dedupeStatus = dedupeStatus;
    }

    public Integer getMatchCount() {
        return matchCount;
    }

    public void setMatchCount(Integer matchCount) {
        this.matchCount = matchCount;
    }

    public List<DuplicateApplicationDto> getDuplicateApplications() {
        return duplicateApplications;
    }

    public void setDuplicateApplications(List<DuplicateApplicationDto> duplicateApplications) {
        this.duplicateApplications = duplicateApplications;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getSearchPeriod() {
        return searchPeriod;
    }

    public void setSearchPeriod(String searchPeriod) {
        this.searchPeriod = searchPeriod;
    }

    public ErrorDetailsDto getErrorDetails() {
        return errorDetails;
    }

    public void setErrorDetails(ErrorDetailsDto errorDetails) {
        this.errorDetails = errorDetails;
    }

    /**
     * Checks if the response indicates a successful deduplication check.
     * 
     * @return true if status is completed and not an error
     */
    public boolean isSuccessful() {
        return dedupeStatus != null && dedupeStatus.isCompleted();
    }

    /**
     * Checks if duplicates were found.
     * 
     * @return true if status is MATCH_FOUND or PARTIAL_MATCH
     */
    public boolean hasDuplicates() {
        return dedupeStatus == DedupeStatus.MATCH_FOUND || dedupeStatus == DedupeStatus.PARTIAL_MATCH;
    }

    @Override
    public String toString() {
        return "DedupeResponseDto{" +
                "referenceNumber='" + referenceNumber + '\'' +
                ", applicationId='" + applicationId + '\'' +
                ", dedupeStatus=" + dedupeStatus +
                ", matchCount=" + matchCount +
                ", message='" + message + '\'' +
                ", timestamp=" + timestamp +
                ", searchPeriod='" + searchPeriod + '\'' +
                ", hasErrorDetails=" + (errorDetails != null) +
                '}';
    }
}
