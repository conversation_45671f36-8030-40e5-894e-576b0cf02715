package com.bank.dedupe.service;

import com.bank.dedupe.config.ExternalServiceConfig;
import com.bank.dedupe.dto.external.ExternalDedupeRequestDto;
import com.bank.dedupe.dto.external.ExternalDedupeResponseDto;
import com.bank.dedupe.util.DataMaskingUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.UUID;

/**
 * Service client for communicating with the external deduplication service.
 * Handles all external service interactions with proper error handling and retry logic.
 */
@Service
public class ExternalDedupeServiceClient {

    private static final Logger logger = LoggerFactory.getLogger(ExternalDedupeServiceClient.class);

    private final WebClient webClient;
    private final ExternalServiceConfig config;

    public ExternalDedupeServiceClient(@Qualifier("dedupeServiceWebClient") WebClient webClient,
                                      ExternalServiceConfig config) {
        this.webClient = webClient;
        this.config = config;
    }

    /**
     * Calls the external deduplication service to check for duplicates.
     *
     * @param request the deduplication request
     * @param referenceNumber unique reference for tracking
     * @return external service response
     * @throws ExternalServiceException if the call fails after all retries
     */
    public ExternalDedupeResponseDto checkDuplicates(ExternalDedupeRequestDto request, String referenceNumber) {
        String traceId = UUID.randomUUID().toString();
        MDC.put("traceId", traceId);
        MDC.put("referenceNumber", referenceNumber);

        try {
            logger.info("Initiating external dedupe service call for reference: {}", referenceNumber);

            String maskedRequest = DataMaskingUtil.createMaskedRequest(request);
            logger.debug("External service request: {}", maskedRequest);

            ExternalDedupeResponseDto response = webClient
                    .post()
                    .uri(config.getEndpoint())
                    .header("X-Trace-Id", traceId)
                    .header("X-Reference-Number", referenceNumber)
                    .bodyValue(request)
                    .retrieve()
                    .onStatus(status -> status.is4xxClientError(), clientResponse -> {
                        logger.error("Client error from external service: {}",
                                   clientResponse.statusCode());
                        return clientResponse.bodyToMono(String.class)
                                .flatMap(errorBody -> {
                                    logger.error("Error response body: {}", errorBody);
                                    return Mono.error(new ExternalServiceException(
                                            "Client error: " + clientResponse.statusCode(),
                                            clientResponse.statusCode().value(),
                                            errorBody));
                                });
                    })
                    .onStatus(status -> status.is5xxServerError(), serverResponse -> {
                        logger.error("Server error from external service: {}",
                                   serverResponse.statusCode());
                        return serverResponse.bodyToMono(String.class)
                                .flatMap(errorBody -> {
                                    logger.error("Error response body: {}", errorBody);
                                    return Mono.error(new ExternalServiceException(
                                            "Server error: " + serverResponse.statusCode(),
                                            serverResponse.statusCode().value(),
                                            errorBody));
                                });
                    })
                    .bodyToMono(ExternalDedupeResponseDto.class)
                    .retryWhen(Retry.backoff(config.getRetry().getMaxAttempts(), config.getRetry().getBackoffDelay())
                            .maxBackoff(config.getRetry().getMaxBackoffDelay())
                            .filter(this::isRetryableException)
                            .doBeforeRetry(retrySignal -> {
                                logger.warn("Retrying external service call, attempt: {} for reference: {}",
                                          retrySignal.totalRetries() + 1, referenceNumber);
                            }))
                    .doOnSuccess(resp -> {
                        String maskedResponse = DataMaskingUtil.createMaskedResponse(resp);
                        logger.info("External service call successful for reference: {}, response: {}",
                                  referenceNumber, maskedResponse);
                    })
                    .doOnError(error -> {
                        logger.error("External service call failed for reference: {}, error: {}",
                                   referenceNumber, error.getMessage(), error);
                    })
                    .block(config.getTimeout().plus(Duration.ofSeconds(5))); // Add buffer to timeout

            if (response == null) {
                throw new ExternalServiceException("Received null response from external service", 500, null);
            }

            return response;

        } catch (WebClientResponseException e) {
            logger.error("WebClient response exception for reference: {}, status: {}, body: {}",
                       referenceNumber, e.getStatusCode(), e.getResponseBodyAsString());
            throw new ExternalServiceException(
                    "External service error: " + e.getMessage(),
                    e.getStatusCode().value(),
                    e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.error("Unexpected error calling external service for reference: {}", referenceNumber, e);
            throw new ExternalServiceException("Unexpected error: " + e.getMessage(), 500, null);
        } finally {
            MDC.clear();
        }
    }

    /**
     * Checks the health of the external deduplication service.
     *
     * @return true if the service is healthy
     */
    public boolean isServiceHealthy() {
        try {
            String response = webClient
                    .get()
                    .uri("/health")
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(5))
                    .block();

            logger.debug("External service health check response: {}", response);
            return true;
        } catch (Exception e) {
            logger.warn("External service health check failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Determines if an exception is retryable.
     *
     * @param throwable the exception to check
     * @return true if the exception is retryable
     */
    private boolean isRetryableException(Throwable throwable) {
        if (throwable instanceof WebClientResponseException webClientException) {
            HttpStatus status = HttpStatus.valueOf(webClientException.getStatusCode().value());

            // Retry on server errors and specific client errors
            return status.is5xxServerError() ||
                   status == HttpStatus.REQUEST_TIMEOUT ||
                   status == HttpStatus.TOO_MANY_REQUESTS;
        }

        if (throwable instanceof ExternalServiceException) {
            ExternalServiceException serviceException = (ExternalServiceException) throwable;
            int statusCode = serviceException.getStatusCode();

            // Retry on server errors and timeouts
            return statusCode >= 500 || statusCode == 408 || statusCode == 429;
        }

        // Retry on network-related exceptions
        return throwable instanceof java.net.ConnectException ||
               throwable instanceof java.net.SocketTimeoutException ||
               throwable instanceof java.util.concurrent.TimeoutException ||
               throwable.getCause() instanceof java.net.ConnectException;
    }

    /**
     * Custom exception for external service errors.
     */
    public static class ExternalServiceException extends RuntimeException {
        private final int statusCode;
        private final String responseBody;

        public ExternalServiceException(String message, int statusCode, String responseBody) {
            super(message);
            this.statusCode = statusCode;
            this.responseBody = responseBody;
        }

        public int getStatusCode() {
            return statusCode;
        }

        public String getResponseBody() {
            return responseBody;
        }

        public boolean isRetryable() {
            return statusCode >= 500 || statusCode == 408 || statusCode == 429;
        }
    }
}
