package com.bank.dedupe;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;

/**
 * Main application class for Application Dedupe Service.
 * 
 * This service provides application deduplication capabilities by:
 * 1. Receiving dedupe requests from upstream applications
 * 2. Calling external dedupe service for actual deduplication
 * 3. Interpreting responses and returning standardized results
 * 4. Maintaining comprehensive audit trails for RBI compliance
 */
@SpringBootApplication
@ConfigurationPropertiesScan
public class ApplicationDedupeApplication {

    public static void main(String[] args) {
        SpringApplication.run(ApplicationDedupeApplication.class, args);
    }
}
