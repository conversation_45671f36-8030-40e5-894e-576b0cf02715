package com.bank.dedupe.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * OpenAPI configuration for Application Dedupe service.
 * Configures Swagger UI and API documentation with proper CORS settings.
 */
@Configuration
public class OpenApiConfig implements WebMvcConfigurer {

    @Value("${server.port:8105}")
    private String serverPort;

    @Value("${server.servlet.context-path:/api/application-dedupe}")
    private String contextPath;

    @Bean
    public OpenAPI applicationDedupeOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Application Dedupe Service API")
                        .description("REST API for application deduplication operations in banking systems. " +
                                   "This service provides endpoints to check for duplicate loan applications " +
                                   "based on customer information and configurable deduplication periods.")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Banking API Team")
                                .email("<EMAIL>")
                                .url("https://bank.com/api-support"))
                        .license(new License()
                                .name("Proprietary")
                                .url("https://bank.com/license")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + contextPath)
                                .description("Local Development Server"),
                        new Server()
                                .url("http://localhost:8080" + contextPath)
                                .description("Docker Development Server"),
                        new Server()
                                .url("https://api.bank.com" + contextPath)
                                .description("Production Server")
                ));
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
}
