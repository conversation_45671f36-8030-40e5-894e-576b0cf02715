package com.bank.dedupe.enums;

/**
 * Enumeration representing the status of a deduplication check.
 * Used to standardize dedupe outcomes across the system.
 */
public enum DedupeStatus {
    
    /**
     * Indicates that one or more duplicate applications were found
     * within the specified deduplication period.
     */
    MATCH_FOUND("Match Found", "Duplicate applications detected"),
    
    /**
     * Indicates that no duplicate applications were found
     * within the specified deduplication period.
     */
    NO_MATCH_FOUND("No Match Found", "No duplicate applications detected"),
    
    /**
     * Indicates a partial match with lower confidence score
     * that may require manual review.
     */
    PARTIAL_MATCH("Partial Match", "Potential duplicate requiring manual review"),
    
    /**
     * Indicates an error occurred during the deduplication process.
     * This could be due to external service failure, network issues, etc.
     */
    ERROR("Error", "Error occurred during deduplication process"),
    
    /**
     * Indicates the deduplication request is being processed.
     */
    PROCESSING("Processing", "Deduplication check in progress"),
    
    /**
     * Indicates the deduplication request has been initiated.
     */
    INITIATED("Initiated", "Deduplication request initiated");

    private final String displayName;
    private final String description;

    DedupeStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Checks if the status indicates a successful completion.
     * 
     * @return true if status is MATCH_FOUND, NO_MATCH_FOUND, or PARTIAL_MATCH
     */
    public boolean isCompleted() {
        return this == MATCH_FOUND || this == NO_MATCH_FOUND || this == PARTIAL_MATCH;
    }

    /**
     * Checks if the status indicates an error condition.
     * 
     * @return true if status is ERROR
     */
    public boolean isError() {
        return this == ERROR;
    }

    /**
     * Checks if the status indicates processing is ongoing.
     * 
     * @return true if status is PROCESSING or INITIATED
     */
    public boolean isInProgress() {
        return this == PROCESSING || this == INITIATED;
    }
}
