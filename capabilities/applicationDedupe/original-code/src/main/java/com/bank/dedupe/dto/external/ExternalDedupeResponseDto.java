package com.bank.dedupe.dto.external;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Data Transfer Object for responses from the external deduplication service.
 * Maps external service response format to internal processing.
 */
public class ExternalDedupeResponseDto {

    @JsonProperty("dedupeStatus")
    private String dedupeStatus;

    @JsonProperty("matchCount")
    private Integer matchCount;

    @JsonProperty("duplicateApplications")
    private List<ExternalDuplicateApplicationDto> duplicateApplications;

    @JsonProperty("message")
    private String message;

    @JsonProperty("timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime timestamp;

    @JsonProperty("transactionId")
    private String transactionId;

    @JsonProperty("searchPeriod")
    private String searchPeriod;

    // Default constructor
    public ExternalDedupeResponseDto() {
    }

    // Constructor with essential fields
    public ExternalDedupeResponseDto(String dedupeStatus, Integer matchCount, String message, String searchPeriod) {
        this.dedupeStatus = dedupeStatus;
        this.matchCount = matchCount;
        this.message = message;
        this.searchPeriod = searchPeriod;
        this.timestamp = LocalDateTime.now();
    }

    // Getters and Setters
    public String getDedupeStatus() {
        return dedupeStatus;
    }

    public void setDedupeStatus(String dedupeStatus) {
        this.dedupeStatus = dedupeStatus;
    }

    public Integer getMatchCount() {
        return matchCount;
    }

    public void setMatchCount(Integer matchCount) {
        this.matchCount = matchCount;
    }

    public List<ExternalDuplicateApplicationDto> getDuplicateApplications() {
        return duplicateApplications;
    }

    public void setDuplicateApplications(List<ExternalDuplicateApplicationDto> duplicateApplications) {
        this.duplicateApplications = duplicateApplications;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getSearchPeriod() {
        return searchPeriod;
    }

    public void setSearchPeriod(String searchPeriod) {
        this.searchPeriod = searchPeriod;
    }

    /**
     * Checks if the external response indicates a match was found.
     * 
     * @return true if status indicates match found
     */
    public boolean isMatchFound() {
        return "MATCH_FOUND".equals(dedupeStatus) || "PARTIAL_MATCH".equals(dedupeStatus);
    }

    /**
     * Checks if the external response indicates no match was found.
     * 
     * @return true if status indicates no match
     */
    public boolean isNoMatchFound() {
        return "NO_MATCH_FOUND".equals(dedupeStatus);
    }

    /**
     * Checks if the external response indicates an error.
     * 
     * @return true if status indicates error
     */
    public boolean isError() {
        return "ERROR".equals(dedupeStatus);
    }

    /**
     * Gets the number of duplicate applications found.
     * 
     * @return count of duplicates, 0 if null
     */
    public int getDuplicateCount() {
        return matchCount != null ? matchCount : 0;
    }

    /**
     * Checks if duplicate applications list is available.
     * 
     * @return true if duplicates list is not null and not empty
     */
    public boolean hasDuplicateApplications() {
        return duplicateApplications != null && !duplicateApplications.isEmpty();
    }

    @Override
    public String toString() {
        return "ExternalDedupeResponseDto{" +
                "dedupeStatus='" + dedupeStatus + '\'' +
                ", matchCount=" + matchCount +
                ", message='" + message + '\'' +
                ", timestamp=" + timestamp +
                ", transactionId='" + transactionId + '\'' +
                ", searchPeriod='" + searchPeriod + '\'' +
                ", duplicateApplicationsCount=" + (duplicateApplications != null ? duplicateApplications.size() : 0) +
                '}';
    }
}
