package com.bank.dedupe.service;

import com.bank.dedupe.entity.AuditLogEntity;
import com.bank.dedupe.repository.AuditLogRepository;
import com.bank.dedupe.util.DataMaskingUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service for managing audit logs and compliance tracking.
 * Provides comprehensive audit trail for all deduplication activities.
 */
@Service
public class AuditService {

    private static final Logger logger = LoggerFactory.getLogger(AuditService.class);

    private final AuditLogRepository auditLogRepository;

    public AuditService(AuditLogRepository auditLogRepository) {
        this.auditLogRepository = auditLogRepository;
    }

    /**
     * Logs the initiation of a deduplication request.
     * 
     * @param referenceNumber unique reference for the request
     * @param applicationId application being checked
     * @param maskedRequestData masked request data for audit
     */
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logRequestInitiated(String referenceNumber, String applicationId, String maskedRequestData) {
        try {
            AuditLogEntity audit = AuditLogEntity.requestInitiated(referenceNumber, applicationId, maskedRequestData);
            auditLogRepository.save(audit);
            
            logger.debug("Audit log created for request initiation: {}", referenceNumber);
        } catch (Exception e) {
            logger.error("Failed to create audit log for request initiation: {}", referenceNumber, e);
        }
    }

    /**
     * Logs external service calls.
     * 
     * @param referenceNumber unique reference for the request
     * @param serviceName name of the external service
     * @param maskedRequestData masked request data
     * @param retryAttempt current retry attempt number
     */
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logExternalServiceCall(String referenceNumber, String serviceName, 
                                     String maskedRequestData, Integer retryAttempt) {
        try {
            AuditLogEntity audit = AuditLogEntity.externalServiceCall(referenceNumber, serviceName, 
                                                                     maskedRequestData, retryAttempt);
            auditLogRepository.save(audit);
            
            logger.debug("Audit log created for external service call: {} to {}", referenceNumber, serviceName);
        } catch (Exception e) {
            logger.error("Failed to create audit log for external service call: {}", referenceNumber, e);
        }
    }

    /**
     * Logs successful response completion.
     * 
     * @param referenceNumber unique reference for the request
     * @param applicationId application that was checked
     * @param maskedResponseData masked response data
     * @param processingTime total processing time
     */
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logResponseSuccess(String referenceNumber, String applicationId, 
                                 String maskedResponseData, Long processingTime) {
        try {
            AuditLogEntity audit = AuditLogEntity.responseSuccess(referenceNumber, applicationId, 
                                                                 maskedResponseData, processingTime);
            auditLogRepository.save(audit);
            
            logger.debug("Audit log created for successful response: {}", referenceNumber);
        } catch (Exception e) {
            logger.error("Failed to create audit log for successful response: {}", referenceNumber, e);
        }
    }

    /**
     * Logs error conditions.
     * 
     * @param referenceNumber unique reference for the request
     * @param applicationId application that was being checked
     * @param errorDetails details about the error
     * @param processingTime processing time before error
     */
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logResponseError(String referenceNumber, String applicationId, 
                               String errorDetails, Long processingTime) {
        try {
            AuditLogEntity audit = AuditLogEntity.responseError(referenceNumber, applicationId, 
                                                               errorDetails, processingTime);
            auditLogRepository.save(audit);
            
            logger.debug("Audit log created for error response: {}", referenceNumber);
        } catch (Exception e) {
            logger.error("Failed to create audit log for error response: {}", referenceNumber, e);
        }
    }

    /**
     * Logs custom audit events.
     * 
     * @param referenceNumber unique reference for the request
     * @param applicationId application ID (optional)
     * @param eventType type of event
     * @param eventDescription description of the event
     * @param additionalData additional data for the event
     */
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logCustomEvent(String referenceNumber, String applicationId, String eventType, 
                             String eventDescription, String additionalData) {
        try {
            AuditLogEntity audit = new AuditLogEntity(referenceNumber, applicationId, eventType, eventDescription);
            
            if (additionalData != null) {
                audit.setRequestData(DataMaskingUtil.maskJsonData(additionalData));
            }
            
            auditLogRepository.save(audit);
            
            logger.debug("Audit log created for custom event: {} - {}", referenceNumber, eventType);
        } catch (Exception e) {
            logger.error("Failed to create audit log for custom event: {} - {}", referenceNumber, eventType, e);
        }
    }

    /**
     * Gets the complete audit trail for a reference number.
     * 
     * @param referenceNumber the reference number
     * @return complete audit trail
     */
    @Transactional(readOnly = true)
    public List<AuditLogEntity> getAuditTrail(String referenceNumber) {
        try {
            return auditLogRepository.findCompleteAuditTrail(referenceNumber);
        } catch (Exception e) {
            logger.error("Failed to retrieve audit trail for reference: {}", referenceNumber, e);
            return List.of();
        }
    }

    /**
     * Gets audit logs for a specific application ID.
     * 
     * @param applicationId the application ID
     * @return audit logs for the application
     */
    @Transactional(readOnly = true)
    public List<AuditLogEntity> getAuditLogsForApplication(String applicationId) {
        try {
            return auditLogRepository.findByApplicationId(applicationId);
        } catch (Exception e) {
            logger.error("Failed to retrieve audit logs for application: {}", applicationId, e);
            return List.of();
        }
    }

    /**
     * Gets audit logs by event type within a time range.
     * 
     * @param eventType the event type
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return audit logs matching the criteria
     */
    @Transactional(readOnly = true)
    public List<AuditLogEntity> getAuditLogsByEventType(String eventType, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return auditLogRepository.findByEventTypeAndTimeRange(eventType, startTime, endTime);
        } catch (Exception e) {
            logger.error("Failed to retrieve audit logs for event type: {}", eventType, e);
            return List.of();
        }
    }

    /**
     * Gets non-compliant audit events within a time range.
     * 
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return non-compliant audit logs
     */
    @Transactional(readOnly = true)
    public List<AuditLogEntity> getNonCompliantEvents(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return auditLogRepository.findNonCompliantEvents(startTime, endTime);
        } catch (Exception e) {
            logger.error("Failed to retrieve non-compliant events", e);
            return List.of();
        }
    }

    /**
     * Gets audit statistics for a time range.
     * 
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return audit statistics
     */
    @Transactional(readOnly = true)
    public List<Object[]> getAuditStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return auditLogRepository.getAuditStatistics(startTime, endTime);
        } catch (Exception e) {
            logger.error("Failed to retrieve audit statistics", e);
            return List.of();
        }
    }

    /**
     * Gets error logs within a time range.
     * 
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return error audit logs
     */
    @Transactional(readOnly = true)
    public List<AuditLogEntity> getErrorLogs(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return auditLogRepository.findErrorLogs(startTime, endTime);
        } catch (Exception e) {
            logger.error("Failed to retrieve error logs", e);
            return List.of();
        }
    }

    /**
     * Gets retry logs within a time range.
     * 
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return retry audit logs
     */
    @Transactional(readOnly = true)
    public List<AuditLogEntity> getRetryLogs(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return auditLogRepository.findRetryLogs(startTime, endTime);
        } catch (Exception e) {
            logger.error("Failed to retrieve retry logs", e);
            return List.of();
        }
    }

    /**
     * Cleans up old audit logs based on retention policy.
     * 
     * @param cutoffDate cutoff date for retention
     * @return number of logs deleted
     */
    @Transactional
    public int cleanupOldAuditLogs(LocalDateTime cutoffDate) {
        try {
            List<AuditLogEntity> oldLogs = auditLogRepository.findLogsOlderThan(cutoffDate);
            int count = oldLogs.size();
            
            if (count > 0) {
                auditLogRepository.deleteAll(oldLogs);
                logger.info("Cleaned up {} old audit logs older than {}", count, cutoffDate);
            }
            
            return count;
        } catch (Exception e) {
            logger.error("Failed to cleanup old audit logs", e);
            return 0;
        }
    }

    /**
     * Validates audit trail completeness for a reference number.
     * 
     * @param referenceNumber the reference number to validate
     * @return true if audit trail is complete
     */
    @Transactional(readOnly = true)
    public boolean validateAuditTrailCompleteness(String referenceNumber) {
        try {
            List<AuditLogEntity> auditTrail = getAuditTrail(referenceNumber);
            
            if (auditTrail.isEmpty()) {
                logger.warn("No audit trail found for reference: {}", referenceNumber);
                return false;
            }
            
            // Check for required events
            boolean hasRequestInitiated = auditTrail.stream()
                    .anyMatch(log -> "REQUEST_INITIATED".equals(log.getEventType()));
            boolean hasResponseEvent = auditTrail.stream()
                    .anyMatch(log -> "RESPONSE_SUCCESS".equals(log.getEventType()) || 
                                   "RESPONSE_ERROR".equals(log.getEventType()));
            
            if (!hasRequestInitiated) {
                logger.warn("Missing REQUEST_INITIATED event for reference: {}", referenceNumber);
                return false;
            }
            
            if (!hasResponseEvent) {
                logger.warn("Missing response event for reference: {}", referenceNumber);
                return false;
            }
            
            return true;
        } catch (Exception e) {
            logger.error("Failed to validate audit trail completeness for reference: {}", referenceNumber, e);
            return false;
        }
    }
}
