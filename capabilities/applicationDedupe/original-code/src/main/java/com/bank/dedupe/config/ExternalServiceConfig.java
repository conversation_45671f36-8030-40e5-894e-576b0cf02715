package com.bank.dedupe.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * Configuration properties for external deduplication service.
 * Contains all settings required to communicate with the external service.
 */
@Configuration
@ConfigurationProperties(prefix = "external.dedupe-service")
public class ExternalServiceConfig {

    private String baseUrl = "http://localhost:8084/api/application-dedupe/internal/v1";
    private String endpoint = "/duplicate-check";
    private Duration timeout = Duration.ofSeconds(30);
    private RetryConfig retry = new RetryConfig();
    private AuthConfig auth = new AuthConfig();

    // Getters and Setters
    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public Duration getTimeout() {
        return timeout;
    }

    public void setTimeout(Duration timeout) {
        this.timeout = timeout;
    }

    public RetryConfig getRetry() {
        return retry;
    }

    public void setRetry(RetryConfig retry) {
        this.retry = retry;
    }

    public AuthConfig getAuth() {
        return auth;
    }

    public void setAuth(AuthConfig auth) {
        this.auth = auth;
    }

    /**
     * Gets the complete URL for the deduplication endpoint.
     * 
     * @return complete URL
     */
    public String getCompleteUrl() {
        return baseUrl + endpoint;
    }

    /**
     * Configuration for retry mechanism.
     */
    public static class RetryConfig {
        private int maxAttempts = 3;
        private Duration backoffDelay = Duration.ofSeconds(1);
        private Duration maxBackoffDelay = Duration.ofSeconds(10);
        private double backoffMultiplier = 2.0;

        public int getMaxAttempts() {
            return maxAttempts;
        }

        public void setMaxAttempts(int maxAttempts) {
            this.maxAttempts = maxAttempts;
        }

        public Duration getBackoffDelay() {
            return backoffDelay;
        }

        public void setBackoffDelay(Duration backoffDelay) {
            this.backoffDelay = backoffDelay;
        }

        public Duration getMaxBackoffDelay() {
            return maxBackoffDelay;
        }

        public void setMaxBackoffDelay(Duration maxBackoffDelay) {
            this.maxBackoffDelay = maxBackoffDelay;
        }

        public double getBackoffMultiplier() {
            return backoffMultiplier;
        }

        public void setBackoffMultiplier(double backoffMultiplier) {
            this.backoffMultiplier = backoffMultiplier;
        }

        /**
         * Calculates the delay for a specific retry attempt.
         * 
         * @param attempt the retry attempt number (1-based)
         * @return delay duration for the attempt
         */
        public Duration calculateDelay(int attempt) {
            if (attempt <= 1) {
                return backoffDelay;
            }
            
            long delayMs = (long) (backoffDelay.toMillis() * Math.pow(backoffMultiplier, attempt - 1));
            Duration calculatedDelay = Duration.ofMillis(delayMs);
            
            return calculatedDelay.compareTo(maxBackoffDelay) > 0 ? maxBackoffDelay : calculatedDelay;
        }
    }

    /**
     * Configuration for authentication with external service.
     */
    public static class AuthConfig {
        private boolean enabled = false;
        private String username = "dedupe_client";
        private String password = "dedupe_secret";
        private String apiKey;
        private String tokenEndpoint;

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getApiKey() {
            return apiKey;
        }

        public void setApiKey(String apiKey) {
            this.apiKey = apiKey;
        }

        public String getTokenEndpoint() {
            return tokenEndpoint;
        }

        public void setTokenEndpoint(String tokenEndpoint) {
            this.tokenEndpoint = tokenEndpoint;
        }

        /**
         * Checks if basic authentication should be used.
         * 
         * @return true if basic auth is configured
         */
        public boolean useBasicAuth() {
            return enabled && username != null && password != null;
        }

        /**
         * Checks if API key authentication should be used.
         * 
         * @return true if API key is configured
         */
        public boolean useApiKey() {
            return enabled && apiKey != null && !apiKey.trim().isEmpty();
        }
    }

    @Override
    public String toString() {
        return "ExternalServiceConfig{" +
                "baseUrl='" + baseUrl + '\'' +
                ", endpoint='" + endpoint + '\'' +
                ", timeout=" + timeout +
                ", retry=" + retry +
                ", auth.enabled=" + auth.enabled +
                '}';
    }
}
