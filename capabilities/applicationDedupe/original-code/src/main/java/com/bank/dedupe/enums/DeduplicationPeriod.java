package com.bank.dedupe.enums;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

/**
 * Enumeration representing different deduplication periods.
 * Defines the lookback period for searching duplicate applications.
 */
public enum DeduplicationPeriod {
    
    /**
     * Search for duplicates in the last 30 days.
     */
    THIRTY_DAYS("30_DAYS", "30 Days", 30, ChronoUnit.DAYS),
    
    /**
     * Search for duplicates in the last 90 days.
     */
    NINETY_DAYS("90_DAYS", "90 Days", 90, ChronoUnit.DAYS),
    
    /**
     * Search for duplicates in the last 180 days (6 months).
     */
    ONE_HUNDRED_EIGHTY_DAYS("180_DAYS", "180 Days", 180, ChronoUnit.DAYS),
    
    /**
     * Search for duplicates in the last 1 year.
     */
    ONE_YEAR("1_YEAR", "1 Year", 1, ChronoUnit.YEARS),
    
    /**
     * Search for duplicates in the last 2 years.
     */
    TWO_YEARS("2_YEARS", "2 Years", 2, ChronoUnit.YEARS);

    private final String code;
    private final String displayName;
    private final long amount;
    private final ChronoUnit unit;

    DeduplicationPeriod(String code, String displayName, long amount, ChronoUnit unit) {
        this.code = code;
        this.displayName = displayName;
        this.amount = amount;
        this.unit = unit;
    }

    public String getCode() {
        return code;
    }

    public String getDisplayName() {
        return displayName;
    }

    public long getAmount() {
        return amount;
    }

    public ChronoUnit getUnit() {
        return unit;
    }

    /**
     * Calculates the start date for the deduplication period from the given reference date.
     * 
     * @param referenceDate the reference date (typically application submission date)
     * @return the start date for searching duplicates
     */
    public LocalDate getStartDate(LocalDate referenceDate) {
        return referenceDate.minus(amount, unit);
    }

    /**
     * Gets the number of days this period represents.
     * 
     * @return number of days in the period
     */
    public long getDays() {
        return switch (unit) {
            case DAYS -> amount;
            case YEARS -> amount * 365; // Approximate
            default -> throw new IllegalStateException("Unsupported unit: " + unit);
        };
    }

    /**
     * Finds a DeduplicationPeriod by its code.
     * 
     * @param code the period code
     * @return the matching DeduplicationPeriod
     * @throws IllegalArgumentException if no matching period is found
     */
    public static DeduplicationPeriod fromCode(String code) {
        if (code == null) {
            return NINETY_DAYS; // Default period
        }
        
        for (DeduplicationPeriod period : values()) {
            if (period.code.equals(code)) {
                return period;
            }
        }
        
        throw new IllegalArgumentException("Invalid deduplication period code: " + code);
    }

    /**
     * Gets the default deduplication period.
     * 
     * @return the default period (90 days)
     */
    public static DeduplicationPeriod getDefault() {
        return NINETY_DAYS;
    }
}
