package com.bank.dedupe.dto;

import com.bank.dedupe.enums.ApplicationStatus;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;
import java.util.List;

/**
 * Data Transfer Object representing a duplicate application found during deduplication.
 * Contains details about the matching application and the fields that matched.
 */
public class DuplicateApplicationDto {

    private String applicationId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate submissionDate;

    private List<String> matchedFields;

    private Double matchScore;

    private ApplicationStatus previousApplicationStatus;

    private String customerName;

    private String matchNotes;

    // Default constructor
    public DuplicateApplicationDto() {
    }

    // Constructor with essential fields
    public DuplicateApplicationDto(String applicationId, LocalDate submissionDate, 
                                  List<String> matchedFields, Double matchScore, 
                                  ApplicationStatus previousApplicationStatus) {
        this.applicationId = applicationId;
        this.submissionDate = submissionDate;
        this.matchedFields = matchedFields;
        this.matchScore = matchScore;
        this.previousApplicationStatus = previousApplicationStatus;
    }

    // Constructor with all fields
    public DuplicateApplicationDto(String applicationId, LocalDate submissionDate, 
                                  List<String> matchedFields, Double matchScore, 
                                  ApplicationStatus previousApplicationStatus, String customerName, 
                                  String matchNotes) {
        this.applicationId = applicationId;
        this.submissionDate = submissionDate;
        this.matchedFields = matchedFields;
        this.matchScore = matchScore;
        this.previousApplicationStatus = previousApplicationStatus;
        this.customerName = customerName;
        this.matchNotes = matchNotes;
    }

    // Getters and Setters
    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public LocalDate getSubmissionDate() {
        return submissionDate;
    }

    public void setSubmissionDate(LocalDate submissionDate) {
        this.submissionDate = submissionDate;
    }

    public List<String> getMatchedFields() {
        return matchedFields;
    }

    public void setMatchedFields(List<String> matchedFields) {
        this.matchedFields = matchedFields;
    }

    public Double getMatchScore() {
        return matchScore;
    }

    public void setMatchScore(Double matchScore) {
        this.matchScore = matchScore;
    }

    public ApplicationStatus getPreviousApplicationStatus() {
        return previousApplicationStatus;
    }

    public void setPreviousApplicationStatus(ApplicationStatus previousApplicationStatus) {
        this.previousApplicationStatus = previousApplicationStatus;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getMatchNotes() {
        return matchNotes;
    }

    public void setMatchNotes(String matchNotes) {
        this.matchNotes = matchNotes;
    }

    /**
     * Checks if this is a high confidence match.
     * 
     * @return true if match score is >= 0.8
     */
    public boolean isHighConfidenceMatch() {
        return matchScore != null && matchScore >= 0.8;
    }

    /**
     * Checks if the duplicate application is still active.
     * 
     * @return true if the previous application status indicates an active application
     */
    public boolean isActiveApplication() {
        return previousApplicationStatus != null && previousApplicationStatus.isActive();
    }

    /**
     * Gets the number of matched fields.
     * 
     * @return count of matched fields
     */
    public int getMatchedFieldCount() {
        return matchedFields != null ? matchedFields.size() : 0;
    }

    @Override
    public String toString() {
        return "DuplicateApplicationDto{" +
                "applicationId='" + applicationId + '\'' +
                ", submissionDate=" + submissionDate +
                ", matchedFields=" + matchedFields +
                ", matchScore=" + matchScore +
                ", previousApplicationStatus=" + previousApplicationStatus +
                ", customerName='" + customerName + '\'' +
                ", matchNotes='" + matchNotes + '\'' +
                '}';
    }
}
