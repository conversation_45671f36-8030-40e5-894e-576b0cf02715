package com.bank.dedupe.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Index;

import java.time.LocalDateTime;

/**
 * Entity representing audit log entries for comprehensive tracking and compliance.
 * Stores all significant events and actions for RBI audit requirements.
 */
@Entity
@Table(name = "audit_logs", indexes = {
    @Index(name = "idx_reference_number", columnList = "reference_number"),
    @Index(name = "idx_application_id", columnList = "application_id"),
    @Index(name = "idx_event_type", columnList = "event_type"),
    @Index(name = "idx_timestamp", columnList = "timestamp"),
    @Index(name = "idx_user_id", columnList = "user_id")
})
public class AuditLogEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "reference_number", nullable = false, length = 50)
    private String referenceNumber;

    @Column(name = "application_id", length = 50)
    private String applicationId;

    @Column(name = "event_type", nullable = false, length = 50)
    private String eventType;

    @Column(name = "event_description", nullable = false, length = 500)
    private String eventDescription;

    @Column(name = "timestamp", nullable = false)
    private LocalDateTime timestamp;

    @Column(name = "user_id", length = 50)
    private String userId;

    @Column(name = "session_id", length = 100)
    private String sessionId;

    @Column(name = "ip_address", length = 45)
    private String ipAddress;

    @Column(name = "user_agent", length = 500)
    private String userAgent;

    @Column(name = "request_data", columnDefinition = "TEXT")
    private String requestData;

    @Column(name = "response_data", columnDefinition = "TEXT")
    private String responseData;

    @Column(name = "error_details", columnDefinition = "TEXT")
    private String errorDetails;

    @Column(name = "processing_time_ms")
    private Long processingTimeMs;

    @Column(name = "external_service_called", length = 100)
    private String externalServiceCalled;

    @Column(name = "retry_attempt")
    private Integer retryAttempt;

    @Column(name = "compliance_flag", nullable = false)
    private Boolean complianceFlag = true;

    // Default constructor
    public AuditLogEntity() {
        this.timestamp = LocalDateTime.now();
    }

    // Constructor with essential fields
    public AuditLogEntity(String referenceNumber, String eventType, String eventDescription) {
        this();
        this.referenceNumber = referenceNumber;
        this.eventType = eventType;
        this.eventDescription = eventDescription;
    }

    // Constructor with 4 parameters
    public AuditLogEntity(String referenceNumber, String applicationId, String eventType,
                         String eventDescription) {
        this();
        this.referenceNumber = referenceNumber;
        this.applicationId = applicationId;
        this.eventType = eventType;
        this.eventDescription = eventDescription;
    }

    // Constructor with common fields
    public AuditLogEntity(String referenceNumber, String applicationId, String eventType,
                         String eventDescription, String userId) {
        this();
        this.referenceNumber = referenceNumber;
        this.applicationId = applicationId;
        this.eventType = eventType;
        this.eventDescription = eventDescription;
        this.userId = userId;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getEventDescription() {
        return eventDescription;
    }

    public void setEventDescription(String eventDescription) {
        this.eventDescription = eventDescription;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getRequestData() {
        return requestData;
    }

    public void setRequestData(String requestData) {
        this.requestData = requestData;
    }

    public String getResponseData() {
        return responseData;
    }

    public void setResponseData(String responseData) {
        this.responseData = responseData;
    }

    public String getErrorDetails() {
        return errorDetails;
    }

    public void setErrorDetails(String errorDetails) {
        this.errorDetails = errorDetails;
    }

    public Long getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(Long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }

    public String getExternalServiceCalled() {
        return externalServiceCalled;
    }

    public void setExternalServiceCalled(String externalServiceCalled) {
        this.externalServiceCalled = externalServiceCalled;
    }

    public Integer getRetryAttempt() {
        return retryAttempt;
    }

    public void setRetryAttempt(Integer retryAttempt) {
        this.retryAttempt = retryAttempt;
    }

    public Boolean getComplianceFlag() {
        return complianceFlag;
    }

    public void setComplianceFlag(Boolean complianceFlag) {
        this.complianceFlag = complianceFlag;
    }

    /**
     * Creates an audit log entry for request initiation.
     *
     * @param referenceNumber unique reference for the request
     * @param applicationId application being checked
     * @param maskedRequestData masked request data for audit
     * @return AuditLogEntity for request initiation
     */
    public static AuditLogEntity requestInitiated(String referenceNumber, String applicationId,
                                                 String maskedRequestData) {
        AuditLogEntity audit = new AuditLogEntity(referenceNumber, applicationId,
                                                 "REQUEST_INITIATED",
                                                 "Deduplication request initiated");
        audit.setRequestData(maskedRequestData);
        return audit;
    }

    /**
     * Creates an audit log entry for external service calls.
     *
     * @param referenceNumber unique reference for the request
     * @param serviceName name of the external service
     * @param maskedRequestData masked request data
     * @param retryAttempt current retry attempt number
     * @return AuditLogEntity for external service call
     */
    public static AuditLogEntity externalServiceCall(String referenceNumber, String serviceName,
                                                    String maskedRequestData, Integer retryAttempt) {
        AuditLogEntity audit = new AuditLogEntity(referenceNumber, "EXTERNAL_SERVICE_CALL",
                                                 "Called external service: " + serviceName);
        audit.setExternalServiceCalled(serviceName);
        audit.setRequestData(maskedRequestData);
        audit.setRetryAttempt(retryAttempt);
        return audit;
    }

    /**
     * Creates an audit log entry for successful responses.
     *
     * @param referenceNumber unique reference for the request
     * @param applicationId application that was checked
     * @param maskedResponseData masked response data
     * @param processingTime total processing time
     * @return AuditLogEntity for successful response
     */
    public static AuditLogEntity responseSuccess(String referenceNumber, String applicationId,
                                               String maskedResponseData, Long processingTime) {
        AuditLogEntity audit = new AuditLogEntity(referenceNumber, applicationId,
                                                 "RESPONSE_SUCCESS",
                                                 "Deduplication completed successfully");
        audit.setResponseData(maskedResponseData);
        audit.setProcessingTimeMs(processingTime);
        return audit;
    }

    /**
     * Creates an audit log entry for error conditions.
     *
     * @param referenceNumber unique reference for the request
     * @param applicationId application that was being checked
     * @param errorDetails details about the error
     * @param processingTime processing time before error
     * @return AuditLogEntity for error condition
     */
    public static AuditLogEntity responseError(String referenceNumber, String applicationId,
                                             String errorDetails, Long processingTime) {
        AuditLogEntity audit = new AuditLogEntity(referenceNumber, applicationId,
                                                 "RESPONSE_ERROR",
                                                 "Deduplication failed with error");
        audit.setErrorDetails(errorDetails);
        audit.setProcessingTimeMs(processingTime);
        audit.setComplianceFlag(false);
        return audit;
    }

    @Override
    public String toString() {
        return "AuditLogEntity{" +
                "id=" + id +
                ", referenceNumber='" + referenceNumber + '\'' +
                ", applicationId='" + applicationId + '\'' +
                ", eventType='" + eventType + '\'' +
                ", eventDescription='" + eventDescription + '\'' +
                ", timestamp=" + timestamp +
                ", userId='" + userId + '\'' +
                ", externalServiceCalled='" + externalServiceCalled + '\'' +
                ", retryAttempt=" + retryAttempt +
                ", processingTimeMs=" + processingTimeMs +
                ", complianceFlag=" + complianceFlag +
                '}';
    }
}
