package com.bank.dedupe.repository;

import com.bank.dedupe.entity.AuditLogEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository interface for AuditLogEntity.
 * Provides data access methods for audit logs and compliance tracking.
 */
@Repository
public interface AuditLogRepository extends JpaRepository<AuditLogEntity, Long> {

    /**
     * Finds all audit logs for a specific reference number.
     * 
     * @param referenceNumber the unique reference number
     * @return list of audit logs ordered by timestamp
     */
    @Query("SELECT a FROM AuditLogEntity a WHERE a.referenceNumber = :referenceNumber " +
           "ORDER BY a.timestamp ASC")
    List<AuditLogEntity> findByReferenceNumber(@Param("referenceNumber") String referenceNumber);

    /**
     * Finds all audit logs for a specific application ID.
     * 
     * @param applicationId the application identifier
     * @return list of audit logs ordered by timestamp
     */
    @Query("SELECT a FROM AuditLogEntity a WHERE a.applicationId = :applicationId " +
           "ORDER BY a.timestamp ASC")
    List<AuditLogEntity> findByApplicationId(@Param("applicationId") String applicationId);

    /**
     * Finds audit logs by event type within a date range.
     * 
     * @param eventType the event type to search for
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of audit logs matching the criteria
     */
    @Query("SELECT a FROM AuditLogEntity a WHERE a.eventType = :eventType " +
           "AND a.timestamp BETWEEN :startTime AND :endTime " +
           "ORDER BY a.timestamp DESC")
    List<AuditLogEntity> findByEventTypeAndTimeRange(@Param("eventType") String eventType,
                                                     @Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * Finds audit logs by user ID within a date range.
     * 
     * @param userId the user identifier
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of audit logs for the user
     */
    @Query("SELECT a FROM AuditLogEntity a WHERE a.userId = :userId " +
           "AND a.timestamp BETWEEN :startTime AND :endTime " +
           "ORDER BY a.timestamp DESC")
    List<AuditLogEntity> findByUserIdAndTimeRange(@Param("userId") String userId,
                                                  @Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime);

    /**
     * Finds audit logs with compliance flag set to false (non-compliant events).
     * 
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of non-compliant audit logs
     */
    @Query("SELECT a FROM AuditLogEntity a WHERE a.complianceFlag = false " +
           "AND a.timestamp BETWEEN :startTime AND :endTime " +
           "ORDER BY a.timestamp DESC")
    List<AuditLogEntity> findNonCompliantEvents(@Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * Finds audit logs for external service calls within a date range.
     * 
     * @param serviceName the external service name
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of external service call logs
     */
    @Query("SELECT a FROM AuditLogEntity a WHERE a.externalServiceCalled = :serviceName " +
           "AND a.timestamp BETWEEN :startTime AND :endTime " +
           "ORDER BY a.timestamp DESC")
    List<AuditLogEntity> findExternalServiceCalls(@Param("serviceName") String serviceName,
                                                  @Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime);

    /**
     * Finds audit logs with error details (failed operations).
     * 
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of error audit logs
     */
    @Query("SELECT a FROM AuditLogEntity a WHERE a.errorDetails IS NOT NULL " +
           "AND a.timestamp BETWEEN :startTime AND :endTime " +
           "ORDER BY a.timestamp DESC")
    List<AuditLogEntity> findErrorLogs(@Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime);

    /**
     * Finds audit logs with retry attempts greater than zero.
     * 
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of retry audit logs
     */
    @Query("SELECT a FROM AuditLogEntity a WHERE a.retryAttempt > 0 " +
           "AND a.timestamp BETWEEN :startTime AND :endTime " +
           "ORDER BY a.timestamp DESC")
    List<AuditLogEntity> findRetryLogs(@Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime);

    /**
     * Counts audit logs by event type within a date range.
     * 
     * @param eventType the event type to count
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return count of audit logs matching the criteria
     */
    @Query("SELECT COUNT(a) FROM AuditLogEntity a WHERE a.eventType = :eventType " +
           "AND a.timestamp BETWEEN :startTime AND :endTime")
    Long countByEventTypeAndTimeRange(@Param("eventType") String eventType,
                                     @Param("startTime") LocalDateTime startTime,
                                     @Param("endTime") LocalDateTime endTime);

    /**
     * Gets audit statistics by event type within a date range.
     * 
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of event type counts
     */
    @Query("SELECT a.eventType, COUNT(a) FROM AuditLogEntity a " +
           "WHERE a.timestamp BETWEEN :startTime AND :endTime " +
           "GROUP BY a.eventType " +
           "ORDER BY COUNT(a) DESC")
    List<Object[]> getAuditStatistics(@Param("startTime") LocalDateTime startTime,
                                     @Param("endTime") LocalDateTime endTime);

    /**
     * Finds audit logs that are older than the specified retention period.
     * Used for data cleanup and retention policy enforcement.
     * 
     * @param cutoffDate cutoff date for retention
     * @return list of audit logs older than the cutoff date
     */
    @Query("SELECT a FROM AuditLogEntity a WHERE a.timestamp < :cutoffDate " +
           "ORDER BY a.timestamp ASC")
    List<AuditLogEntity> findLogsOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * Finds audit logs with processing time exceeding the threshold.
     * Used for performance monitoring.
     * 
     * @param thresholdMs processing time threshold in milliseconds
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of slow operation logs
     */
    @Query("SELECT a FROM AuditLogEntity a WHERE a.processingTimeMs > :thresholdMs " +
           "AND a.timestamp BETWEEN :startTime AND :endTime " +
           "ORDER BY a.processingTimeMs DESC")
    List<AuditLogEntity> findSlowOperations(@Param("thresholdMs") Long thresholdMs,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * Finds the complete audit trail for a reference number.
     * Includes all events from initiation to completion.
     * 
     * @param referenceNumber the unique reference number
     * @return complete audit trail ordered by timestamp
     */
    @Query("SELECT a FROM AuditLogEntity a WHERE a.referenceNumber = :referenceNumber " +
           "ORDER BY a.timestamp ASC, a.id ASC")
    List<AuditLogEntity> findCompleteAuditTrail(@Param("referenceNumber") String referenceNumber);

    /**
     * Finds audit logs by IP address within a date range.
     * Used for security monitoring and access tracking.
     * 
     * @param ipAddress the IP address to search for
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of audit logs from the IP address
     */
    @Query("SELECT a FROM AuditLogEntity a WHERE a.ipAddress = :ipAddress " +
           "AND a.timestamp BETWEEN :startTime AND :endTime " +
           "ORDER BY a.timestamp DESC")
    List<AuditLogEntity> findByIpAddressAndTimeRange(@Param("ipAddress") String ipAddress,
                                                     @Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * Gets daily audit log counts for a date range.
     * Used for monitoring and reporting.
     * 
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of daily counts
     */
    @Query("SELECT DATE(a.timestamp), COUNT(a) FROM AuditLogEntity a " +
           "WHERE a.timestamp BETWEEN :startTime AND :endTime " +
           "GROUP BY DATE(a.timestamp) " +
           "ORDER BY DATE(a.timestamp)")
    List<Object[]> getDailyAuditCounts(@Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime);
}
