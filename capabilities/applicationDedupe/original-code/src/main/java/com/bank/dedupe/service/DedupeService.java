package com.bank.dedupe.service;

import com.bank.dedupe.dto.DedupeRequestDto;
import com.bank.dedupe.dto.DedupeResponseDto;
import com.bank.dedupe.dto.DuplicateApplicationDto;
import com.bank.dedupe.dto.ErrorDetailsDto;
import com.bank.dedupe.dto.external.ExternalDedupeRequestDto;
import com.bank.dedupe.dto.external.ExternalDedupeResponseDto;
import com.bank.dedupe.dto.external.ExternalDuplicateApplicationDto;
import com.bank.dedupe.entity.DedupeRequestEntity;
import com.bank.dedupe.enums.ApplicationStatus;
import com.bank.dedupe.enums.DedupeStatus;
import com.bank.dedupe.enums.DeduplicationPeriod;
import com.bank.dedupe.repository.DedupeRequestRepository;
import com.bank.dedupe.util.DataMaskingUtil;
import com.bank.dedupe.util.ReferenceNumberGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Main service for handling deduplication requests.
 * Orchestrates the entire deduplication process from request to response.
 */
@Service
@Transactional
public class DedupeService {

    private static final Logger logger = LoggerFactory.getLogger(DedupeService.class);

    private final DedupeRequestRepository dedupeRequestRepository;
    private final ExternalDedupeServiceClient externalServiceClient;
    private final AuditService auditService;
    private final ReferenceNumberGenerator referenceNumberGenerator;
    private final ObjectMapper objectMapper;

    public DedupeService(DedupeRequestRepository dedupeRequestRepository,
                        ExternalDedupeServiceClient externalServiceClient,
                        AuditService auditService,
                        ReferenceNumberGenerator referenceNumberGenerator,
                        ObjectMapper objectMapper) {
        this.dedupeRequestRepository = dedupeRequestRepository;
        this.externalServiceClient = externalServiceClient;
        this.auditService = auditService;
        this.referenceNumberGenerator = referenceNumberGenerator;
        this.objectMapper = objectMapper;
    }

    /**
     * Processes a deduplication request end-to-end.
     * 
     * @param requestDto the deduplication request
     * @return deduplication response
     */
    public DedupeResponseDto processDeduplicationRequest(DedupeRequestDto requestDto) {
        String referenceNumber = referenceNumberGenerator.generateReferenceNumber();
        MDC.put("referenceNumber", referenceNumber);
        MDC.put("applicationId", requestDto.getApplicationId());

        long startTime = System.currentTimeMillis();
        
        try {
            logger.info("Processing deduplication request for application: {}, reference: {}", 
                       requestDto.getApplicationId(), referenceNumber);

            // Phase 1: Input Validation and Request Persistence
            DedupeRequestEntity requestEntity = validateAndPersistRequest(requestDto, referenceNumber);
            
            // Audit: Request initiated
            auditService.logRequestInitiated(referenceNumber, requestDto.getApplicationId(), 
                                           DataMaskingUtil.createMaskedRequest(requestDto));

            // Phase 2: External Service Call
            ExternalDedupeResponseDto externalResponse = callExternalService(requestEntity);
            
            // Phase 3: Response Interpretation and Final Response
            DedupeResponseDto response = interpretAndBuildResponse(requestEntity, externalResponse);
            
            // Update request entity with final status and processing time
            long processingTime = System.currentTimeMillis() - startTime;
            updateRequestEntity(requestEntity, response, externalResponse, processingTime);
            
            // Audit: Response success
            auditService.logResponseSuccess(referenceNumber, requestDto.getApplicationId(), 
                                          DataMaskingUtil.createMaskedResponse(response), processingTime);

            logger.info("Deduplication request completed successfully for reference: {}, status: {}, processing time: {}ms", 
                       referenceNumber, response.getDedupeStatus(), processingTime);

            return response;

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            logger.error("Deduplication request failed for reference: {}, error: {}", referenceNumber, e.getMessage(), e);
            
            // Create error response
            DedupeResponseDto errorResponse = createErrorResponse(referenceNumber, requestDto.getApplicationId(), e);
            
            // Update request entity with error status
            try {
                DedupeRequestEntity requestEntity = dedupeRequestRepository.findByReferenceNumber(referenceNumber)
                        .orElse(createRequestEntity(requestDto, referenceNumber));
                updateRequestEntityWithError(requestEntity, e, processingTime);
            } catch (Exception updateError) {
                logger.error("Failed to update request entity with error for reference: {}", referenceNumber, updateError);
            }
            
            // Audit: Response error
            auditService.logResponseError(referenceNumber, requestDto.getApplicationId(), 
                                        e.getMessage(), processingTime);

            return errorResponse;
        } finally {
            MDC.clear();
        }
    }

    /**
     * Validates the request and persists it to the database.
     * 
     * @param requestDto the request to validate and persist
     * @param referenceNumber the generated reference number
     * @return persisted request entity
     */
    private DedupeRequestEntity validateAndPersistRequest(DedupeRequestDto requestDto, String referenceNumber) {
        logger.debug("Validating and persisting request for reference: {}", referenceNumber);
        
        // Create and save request entity
        DedupeRequestEntity requestEntity = createRequestEntity(requestDto, referenceNumber);
        requestEntity = dedupeRequestRepository.save(requestEntity);
        
        logger.debug("Request persisted with ID: {} for reference: {}", requestEntity.getId(), referenceNumber);
        return requestEntity;
    }

    /**
     * Creates a request entity from the DTO.
     * 
     * @param requestDto the request DTO
     * @param referenceNumber the reference number
     * @return request entity
     */
    private DedupeRequestEntity createRequestEntity(DedupeRequestDto requestDto, String referenceNumber) {
        DeduplicationPeriod period = requestDto.getDeduplicationPeriodEnum();
        
        return new DedupeRequestEntity(
                referenceNumber,
                requestDto.getApplicationId(),
                requestDto.getDedupedCustomerName(),
                requestDto.getMobileNumber(),
                requestDto.getEmailAddress(),
                requestDto.getPan(),
                requestDto.getAadhaarNumber(),
                requestDto.getDateOfBirth(),
                requestDto.getApplicationSubmissionDate(),
                period
        );
    }

    /**
     * Calls the external deduplication service.
     * 
     * @param requestEntity the request entity
     * @return external service response
     */
    private ExternalDedupeResponseDto callExternalService(DedupeRequestEntity requestEntity) {
        logger.debug("Calling external service for reference: {}", requestEntity.getReferenceNumber());
        
        // Update status to processing
        requestEntity.updateStatus(DedupeStatus.PROCESSING);
        dedupeRequestRepository.save(requestEntity);
        
        // Create external request
        ExternalDedupeRequestDto externalRequest = createExternalRequest(requestEntity);
        
        // Store external request payload for audit
        try {
            String requestPayload = objectMapper.writeValueAsString(externalRequest);
            requestEntity.setExternalRequestPayload(DataMaskingUtil.maskJsonData(requestPayload));
        } catch (JsonProcessingException e) {
            logger.warn("Failed to serialize external request payload", e);
        }
        
        // Call external service
        ExternalDedupeResponseDto externalResponse = externalServiceClient.checkDuplicates(
                externalRequest, requestEntity.getReferenceNumber());
        
        // Store external response payload for audit
        try {
            String responsePayload = objectMapper.writeValueAsString(externalResponse);
            requestEntity.setExternalResponsePayload(DataMaskingUtil.maskJsonData(responsePayload));
        } catch (JsonProcessingException e) {
            logger.warn("Failed to serialize external response payload", e);
        }
        
        dedupeRequestRepository.save(requestEntity);
        
        return externalResponse;
    }

    /**
     * Creates an external service request from the request entity.
     * 
     * @param requestEntity the request entity
     * @return external service request
     */
    private ExternalDedupeRequestDto createExternalRequest(DedupeRequestEntity requestEntity) {
        return new ExternalDedupeRequestDto(
                requestEntity.getApplicationId(),
                requestEntity.getDedupedCustomerName(),
                requestEntity.getMobileNumber(),
                requestEntity.getEmailAddress(),
                requestEntity.getPan(),
                requestEntity.getAadhaarNumber(),
                requestEntity.getDateOfBirth(),
                requestEntity.getApplicationSubmissionDate(),
                requestEntity.getDeduplicationPeriod().getCode()
        );
    }

    /**
     * Interprets the external response and builds the final response.
     * 
     * @param requestEntity the request entity
     * @param externalResponse the external service response
     * @return final deduplication response
     */
    private DedupeResponseDto interpretAndBuildResponse(DedupeRequestEntity requestEntity, 
                                                       ExternalDedupeResponseDto externalResponse) {
        logger.debug("Interpreting external response for reference: {}", requestEntity.getReferenceNumber());
        
        DedupeStatus status = mapExternalStatusToInternal(externalResponse.getDedupeStatus());
        
        if (status == DedupeStatus.MATCH_FOUND || status == DedupeStatus.PARTIAL_MATCH) {
            return createMatchFoundResponse(requestEntity, externalResponse, status);
        } else if (status == DedupeStatus.NO_MATCH_FOUND) {
            return createNoMatchResponse(requestEntity, externalResponse);
        } else {
            return createErrorResponseFromExternal(requestEntity, externalResponse);
        }
    }

    /**
     * Maps external service status to internal status.
     * 
     * @param externalStatus the external status
     * @return internal dedupe status
     */
    private DedupeStatus mapExternalStatusToInternal(String externalStatus) {
        return switch (externalStatus) {
            case "MATCH_FOUND" -> DedupeStatus.MATCH_FOUND;
            case "NO_MATCH_FOUND" -> DedupeStatus.NO_MATCH_FOUND;
            case "PARTIAL_MATCH" -> DedupeStatus.PARTIAL_MATCH;
            case "ERROR" -> DedupeStatus.ERROR;
            default -> DedupeStatus.ERROR;
        };
    }

    /**
     * Creates a response for when matches are found.
     * 
     * @param requestEntity the request entity
     * @param externalResponse the external response
     * @param status the dedupe status
     * @return match found response
     */
    private DedupeResponseDto createMatchFoundResponse(DedupeRequestEntity requestEntity, 
                                                      ExternalDedupeResponseDto externalResponse, 
                                                      DedupeStatus status) {
        List<DuplicateApplicationDto> duplicates = null;
        
        if (externalResponse.hasDuplicateApplications()) {
            duplicates = externalResponse.getDuplicateApplications().stream()
                    .map(this::mapExternalDuplicateToInternal)
                    .collect(Collectors.toList());
        }
        
        String message = status == DedupeStatus.MATCH_FOUND ? 
                "Duplicate applications found" : 
                "Potential duplicate applications found requiring review";
        
        return new DedupeResponseDto(
                requestEntity.getReferenceNumber(),
                requestEntity.getApplicationId(),
                status,
                externalResponse.getDuplicateCount(),
                duplicates,
                message,
                requestEntity.getDeduplicationPeriod().getCode()
        );
    }

    /**
     * Creates a response for when no matches are found.
     * 
     * @param requestEntity the request entity
     * @param externalResponse the external response
     * @return no match response
     */
    private DedupeResponseDto createNoMatchResponse(DedupeRequestEntity requestEntity, 
                                                   ExternalDedupeResponseDto externalResponse) {
        String message = String.format("No duplicates found in the last %s", 
                                     requestEntity.getDeduplicationPeriod().getDisplayName().toLowerCase());
        
        return new DedupeResponseDto(
                requestEntity.getReferenceNumber(),
                requestEntity.getApplicationId(),
                DedupeStatus.NO_MATCH_FOUND,
                0,
                null,
                message,
                requestEntity.getDeduplicationPeriod().getCode()
        );
    }

    /**
     * Creates an error response from external service error.
     * 
     * @param requestEntity the request entity
     * @param externalResponse the external response
     * @return error response
     */
    private DedupeResponseDto createErrorResponseFromExternal(DedupeRequestEntity requestEntity, 
                                                             ExternalDedupeResponseDto externalResponse) {
        ErrorDetailsDto errorDetails = ErrorDetailsDto.externalServiceError(
                externalResponse.getMessage(), 
                requestEntity.getRetryCount());
        
        return new DedupeResponseDto(
                requestEntity.getReferenceNumber(),
                requestEntity.getApplicationId(),
                DedupeStatus.ERROR,
                "Deduplication service error: " + externalResponse.getMessage(),
                errorDetails
        );
    }

    /**
     * Maps external duplicate application to internal format.
     * 
     * @param external the external duplicate application
     * @return internal duplicate application DTO
     */
    private DuplicateApplicationDto mapExternalDuplicateToInternal(ExternalDuplicateApplicationDto external) {
        ApplicationStatus status = ApplicationStatus.fromDisplayName(external.getPreviousApplicationStatus());
        
        return new DuplicateApplicationDto(
                external.getApplicationId(),
                external.getSubmissionDate(),
                external.getMatchedFields(),
                external.getMatchScore(),
                status,
                external.getCustomerName(),
                external.getMatchNotes()
        );
    }

    /**
     * Creates an error response for exceptions.
     * 
     * @param referenceNumber the reference number
     * @param applicationId the application ID
     * @param exception the exception
     * @return error response
     */
    private DedupeResponseDto createErrorResponse(String referenceNumber, String applicationId, Exception exception) {
        ErrorDetailsDto errorDetails;
        
        if (exception instanceof ExternalDedupeServiceClient.ExternalServiceException) {
            ExternalDedupeServiceClient.ExternalServiceException serviceException = 
                    (ExternalDedupeServiceClient.ExternalServiceException) exception;
            errorDetails = ErrorDetailsDto.externalServiceError(
                    serviceException.getMessage(), 
                    0);
        } else {
            errorDetails = new ErrorDetailsDto("INTERNAL_ERROR", "Internal processing error", 
                                             exception.getMessage(), 0, "Contact support");
        }
        
        return new DedupeResponseDto(referenceNumber, applicationId, DedupeStatus.ERROR, 
                                   "Deduplication check failed", errorDetails);
    }

    /**
     * Updates the request entity with final status and response data.
     * 
     * @param requestEntity the request entity
     * @param response the final response
     * @param externalResponse the external response
     * @param processingTime the processing time
     */
    private void updateRequestEntity(DedupeRequestEntity requestEntity, DedupeResponseDto response, 
                                   ExternalDedupeResponseDto externalResponse, long processingTime) {
        requestEntity.updateStatus(response.getDedupeStatus());
        requestEntity.setProcessingTimeMs(processingTime);
        dedupeRequestRepository.save(requestEntity);
    }

    /**
     * Updates the request entity with error information.
     * 
     * @param requestEntity the request entity
     * @param exception the exception
     * @param processingTime the processing time
     */
    private void updateRequestEntityWithError(DedupeRequestEntity requestEntity, Exception exception, long processingTime) {
        requestEntity.updateStatus(DedupeStatus.ERROR);
        requestEntity.setErrorDetails(exception.getMessage());
        requestEntity.setProcessingTimeMs(processingTime);
        dedupeRequestRepository.save(requestEntity);
    }
}
