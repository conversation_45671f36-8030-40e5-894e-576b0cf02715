package com.bank.dedupe.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for the application.
 * Contains application-specific settings and business rules.
 */
@Configuration
@ConfigurationProperties(prefix = "app.dedupe")
public class ApplicationConfig {

    private String defaultPeriod = "90_DAYS";
    private String maxPeriod = "1_YEAR";
    private AuditConfig audit = new AuditConfig();
    private LoggingConfig logging = new LoggingConfig();

    // Getters and Setters
    public String getDefaultPeriod() {
        return defaultPeriod;
    }

    public void setDefaultPeriod(String defaultPeriod) {
        this.defaultPeriod = defaultPeriod;
    }

    public String getMaxPeriod() {
        return maxPeriod;
    }

    public void setMaxPeriod(String maxPeriod) {
        this.maxPeriod = maxPeriod;
    }

    public AuditConfig getAudit() {
        return audit;
    }

    public void setAudit(AuditConfig audit) {
        this.audit = audit;
    }

    public LoggingConfig getLogging() {
        return logging;
    }

    public void setLogging(LoggingConfig logging) {
        this.logging = logging;
    }

    /**
     * Configuration for audit and compliance settings.
     */
    public static class AuditConfig {
        private int retentionDays = 2555; // 7 years for RBI compliance
        private boolean enableDetailedAudit = true;
        private boolean enablePerformanceTracking = true;
        private boolean enableComplianceChecks = true;

        public int getRetentionDays() {
            return retentionDays;
        }

        public void setRetentionDays(int retentionDays) {
            this.retentionDays = retentionDays;
        }

        public boolean isEnableDetailedAudit() {
            return enableDetailedAudit;
        }

        public void setEnableDetailedAudit(boolean enableDetailedAudit) {
            this.enableDetailedAudit = enableDetailedAudit;
        }

        public boolean isEnablePerformanceTracking() {
            return enablePerformanceTracking;
        }

        public void setEnablePerformanceTracking(boolean enablePerformanceTracking) {
            this.enablePerformanceTracking = enablePerformanceTracking;
        }

        public boolean isEnableComplianceChecks() {
            return enableComplianceChecks;
        }

        public void setEnableComplianceChecks(boolean enableComplianceChecks) {
            this.enableComplianceChecks = enableComplianceChecks;
        }

        /**
         * Gets the retention period in milliseconds.
         * 
         * @return retention period in milliseconds
         */
        public long getRetentionPeriodMs() {
            return retentionDays * 24L * 60L * 60L * 1000L;
        }
    }

    /**
     * Configuration for logging settings.
     */
    public static class LoggingConfig {
        private boolean maskSensitiveData = true;
        private boolean includeRequestResponse = true;
        private boolean enableStructuredLogging = true;
        private String logLevel = "INFO";
        private boolean enableMetrics = true;

        public boolean isMaskSensitiveData() {
            return maskSensitiveData;
        }

        public void setMaskSensitiveData(boolean maskSensitiveData) {
            this.maskSensitiveData = maskSensitiveData;
        }

        public boolean isIncludeRequestResponse() {
            return includeRequestResponse;
        }

        public void setIncludeRequestResponse(boolean includeRequestResponse) {
            this.includeRequestResponse = includeRequestResponse;
        }

        public boolean isEnableStructuredLogging() {
            return enableStructuredLogging;
        }

        public void setEnableStructuredLogging(boolean enableStructuredLogging) {
            this.enableStructuredLogging = enableStructuredLogging;
        }

        public String getLogLevel() {
            return logLevel;
        }

        public void setLogLevel(String logLevel) {
            this.logLevel = logLevel;
        }

        public boolean isEnableMetrics() {
            return enableMetrics;
        }

        public void setEnableMetrics(boolean enableMetrics) {
            this.enableMetrics = enableMetrics;
        }
    }

    @Override
    public String toString() {
        return "ApplicationConfig{" +
                "defaultPeriod='" + defaultPeriod + '\'' +
                ", maxPeriod='" + maxPeriod + '\'' +
                ", audit.retentionDays=" + audit.retentionDays +
                ", logging.maskSensitiveData=" + logging.maskSensitiveData +
                '}';
    }
}
