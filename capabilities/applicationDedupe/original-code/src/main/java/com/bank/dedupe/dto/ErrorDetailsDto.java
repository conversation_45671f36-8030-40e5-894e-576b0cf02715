package com.bank.dedupe.dto;

import com.fasterxml.jackson.annotation.JsonFormat;


import java.time.LocalDateTime;

/**
 * Data Transfer Object for error details in deduplication responses.
 * Provides structured error information for debugging and audit purposes.
 */
public class ErrorDetailsDto {

    private String errorCode;

    private String errorMessage;

    private String technicalDetails;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime errorTimestamp;

    private Integer retryAttempts;

    private String suggestedAction;

    // Default constructor
    public ErrorDetailsDto() {
        this.errorTimestamp = LocalDateTime.now();
    }

    // Constructor with essential fields
    public ErrorDetailsDto(String errorCode, String errorMessage) {
        this();
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    // Constructor with all fields
    public ErrorDetailsDto(String errorCode, String errorMessage, String technicalDetails,
                          Integer retryAttempts, String suggestedAction) {
        this();
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.technicalDetails = technicalDetails;
        this.retryAttempts = retryAttempts;
        this.suggestedAction = suggestedAction;
    }

    // Getters and Setters
    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getTechnicalDetails() {
        return technicalDetails;
    }

    public void setTechnicalDetails(String technicalDetails) {
        this.technicalDetails = technicalDetails;
    }

    public LocalDateTime getErrorTimestamp() {
        return errorTimestamp;
    }

    public void setErrorTimestamp(LocalDateTime errorTimestamp) {
        this.errorTimestamp = errorTimestamp;
    }

    public Integer getRetryAttempts() {
        return retryAttempts;
    }

    public void setRetryAttempts(Integer retryAttempts) {
        this.retryAttempts = retryAttempts;
    }

    public String getSuggestedAction() {
        return suggestedAction;
    }

    public void setSuggestedAction(String suggestedAction) {
        this.suggestedAction = suggestedAction;
    }

    /**
     * Checks if this error is retryable based on the error code.
     * 
     * @return true if the error suggests a retry might be successful
     */
    public boolean isRetryable() {
        if (errorCode == null) {
            return false;
        }
        
        return errorCode.contains("TIMEOUT") || 
               errorCode.contains("UNAVAILABLE") || 
               errorCode.contains("NETWORK") ||
               errorCode.contains("TEMPORARY");
    }

    /**
     * Creates a standard error details object for external service failures.
     * 
     * @param technicalDetails specific technical error information
     * @param retryAttempts number of retry attempts made
     * @return ErrorDetailsDto for external service failure
     */
    public static ErrorDetailsDto externalServiceError(String technicalDetails, Integer retryAttempts) {
        return new ErrorDetailsDto(
            "EXT_SERVICE_UNAVAILABLE",
            "External deduplication service is temporarily unavailable",
            technicalDetails,
            retryAttempts,
            "Retry the request after a few minutes or contact support if the issue persists"
        );
    }

    /**
     * Creates a standard error details object for timeout errors.
     * 
     * @param timeoutDuration the timeout duration that was exceeded
     * @param retryAttempts number of retry attempts made
     * @return ErrorDetailsDto for timeout error
     */
    public static ErrorDetailsDto timeoutError(String timeoutDuration, Integer retryAttempts) {
        return new ErrorDetailsDto(
            "REQUEST_TIMEOUT",
            "Request to external service timed out",
            "Timeout after " + timeoutDuration,
            retryAttempts,
            "Retry the request or check network connectivity"
        );
    }

    /**
     * Creates a standard error details object for validation errors.
     * 
     * @param validationMessage specific validation error message
     * @return ErrorDetailsDto for validation error
     */
    public static ErrorDetailsDto validationError(String validationMessage) {
        return new ErrorDetailsDto(
            "VALIDATION_ERROR",
            "Request validation failed",
            validationMessage,
            0,
            "Correct the request data and retry"
        );
    }

    @Override
    public String toString() {
        return "ErrorDetailsDto{" +
                "errorCode='" + errorCode + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", technicalDetails='" + technicalDetails + '\'' +
                ", errorTimestamp=" + errorTimestamp +
                ", retryAttempts=" + retryAttempts +
                ", suggestedAction='" + suggestedAction + '\'' +
                '}';
    }
}
