package com.bank.dedupe.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * Configuration for WebClient used to communicate with external services.
 * Provides optimized HTTP client settings for reliable external service calls.
 */
@Configuration
public class WebClientConfig {

    private final ExternalServiceConfig externalServiceConfig;

    public WebClientConfig(ExternalServiceConfig externalServiceConfig) {
        this.externalServiceConfig = externalServiceConfig;
    }

    /**
     * Creates a WebClient bean configured for external service communication.
     * 
     * @return configured WebClient instance
     */
    @Bean
    public WebClient webClient() {
        HttpClient httpClient = HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 
                       (int) externalServiceConfig.getTimeout().toMillis())
                .responseTimeout(externalServiceConfig.getTimeout())
                .doOnConnected(conn -> 
                    conn.addHandlerLast(new ReadTimeoutHandler(
                            externalServiceConfig.getTimeout().toSeconds(), TimeUnit.SECONDS))
                        .addHandlerLast(new WriteTimeoutHandler(
                            externalServiceConfig.getTimeout().toSeconds(), TimeUnit.SECONDS)));

        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(1024 * 1024)) // 1MB buffer
                .build();
    }

    /**
     * Creates a WebClient specifically for external deduplication service.
     * 
     * @return WebClient configured for dedupe service
     */
    @Bean("dedupeServiceWebClient")
    public WebClient dedupeServiceWebClient() {
        HttpClient httpClient = HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 
                       (int) externalServiceConfig.getTimeout().toMillis())
                .responseTimeout(externalServiceConfig.getTimeout())
                .doOnConnected(conn -> 
                    conn.addHandlerLast(new ReadTimeoutHandler(
                            externalServiceConfig.getTimeout().toSeconds(), TimeUnit.SECONDS))
                        .addHandlerLast(new WriteTimeoutHandler(
                            externalServiceConfig.getTimeout().toSeconds(), TimeUnit.SECONDS)));

        WebClient.Builder builder = WebClient.builder()
                .baseUrl(externalServiceConfig.getBaseUrl())
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(1024 * 1024)); // 1MB buffer

        // Add authentication if enabled
        if (externalServiceConfig.getAuth().isEnabled()) {
            if (externalServiceConfig.getAuth().useBasicAuth()) {
                builder.defaultHeaders(headers -> {
                    String auth = externalServiceConfig.getAuth().getUsername() + ":" + 
                                 externalServiceConfig.getAuth().getPassword();
                    String encodedAuth = java.util.Base64.getEncoder()
                            .encodeToString(auth.getBytes());
                    headers.add("Authorization", "Basic " + encodedAuth);
                });
            } else if (externalServiceConfig.getAuth().useApiKey()) {
                builder.defaultHeaders(headers -> 
                    headers.add("X-API-Key", externalServiceConfig.getAuth().getApiKey()));
            }
        }

        // Add common headers
        builder.defaultHeaders(headers -> {
            headers.add("Content-Type", "application/json");
            headers.add("Accept", "application/json");
            headers.add("User-Agent", "Application-Dedupe-Service/1.0");
        });

        return builder.build();
    }

    /**
     * Creates a WebClient for health checks and monitoring.
     * 
     * @return WebClient configured for health checks
     */
    @Bean("healthCheckWebClient")
    public WebClient healthCheckWebClient() {
        HttpClient httpClient = HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 5000) // 5 second timeout for health checks
                .responseTimeout(Duration.ofSeconds(5))
                .doOnConnected(conn -> 
                    conn.addHandlerLast(new ReadTimeoutHandler(5, TimeUnit.SECONDS))
                        .addHandlerLast(new WriteTimeoutHandler(5, TimeUnit.SECONDS)));

        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(256 * 1024)) // 256KB buffer for health checks
                .defaultHeaders(headers -> {
                    headers.add("Accept", "application/json");
                    headers.add("User-Agent", "Application-Dedupe-Service-HealthCheck/1.0");
                })
                .build();
    }
}
