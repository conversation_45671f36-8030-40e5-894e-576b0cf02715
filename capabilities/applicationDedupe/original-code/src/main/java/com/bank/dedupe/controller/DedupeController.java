package com.bank.dedupe.controller;

import com.bank.dedupe.dto.DedupeRequestDto;
import com.bank.dedupe.dto.DedupeResponseDto;
import com.bank.dedupe.dto.ErrorDetailsDto;
import com.bank.dedupe.entity.AuditLogEntity;
import com.bank.dedupe.enums.DedupeStatus;
import com.bank.dedupe.service.AuditService;
import com.bank.dedupe.service.DedupeService;
import com.bank.dedupe.util.DataMaskingUtil;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * REST Controller for application deduplication operations.
 * Provides endpoints for deduplication checks and audit trail access.
 */
@RestController
@RequestMapping("/v1")

public class DedupeController {

    private static final Logger logger = LoggerFactory.getLogger(DedupeController.class);

    private final DedupeService dedupeService;
    private final AuditService auditService;

    public DedupeController(DedupeService dedupeService, AuditService auditService) {
        this.dedupeService = dedupeService;
        this.auditService = auditService;
    }

    /**
     * Performs application deduplication check.
     * 
     * @param request the deduplication request
     * @param bindingResult validation results
     * @param httpRequest HTTP request for audit
     * @return deduplication response
     */
    @PostMapping("/duplicate-check")

    public ResponseEntity<DedupeResponseDto> checkDuplicates(
            @Valid @RequestBody DedupeRequestDto request,
            BindingResult bindingResult,
            HttpServletRequest httpRequest) {
        
        String traceId = UUID.randomUUID().toString();
        MDC.put("traceId", traceId);
        MDC.put("applicationId", request.getApplicationId());

        try {
            logger.info("Received deduplication request for application: {}", request.getApplicationId());
            
            // Validate request
            if (bindingResult.hasErrors()) {
                String validationErrors = bindingResult.getFieldErrors().stream()
                        .map(error -> error.getField() + ": " + error.getDefaultMessage())
                        .collect(Collectors.joining(", "));
                
                logger.warn("Validation errors in deduplication request: {}", validationErrors);
                
                DedupeResponseDto errorResponse = new DedupeResponseDto(
                        null, 
                        request.getApplicationId(), 
                        DedupeStatus.ERROR,
                        "Validation failed: " + validationErrors,
                        ErrorDetailsDto.validationError(validationErrors)
                );
                
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // Log masked request for audit
            String maskedRequest = DataMaskingUtil.createMaskedRequest(request);
            logger.debug("Processing deduplication request: {}", maskedRequest);

            // Process deduplication request
            DedupeResponseDto response = dedupeService.processDeduplicationRequest(request);
            
            // Determine HTTP status based on response
            HttpStatus status = determineHttpStatus(response);
            
            logger.info("Deduplication request completed for application: {}, status: {}, reference: {}", 
                       request.getApplicationId(), response.getDedupeStatus(), response.getReferenceNumber());

            return ResponseEntity.status(status).body(response);

        } catch (Exception e) {
            logger.error("Unexpected error processing deduplication request for application: {}", 
                        request.getApplicationId(), e);
            
            DedupeResponseDto errorResponse = new DedupeResponseDto(
                    null,
                    request.getApplicationId(),
                    DedupeStatus.ERROR,
                    "Internal server error occurred",
                    new ErrorDetailsDto("INTERNAL_ERROR", "Internal server error", 
                                       e.getMessage(), 0, "Contact support")
            );
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        } finally {
            MDC.clear();
        }
    }

    /**
     * Gets the audit trail for a specific reference number.
     * 
     * @param referenceNumber the reference number
     * @return audit trail
     */
    @GetMapping("/audit-trail/{referenceNumber}")

    public ResponseEntity<List<AuditLogEntity>> getAuditTrail(

            @PathVariable String referenceNumber) {
        
        try {
            logger.info("Retrieving audit trail for reference: {}", referenceNumber);
            
            List<AuditLogEntity> auditTrail = auditService.getAuditTrail(referenceNumber);
            
            if (auditTrail.isEmpty()) {
                logger.warn("No audit trail found for reference: {}", referenceNumber);
                return ResponseEntity.notFound().build();
            }
            
            logger.info("Retrieved {} audit log entries for reference: {}", auditTrail.size(), referenceNumber);
            return ResponseEntity.ok(auditTrail);
            
        } catch (Exception e) {
            logger.error("Error retrieving audit trail for reference: {}", referenceNumber, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Gets audit logs for a specific application ID.
     * 
     * @param applicationId the application ID
     * @return audit logs
     */
    @GetMapping("/audit-logs/application/{applicationId}")

    public ResponseEntity<List<AuditLogEntity>> getAuditLogsByApplication(

            @PathVariable String applicationId) {
        
        try {
            logger.info("Retrieving audit logs for application: {}", applicationId);
            
            List<AuditLogEntity> auditLogs = auditService.getAuditLogsForApplication(applicationId);
            
            if (auditLogs.isEmpty()) {
                logger.warn("No audit logs found for application: {}", applicationId);
                return ResponseEntity.notFound().build();
            }
            
            logger.info("Retrieved {} audit log entries for application: {}", auditLogs.size(), applicationId);
            return ResponseEntity.ok(auditLogs);
            
        } catch (Exception e) {
            logger.error("Error retrieving audit logs for application: {}", applicationId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Health check endpoint.
     * 
     * @return health status
     */
    @GetMapping("/health")

    public ResponseEntity<String> healthCheck() {
        try {
            // Perform basic health checks
            LocalDateTime now = LocalDateTime.now();
            
            return ResponseEntity.ok("Deduplication service is healthy at " + now);
        } catch (Exception e) {
            logger.error("Health check failed", e);
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body("Deduplication service is unhealthy: " + e.getMessage());
        }
    }

    /**
     * Determines the appropriate HTTP status based on the deduplication response.
     * 
     * @param response the deduplication response
     * @return appropriate HTTP status
     */
    private HttpStatus determineHttpStatus(DedupeResponseDto response) {
        if (response.getDedupeStatus() == DedupeStatus.ERROR) {
            // Check if it's a validation error or external service error
            if (response.getErrorDetails() != null && 
                "VALIDATION_ERROR".equals(response.getErrorDetails().getErrorCode())) {
                return HttpStatus.BAD_REQUEST;
            } else {
                return HttpStatus.INTERNAL_SERVER_ERROR;
            }
        }
        
        return HttpStatus.OK;
    }
}
