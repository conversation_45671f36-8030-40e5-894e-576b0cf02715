package com.bank.dedupe.repository;

import com.bank.dedupe.entity.DedupeRequestEntity;
import com.bank.dedupe.enums.DedupeStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for DedupeRequestEntity.
 * Provides data access methods for deduplication requests.
 */
@Repository
public interface DedupeRequestRepository extends JpaRepository<DedupeRequestEntity, Long> {

    /**
     * Finds a deduplication request by its reference number.
     * 
     * @param referenceNumber the unique reference number
     * @return Optional containing the request if found
     */
    Optional<DedupeRequestEntity> findByReferenceNumber(String referenceNumber);

    /**
     * Finds all deduplication requests for a specific application ID.
     * 
     * @param applicationId the application identifier
     * @return list of deduplication requests
     */
    List<DedupeRequestEntity> findByApplicationId(String applicationId);

    /**
     * Finds deduplication requests by status.
     * 
     * @param status the dedupe status to search for
     * @return list of requests with the specified status
     */
    List<DedupeRequestEntity> findByStatus(DedupeStatus status);

    /**
     * Finds deduplication requests by PAN within a date range.
     * 
     * @param pan the PAN to search for
     * @param startDate start of the date range
     * @param endDate end of the date range
     * @return list of requests matching the criteria
     */
    @Query("SELECT d FROM DedupeRequestEntity d WHERE d.pan = :pan " +
           "AND d.applicationSubmissionDate BETWEEN :startDate AND :endDate " +
           "ORDER BY d.applicationSubmissionDate DESC")
    List<DedupeRequestEntity> findByPanAndDateRange(@Param("pan") String pan,
                                                    @Param("startDate") LocalDate startDate,
                                                    @Param("endDate") LocalDate endDate);

    /**
     * Finds deduplication requests by mobile number within a date range.
     * 
     * @param mobileNumber the mobile number to search for
     * @param startDate start of the date range
     * @param endDate end of the date range
     * @return list of requests matching the criteria
     */
    @Query("SELECT d FROM DedupeRequestEntity d WHERE d.mobileNumber = :mobileNumber " +
           "AND d.applicationSubmissionDate BETWEEN :startDate AND :endDate " +
           "ORDER BY d.applicationSubmissionDate DESC")
    List<DedupeRequestEntity> findByMobileNumberAndDateRange(@Param("mobileNumber") String mobileNumber,
                                                             @Param("startDate") LocalDate startDate,
                                                             @Param("endDate") LocalDate endDate);

    /**
     * Finds deduplication requests by email address within a date range.
     * 
     * @param emailAddress the email address to search for
     * @param startDate start of the date range
     * @param endDate end of the date range
     * @return list of requests matching the criteria
     */
    @Query("SELECT d FROM DedupeRequestEntity d WHERE d.emailAddress = :emailAddress " +
           "AND d.applicationSubmissionDate BETWEEN :startDate AND :endDate " +
           "ORDER BY d.applicationSubmissionDate DESC")
    List<DedupeRequestEntity> findByEmailAddressAndDateRange(@Param("emailAddress") String emailAddress,
                                                             @Param("startDate") LocalDate startDate,
                                                             @Param("endDate") LocalDate endDate);

    /**
     * Finds deduplication requests that need retry (failed with retryable errors).
     * 
     * @param maxRetryCount maximum number of retries allowed
     * @param cutoffTime cutoff time for considering requests for retry
     * @return list of requests that can be retried
     */
    @Query("SELECT d FROM DedupeRequestEntity d WHERE d.status = :status " +
           "AND d.retryCount < :maxRetryCount " +
           "AND d.updatedAt < :cutoffTime " +
           "ORDER BY d.updatedAt ASC")
    List<DedupeRequestEntity> findRetryableRequests(@Param("status") DedupeStatus status,
                                                    @Param("maxRetryCount") Integer maxRetryCount,
                                                    @Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * Finds requests created within a specific time range.
     * 
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of requests created within the time range
     */
    @Query("SELECT d FROM DedupeRequestEntity d WHERE d.createdAt BETWEEN :startTime AND :endTime " +
           "ORDER BY d.createdAt DESC")
    List<DedupeRequestEntity> findByCreatedAtBetween(@Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * Counts requests by status within a date range.
     * 
     * @param status the status to count
     * @param startDate start of the date range
     * @param endDate end of the date range
     * @return count of requests matching the criteria
     */
    @Query("SELECT COUNT(d) FROM DedupeRequestEntity d WHERE d.status = :status " +
           "AND d.createdAt BETWEEN :startDate AND :endDate")
    Long countByStatusAndDateRange(@Param("status") DedupeStatus status,
                                  @Param("startDate") LocalDateTime startDate,
                                  @Param("endDate") LocalDateTime endDate);

    /**
     * Finds requests that are older than the specified retention period.
     * Used for data cleanup and retention policy enforcement.
     * 
     * @param cutoffDate cutoff date for retention
     * @return list of requests older than the cutoff date
     */
    @Query("SELECT d FROM DedupeRequestEntity d WHERE d.createdAt < :cutoffDate " +
           "ORDER BY d.createdAt ASC")
    List<DedupeRequestEntity> findRequestsOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * Checks if a request exists for the given application ID.
     * 
     * @param applicationId the application identifier
     * @return true if a request exists for the application
     */
    boolean existsByApplicationId(String applicationId);

    /**
     * Finds the most recent request for a given application ID.
     * 
     * @param applicationId the application identifier
     * @return Optional containing the most recent request if found
     */
    @Query("SELECT d FROM DedupeRequestEntity d WHERE d.applicationId = :applicationId " +
           "ORDER BY d.createdAt DESC LIMIT 1")
    Optional<DedupeRequestEntity> findMostRecentByApplicationId(@Param("applicationId") String applicationId);

    /**
     * Gets statistics for requests within a date range.
     * 
     * @param startDate start of the date range
     * @param endDate end of the date range
     * @return list of status counts
     */
    @Query("SELECT d.status, COUNT(d) FROM DedupeRequestEntity d " +
           "WHERE d.createdAt BETWEEN :startDate AND :endDate " +
           "GROUP BY d.status")
    List<Object[]> getRequestStatistics(@Param("startDate") LocalDateTime startDate,
                                       @Param("endDate") LocalDateTime endDate);

    /**
     * Finds requests with processing time exceeding the threshold.
     * Used for performance monitoring and optimization.
     * 
     * @param thresholdMs processing time threshold in milliseconds
     * @param startDate start of the date range
     * @param endDate end of the date range
     * @return list of slow requests
     */
    @Query("SELECT d FROM DedupeRequestEntity d WHERE d.processingTimeMs > :thresholdMs " +
           "AND d.createdAt BETWEEN :startDate AND :endDate " +
           "ORDER BY d.processingTimeMs DESC")
    List<DedupeRequestEntity> findSlowRequests(@Param("thresholdMs") Long thresholdMs,
                                              @Param("startDate") LocalDateTime startDate,
                                              @Param("endDate") LocalDateTime endDate);
}
