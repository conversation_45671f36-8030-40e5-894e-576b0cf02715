package com.bank.dedupe.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * Utility class for masking sensitive data in logs and audit trails.
 * Ensures PII compliance and data privacy requirements.
 */
public class DataMaskingUtil {

    private static final Logger logger = LoggerFactory.getLogger(DataMaskingUtil.class);

    private static final String MASK_VALUE = "***MASKED***";
    private static final String PARTIAL_MASK_CHAR = "*";

    // Fields that should be completely masked
    private static final Set<String> SENSITIVE_FIELDS = new HashSet<>(Arrays.asList(
        "pan", "aadhaar", "aadhaarnumber", "mobile", "mobilenumber", "email", "emailaddress",
        "password", "token", "apikey", "secret"
    ));

    // Fields that should be partially masked (show first/last few characters)
    private static final Set<String> PARTIALLY_MASKABLE_FIELDS = new HashSet<>(Arrays.asList(
        "applicationId", "referenceNumber", "customerName", "dedupedCustomerName"
    ));

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Masks sensitive data in a JSON string.
     *
     * @param jsonString the JSON string to mask
     * @return masked JSON string
     */
    public static String maskJsonData(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return jsonString;
        }

        try {
            JsonNode rootNode = objectMapper.readTree(jsonString);
            JsonNode maskedNode = maskJsonNode(rootNode);
            return objectMapper.writeValueAsString(maskedNode);
        } catch (Exception e) {
            logger.warn("Failed to mask JSON data, returning masked placeholder", e);
            return "{\"data\":\"" + MASK_VALUE + "\"}";
        }
    }

    /**
     * Recursively masks sensitive fields in a JSON node.
     *
     * @param node the JSON node to mask
     * @return masked JSON node
     */
    private static JsonNode maskJsonNode(JsonNode node) {
        if (node.isObject()) {
            ObjectNode objectNode = (ObjectNode) node;
            ObjectNode maskedNode = objectMapper.createObjectNode();

            objectNode.fields().forEachRemaining(entry -> {
                String fieldName = entry.getKey();
                JsonNode fieldValue = entry.getValue();

                if (SENSITIVE_FIELDS.contains(fieldName.toLowerCase())) {
                    maskedNode.put(fieldName, MASK_VALUE);
                } else if (PARTIALLY_MASKABLE_FIELDS.contains(fieldName.toLowerCase()) && fieldValue.isTextual()) {
                    maskedNode.put(fieldName, partiallyMaskString(fieldValue.asText()));
                } else if (fieldValue.isObject() || fieldValue.isArray()) {
                    maskedNode.set(fieldName, maskJsonNode(fieldValue));
                } else {
                    maskedNode.set(fieldName, fieldValue);
                }
            });

            return maskedNode;
        } else if (node.isArray()) {
            for (int i = 0; i < node.size(); i++) {
                JsonNode arrayElement = node.get(i);
                if (arrayElement.isObject() || arrayElement.isArray()) {
                    ((ObjectNode) node).set(String.valueOf(i), maskJsonNode(arrayElement));
                }
            }
        }

        return node;
    }

    /**
     * Masks a PAN number, showing only first and last character.
     *
     * @param pan the PAN to mask
     * @return masked PAN
     */
    public static String maskPan(String pan) {
        if (pan == null || pan.length() < 3) {
            return MASK_VALUE;
        }

        return pan.charAt(0) + PARTIAL_MASK_CHAR.repeat(pan.length() - 2) + pan.charAt(pan.length() - 1);
    }

    /**
     * Masks an Aadhaar number, showing only last 4 digits.
     *
     * @param aadhaar the Aadhaar number to mask
     * @return masked Aadhaar number
     */
    public static String maskAadhaar(String aadhaar) {
        if (aadhaar == null || aadhaar.length() < 4) {
            return MASK_VALUE;
        }

        String cleanAadhaar = aadhaar.replaceAll("[^0-9]", "");
        if (cleanAadhaar.length() < 4) {
            return MASK_VALUE;
        }

        return "XXXX-XXXX-" + cleanAadhaar.substring(cleanAadhaar.length() - 4);
    }

    /**
     * Masks a mobile number, showing only last 4 digits.
     *
     * @param mobile the mobile number to mask
     * @return masked mobile number
     */
    public static String maskMobileNumber(String mobile) {
        if (mobile == null || mobile.length() < 4) {
            return MASK_VALUE;
        }

        String cleanMobile = mobile.replaceAll("[^0-9]", "");
        if (cleanMobile.length() < 4) {
            return MASK_VALUE;
        }

        return PARTIAL_MASK_CHAR.repeat(cleanMobile.length() - 4) + cleanMobile.substring(cleanMobile.length() - 4);
    }

    /**
     * Masks an email address, showing only domain.
     *
     * @param email the email address to mask
     * @return masked email address
     */
    public static String maskEmailAddress(String email) {
        if (email == null || !email.contains("@")) {
            return MASK_VALUE;
        }

        String[] parts = email.split("@");
        if (parts.length != 2) {
            return MASK_VALUE;
        }

        return PARTIAL_MASK_CHAR.repeat(Math.max(1, parts[0].length())) + "@" + parts[1];
    }

    /**
     * Partially masks a string, showing first and last few characters.
     *
     * @param value the string to partially mask
     * @return partially masked string
     */
    public static String partiallyMaskString(String value) {
        if (value == null || value.length() <= 4) {
            return MASK_VALUE;
        }

        int showChars = Math.min(2, value.length() / 4);
        String start = value.substring(0, showChars);
        String end = value.substring(value.length() - showChars);
        String middle = PARTIAL_MASK_CHAR.repeat(value.length() - (2 * showChars));

        return start + middle + end;
    }

    /**
     * Masks sensitive data in a string representation of an object.
     *
     * @param objectString the string representation to mask
     * @return masked string
     */
    public static String maskObjectString(String objectString) {
        if (objectString == null) {
            return null;
        }

        String masked = objectString;

        // Mask common patterns
        masked = masked.replaceAll("pan='[^']*'", "pan='" + MASK_VALUE + "'");
        masked = masked.replaceAll("aadhaarNumber='[^']*'", "aadhaarNumber='" + MASK_VALUE + "'");
        masked = masked.replaceAll("mobileNumber='[^']*'", "mobileNumber='" + MASK_VALUE + "'");
        masked = masked.replaceAll("emailAddress='[^']*'", "emailAddress='" + MASK_VALUE + "'");

        // Mask JSON-like patterns
        masked = masked.replaceAll("\"pan\"\\s*:\\s*\"[^\"]*\"", "\"pan\":\"" + MASK_VALUE + "\"");
        masked = masked.replaceAll("\"aadhaarNumber\"\\s*:\\s*\"[^\"]*\"", "\"aadhaarNumber\":\"" + MASK_VALUE + "\"");
        masked = masked.replaceAll("\"mobileNumber\"\\s*:\\s*\"[^\"]*\"", "\"mobileNumber\":\"" + MASK_VALUE + "\"");
        masked = masked.replaceAll("\"emailAddress\"\\s*:\\s*\"[^\"]*\"", "\"emailAddress\":\"" + MASK_VALUE + "\"");

        return masked;
    }

    /**
     * Creates a masked version of a request for logging purposes.
     *
     * @param originalRequest the original request object
     * @return masked request string
     */
    public static String createMaskedRequest(Object originalRequest) {
        if (originalRequest == null) {
            return null;
        }

        try {
            String jsonString = objectMapper.writeValueAsString(originalRequest);
            return maskJsonData(jsonString);
        } catch (Exception e) {
            logger.warn("Failed to create masked request, using toString", e);
            return maskObjectString(originalRequest.toString());
        }
    }

    /**
     * Creates a masked version of a response for logging purposes.
     *
     * @param originalResponse the original response object
     * @return masked response string
     */
    public static String createMaskedResponse(Object originalResponse) {
        if (originalResponse == null) {
            return null;
        }

        try {
            String jsonString = objectMapper.writeValueAsString(originalResponse);
            return maskJsonData(jsonString);
        } catch (Exception e) {
            logger.warn("Failed to create masked response, using toString", e);
            return maskObjectString(originalResponse.toString());
        }
    }

    /**
     * Checks if a field name is considered sensitive.
     *
     * @param fieldName the field name to check
     * @return true if the field is sensitive
     */
    public static boolean isSensitiveField(String fieldName) {
        return fieldName != null && SENSITIVE_FIELDS.contains(fieldName.toLowerCase());
    }

    /**
     * Gets the mask value used for sensitive data.
     *
     * @return the mask value
     */
    public static String getMaskValue() {
        return MASK_VALUE;
    }
}
