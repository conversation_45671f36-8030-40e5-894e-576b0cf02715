package com.bank.dedupe.enums;

/**
 * Enumeration representing the status of a loan application.
 * Used to track the current state of applications found during deduplication.
 */
public enum ApplicationStatus {
    
    /**
     * Application has been submitted and is pending review.
     */
    PENDING("Pending", "Application submitted and pending review"),
    
    /**
     * Application is currently under review.
     */
    UNDER_REVIEW("Under Review", "Application is being reviewed"),
    
    /**
     * Application has been approved.
     */
    APPROVED("Approved", "Application has been approved"),
    
    /**
     * Application has been rejected.
     */
    REJECTED("Rejected", "Application has been rejected"),
    
    /**
     * Application has been withdrawn by the customer.
     */
    WITHDRAWN("Withdrawn", "Application withdrawn by customer"),
    
    /**
     * Application has been cancelled by the system or bank.
     */
    CANCELLED("Cancelled", "Application has been cancelled"),
    
    /**
     * Application has been disbursed (loan amount released).
     */
    DISBURSED("Disbursed", "Loan amount has been disbursed"),
    
    /**
     * Application is on hold pending additional information.
     */
    ON_HOLD("On Hold", "Application on hold pending additional information"),
    
    /**
     * Application has expired due to inactivity.
     */
    EXPIRED("Expired", "Application has expired"),
    
    /**
     * Application status is unknown or not available.
     */
    UNKNOWN("Unknown", "Application status unknown");

    private final String displayName;
    private final String description;

    ApplicationStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Checks if the application status indicates an active application.
     * 
     * @return true if status is PENDING, UNDER_REVIEW, or ON_HOLD
     */
    public boolean isActive() {
        return this == PENDING || this == UNDER_REVIEW || this == ON_HOLD;
    }

    /**
     * Checks if the application status indicates a completed application.
     * 
     * @return true if status is APPROVED, REJECTED, WITHDRAWN, CANCELLED, DISBURSED, or EXPIRED
     */
    public boolean isCompleted() {
        return this == APPROVED || this == REJECTED || this == WITHDRAWN || 
               this == CANCELLED || this == DISBURSED || this == EXPIRED;
    }

    /**
     * Checks if the application status indicates a successful outcome.
     * 
     * @return true if status is APPROVED or DISBURSED
     */
    public boolean isSuccessful() {
        return this == APPROVED || this == DISBURSED;
    }

    /**
     * Finds an ApplicationStatus by its display name (case-insensitive).
     * 
     * @param displayName the display name to search for
     * @return the matching ApplicationStatus, or UNKNOWN if not found
     */
    public static ApplicationStatus fromDisplayName(String displayName) {
        if (displayName == null) {
            return UNKNOWN;
        }
        
        for (ApplicationStatus status : values()) {
            if (status.displayName.equalsIgnoreCase(displayName)) {
                return status;
            }
        }
        
        return UNKNOWN;
    }
}
