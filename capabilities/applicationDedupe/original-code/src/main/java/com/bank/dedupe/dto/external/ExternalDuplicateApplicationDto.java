package com.bank.dedupe.dto.external;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDate;
import java.util.List;

/**
 * Data Transfer Object for duplicate application details from external service.
 * Represents individual duplicate applications found by the external service.
 */
public class ExternalDuplicateApplicationDto {

    @JsonProperty("applicationId")
    private String applicationId;

    @JsonProperty("submissionDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate submissionDate;

    @JsonProperty("matchedFields")
    private List<String> matchedFields;

    @JsonProperty("matchScore")
    private Double matchScore;

    @JsonProperty("previousApplicationStatus")
    private String previousApplicationStatus;

    @JsonProperty("customerName")
    private String customerName;

    @JsonProperty("matchNotes")
    private String matchNotes;

    // Default constructor
    public ExternalDuplicateApplicationDto() {
    }

    // Constructor with essential fields
    public ExternalDuplicateApplicationDto(String applicationId, LocalDate submissionDate, 
                                          List<String> matchedFields, Double matchScore, 
                                          String previousApplicationStatus) {
        this.applicationId = applicationId;
        this.submissionDate = submissionDate;
        this.matchedFields = matchedFields;
        this.matchScore = matchScore;
        this.previousApplicationStatus = previousApplicationStatus;
    }

    // Getters and Setters
    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public LocalDate getSubmissionDate() {
        return submissionDate;
    }

    public void setSubmissionDate(LocalDate submissionDate) {
        this.submissionDate = submissionDate;
    }

    public List<String> getMatchedFields() {
        return matchedFields;
    }

    public void setMatchedFields(List<String> matchedFields) {
        this.matchedFields = matchedFields;
    }

    public Double getMatchScore() {
        return matchScore;
    }

    public void setMatchScore(Double matchScore) {
        this.matchScore = matchScore;
    }

    public String getPreviousApplicationStatus() {
        return previousApplicationStatus;
    }

    public void setPreviousApplicationStatus(String previousApplicationStatus) {
        this.previousApplicationStatus = previousApplicationStatus;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getMatchNotes() {
        return matchNotes;
    }

    public void setMatchNotes(String matchNotes) {
        this.matchNotes = matchNotes;
    }

    /**
     * Checks if this is a high confidence match.
     * 
     * @return true if match score is >= 0.8
     */
    public boolean isHighConfidenceMatch() {
        return matchScore != null && matchScore >= 0.8;
    }

    /**
     * Checks if this is a medium confidence match.
     * 
     * @return true if match score is between 0.5 and 0.8
     */
    public boolean isMediumConfidenceMatch() {
        return matchScore != null && matchScore >= 0.5 && matchScore < 0.8;
    }

    /**
     * Checks if this is a low confidence match.
     * 
     * @return true if match score is < 0.5
     */
    public boolean isLowConfidenceMatch() {
        return matchScore != null && matchScore < 0.5;
    }

    /**
     * Gets the number of matched fields.
     * 
     * @return count of matched fields
     */
    public int getMatchedFieldCount() {
        return matchedFields != null ? matchedFields.size() : 0;
    }

    /**
     * Checks if a specific field was matched.
     * 
     * @param fieldName the field name to check
     * @return true if the field was matched
     */
    public boolean isFieldMatched(String fieldName) {
        return matchedFields != null && matchedFields.contains(fieldName);
    }

    /**
     * Checks if PAN was matched.
     * 
     * @return true if PAN was in matched fields
     */
    public boolean isPanMatched() {
        return isFieldMatched("PAN");
    }

    /**
     * Checks if Aadhaar was matched.
     * 
     * @return true if Aadhaar was in matched fields
     */
    public boolean isAadhaarMatched() {
        return isFieldMatched("AADHAAR");
    }

    /**
     * Checks if mobile number was matched.
     * 
     * @return true if mobile was in matched fields
     */
    public boolean isMobileMatched() {
        return isFieldMatched("MOBILE");
    }

    /**
     * Checks if email was matched.
     * 
     * @return true if email was in matched fields
     */
    public boolean isEmailMatched() {
        return isFieldMatched("EMAIL");
    }

    @Override
    public String toString() {
        return "ExternalDuplicateApplicationDto{" +
                "applicationId='" + applicationId + '\'' +
                ", submissionDate=" + submissionDate +
                ", matchedFields=" + matchedFields +
                ", matchScore=" + matchScore +
                ", previousApplicationStatus='" + previousApplicationStatus + '\'' +
                ", customerName='" + customerName + '\'' +
                ", matchNotes='" + matchNotes + '\'' +
                '}';
    }
}
