package com.bank.dedupe.util;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Utility class for generating unique reference numbers for deduplication requests.
 * Ensures traceability and uniqueness across all requests.
 */
@Component
public class ReferenceNumberGenerator {

    private static final String PREFIX = "DDR";
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final AtomicLong sequence = new AtomicLong(1);

    /**
     * Generates a unique reference number for a deduplication request.
     * Format: DDR-YYYYMMDD-HHMMSS-SEQUENCE
     * 
     * @return unique reference number
     */
    public String generateReferenceNumber() {
        LocalDateTime now = LocalDateTime.now();
        String datePart = now.format(DATE_FORMAT);
        String timePart = String.format("%02d%02d%02d", 
                                       now.getHour(), 
                                       now.getMinute(), 
                                       now.getSecond());
        long sequenceNumber = sequence.getAndIncrement();
        
        return String.format("%s-%s-%s-%06d", PREFIX, datePart, timePart, sequenceNumber);
    }

    /**
     * Generates a reference number with a custom prefix.
     * 
     * @param customPrefix the custom prefix to use
     * @return unique reference number with custom prefix
     */
    public String generateReferenceNumber(String customPrefix) {
        LocalDateTime now = LocalDateTime.now();
        String datePart = now.format(DATE_FORMAT);
        String timePart = String.format("%02d%02d%02d", 
                                       now.getHour(), 
                                       now.getMinute(), 
                                       now.getSecond());
        long sequenceNumber = sequence.getAndIncrement();
        
        return String.format("%s-%s-%s-%06d", customPrefix, datePart, timePart, sequenceNumber);
    }

    /**
     * Validates if a reference number follows the expected format.
     * 
     * @param referenceNumber the reference number to validate
     * @return true if the format is valid
     */
    public boolean isValidReferenceNumber(String referenceNumber) {
        if (referenceNumber == null || referenceNumber.trim().isEmpty()) {
            return false;
        }
        
        // Expected format: PREFIX-YYYYMMDD-HHMMSS-SEQUENCE
        String[] parts = referenceNumber.split("-");
        if (parts.length != 4) {
            return false;
        }
        
        // Check prefix
        if (!parts[0].equals(PREFIX)) {
            return false;
        }
        
        // Check date part (8 digits)
        if (parts[1].length() != 8 || !parts[1].matches("\\d{8}")) {
            return false;
        }
        
        // Check time part (6 digits)
        if (parts[2].length() != 6 || !parts[2].matches("\\d{6}")) {
            return false;
        }
        
        // Check sequence part (6 digits)
        if (parts[3].length() != 6 || !parts[3].matches("\\d{6}")) {
            return false;
        }
        
        return true;
    }

    /**
     * Extracts the date part from a reference number.
     * 
     * @param referenceNumber the reference number
     * @return date part as string (YYYYMMDD) or null if invalid
     */
    public String extractDatePart(String referenceNumber) {
        if (!isValidReferenceNumber(referenceNumber)) {
            return null;
        }
        
        String[] parts = referenceNumber.split("-");
        return parts[1];
    }

    /**
     * Extracts the time part from a reference number.
     * 
     * @param referenceNumber the reference number
     * @return time part as string (HHMMSS) or null if invalid
     */
    public String extractTimePart(String referenceNumber) {
        if (!isValidReferenceNumber(referenceNumber)) {
            return null;
        }
        
        String[] parts = referenceNumber.split("-");
        return parts[2];
    }

    /**
     * Extracts the sequence number from a reference number.
     * 
     * @param referenceNumber the reference number
     * @return sequence number or -1 if invalid
     */
    public long extractSequenceNumber(String referenceNumber) {
        if (!isValidReferenceNumber(referenceNumber)) {
            return -1;
        }
        
        String[] parts = referenceNumber.split("-");
        try {
            return Long.parseLong(parts[3]);
        } catch (NumberFormatException e) {
            return -1;
        }
    }

    /**
     * Gets the current sequence value (for monitoring purposes).
     * 
     * @return current sequence value
     */
    public long getCurrentSequence() {
        return sequence.get();
    }

    /**
     * Resets the sequence counter (use with caution, mainly for testing).
     */
    public void resetSequence() {
        sequence.set(1);
    }

    /**
     * Generates a batch of reference numbers for bulk operations.
     * 
     * @param count number of reference numbers to generate
     * @return array of unique reference numbers
     */
    public String[] generateBatch(int count) {
        if (count <= 0) {
            return new String[0];
        }
        
        String[] referenceNumbers = new String[count];
        for (int i = 0; i < count; i++) {
            referenceNumbers[i] = generateReferenceNumber();
        }
        
        return referenceNumbers;
    }

    /**
     * Generates a reference number for testing purposes with a specific timestamp.
     * 
     * @param timestamp the timestamp to use
     * @return reference number with the specified timestamp
     */
    public String generateTestReferenceNumber(LocalDateTime timestamp) {
        String datePart = timestamp.format(DATE_FORMAT);
        String timePart = String.format("%02d%02d%02d", 
                                       timestamp.getHour(), 
                                       timestamp.getMinute(), 
                                       timestamp.getSecond());
        long sequenceNumber = sequence.getAndIncrement();
        
        return String.format("%s-%s-%s-%06d", PREFIX, datePart, timePart, sequenceNumber);
    }
}
