package com.bank.dedupe.entity;

import com.bank.dedupe.enums.DedupeStatus;
import com.bank.dedupe.enums.DeduplicationPeriod;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Index;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Entity representing a deduplication request in the database.
 * Stores all information about deduplication requests for audit and tracking purposes.
 */
@Entity
@Table(name = "dedupe_requests", indexes = {
    @Index(name = "idx_application_id", columnList = "application_id"),
    @Index(name = "idx_reference_number", columnList = "reference_number"),
    @Index(name = "idx_pan", columnList = "pan"),
    @Index(name = "idx_mobile_number", columnList = "mobile_number"),
    @Index(name = "idx_created_at", columnList = "created_at"),
    @Index(name = "idx_status", columnList = "status")
})
public class DedupeRequestEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "reference_number", nullable = false, unique = true, length = 50)
    private String referenceNumber;

    @Column(name = "application_id", nullable = false, length = 50)
    private String applicationId;

    @Column(name = "deduped_customer_name", nullable = false, length = 200)
    private String dedupedCustomerName;

    @Column(name = "mobile_number", nullable = false, length = 15)
    private String mobileNumber;

    @Column(name = "email_address", nullable = false, length = 100)
    private String emailAddress;

    @Column(name = "pan", nullable = false, length = 10)
    private String pan;

    @Column(name = "aadhaar_number", nullable = false, length = 20)
    private String aadhaarNumber;

    @Column(name = "date_of_birth", nullable = false)
    private LocalDate dateOfBirth;

    @Column(name = "application_submission_date", nullable = false)
    private LocalDate applicationSubmissionDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "deduplication_period", nullable = false)
    private DeduplicationPeriod deduplicationPeriod;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private DedupeStatus status;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "external_request_payload", columnDefinition = "TEXT")
    private String externalRequestPayload;

    @Column(name = "external_response_payload", columnDefinition = "TEXT")
    private String externalResponsePayload;

    @Column(name = "error_details", columnDefinition = "TEXT")
    private String errorDetails;

    @Column(name = "retry_count", nullable = false)
    private Integer retryCount = 0;

    @Column(name = "processing_time_ms")
    private Long processingTimeMs;

    // Default constructor
    public DedupeRequestEntity() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.status = DedupeStatus.INITIATED;
    }

    // Constructor with essential fields
    public DedupeRequestEntity(String referenceNumber, String applicationId, String dedupedCustomerName,
                              String mobileNumber, String emailAddress, String pan, String aadhaarNumber,
                              LocalDate dateOfBirth, LocalDate applicationSubmissionDate,
                              DeduplicationPeriod deduplicationPeriod) {
        this();
        this.referenceNumber = referenceNumber;
        this.applicationId = applicationId;
        this.dedupedCustomerName = dedupedCustomerName;
        this.mobileNumber = mobileNumber;
        this.emailAddress = emailAddress;
        this.pan = pan;
        this.aadhaarNumber = aadhaarNumber;
        this.dateOfBirth = dateOfBirth;
        this.applicationSubmissionDate = applicationSubmissionDate;
        this.deduplicationPeriod = deduplicationPeriod;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getDedupedCustomerName() {
        return dedupedCustomerName;
    }

    public void setDedupedCustomerName(String dedupedCustomerName) {
        this.dedupedCustomerName = dedupedCustomerName;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getAadhaarNumber() {
        return aadhaarNumber;
    }

    public void setAadhaarNumber(String aadhaarNumber) {
        this.aadhaarNumber = aadhaarNumber;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public LocalDate getApplicationSubmissionDate() {
        return applicationSubmissionDate;
    }

    public void setApplicationSubmissionDate(LocalDate applicationSubmissionDate) {
        this.applicationSubmissionDate = applicationSubmissionDate;
    }

    public DeduplicationPeriod getDeduplicationPeriod() {
        return deduplicationPeriod;
    }

    public void setDeduplicationPeriod(DeduplicationPeriod deduplicationPeriod) {
        this.deduplicationPeriod = deduplicationPeriod;
    }

    public DedupeStatus getStatus() {
        return status;
    }

    public void setStatus(DedupeStatus status) {
        this.status = status;
        this.updatedAt = LocalDateTime.now();
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getExternalRequestPayload() {
        return externalRequestPayload;
    }

    public void setExternalRequestPayload(String externalRequestPayload) {
        this.externalRequestPayload = externalRequestPayload;
    }

    public String getExternalResponsePayload() {
        return externalResponsePayload;
    }

    public void setExternalResponsePayload(String externalResponsePayload) {
        this.externalResponsePayload = externalResponsePayload;
    }

    public String getErrorDetails() {
        return errorDetails;
    }

    public void setErrorDetails(String errorDetails) {
        this.errorDetails = errorDetails;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public Long getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(Long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }

    /**
     * Increments the retry count and updates the timestamp.
     */
    public void incrementRetryCount() {
        this.retryCount++;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Updates the status and timestamp.
     * 
     * @param newStatus the new status to set
     */
    public void updateStatus(DedupeStatus newStatus) {
        this.status = newStatus;
        this.updatedAt = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "DedupeRequestEntity{" +
                "id=" + id +
                ", referenceNumber='" + referenceNumber + '\'' +
                ", applicationId='" + applicationId + '\'' +
                ", dedupedCustomerName='" + dedupedCustomerName + '\'' +
                ", mobileNumber='***MASKED***'" +
                ", emailAddress='***MASKED***'" +
                ", pan='***MASKED***'" +
                ", aadhaarNumber='***MASKED***'" +
                ", dateOfBirth=" + dateOfBirth +
                ", applicationSubmissionDate=" + applicationSubmissionDate +
                ", deduplicationPeriod=" + deduplicationPeriod +
                ", status=" + status +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", retryCount=" + retryCount +
                ", processingTimeMs=" + processingTimeMs +
                '}';
    }
}
