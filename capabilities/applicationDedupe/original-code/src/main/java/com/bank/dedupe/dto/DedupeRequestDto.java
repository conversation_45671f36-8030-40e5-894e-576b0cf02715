package com.bank.dedupe.dto;

import com.bank.dedupe.enums.DeduplicationPeriod;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

import java.time.LocalDate;

/**
 * Data Transfer Object for deduplication requests from upstream applications.
 * Contains all necessary information to perform application deduplication.
 */
public class DedupeRequestDto {

    @NotBlank(message = "Application ID is required")
    @Size(max = 50, message = "Application ID must not exceed 50 characters")
    private String applicationId;

    @NotBlank(message = "Deduped customer name is required")
    @Size(max = 200, message = "Customer name must not exceed 200 characters")
    private String dedupedCustomerName;

    @NotBlank(message = "Mobile number is required")
    @Pattern(regexp = "^[0-9]{10}$", message = "Mobile number must be 10 digits")
    private String mobileNumber;

    @NotBlank(message = "Email address is required")
    @Email(message = "Email address must be valid")
    @Size(max = 100, message = "Email address must not exceed 100 characters")
    private String emailAddress;

    @NotBlank(message = "PAN is required")
    @Pattern(regexp = "^[A-Z]{5}[0-9]{4}[A-Z]{1}$", message = "PAN must be in valid format (**********)")
    private String pan;

    @NotBlank(message = "Aadhaar number is required")
    @Size(max = 20, message = "Aadhaar number must not exceed 20 characters")
    private String aadhaarNumber;

    @NotNull(message = "Date of birth is required")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate dateOfBirth;

    @NotNull(message = "Application submission date is required")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate applicationSubmissionDate;

    private String deduplicationPeriod;

    // Default constructor
    public DedupeRequestDto() {
    }

    // Constructor with all fields
    public DedupeRequestDto(String applicationId, String dedupedCustomerName, String mobileNumber,
                           String emailAddress, String pan, String aadhaarNumber, LocalDate dateOfBirth,
                           LocalDate applicationSubmissionDate, String deduplicationPeriod) {
        this.applicationId = applicationId;
        this.dedupedCustomerName = dedupedCustomerName;
        this.mobileNumber = mobileNumber;
        this.emailAddress = emailAddress;
        this.pan = pan;
        this.aadhaarNumber = aadhaarNumber;
        this.dateOfBirth = dateOfBirth;
        this.applicationSubmissionDate = applicationSubmissionDate;
        this.deduplicationPeriod = deduplicationPeriod;
    }

    // Getters and Setters
    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getDedupedCustomerName() {
        return dedupedCustomerName;
    }

    public void setDedupedCustomerName(String dedupedCustomerName) {
        this.dedupedCustomerName = dedupedCustomerName;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getAadhaarNumber() {
        return aadhaarNumber;
    }

    public void setAadhaarNumber(String aadhaarNumber) {
        this.aadhaarNumber = aadhaarNumber;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public LocalDate getApplicationSubmissionDate() {
        return applicationSubmissionDate;
    }

    public void setApplicationSubmissionDate(LocalDate applicationSubmissionDate) {
        this.applicationSubmissionDate = applicationSubmissionDate;
    }

    public String getDeduplicationPeriod() {
        return deduplicationPeriod;
    }

    public void setDeduplicationPeriod(String deduplicationPeriod) {
        this.deduplicationPeriod = deduplicationPeriod;
    }

    /**
     * Gets the deduplication period as an enum, with fallback to default.
     * 
     * @return DeduplicationPeriod enum value
     */
    public DeduplicationPeriod getDeduplicationPeriodEnum() {
        return DeduplicationPeriod.fromCode(this.deduplicationPeriod);
    }

    @Override
    public String toString() {
        return "DedupeRequestDto{" +
                "applicationId='" + applicationId + '\'' +
                ", dedupedCustomerName='" + dedupedCustomerName + '\'' +
                ", mobileNumber='***MASKED***'" +
                ", emailAddress='***MASKED***'" +
                ", pan='***MASKED***'" +
                ", aadhaarNumber='***MASKED***'" +
                ", dateOfBirth=" + dateOfBirth +
                ", applicationSubmissionDate=" + applicationSubmissionDate +
                ", deduplicationPeriod='" + deduplicationPeriod + '\'' +
                '}';
    }
}
