openapi: 3.0.3
info:
  title: Application Dedupe Service API
  description: |
    REST API for application deduplication operations in banking systems. 
    This service provides endpoints to check for duplicate loan applications 
    based on customer information and configurable deduplication periods.
    
    ## Features
    - Comprehensive deduplication checks using multiple customer identifiers
    - Configurable deduplication periods (30 days to 2 years)
    - Detailed audit trail and compliance tracking
    - Real-time duplicate detection with confidence scoring
    - Integration with external deduplication services
    
    ## Authentication
    Currently no authentication is required for this service.
    
    ## Rate Limiting
    Standard rate limiting applies to all endpoints.
  version: 1.0.0
  contact:
    name: Banking API Team
    email: <EMAIL>
    url: https://bank.com/api-support
  license:
    name: Proprietary
    url: https://bank.com/license

servers:
  - url: http://localhost:8105/api/application-dedupe
    description: Local Development Server
  - url: http://localhost:8080/api/application-dedupe
    description: Docker Development Server
  - url: https://api.bank.com/api/application-dedupe
    description: Production Server

paths:
  /v1/duplicate-check:
    post:
      tags:
        - Deduplication
      summary: Check for duplicate applications
      description: |
        Performs comprehensive deduplication check for a loan application.
        Searches for potential duplicates based on customer information
        within the specified deduplication period.
      operationId: checkDuplicates
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DedupeRequest'
            examples:
              standard_request:
                summary: Standard deduplication request
                value:
                  applicationId: "APP12345"
                  dedupedCustomerName: "ALICE JOHNSON"
                  mobileNumber: "**********"
                  emailAddress: "<EMAIL>"
                  pan: "**********"
                  aadhaarNumber: "XXXX-XXXX-1234"
                  dateOfBirth: "1990-05-20"
                  applicationSubmissionDate: "2025-06-04"
                  deduplicationPeriod: "90_DAYS"
              minimal_request:
                summary: Minimal required fields
                value:
                  applicationId: "APP67890"
                  dedupedCustomerName: "JOHN DOE"
                  mobileNumber: "9123456789"
                  emailAddress: "<EMAIL>"
                  pan: "**********"
                  aadhaarNumber: "YYYY-YYYY-5678"
                  dateOfBirth: "1985-03-15"
                  applicationSubmissionDate: "2025-06-04"
      responses:
        '200':
          description: Deduplication check completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DedupeResponse'
              examples:
                match_found:
                  summary: Duplicates found
                  value:
                    referenceNumber: "REF-2025-001234"
                    applicationId: "APP12345"
                    dedupeStatus: "MATCH_FOUND"
                    matchCount: 2
                    duplicateApplications:
                      - applicationId: "APP11111"
                        submissionDate: "2025-05-15"
                        matchedFields: ["pan", "mobileNumber", "emailAddress"]
                        matchScore: 95.5
                        previousApplicationStatus: "APPROVED"
                        customerName: "ALICE JOHNSON"
                        matchNotes: "High confidence match on PAN and mobile"
                      - applicationId: "APP22222"
                        submissionDate: "2025-04-20"
                        matchedFields: ["pan", "aadhaarNumber"]
                        matchScore: 88.2
                        previousApplicationStatus: "REJECTED"
                        customerName: "ALICE JOHNSON"
                        matchNotes: "Strong match on identity documents"
                    message: "2 duplicate applications found within the specified period"
                    timestamp: "2025-06-04T10:30:00"
                    searchPeriod: "90_DAYS"
                no_match_found:
                  summary: No duplicates found
                  value:
                    referenceNumber: "REF-2025-001235"
                    applicationId: "APP67890"
                    dedupeStatus: "NO_MATCH_FOUND"
                    matchCount: 0
                    duplicateApplications: []
                    message: "No duplicate applications found within the specified period"
                    timestamp: "2025-06-04T10:35:00"
                    searchPeriod: "90_DAYS"
                partial_match:
                  summary: Partial match requiring review
                  value:
                    referenceNumber: "REF-2025-001236"
                    applicationId: "APP33333"
                    dedupeStatus: "PARTIAL_MATCH"
                    matchCount: 1
                    duplicateApplications:
                      - applicationId: "APP44444"
                        submissionDate: "2025-05-25"
                        matchedFields: ["mobileNumber"]
                        matchScore: 65.0
                        previousApplicationStatus: "PENDING"
                        customerName: "SIMILAR NAME"
                        matchNotes: "Partial match - manual review recommended"
                    message: "Potential duplicate found - manual review required"
                    timestamp: "2025-06-04T10:40:00"
                    searchPeriod: "90_DAYS"
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DedupeErrorResponse'
              examples:
                validation_error:
                  summary: Validation error
                  value:
                    referenceNumber: "REF-2025-001237"
                    applicationId: "APP_INVALID"
                    dedupeStatus: "ERROR"
                    matchCount: 0
                    message: "Request validation failed"
                    timestamp: "2025-06-04T10:45:00"
                    errorDetails:
                      errorCode: "VALIDATION_ERROR"
                      errorMessage: "Request validation failed"
                      technicalDetails: "PAN format is invalid"
                      errorTimestamp: "2025-06-04T10:45:00"
                      retryAttempts: 0
                      suggestedAction: "Correct the request data and retry"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DedupeErrorResponse'
              examples:
                external_service_error:
                  summary: External service unavailable
                  value:
                    referenceNumber: "REF-2025-001238"
                    applicationId: "APP55555"
                    dedupeStatus: "ERROR"
                    matchCount: 0
                    message: "External deduplication service is temporarily unavailable"
                    timestamp: "2025-06-04T10:50:00"
                    errorDetails:
                      errorCode: "EXT_SERVICE_UNAVAILABLE"
                      errorMessage: "External deduplication service is temporarily unavailable"
                      technicalDetails: "Connection timeout after 30 seconds"
                      errorTimestamp: "2025-06-04T10:50:00"
                      retryAttempts: 3
                      suggestedAction: "Retry the request after a few minutes or contact support if the issue persists"

  /v1/audit/{applicationId}:
    get:
      tags:
        - Audit
      summary: Get audit trail for an application
      description: Retrieves the complete audit trail for deduplication requests related to a specific application ID.
      operationId: getAuditTrail
      parameters:
        - name: applicationId
          in: path
          required: true
          description: The application ID to retrieve audit trail for
          schema:
            type: string
            example: "APP12345"
        - name: limit
          in: query
          required: false
          description: Maximum number of audit records to return
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 50
        - name: offset
          in: query
          required: false
          description: Number of records to skip for pagination
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: Audit trail retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AuditRecord'
              examples:
                audit_trail:
                  summary: Sample audit trail
                  value:
                    - id: 1001
                      applicationId: "APP12345"
                      requestTimestamp: "2025-06-04T10:30:00"
                      responseTimestamp: "2025-06-04T10:30:02"
                      dedupeStatus: "MATCH_FOUND"
                      matchCount: 2
                      searchPeriod: "90_DAYS"
                      processingTimeMs: 2150
                      externalServiceCalled: true
                      maskedCustomerData: "Name: ALICE J*****, Mobile: 987****210"
        '404':
          description: Application not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v1/health:
    get:
      tags:
        - Health
      summary: Health check endpoint
      description: Returns the health status of the deduplication service
      operationId: healthCheck
      responses:
        '200':
          description: Service is healthy
          content:
            text/plain:
              schema:
                type: string
                example: "Deduplication service is healthy at 2025-06-04T10:30:00"
        '503':
          description: Service is unhealthy
          content:
            text/plain:
              schema:
                type: string
                example: "Deduplication service is unhealthy: Database connection failed"

  /v1/application-dedupe/original/health:
    get:
      tags:
        - Health
      summary: Detailed health check
      description: Returns detailed health status information
      operationId: detailedHealthCheck
      responses:
        '200':
          description: Detailed health status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatus'
              examples:
                healthy:
                  summary: Healthy service
                  value:
                    status: "UP"
                    service: "Application Dedupe Original"
                    timestamp: "2025-06-04T10:30:00"
                    version: "1.0.0"

components:
  schemas:
    DedupeRequest:
      type: object
      required:
        - applicationId
        - dedupedCustomerName
        - mobileNumber
        - emailAddress
        - pan
        - aadhaarNumber
        - dateOfBirth
        - applicationSubmissionDate
      properties:
        applicationId:
          type: string
          maxLength: 50
          description: Unique identifier for the loan application
          example: "APP12345"
        dedupedCustomerName:
          type: string
          maxLength: 200
          description: Standardized customer name for deduplication
          example: "ALICE JOHNSON"
        mobileNumber:
          type: string
          pattern: '^[0-9]{10}$'
          description: Customer's 10-digit mobile number
          example: "**********"
        emailAddress:
          type: string
          format: email
          maxLength: 100
          description: Customer's email address
          example: "<EMAIL>"
        pan:
          type: string
          pattern: '^[A-Z]{5}[0-9]{4}[A-Z]{1}$'
          description: Customer's PAN in format **********
          example: "**********"
        aadhaarNumber:
          type: string
          maxLength: 20
          description: Customer's Aadhaar number (masked for security)
          example: "XXXX-XXXX-1234"
        dateOfBirth:
          type: string
          format: date
          description: Customer's date of birth in YYYY-MM-DD format
          example: "1990-05-20"
        applicationSubmissionDate:
          type: string
          format: date
          description: Date when the application was submitted
          example: "2025-06-04"
        deduplicationPeriod:
          type: string
          enum: ["30_DAYS", "90_DAYS", "180_DAYS", "1_YEAR", "2_YEARS"]
          description: Period to search for duplicates
          example: "90_DAYS"
          default: "90_DAYS"

    DedupeResponse:
      type: object
      properties:
        referenceNumber:
          type: string
          description: Unique reference number for this deduplication request
          example: "REF-2025-001234"
        applicationId:
          type: string
          description: The application ID that was checked
          example: "APP12345"
        dedupeStatus:
          type: string
          enum: ["MATCH_FOUND", "NO_MATCH_FOUND", "PARTIAL_MATCH", "ERROR", "PROCESSING", "INITIATED"]
          description: Status of the deduplication check
          example: "MATCH_FOUND"
        matchCount:
          type: integer
          minimum: 0
          description: Number of duplicate applications found
          example: 2
        duplicateApplications:
          type: array
          items:
            $ref: '#/components/schemas/DuplicateApplication'
          description: List of duplicate applications found
        message:
          type: string
          description: Human-readable message describing the result
          example: "2 duplicate applications found within the specified period"
        timestamp:
          type: string
          format: date-time
          description: Timestamp when the response was generated
          example: "2025-06-04T10:30:00"
        searchPeriod:
          type: string
          description: The deduplication period that was used
          example: "90_DAYS"
        errorDetails:
          $ref: '#/components/schemas/ErrorDetails'

    DedupeErrorResponse:
      allOf:
        - $ref: '#/components/schemas/DedupeResponse'
        - type: object
          required:
            - errorDetails
          properties:
            dedupeStatus:
              type: string
              enum: ["ERROR"]
              example: "ERROR"
            errorDetails:
              $ref: '#/components/schemas/ErrorDetails'

    DuplicateApplication:
      type: object
      properties:
        applicationId:
          type: string
          description: ID of the duplicate application
          example: "APP11111"
        submissionDate:
          type: string
          format: date
          description: Date when the duplicate application was submitted
          example: "2025-05-15"
        matchedFields:
          type: array
          items:
            type: string
          description: List of fields that matched
          example: ["pan", "mobileNumber", "emailAddress"]
        matchScore:
          type: number
          format: double
          minimum: 0
          maximum: 100
          description: Confidence score of the match (0-100)
          example: 95.5
        previousApplicationStatus:
          type: string
          enum: ["PENDING", "UNDER_REVIEW", "APPROVED", "REJECTED", "WITHDRAWN", "CANCELLED", "DISBURSED", "ON_HOLD", "EXPIRED", "UNKNOWN"]
          description: Status of the previous application
          example: "APPROVED"
        customerName:
          type: string
          description: Customer name from the duplicate application
          example: "ALICE JOHNSON"
        matchNotes:
          type: string
          description: Additional notes about the match
          example: "High confidence match on PAN and mobile"

    ErrorDetails:
      type: object
      properties:
        errorCode:
          type: string
          description: Specific error code for programmatic handling
          example: "VALIDATION_ERROR"
        errorMessage:
          type: string
          description: Human-readable error message
          example: "Request validation failed"
        technicalDetails:
          type: string
          description: Technical details about the error
          example: "PAN format is invalid"
        errorTimestamp:
          type: string
          format: date-time
          description: When the error occurred
          example: "2025-06-04T10:45:00"
        retryAttempts:
          type: integer
          minimum: 0
          description: Number of retry attempts made
          example: 0
        suggestedAction:
          type: string
          description: Suggested action to resolve the error
          example: "Correct the request data and retry"

    AuditRecord:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Unique audit record ID
          example: 1001
        applicationId:
          type: string
          description: Application ID that was processed
          example: "APP12345"
        requestTimestamp:
          type: string
          format: date-time
          description: When the request was received
          example: "2025-06-04T10:30:00"
        responseTimestamp:
          type: string
          format: date-time
          description: When the response was sent
          example: "2025-06-04T10:30:02"
        dedupeStatus:
          type: string
          description: Final status of the deduplication check
          example: "MATCH_FOUND"
        matchCount:
          type: integer
          description: Number of matches found
          example: 2
        searchPeriod:
          type: string
          description: Deduplication period used
          example: "90_DAYS"
        processingTimeMs:
          type: integer
          description: Processing time in milliseconds
          example: 2150
        externalServiceCalled:
          type: boolean
          description: Whether external service was called
          example: true
        maskedCustomerData:
          type: string
          description: Masked customer data for audit purposes
          example: "Name: ALICE J*****, Mobile: 987****210"

    HealthStatus:
      type: object
      properties:
        status:
          type: string
          enum: ["UP", "DOWN"]
          description: Overall health status
          example: "UP"
        service:
          type: string
          description: Service name
          example: "Application Dedupe Original"
        timestamp:
          type: string
          format: date-time
          description: Health check timestamp
          example: "2025-06-04T10:30:00"
        version:
          type: string
          description: Service version
          example: "1.0.0"

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: Error message
          example: "Application not found"
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
          example: "2025-06-04T10:30:00"
        path:
          type: string
          description: Request path that caused the error
          example: "/v1/audit/INVALID_APP_ID"

tags:
  - name: Deduplication
    description: Application deduplication operations
  - name: Audit
    description: Audit trail and compliance operations
  - name: Health
    description: Service health and monitoring endpoints
