Goal: 
=====
The overarching reason to perform application dedupe is to detect and prevent multiple, overlapping or fraudulent loan applications by the same customer (or identity) to avoid credit risk, abuse, or policy violations. The actual application dedupe will be done by a service external to the capability. The capability will accept application details from an upstream application, relay them to an external Application Dedupe Service, accept and interpret the external service's response, and return a clear "Match Found" with details or "No Match Found within X period" to the upstream application, supporting RBI regulations.

Phase 1: Request Reception & External Service Interaction
=========================================================   
This phase focuses on receiving the deduplication request from an upstream system, validating its content, constructing the appropriate request for the external Application Dedupe Service, making the call, and capturing its raw response.
    - Objective: To receive all necessary application details for deduplication, securely pass them to the external Application Dedupe Service, and capture its raw response.
    - Trigger: An upstream application (e.g., Loan Origination System, KYC workflow) submits a new application for a deduplication check.

Inputs from Upstream Application:
    - Application Identifier
        Description: A unique ID for the application currently being processed
        Mandatory: Yes
    - Deduped Customer Name
        Description: A standardized/normalized customer name provided by another service
        Mandatory: Yes

    - Customer Contact Details
        - Mobile Number
            Mandatory: Yes
        - Email Address
            Mandatory: Yes

    - Customer Identity Details
        - PAN (Permanent Account Number)
            Mandatory: Yes
        - Aadhaar Number
            Description: Masked/tokenized as per regulations
            Mandatory: Yes
        - Date of Birth
            Mandatory: Yes
    - Application Submission Date
        Description: The date the current application was submitted
        Mandatory: Yes

    - Deduplication Period
        Description: The lookback period (e.g., 90 days, 6 months, 1 year) to search for duplicates
        Mandatory: Optional (Configurable)
        Comments: If not provided, a default from configuration will be used

Key Activities for phase 1:
--------------------------
1. Input Validation:
    - Validate the presence and basic format of all inputs received from the upstream application.
    - Perform minor sanitization (e.g., trimming whitespace, removing non-numeric characters from phone numbers) to ensure clean data for the external service, but avoid complex data transformations or normalization as the external service is expected to handle those for its matching logic.
2. External Service Configuration Retrieval:
    - Access your secure configuration store to get the external Application Dedupe Service's API endpoint, authentication credentials (e.g., API keys, tokens), and any other vendor-specific parameters required for the request.
3. Construct External Service Request:
    - Map the validated inputs from your upstream application to the exact request payload format expected by the external Application Dedupe Service. This will include the Deduped Customer Name, contact details, identity details, and the Deduplication Period.
4. Establish Secure Connection & Authenticate:
    - Utilize the retrieved credentials to securely authenticate with the external service's API and establish a connection.
5. Call External Application Dedupe Service:
    - Transmit the constructed request payload to the external service's API. This will be a synchronous call.
6. Receive & Parse Raw Response:
    - Capture the synchronous response from the external Application Dedupe Service.
    - Parse the raw response to extract the core deduplication result (e.g., a flag indicating match found or no match, and a list of duplicate details if found). Also capture any error codes or messages provided by the external service.
7. Error Handling for External Call:
    - Implement robust error handling for communication failures (e.g., network timeouts, external service unavailability, authentication failures).
    - Implement retry mechanisms with exponential backoff for transient, recoverable errors.
    - Log all successful and failed external calls, updating your internal request status (e.g., EXTERNAL_CALL_SUCCESS, EXTERNAL_CALL_FAILED).
8. Outputs from this phase to next phase
    - The raw, parsed response from the external Application Dedupe Service.
    - Any error details related to the external call.
    - Internal record of the dedupe request and the outcome of the external call.

Phase 2: Response Interpretation & Reporting
============================================
This phase takes the raw response from the external service, interprets it into your bank's standardized dedupe outcome, and reports this decision back to the upstream application.
    - Objective: To interpret the external service's deduplication results into a clear "Match Found" or "No Match Found" status with relevant details, and communicate this decision back to the upstream application.
    - Trigger: Successful receipt of the raw response from the external Application Dedupe Service in Phase 1.

Key Activities within Your Capability:
--------------------------------------
1. Interpret External Service Response:
    - Analyze the parsed raw response from the external service.
    - Map the external service's dedupe status (e.g., their is_duplicate flag, match_strength code) to your internal, standardized Dedupe Status (e.g., MATCH_FOUND, NO_MATCH_FOUND).
    - If MATCH_FOUND, extract all relevant details of the duplicate applications identified by the external service (e.g., their application_id, submission_date, matched_fields, match_score, status_of_prev_application).
    - If the external call failed in Phase 1, or the external service indicated an internal error, generate an appropriate internal error status for the dedupe check.
2. Determine Final Output Message/Details:
    - If MATCH_FOUND: Prepare a structured response including the MATCH_FOUND status and a list of the identified duplicate applications with their key details.
    - If NO_MATCH_FOUND: Prepare a response including the NO_MATCH_FOUND status and a message indicating the lookback period searched (e.g., "No duplicates found in the last X days/months").
    - If ERROR: Prepare a response indicating an error in the dedupe process (e.g., "Dedupe service unavailable, manual review required").
3. Result Persistence & Audit Trail:
    - Persist the complete deduplication outcome in your database, linked to the Application Identifier. This includes the raw vendor response, the interpreted dedupe status, and the final output details. This ensures a comprehensive audit trail for RBI compliance.
4. Report to Upstream Application:
    - Construct and send the final response payload to the initiating upstream application (typically a synchronous HTTP response).
    - The response should be clear, concise, and provide all necessary information for the upstream application to take action (e.g., proceed with application, send for manual review, reject).

Outputs from Your Capability:
-----------------------------
- A definitive Dedupe Status (MATCH_FOUND or NO_MATCH_FOUND or an ERROR status).
- Detailed information about any identified duplicate applications if a match is found.
- A clear message indicating the lookback period if no match is found.
- A robust, persisted audit record of the deduplication check within your system.
- A response sent back to the upstream application.

