package com.bankstatement.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

public class StartTransactionRequest {

    @NotBlank(message = "PAN is required")
    @Size(max = 10, message = "PAN must not exceed 10 characters")
    private String pan;

    @NotBlank(message = "Client transaction ID is required")
    @Size(max = 64, message = "Client transaction ID must not exceed 64 characters")
    private String clientTransactionId;

    @NotBlank(message = "Transaction complete URL is required")
    private String transactionCompleteUrl;

    @NotBlank(message = "Type is required")
    private String type;

    @NotBlank(message = "Redirection URL is required")
    private String redirectionUrl;

    public StartTransactionRequest() {
    }

    public StartTransactionRequest(String pan, String clientTransactionId, String transactionCompleteUrl, 
                                 String type, String redirectionUrl) {
        this.pan = pan;
        this.clientTransactionId = clientTransactionId;
        this.transactionCompleteUrl = transactionCompleteUrl;
        this.type = type;
        this.redirectionUrl = redirectionUrl;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getClientTransactionId() {
        return clientTransactionId;
    }

    public void setClientTransactionId(String clientTransactionId) {
        this.clientTransactionId = clientTransactionId;
    }

    public String getTransactionCompleteUrl() {
        return transactionCompleteUrl;
    }

    public void setTransactionCompleteUrl(String transactionCompleteUrl) {
        this.transactionCompleteUrl = transactionCompleteUrl;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRedirectionUrl() {
        return redirectionUrl;
    }

    public void setRedirectionUrl(String redirectionUrl) {
        this.redirectionUrl = redirectionUrl;
    }
}
