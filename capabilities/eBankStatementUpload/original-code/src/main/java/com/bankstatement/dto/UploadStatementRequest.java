package com.bankstatement.dto;

import jakarta.validation.constraints.NotBlank;

public class UploadStatementRequest {

    @NotBlank(message = "Transaction ID is required")
    private String transactionId;

    @NotBlank(message = "File content is required")
    private String fileContent; // Base64 encoded PDF

    public UploadStatementRequest() {
    }

    public UploadStatementRequest(String transactionId, String fileContent) {
        this.transactionId = transactionId;
        this.fileContent = fileContent;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getFileContent() {
        return fileContent;
    }

    public void setFileContent(String fileContent) {
        this.fileContent = fileContent;
    }
}
