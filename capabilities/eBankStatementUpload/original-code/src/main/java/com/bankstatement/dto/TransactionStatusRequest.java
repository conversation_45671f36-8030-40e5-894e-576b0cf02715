package com.bankstatement.dto;

import jakarta.validation.constraints.NotBlank;

public class TransactionStatusRequest {

    @NotBlank(message = "Transaction ID is required")
    private String transactionId;

    public TransactionStatusRequest() {
    }

    public TransactionStatusRequest(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }
}
