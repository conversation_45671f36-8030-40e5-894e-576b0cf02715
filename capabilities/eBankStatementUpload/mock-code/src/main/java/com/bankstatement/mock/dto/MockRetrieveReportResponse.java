package com.bankstatement.mock.dto;

public class MockRetrieveReportResponse {

    private String transactionId;
    private String fileName;
    private String content;
    private String traceId;

    public MockRetrieveReportResponse() {
    }

    public MockRetrieveReportResponse(String transactionId, String fileName, String content, String traceId) {
        this.transactionId = transactionId;
        this.fileName = fileName;
        this.content = content;
        this.traceId = traceId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }
}
