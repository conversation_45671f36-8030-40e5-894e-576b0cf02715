package com.bankstatement.mock.util;

public class MockDataMaskingUtil {

    private static final String MASK_CHAR = "*";

    public static String maskPan(String pan) {
        if (pan == null || pan.length() < 4) {
            return MASK_CHAR.repeat(10);
        }
        
        String firstTwo = pan.substring(0, 2);
        String lastTwo = pan.substring(pan.length() - 2);
        return firstTwo + MASK_CHAR.repeat(pan.length() - 4) + lastTwo;
    }

    public static String maskTransactionId(String transactionId) {
        if (transactionId == null || transactionId.length() < 6) {
            return MASK_CHAR.repeat(8);
        }
        
        String prefix = transactionId.substring(0, 3);
        String suffix = transactionId.substring(transactionId.length() - 3);
        return prefix + MASK_CHAR.repeat(transactionId.length() - 6) + suffix;
    }

    public static String maskContent(String content) {
        if (content == null || content.isEmpty()) {
            return "****";
        }
        
        return "***MASKED_CONTENT_LENGTH_" + content.length() + "***";
    }
}
