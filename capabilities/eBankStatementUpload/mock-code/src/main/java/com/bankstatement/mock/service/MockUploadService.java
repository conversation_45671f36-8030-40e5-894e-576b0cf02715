package com.bankstatement.mock.service;

import com.bankstatement.mock.dto.*;
import com.bankstatement.mock.entity.MockTransaction;
import com.bankstatement.mock.repository.MockTransactionRepository;
import com.bankstatement.mock.util.MockDataMaskingUtil;
import com.bankstatement.mock.util.MockTraceIdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Base64;

@Service
@Transactional
public class MockUploadService {

    private static final Logger logger = LoggerFactory.getLogger(MockUploadService.class);

    private final MockTransactionRepository transactionRepository;
    private final MockSimulationService simulationService;

    public MockUploadService(MockTransactionRepository transactionRepository,
                           MockSimulationService simulationService) {
        this.transactionRepository = transactionRepository;
        this.simulationService = simulationService;
    }

    public MockApiResponse startTransaction(MockStartTransactionRequest request) {
        String traceId = getTraceId();
        
        logger.info("Mock Start Transaction - TraceId: {}, PAN: {}, ClientTransactionId: {}", 
                   traceId, MockDataMaskingUtil.maskPan(request.getPan()), request.getClientTransactionId());

        // Simulate error scenarios
        simulationService.simulateErrorIfNeeded(request.getPan(), traceId);
        
        // Simulate delay
        simulationService.simulateDelayIfNeeded();

        // Create transaction
        String transactionId = MockTraceIdGenerator.generateTransactionId();
        MockTransaction transaction = new MockTransaction();
        transaction.setTransactionId(transactionId);
        transaction.setPan(request.getPan());
        transaction.setClientTransactionId(request.getClientTransactionId());
        transaction.setStatus("INITIATED");
        
        transactionRepository.save(transaction);

        logger.info("Mock Start Transaction successful - TraceId: {}, TransactionId: {}", 
                   traceId, MockDataMaskingUtil.maskTransactionId(transactionId));

        return new MockApiResponse(true, transactionId, traceId);
    }

    public MockApiResponse uploadStatement(MockUploadRequest request) {
        String traceId = getTraceId();
        
        logger.info("Mock Upload Statement - TraceId: {}, TransactionId: {}", 
                   traceId, MockDataMaskingUtil.maskTransactionId(request.getTransactionId()));

        // Find transaction
        MockTransaction transaction = findTransaction(request.getTransactionId(), traceId);
        
        // Simulate error scenarios
        simulationService.simulateErrorIfNeeded(request.getTransactionId(), traceId);
        
        // Simulate delay
        simulationService.simulateDelayIfNeeded();

        // Update transaction
        transaction.setStatus("UPLOADED");
        transactionRepository.save(transaction);

        logger.info("Mock Upload Statement successful - TraceId: {}, TransactionId: {}", 
                   traceId, MockDataMaskingUtil.maskTransactionId(request.getTransactionId()));

        return new MockApiResponse(true, request.getTransactionId(), traceId);
    }

    public MockApiResponse processStatement(MockTransactionRequest request) {
        String traceId = getTraceId();
        
        logger.info("Mock Process Statement - TraceId: {}, TransactionId: {}", 
                   traceId, MockDataMaskingUtil.maskTransactionId(request.getTransactionId()));

        // Find transaction
        MockTransaction transaction = findTransaction(request.getTransactionId(), traceId);
        
        // Simulate error scenarios
        simulationService.simulateErrorIfNeeded(request.getTransactionId(), traceId);
        
        // Simulate delay
        simulationService.simulateDelayIfNeeded();

        // Update transaction
        transaction.setStatus("PROCESSING");
        transactionRepository.save(transaction);

        logger.info("Mock Process Statement successful - TraceId: {}, TransactionId: {}", 
                   traceId, MockDataMaskingUtil.maskTransactionId(request.getTransactionId()));

        return new MockApiResponse(true, request.getTransactionId(), traceId);
    }

    public MockTransactionStatusResponse getTransactionStatus(MockTransactionRequest request) {
        String traceId = getTraceId();
        
        logger.info("Mock Get Transaction Status - TraceId: {}, TransactionId: {}", 
                   traceId, MockDataMaskingUtil.maskTransactionId(request.getTransactionId()));

        // Find transaction
        MockTransaction transaction = findTransaction(request.getTransactionId(), traceId);
        
        // Simulate error scenarios
        simulationService.simulateErrorIfNeeded(request.getTransactionId(), traceId);
        
        // Simulate delay
        simulationService.simulateDelayIfNeeded();

        // Update transaction to report generated status
        transaction.setFileType("PDF");
        transaction.setFileName("BANK_STATEMENT");
        transaction.setStatementsStatus("REPORT_GENERATED");
        transaction.setTransactionStatus("REPORT_GENERATED");
        transaction.setStatus("REPORT_GENERATED");
        transactionRepository.save(transaction);

        logger.info("Mock Get Transaction Status successful - TraceId: {}, TransactionId: {}, Status: {}", 
                   traceId, MockDataMaskingUtil.maskTransactionId(request.getTransactionId()),
                   transaction.getTransactionStatus());

        return new MockTransactionStatusResponse(
            request.getTransactionId(),
            "PDF",
            "BANK_STATEMENT",
            "REPORT_GENERATED",
            "REPORT_GENERATED",
            traceId
        );
    }

    public MockRetrieveReportResponse retrieveReport(MockTransactionRequest request) {
        String traceId = getTraceId();
        
        logger.info("Mock Retrieve Report - TraceId: {}, TransactionId: {}", 
                   traceId, MockDataMaskingUtil.maskTransactionId(request.getTransactionId()));

        // Find transaction
        MockTransaction transaction = findTransaction(request.getTransactionId(), traceId);
        
        // Simulate error scenarios
        simulationService.simulateErrorIfNeeded(request.getTransactionId(), traceId);
        
        // Simulate delay
        simulationService.simulateDelayIfNeeded();

        // Generate mock report content
        String reportContent = generateMockReportContent(transaction);
        String base64Content = Base64.getEncoder().encodeToString(reportContent.getBytes());
        
        // Update transaction
        transaction.setReportContent(base64Content);
        transaction.setStatus("COMPLETED");
        transactionRepository.save(transaction);

        logger.info("Mock Retrieve Report successful - TraceId: {}, TransactionId: {}, FileName: {}", 
                   traceId, MockDataMaskingUtil.maskTransactionId(request.getTransactionId()),
                   "final_report.pdf");

        return new MockRetrieveReportResponse(
            request.getTransactionId(),
            "final_report.pdf",
            base64Content,
            traceId
        );
    }

    private MockTransaction findTransaction(String transactionId, String traceId) {
        return transactionRepository.findByTransactionId(transactionId)
            .orElseThrow(() -> new RuntimeException("Transaction not found: " + transactionId));
    }

    private String generateMockReportContent(MockTransaction transaction) {
        return String.format("""
            BANK STATEMENT ANALYSIS REPORT
            ==============================
            
            Transaction ID: %s
            Client Transaction ID: %s
            PAN: %s
            Analysis Date: %s
            
            SUMMARY:
            - Account Type: Savings
            - Average Monthly Balance: ₹50,000
            - Total Credits: ₹2,00,000
            - Total Debits: ₹1,50,000
            - Net Cash Flow: ₹50,000
            
            INCOME ASSESSMENT:
            - Salary Credits: ₹75,000/month
            - Other Credits: ₹25,000/month
            - Income Stability: High
            - Credit Score Impact: Positive
            
            RECOMMENDATION:
            Applicant shows stable income pattern suitable for loan processing.
            
            Generated by Mock E-Statement Service
            """, 
            transaction.getTransactionId(),
            transaction.getClientTransactionId(),
            MockDataMaskingUtil.maskPan(transaction.getPan()),
            transaction.getUpdatedAt()
        );
    }

    private String getTraceId() {
        String traceId = MDC.get("traceId");
        return traceId != null ? traceId : MockTraceIdGenerator.generateTraceId();
    }
}
