package com.bankstatement.mock.dto;

import jakarta.validation.constraints.NotBlank;

public class MockTransactionRequest {

    @NotBlank(message = "Transaction ID is required")
    private String transactionId;

    public MockTransactionRequest() {
    }

    public MockTransactionRequest(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }
}
