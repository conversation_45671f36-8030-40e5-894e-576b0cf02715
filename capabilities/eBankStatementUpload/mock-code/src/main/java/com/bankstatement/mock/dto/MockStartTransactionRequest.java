package com.bankstatement.mock.dto;

import jakarta.validation.constraints.NotBlank;

public class MockStartTransactionRequest {

    @NotBlank(message = "PAN is required")
    private String pan;

    @NotBlank(message = "Client transaction ID is required")
    private String clientTransactionId;

    @NotBlank(message = "Transaction complete URL is required")
    private String transactionCompleteUrl;

    @NotBlank(message = "Type is required")
    private String type;

    @NotBlank(message = "Redirection URL is required")
    private String redirectionUrl;

    public MockStartTransactionRequest() {
    }

    public MockStartTransactionRequest(String pan, String clientTransactionId, String transactionCompleteUrl, 
                                     String type, String redirectionUrl) {
        this.pan = pan;
        this.clientTransactionId = clientTransactionId;
        this.transactionCompleteUrl = transactionCompleteUrl;
        this.type = type;
        this.redirectionUrl = redirectionUrl;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getClientTransactionId() {
        return clientTransactionId;
    }

    public void setClientTransactionId(String clientTransactionId) {
        this.clientTransactionId = clientTransactionId;
    }

    public String getTransactionCompleteUrl() {
        return transactionCompleteUrl;
    }

    public void setTransactionCompleteUrl(String transactionCompleteUrl) {
        this.transactionCompleteUrl = transactionCompleteUrl;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRedirectionUrl() {
        return redirectionUrl;
    }

    public void setRedirectionUrl(String redirectionUrl) {
        this.redirectionUrl = redirectionUrl;
    }
}
