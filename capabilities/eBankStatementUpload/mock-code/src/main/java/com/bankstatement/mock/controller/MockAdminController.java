package com.bankstatement.mock.controller;

import com.bankstatement.mock.service.MockSimulationService;
import com.bankstatement.mock.service.MockSimulationService.SimulationConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/admin")
public class MockAdminController {

    private static final Logger logger = LoggerFactory.getLogger(MockAdminController.class);

    private final MockSimulationService simulationService;

    public MockAdminController(MockSimulationService simulationService) {
        this.simulationService = simulationService;
    }

    @GetMapping("/logs")
    public ResponseEntity<Map<String, Object>> getRecentLogs() {
        logger.info("Admin request for recent logs");
        
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Log retrieval functionality would be implemented here");
        response.put("note", "In a real implementation, this would return masked log entries");
        
        return ResponseEntity.ok(response);
    }

    @PostMapping("/config")
    public ResponseEntity<Map<String, String>> setOverrideConfig(
            @RequestParam String identifier,
            @RequestParam(defaultValue = "false") boolean simulateError,
            @RequestParam(defaultValue = "0") long delayMs,
            @RequestParam(defaultValue = "Simulated error") String errorMessage) {
        
        logger.info("Admin setting override config for identifier: {}, simulateError: {}, delayMs: {}", 
                   identifier, simulateError, delayMs);
        
        SimulationConfig config = new SimulationConfig(simulateError, delayMs, errorMessage);
        simulationService.setOverrideConfig(identifier, config);
        
        Map<String, String> response = new HashMap<>();
        response.put("message", "Override config set successfully");
        response.put("identifier", identifier);
        response.put("config", config.toString());
        
        return ResponseEntity.ok(response);
    }

    @PostMapping("/reset")
    public ResponseEntity<Map<String, String>> resetSimulation() {
        logger.info("Admin resetting simulation");
        
        simulationService.clearAllOverrideConfigs();
        
        Map<String, String> response = new HashMap<>();
        response.put("message", "Simulation reset successfully");
        response.put("status", "All override configs cleared");
        
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/config/{identifier}")
    public ResponseEntity<Map<String, String>> clearOverrideConfig(@PathVariable String identifier) {
        logger.info("Admin clearing override config for identifier: {}", identifier);
        
        simulationService.clearOverrideConfig(identifier);
        
        Map<String, String> response = new HashMap<>();
        response.put("message", "Override config cleared successfully");
        response.put("identifier", identifier);
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Mock E-Statement Service");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
}
