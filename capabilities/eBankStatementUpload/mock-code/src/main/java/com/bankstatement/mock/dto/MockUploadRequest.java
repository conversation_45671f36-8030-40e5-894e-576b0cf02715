package com.bankstatement.mock.dto;

import jakarta.validation.constraints.NotBlank;

public class MockUploadRequest {

    @NotBlank(message = "Transaction ID is required")
    private String transactionId;

    @NotBlank(message = "File content is required")
    private String fileContent;

    public MockUploadRequest() {
    }

    public MockUploadRequest(String transactionId, String fileContent) {
        this.transactionId = transactionId;
        this.fileContent = fileContent;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getFileContent() {
        return fileContent;
    }

    public void setFileContent(String fileContent) {
        this.fileContent = fileContent;
    }
}
