package com.bankstatement.mock.controller;

import com.bankstatement.mock.dto.*;
import com.bankstatement.mock.service.MockUploadService;
import com.bankstatement.mock.util.MockTraceIdGenerator;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("")
public class MockStatementController {

    private static final Logger logger = LoggerFactory.getLogger(MockStatementController.class);

    private final MockUploadService mockUploadService;

    public MockStatementController(MockUploadService mockUploadService) {
        this.mockUploadService = mockUploadService;
    }

    @PostMapping("/upload")
    public ResponseEntity<MockApiResponse> uploadStatement(
            @Valid @RequestBody MockUploadRequest request,
            HttpServletRequest httpRequest) {
        
        String traceId = getOrGenerateTraceId(httpRequest);
        MDC.put("traceId", traceId);
        
        try {
            logger.info("Mock Upload Statement request received - TraceId: {}, TransactionId: {}", 
                       traceId, request.getTransactionId());
            
            MockApiResponse response = mockUploadService.uploadStatement(request);
            return ResponseEntity.ok(response);
        } finally {
            MDC.clear();
        }
    }

    @PostMapping("/process")
    public ResponseEntity<MockApiResponse> processStatement(
            @Valid @RequestBody MockTransactionRequest request,
            HttpServletRequest httpRequest) {
        
        String traceId = getOrGenerateTraceId(httpRequest);
        MDC.put("traceId", traceId);
        
        try {
            logger.info("Mock Process Statement request received - TraceId: {}, TransactionId: {}", 
                       traceId, request.getTransactionId());
            
            MockApiResponse response = mockUploadService.processStatement(request);
            return ResponseEntity.ok(response);
        } finally {
            MDC.clear();
        }
    }

    @PostMapping("/status")
    public ResponseEntity<MockTransactionStatusResponse> getTransactionStatus(
            @Valid @RequestBody MockTransactionRequest request,
            HttpServletRequest httpRequest) {
        
        String traceId = getOrGenerateTraceId(httpRequest);
        MDC.put("traceId", traceId);
        
        try {
            logger.info("Mock Get Transaction Status request received - TraceId: {}, TransactionId: {}", 
                       traceId, request.getTransactionId());
            
            MockTransactionStatusResponse response = mockUploadService.getTransactionStatus(request);
            return ResponseEntity.ok(response);
        } finally {
            MDC.clear();
        }
    }

    @PostMapping("/report")
    public ResponseEntity<MockRetrieveReportResponse> retrieveReport(
            @Valid @RequestBody MockTransactionRequest request,
            HttpServletRequest httpRequest) {
        
        String traceId = getOrGenerateTraceId(httpRequest);
        MDC.put("traceId", traceId);
        
        try {
            logger.info("Mock Retrieve Report request received - TraceId: {}, TransactionId: {}", 
                       traceId, request.getTransactionId());
            
            MockRetrieveReportResponse response = mockUploadService.retrieveReport(request);
            return ResponseEntity.ok(response);
        } finally {
            MDC.clear();
        }
    }

    private String getOrGenerateTraceId(HttpServletRequest request) {
        String traceId = request.getHeader("X-Trace-ID");
        return traceId != null ? traceId : MockTraceIdGenerator.generateTraceId();
    }
}
