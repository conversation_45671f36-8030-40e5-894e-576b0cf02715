package com.bankstatement.mock.dto;

public class MockApiResponse {

    private boolean success;
    private String transactionId;
    private String traceId;

    public MockApiResponse() {
    }

    public MockApiResponse(boolean success, String transactionId, String traceId) {
        this.success = success;
        this.transactionId = transactionId;
        this.traceId = traceId;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }
}
