package com.bankstatement.mock.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("E-Statement Mock Service API")
                        .description("Mock service for E-Bank Statement Upload capability that simulates external bank statement " +
                                "processing APIs. This service provides endpoints to simulate the complete bank statement " +
                                "upload workflow including transaction initiation, file upload, processing, and report retrieval.")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Bank Statement Upload Team")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("MIT")
                                .url("https://opensource.org/licenses/MIT")));
    }
}
