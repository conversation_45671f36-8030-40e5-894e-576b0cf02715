package com.bankstatement.mock.controller;

import com.bankstatement.mock.dto.MockApiResponse;
import com.bankstatement.mock.dto.MockStartTransactionRequest;
import com.bankstatement.mock.service.MockUploadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/ebank-statement-upload/mock")
@Tag(name = "Statement Processing", description = "Bank statement upload and processing operations")
public class MockUploadController {

    @Autowired
    private MockUploadService mockUploadService;

    @Operation(
        summary = "Start Transaction",
        description = "Initiates a new bank statement upload transaction"
    )
    @PostMapping("/start")
    public ResponseEntity<MockApiResponse> startTransaction(@RequestBody MockStartTransactionRequest request) {
        MockApiResponse response = mockUploadService.startTransaction(request);
        return ResponseEntity.ok(response);
    }
}