package com.bankstatement.mock.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Service
public class MockSimulationService {

    private static final Logger logger = LoggerFactory.getLogger(MockSimulationService.class);

    @Value("${mock.simulation.delay-enabled:true}")
    private boolean delayEnabled;

    @Value("${mock.simulation.error-simulation-enabled:true}")
    private boolean errorSimulationEnabled;

    @Value("${mock.simulation.default-delay-ms:0}")
    private long defaultDelayMs;

    @Value("${mock.simulation.max-delay-ms:5000}")
    private long maxDelayMs;

    private final ConcurrentMap<String, SimulationConfig> overrideConfigs = new ConcurrentHashMap<>();

    public void simulateErrorIfNeeded(String identifier, String traceId) {
        if (!errorSimulationEnabled) {
            return;
        }

        // Check for override config
        SimulationConfig config = overrideConfigs.get(identifier);
        if (config != null && config.shouldSimulateError()) {
            logger.warn("Simulating error for identifier: {} - TraceId: {}", identifier, traceId);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Simulated error");
        }

        // Built-in error simulation rules
        if (identifier != null) {
            // PAN starting with "ERR" → simulate HTTP 500
            if (identifier.startsWith("ERR")) {
                logger.warn("Simulating error for PAN starting with ERR - TraceId: {}", traceId);
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Simulated error for ERR PAN");
            }

            // TransactionId = "TXN_RETRY" → simulate retry failure
            if ("TXN_RETRY".equals(identifier)) {
                logger.warn("Simulating retry failure for TXN_RETRY - TraceId: {}", traceId);
                throw new ResponseStatusException(HttpStatus.SERVICE_UNAVAILABLE, "Simulated retry failure");
            }
        }
    }

    public void simulateDelayIfNeeded() {
        if (!delayEnabled) {
            return;
        }

        long delayMs = defaultDelayMs;

        // Check if timestamp ends with "999" → simulate 5s delay
        String timestamp = LocalDateTime.now().toString();
        if (timestamp.endsWith("999")) {
            delayMs = 5000;
            logger.info("Simulating 5s delay due to timestamp ending with 999");
        }

        if (delayMs > 0) {
            try {
                Thread.sleep(Math.min(delayMs, maxDelayMs));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.warn("Delay simulation interrupted", e);
            }
        }
    }

    public void setOverrideConfig(String identifier, SimulationConfig config) {
        overrideConfigs.put(identifier, config);
        logger.info("Override config set for identifier: {}, config: {}", identifier, config);
    }

    public void clearOverrideConfig(String identifier) {
        overrideConfigs.remove(identifier);
        logger.info("Override config cleared for identifier: {}", identifier);
    }

    public void clearAllOverrideConfigs() {
        overrideConfigs.clear();
        logger.info("All override configs cleared");
    }

    public static class SimulationConfig {
        private boolean simulateError;
        private long delayMs;
        private String errorMessage;

        public SimulationConfig() {
        }

        public SimulationConfig(boolean simulateError, long delayMs, String errorMessage) {
            this.simulateError = simulateError;
            this.delayMs = delayMs;
            this.errorMessage = errorMessage;
        }

        public boolean shouldSimulateError() {
            return simulateError;
        }

        public void setSimulateError(boolean simulateError) {
            this.simulateError = simulateError;
        }

        public long getDelayMs() {
            return delayMs;
        }

        public void setDelayMs(long delayMs) {
            this.delayMs = delayMs;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        @Override
        public String toString() {
            return "SimulationConfig{" +
                    "simulateError=" + simulateError +
                    ", delayMs=" + delayMs +
                    ", errorMessage='" + errorMessage + '\'' +
                    '}';
        }
    }
}
