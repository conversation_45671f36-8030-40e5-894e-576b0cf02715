Business Flow

Phase 1: Initiate Create Customer ID Request

    Trigger:
        - When a loan application requires creation of a new Customer ID (CIF).

    Request Payload:
        - customerDetails (mandatory):
            - salutation (default: Mr)
            - firstName (mandatory)
            - lastName (mandatory)
            - dateOfBirth (mandatory)
            - language (default: ENG)
            - maritalStatus (default: Y)
            - nationality (default: IN)
            - isMinor (default: N)
            - isCustomerNRE (default: N)
            - defaultAddressType (default: Mailing)
            - gender (default: Male)
            - nativeLanguageCode (default: ENG01)
            - occupation (default: A102)
            - pan (mandatory)
            - branchid (default: 887)
            - isStaff (default: N)
            - taxDeductionTable (default: TDS11)
            - maidenNameOfMother (mandatory)
            - custHealthCode (default: AB01)

        - addressDetails (at least one mandatory):
            - addressLine1, addressLine2 (mandatory)
            - addressCategory (Home/Mailing) (mandatory)
            - city, state, postalCode (mandatory)
            - country (default: IN)

        - contactDetails (mandatory):
            - contactType (default: Phone)
            - contactSubType (default: Cell)
            - countryCode (default: +91)
            - phoneNumber (mandatory)
            - isPreferredContact (default: Yes)
            - email (mandatory)

        - entityDocDetails (mandatory):
            - countryOfIssue (default: IN)
            - documentCode (e.g., AADHAR_IDN_OF)
            - issueDate (default: 2000-12-31)
            - expiryDate (default: 2099-12-31)
            - typeCode (e.g., Identification)
            - placeOfIssue (default: HRD)
            - isMandatory (default: Y)
            - receivedDate (system date)

        - psychographicData:
            - currencyCode (default: INR)

    Validations:
        - All mandatory fields must be present and non-null.
        - PAN must be in valid format.
        - Date of birth must be valid ISO date.
        - Contact details must include valid phone and email.
        - At least one valid address must be provided.

    System Behavior:
        - On validation failure:
            - Log failure reason
            - Return failure response to requesting system
        - On successful validation:
            - Prepare payload for CIF creation request
            - Proceed to external API integration

Phase 2: Request and Receive CIF Creation Confirmation

    External API Integration:
        - POST /external/cif/initiate
            - Sends the prepared customer payload
            - Includes subHeader with channelId (mandatory unique request ID)

    API Response Processing:
        - channelId
        - customerId
        - description
        - entity
        - status (e.g., SUCCESS, FAILED)

      Failure Scenarios:
          - Service unavailable or error in request:
              - Retry up to 3 times (configurable)
              - On max retries failed:
                  - Log failure
                  - Return fallback message to user:
                    “We are unable to proceed with your loan application request right now. Please try again later.”

          - Response has null or failed status:
              - Halt journey
              - Log issue and notify internal ops

      Success Scenarios:
          - status = SUCCESS and customerId is present:
              - Log success
              - Return enriched response (including customerId) to requester
              - Proceed with loan journey

Phase 3: Analyse CIF Creation Response

    Validations:
        - Verify response fields:
            - customerId must be non-null
            - status must equal SUCCESS

      On Success:
          - Log response
          - Update internal database with customerId
          - Prepare next step (loan account creation request)

      On Failure:
          - Log detailed error reason and outcome code (e.g., "CIF_CREATION_FAILED")
          - Return user-friendly fallback message:
            “We are unable to proceed with your loan application request right now. Please try again later.”

      Query & Reporting:
          - Provide API to fetch CIF creation status and outcome.
          - Maintain complete audit trail for all API requests and responses.
