openapi: 3.0.3
info:
  title: CIF (Customer Information File) Service API
  description: |
    Customer ID (CIF) Creation Service for loan application processing.
    This service handles the complete customer creation workflow including validation,
    external API integration, and data persistence.
    
    ## Features
    - Customer ID creation with comprehensive validation
    - Integration with external CIF systems
    - Retry logic for resilient API calls
    - Complete audit trail and logging
    - Health monitoring endpoints
    
    ## Business Flow
    1. **Phase 1**: Validate request data and prepare customer entity
    2. **Phase 2**: Call external CIF API with retry logic
    3. **Phase 3**: Process CIF response and persist customer data
    
  version: 1.0.0
  contact:
    name: CIF Service Team
    email: <EMAIL>
  license:
    name: Proprietary
    url: https://financial.com/license

servers:
  - url: http://localhost:8104/api/cif
    description: Local development server
  - url: https://dev-api.financial.com/api/cif
    description: Development server
  - url: https://staging-api.financial.com/api/cif
    description: Staging server
  - url: https://api.financial.com/api/cif
    description: Production server

tags:
  - name: CIF Operations
    description: Customer ID creation and management operations
  - name: Health Check
    description: Service health monitoring endpoints

paths:
  /cif/create:
    post:
      tags:
        - CIF Operations
      summary: Create Customer ID
      description: |
        Creates a new Customer ID (CIF) for loan application processing.
        
        **Business Rules:**
        - PAN must be unique across all customers
        - All mandatory fields must be provided
        - At least one address and contact detail required
        - Document details must include valid identification
        
        **Processing Flow:**
        1. Validate request payload
        2. Check for duplicate PAN
        3. Call external CIF API with retry logic
        4. Process response and persist customer data
        5. Return customer creation response
        
      operationId: createCustomerId
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomerIdRequest'
            examples:
              valid_request:
                summary: Valid customer creation request
                value:
                  body:
                    createCustomerIdRequest:
                      requestBody:
                        salutation: "Mr"
                        firstName: "Rajesh"
                        lastName: "Kumar"
                        dateOfBirth: "1985-03-15"
                        language: "ENG"
                        maritalStatus: "Y"
                        nationality: "IN"
                        isMinor: "N"
                        isCustomerNRE: "N"
                        defaultAddressType: "Mailing"
                        gender: "Male"
                        nativeLanguageCode: "ENG01"
                        occupation: "A102"
                        pan: "**********"
                        branchId: "887"
                        isStaff: "N"
                        taxDeductionTable: "TDS11"
                        maidenNameOfMother: "Sunita Sharma"
                        custHealthCode: "AB01"
                        addressDetails:
                          - addressLine1: "Plot No 123, Sector 15"
                            addressLine2: "Near City Mall"
                            addressCategory: "Mailing"
                            city: "Gurgaon"
                            state: "Haryana"
                            postalCode: "122001"
                            country: "IN"
                        contactDetails:
                          - contactType: "Phone"
                            contactSubType: "Cell"
                            countryCode: "+91"
                            phoneNumber: "**********"
                            isPreferredContact: "Yes"
                            email: "<EMAIL>"
                        entityDocDetails:
                          - countryOfIssue: "IN"
                            documentCode: "AADHAR_IDN_OF"
                            issueDate: "2010-05-20T00:00:00"
                            expiryDate: "2099-12-31T00:00:00"
                            typeCode: "Identification"
                            typeDescription: "Aadhar card for identification"
                            placeOfIssue: "HRD"
                            isMandatory: "Y"
                            receivedDate: "2024-01-15T10:30:00"
                        psychographicData:
                          - currencyCode: "INR"
      responses:
        '200':
          description: Customer ID created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerCreationResponse'
              examples:
                success_response:
                  summary: Successful customer creation
                  value:
                    referenceNumber: "REF123456789"
                    customerId: "CIF100001"
                    status: "SUCCESS"
                    message: "Customer ID created successfully"
                    timestamp: "2024-01-15T10:30:45.123"
                    processingTimeMs: 2500
        '400':
          description: Validation error or bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerCreationResponse'
              examples:
                validation_error:
                  summary: Validation error response
                  value:
                    referenceNumber: "REF123456789"
                    customerId: null
                    status: "VALIDATION_ERROR"
                    message: "PAN is mandatory and must be in valid format"
                    timestamp: "2024-01-15T10:30:45.123"
                    processingTimeMs: null
                duplicate_pan:
                  summary: Duplicate PAN error
                  value:
                    referenceNumber: "REF123456789"
                    customerId: null
                    status: "VALIDATION_ERROR"
                    message: "Customer with PAN ********** already exists"
                    timestamp: "2024-01-15T10:30:45.123"
                    processingTimeMs: null
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerCreationResponse'
              examples:
                internal_error:
                  summary: Internal server error
                  value:
                    referenceNumber: "REF123456789"
                    customerId: null
                    status: "FAILURE"
                    message: "An internal error occurred during customer creation"
                    timestamp: "2024-01-15T10:30:45.123"
                    processingTimeMs: 1200
        '503':
          description: Service unavailable
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerCreationResponse'
              examples:
                service_unavailable:
                  summary: External service unavailable
                  value:
                    referenceNumber: "REF123456789"
                    customerId: null
                    status: "SERVICE_UNAVAILABLE"
                    message: "We are unable to proceed with your loan application request right now. Please try again later."
                    timestamp: "2024-01-15T10:30:45.123"
                    processingTimeMs: 5000

  /cif/customer/{referenceNumber}:
    get:
      tags:
        - CIF Operations
      summary: Get Customer by Reference Number
      description: |
        Retrieves customer details by reference number.
        
        **Use Cases:**
        - Check customer creation status
        - Retrieve customer details for verification
        - Audit and tracking purposes
        
      operationId: getCustomerByReferenceNumber
      parameters:
        - name: referenceNumber
          in: path
          required: true
          description: Unique reference number assigned during customer creation
          schema:
            type: string
            pattern: '^REF[0-9]{9}$'
            example: "REF123456789"
      responses:
        '200':
          description: Customer found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Customer'
        '404':
          description: Customer not found
        '500':
          description: Internal server error

  /cif/customer/by-id/{customerId}:
    get:
      tags:
        - CIF Operations
      summary: Get Customer by Customer ID
      description: |
        Retrieves customer details by customer ID (CIF).
        
        **Use Cases:**
        - Lookup customer by CIF number
        - Verify customer existence
        - Integration with other services
        
      operationId: getCustomerByCustomerId
      parameters:
        - name: customerId
          in: path
          required: true
          description: Customer ID (CIF) assigned by external system
          schema:
            type: string
            pattern: '^CIF[0-9]+$'
            example: "CIF100001"
      responses:
        '200':
          description: Customer found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Customer'
        '404':
          description: Customer not found
        '500':
          description: Internal server error

  /cif/health:
    get:
      tags:
        - Health Check
      summary: CIF Service Health Check
      description: Simple health check endpoint for the CIF service
      operationId: cifHealthCheck
      responses:
        '200':
          description: Service is healthy
          content:
            text/plain:
              schema:
                type: string
                example: "CIF Service is healthy"

  /v1/cif/original/health:
    get:
      tags:
        - Health Check
      summary: Detailed Health Status
      description: |
        Comprehensive health status endpoint with detailed service information.
        
        **Health Indicators:**
        - Service status
        - Version information
        - Timestamp
        - Database connectivity (if applicable)
        
      operationId: detailedHealthCheck
      responses:
        '200':
          description: Detailed health status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatus'
              examples:
                healthy_status:
                  summary: Healthy service status
                  value:
                    status: "UP"
                    service: "CIF Original"
                    timestamp: "2024-01-15T10:30:45.123"
                    version: "1.0.0"

components:
  schemas:
    CreateCustomerIdRequest:
      type: object
      required:
        - body
      properties:
        body:
          $ref: '#/components/schemas/RequestBody'
      example:
        body:
          createCustomerIdRequest:
            requestBody:
              salutation: "Mr"
              firstName: "Rajesh"
              lastName: "Kumar"
              dateOfBirth: "1985-03-15"
              language: "ENG"
              maritalStatus: "Y"
              nationality: "IN"
              isMinor: "N"
              isCustomerNRE: "N"
              defaultAddressType: "Mailing"
              gender: "Male"
              nativeLanguageCode: "ENG01"
              occupation: "A102"
              pan: "**********"
              branchId: "887"
              isStaff: "N"
              taxDeductionTable: "TDS11"
              maidenNameOfMother: "Sunita Sharma"
              custHealthCode: "AB01"
              addressDetails:
                - addressLine1: "Plot No 123, Sector 15"
                  addressCategory: "Mailing"
                  city: "Gurgaon"
                  state: "Haryana"
                  postalCode: "122001"
                  country: "IN"
              contactDetails:
                - contactType: "Phone"
                  contactSubType: "Cell"
                  countryCode: "+91"
                  phoneNumber: "**********"
                  isPreferredContact: "Yes"
                  email: "<EMAIL>"
              entityDocDetails:
                - countryOfIssue: "IN"
                  documentCode: "AADHAR_IDN_OF"
                  issueDate: "2010-05-20T00:00:00"
                  expiryDate: "2099-12-31T00:00:00"
                  typeCode: "Identification"
                  placeOfIssue: "HRD"
                  isMandatory: "Y"
                  receivedDate: "2024-01-15T10:30:00"
              psychographicData:
                - currencyCode: "INR"

    RequestBody:
      type: object
      required:
        - createCustomerIdRequest
      properties:
        createCustomerIdRequest:
          $ref: '#/components/schemas/CustomerIdRequestBody'

    CustomerIdRequestBody:
      type: object
      required:
        - requestBody
      properties:
        requestBody:
          $ref: '#/components/schemas/CustomerDetailsRequest'

    CustomerDetailsRequest:
      type: object
      required:
        - salutation
        - firstName
        - lastName
        - dateOfBirth
        - language
        - maritalStatus
        - nationality
        - isMinor
        - isCustomerNRE
        - defaultAddressType
        - gender
        - nativeLanguageCode
        - occupation
        - pan
        - branchId
        - isStaff
        - taxDeductionTable
        - maidenNameOfMother
        - custHealthCode
        - addressDetails
        - contactDetails
        - entityDocDetails
        - psychographicData
      properties:
        salutation:
          type: string
          maxLength: 10
          default: "Mr"
          example: "Mr"
          description: Customer salutation
        firstName:
          type: string
          maxLength: 100
          example: "Rajesh"
          description: Customer first name
        lastName:
          type: string
          maxLength: 100
          example: "Kumar"
          description: Customer last name
        dateOfBirth:
          type: string
          format: date
          example: "1985-03-15"
          description: Customer date of birth (must be in the past)
        language:
          type: string
          maxLength: 10
          default: "ENG"
          example: "ENG"
          description: Preferred language code
        maritalStatus:
          type: string
          maxLength: 1
          default: "Y"
          example: "Y"
          description: Marital status (Y/N)
        nationality:
          type: string
          maxLength: 10
          default: "IN"
          example: "IN"
          description: Nationality code
        isMinor:
          type: string
          maxLength: 1
          default: "N"
          example: "N"
          description: Minor status (Y/N)
        isCustomerNRE:
          type: string
          maxLength: 1
          default: "N"
          example: "N"
          description: NRE customer status (Y/N)
        defaultAddressType:
          type: string
          maxLength: 20
          default: "Mailing"
          example: "Mailing"
          description: Default address type
        gender:
          type: string
          maxLength: 10
          default: "Male"
          example: "Male"
          description: Customer gender
        nativeLanguageCode:
          type: string
          maxLength: 10
          default: "ENG01"
          example: "ENG01"
          description: Native language code
        occupation:
          type: string
          maxLength: 10
          default: "A102"
          example: "A102"
          description: Occupation code
        pan:
          type: string
          pattern: '^[A-Z]{5}[0-9]{4}[A-Z]{1}$'
          example: "**********"
          description: PAN (Permanent Account Number) - must be unique
        branchId:
          type: string
          maxLength: 10
          default: "887"
          example: "887"
          description: Branch ID
        isStaff:
          type: string
          maxLength: 1
          default: "N"
          example: "N"
          description: Staff member status (Y/N)
        taxDeductionTable:
          type: string
          maxLength: 10
          default: "TDS11"
          example: "TDS11"
          description: Tax deduction table code
        maidenNameOfMother:
          type: string
          maxLength: 100
          example: "Sunita Sharma"
          description: Mother's maiden name
        custHealthCode:
          type: string
          maxLength: 10
          default: "AB01"
          example: "AB01"
          description: Customer health code
        addressDetails:
          type: array
          minItems: 1
          maxItems: 2
          items:
            $ref: '#/components/schemas/AddressDetailsRequest'
          description: Customer address details (1-2 addresses required)
        contactDetails:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/ContactDetailsRequest'
          description: Customer contact details (at least one required)
        entityDocDetails:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/EntityDocDetailsRequest'
          description: Customer document details (at least one required)
        psychographicData:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/PsychographicDataRequest'
          description: Customer psychographic data (at least one required)

    AddressDetailsRequest:
      type: object
      required:
        - addressLine1
        - addressCategory
        - city
        - state
        - postalCode
        - country
      properties:
        addressLine1:
          type: string
          maxLength: 255
          example: "Plot No 123, Sector 15"
          description: Primary address line
        addressLine2:
          type: string
          maxLength: 255
          example: "Near City Mall"
          description: Secondary address line (optional)
        addressCategory:
          type: string
          maxLength: 20
          example: "Mailing"
          enum: ["Home", "Mailing", "Office", "Permanent"]
          description: Address category type
        city:
          type: string
          maxLength: 100
          example: "Gurgaon"
          description: City name
        state:
          type: string
          maxLength: 100
          example: "Haryana"
          description: State name
        postalCode:
          type: string
          maxLength: 20
          example: "122001"
          description: Postal/ZIP code
        country:
          type: string
          maxLength: 10
          default: "IN"
          example: "IN"
          description: Country code

    ContactDetailsRequest:
      type: object
      required:
        - contactType
        - contactSubType
        - countryCode
        - phoneNumber
        - isPreferredContact
        - email
      properties:
        contactType:
          type: string
          maxLength: 20
          default: "Phone"
          example: "Phone"
          description: Contact type
        contactSubType:
          type: string
          maxLength: 20
          default: "Cell"
          example: "Cell"
          description: Contact sub-type
        countryCode:
          type: string
          maxLength: 10
          default: "+91"
          example: "+91"
          description: Country code for phone number
        phoneNumber:
          type: string
          pattern: '^\\d{10}$'
          example: "**********"
          description: 10-digit phone number
        isPreferredContact:
          type: string
          pattern: '^(Yes|No)$'
          default: "Yes"
          example: "Yes"
          description: Preferred contact flag (Yes/No)
        email:
          type: string
          format: email
          maxLength: 255
          example: "<EMAIL>"
          description: Email address

    EntityDocDetailsRequest:
      type: object
      required:
        - countryOfIssue
        - documentCode
        - issueDate
        - expiryDate
        - typeCode
        - placeOfIssue
        - isMandatory
        - receivedDate
      properties:
        countryOfIssue:
          type: string
          maxLength: 10
          default: "IN"
          example: "IN"
          description: Country of document issue
        documentCode:
          type: string
          maxLength: 50
          example: "AADHAR_IDN_OF"
          description: Document code identifier
        issueDate:
          type: string
          format: date-time
          example: "2010-05-20T00:00:00"
          description: Document issue date
        expiryDate:
          type: string
          format: date-time
          example: "2099-12-31T00:00:00"
          description: Document expiry date
        typeCode:
          type: string
          maxLength: 50
          example: "Identification"
          description: Document type code
        typeDescription:
          type: string
          maxLength: 100
          example: "Aadhar card for identification"
          description: Document type description (optional)
        placeOfIssue:
          type: string
          maxLength: 50
          default: "HRD"
          example: "HRD"
          description: Place of document issue
        isMandatory:
          type: string
          pattern: '^[YN]$'
          default: "Y"
          example: "Y"
          description: Mandatory document flag (Y/N)
        receivedDate:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00"
          description: Date when document was received

    PsychographicDataRequest:
      type: object
      required:
        - currencyCode
      properties:
        currencyCode:
          type: string
          maxLength: 10
          default: "INR"
          example: "INR"
          description: Preferred currency code

    CustomerCreationResponse:
      type: object
      properties:
        referenceNumber:
          type: string
          example: "REF123456789"
          description: Unique reference number for the customer creation request
        customerId:
          type: string
          example: "CIF100001"
          description: Customer ID (CIF) assigned by external system (null on failure)
        status:
          type: string
          enum: ["SUCCESS", "FAILURE", "VALIDATION_ERROR", "SERVICE_UNAVAILABLE"]
          example: "SUCCESS"
          description: Status of the customer creation request
        message:
          type: string
          example: "Customer ID created successfully"
          description: Descriptive message about the operation result
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T10:30:45.123"
          description: Timestamp when the response was generated
        processingTimeMs:
          type: integer
          format: int64
          example: 2500
          description: Processing time in milliseconds (null for validation errors)

    Customer:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
          description: Internal database ID
        customerId:
          type: string
          example: "CIF100001"
          description: Customer ID (CIF) assigned by external system
        referenceNumber:
          type: string
          example: "REF123456789"
          description: Unique reference number
        salutation:
          type: string
          example: "Mr"
          description: Customer salutation
        firstName:
          type: string
          example: "Rajesh"
          description: Customer first name
        lastName:
          type: string
          example: "Kumar"
          description: Customer last name
        dateOfBirth:
          type: string
          format: date
          example: "1985-03-15"
          description: Customer date of birth
        language:
          type: string
          example: "ENG"
          description: Preferred language code
        maritalStatus:
          type: string
          example: "Y"
          description: Marital status
        nationality:
          type: string
          example: "IN"
          description: Nationality code
        isMinor:
          type: string
          example: "N"
          description: Minor status
        isCustomerNRE:
          type: string
          example: "N"
          description: NRE customer status
        defaultAddressType:
          type: string
          example: "Mailing"
          description: Default address type
        gender:
          type: string
          example: "Male"
          description: Customer gender
        nativeLanguageCode:
          type: string
          example: "ENG01"
          description: Native language code
        occupation:
          type: string
          example: "A102"
          description: Occupation code
        pan:
          type: string
          example: "**********"
          description: PAN (Permanent Account Number)
        branchId:
          type: string
          example: "887"
          description: Branch ID
        isStaff:
          type: string
          example: "N"
          description: Staff member status
        taxDeductionTable:
          type: string
          example: "TDS11"
          description: Tax deduction table code
        maidenNameOfMother:
          type: string
          example: "Sunita Sharma"
          description: Mother's maiden name
        custHealthCode:
          type: string
          example: "AB01"
          description: Customer health code
        status:
          type: string
          enum: ["PENDING", "SUCCESS", "FAILURE", "RETRY"]
          example: "SUCCESS"
          description: Customer status in the system
        createdAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:45.123"
          description: Customer creation timestamp
        updatedAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:45.123"
          description: Last update timestamp
        createdBy:
          type: string
          example: "SYSTEM"
          description: Created by user/system
        updatedBy:
          type: string
          example: "SYSTEM"
          description: Last updated by user/system
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/Address'
          description: Customer addresses
        contactDetails:
          type: array
          items:
            $ref: '#/components/schemas/ContactDetail'
          description: Customer contact details
        documentDetails:
          type: array
          items:
            $ref: '#/components/schemas/DocumentDetail'
          description: Customer document details
        psychographicData:
          type: array
          items:
            $ref: '#/components/schemas/PsychographicData'
          description: Customer psychographic data

    Address:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
          description: Internal address ID
        addressLine1:
          type: string
          example: "Plot No 123, Sector 15"
          description: Primary address line
        addressLine2:
          type: string
          example: "Near City Mall"
          description: Secondary address line
        addressCategory:
          type: string
          example: "MAILING"
          description: Address category
        city:
          type: string
          example: "Gurgaon"
          description: City name
        state:
          type: string
          example: "Haryana"
          description: State name
        postalCode:
          type: string
          example: "122001"
          description: Postal code
        country:
          type: string
          example: "IN"
          description: Country code
        createdAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:45.123"
          description: Address creation timestamp
        updatedAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:45.123"
          description: Last update timestamp

    ContactDetail:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
          description: Internal contact detail ID
        contactType:
          type: string
          example: "Phone"
          description: Contact type
        contactSubType:
          type: string
          example: "Cell"
          description: Contact sub-type
        countryCode:
          type: string
          example: "+91"
          description: Country code
        phoneNumber:
          type: string
          example: "**********"
          description: Phone number
        isPreferredContact:
          type: string
          example: "Yes"
          description: Preferred contact flag
        email:
          type: string
          example: "<EMAIL>"
          description: Email address
        createdAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:45.123"
          description: Contact detail creation timestamp
        updatedAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:45.123"
          description: Last update timestamp

    DocumentDetail:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
          description: Internal document detail ID
        countryOfIssue:
          type: string
          example: "IN"
          description: Country of document issue
        documentCode:
          type: string
          example: "AADHAR_IDN_OF"
          description: Document code
        issueDate:
          type: string
          format: date-time
          example: "2010-05-20T00:00:00"
          description: Document issue date
        expiryDate:
          type: string
          format: date-time
          example: "2099-12-31T00:00:00"
          description: Document expiry date
        typeCode:
          type: string
          example: "Identification"
          description: Document type code
        typeDescription:
          type: string
          example: "Aadhar card for identification"
          description: Document type description
        placeOfIssue:
          type: string
          example: "HRD"
          description: Place of document issue
        isMandatory:
          type: string
          example: "Y"
          description: Mandatory document flag
        receivedDate:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00"
          description: Date when document was received
        createdAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:45.123"
          description: Document detail creation timestamp
        updatedAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:45.123"
          description: Last update timestamp

    PsychographicData:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
          description: Internal psychographic data ID
        currencyCode:
          type: string
          example: "INR"
          description: Preferred currency code
        createdAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:45.123"
          description: Psychographic data creation timestamp
        updatedAt:
          type: string
          format: date-time
          example: "2024-01-15T10:30:45.123"
          description: Last update timestamp

    HealthStatus:
      type: object
      properties:
        status:
          type: string
          enum: ["UP", "DOWN", "UNKNOWN"]
          example: "UP"
          description: Overall service status
        service:
          type: string
          example: "CIF Original"
          description: Service name
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T10:30:45.123"
          description: Health check timestamp
        version:
          type: string
          example: "1.0.0"
          description: Service version

    ErrorResponse:
      type: object
      properties:
        errorCode:
          type: string
          example: "VALIDATION_ERROR"
          description: Error code identifier
        message:
          type: string
          example: "Invalid request parameters"
          description: Human-readable error message
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T10:30:45.123"
          description: Error timestamp
        path:
          type: string
          example: "/api/cif/cif/create"
          description: Request path that caused the error

  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key for authentication (if required)

    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token authentication (if required)

# Global security (uncomment if authentication is required)
# security:
#   - ApiKeyAuth: []
#   - BearerAuth: []
