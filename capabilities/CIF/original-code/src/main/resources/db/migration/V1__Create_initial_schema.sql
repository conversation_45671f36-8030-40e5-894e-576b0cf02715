-- Create initial schema for CIF Service

-- Create sequence for reference numbers
CREATE SEQUENCE IF NOT EXISTS ref_number_seq START 1000000;

-- Customer table
CREATE TABLE customers (
    id BIGSERIAL PRIMARY KEY,
    customer_id VARCHAR(50) UNIQUE,
    reference_number VARCHAR(25) NOT NULL UNIQUE,
    salutation VARCHAR(10) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    date_of_birth DATE NOT NULL,
    language VARCHAR(10) NOT NULL DEFAULT 'ENG',
    marital_status VARCHAR(1) NOT NULL DEFAULT 'Y',
    nationality VARCHAR(10) NOT NULL DEFAULT 'IN',
    is_minor VARCHAR(1) NOT NULL DEFAULT 'N',
    is_customer_nre VARCHAR(1) NOT NULL DEFAULT 'N',
    default_address_type VARCHAR(20) NOT NULL DEFAULT 'Mailing',
    gender VARCHAR(10) NOT NULL DEFAULT 'Male',
    native_language_code VARCHAR(10) NOT NULL DEFAULT 'ENG01',
    occupation VARCHAR(10) NOT NULL DEFAULT 'A102',
    pan VARCHAR(10) NOT NULL,
    branch_id VARCHAR(10) NOT NULL DEFAULT '887',
    is_staff VARCHAR(1) NOT NULL DEFAULT 'N',
    tax_deduction_table VARCHAR(10) NOT NULL DEFAULT 'TDS11',
    maiden_name_of_mother VARCHAR(100) NOT NULL,
    cust_health_code VARCHAR(10) NOT NULL DEFAULT 'AB01',
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50) NOT NULL DEFAULT 'SYSTEM',
    updated_by VARCHAR(50) NOT NULL DEFAULT 'SYSTEM'
);

-- Address table
CREATE TABLE addresses (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    address_line1 VARCHAR(255) NOT NULL,
    address_line2 VARCHAR(255),
    address_category VARCHAR(20) NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    postal_code VARCHAR(20) NOT NULL,
    country VARCHAR(10) NOT NULL DEFAULT 'IN',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Contact details table
CREATE TABLE contact_details (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    contact_type VARCHAR(20) NOT NULL DEFAULT 'Phone',
    contact_sub_type VARCHAR(20) NOT NULL DEFAULT 'Cell',
    country_code VARCHAR(10) NOT NULL DEFAULT '+91',
    phone_number VARCHAR(20) NOT NULL,
    is_preferred_contact VARCHAR(3) NOT NULL DEFAULT 'Yes',
    email VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Document details table
CREATE TABLE document_details (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    country_of_issue VARCHAR(10) NOT NULL DEFAULT 'IN',
    document_code VARCHAR(50) NOT NULL,
    issue_date TIMESTAMP NOT NULL DEFAULT '2000-12-31 00:00:00',
    expiry_date TIMESTAMP NOT NULL DEFAULT '2099-12-31 00:00:00',
    type_code VARCHAR(50) NOT NULL,
    type_description VARCHAR(100),
    place_of_issue VARCHAR(50) NOT NULL DEFAULT 'HRD',
    is_mandatory VARCHAR(1) NOT NULL DEFAULT 'Y',
    received_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Psychographic data table
CREATE TABLE psychographic_data (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    currency_code VARCHAR(10) NOT NULL DEFAULT 'INR',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Audit log table for tracking all operations
CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    reference_number VARCHAR(25) NOT NULL,
    operation VARCHAR(50) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id VARCHAR(50),
    request_data TEXT,
    response_data TEXT,
    status VARCHAR(20) NOT NULL,
    error_code VARCHAR(50),
    error_message TEXT,
    processing_time_ms BIGINT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50) NOT NULL DEFAULT 'SYSTEM'
);

-- Create indexes for better performance
CREATE INDEX idx_customers_customer_id ON customers(customer_id);
CREATE INDEX idx_customers_reference_number ON customers(reference_number);
CREATE INDEX idx_customers_pan ON customers(pan);
CREATE INDEX idx_customers_status ON customers(status);
CREATE INDEX idx_customers_created_at ON customers(created_at);

CREATE INDEX idx_addresses_customer_id ON addresses(customer_id);
CREATE INDEX idx_addresses_category ON addresses(address_category);

CREATE INDEX idx_contact_details_customer_id ON contact_details(customer_id);
CREATE INDEX idx_contact_details_email ON contact_details(email);

CREATE INDEX idx_document_details_customer_id ON document_details(customer_id);
CREATE INDEX idx_document_details_type_code ON document_details(type_code);

CREATE INDEX idx_psychographic_data_customer_id ON psychographic_data(customer_id);

CREATE INDEX idx_audit_logs_reference_number ON audit_logs(reference_number);
CREATE INDEX idx_audit_logs_operation ON audit_logs(operation);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_audit_logs_status ON audit_logs(status);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_addresses_updated_at BEFORE UPDATE ON addresses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_contact_details_updated_at BEFORE UPDATE ON contact_details FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_document_details_updated_at BEFORE UPDATE ON document_details FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_psychographic_data_updated_at BEFORE UPDATE ON psychographic_data FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
