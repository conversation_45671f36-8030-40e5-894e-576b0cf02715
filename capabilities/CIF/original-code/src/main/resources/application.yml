spring:
  application:
    name: cif-service

  datasource:
    url: **************************************
    username: cifuser
    password: cifpass
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
    clean-disabled: true

  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
    default-property-inclusion: non_null

server:
  port: ${SERVER_PORT:8104}
  servlet:
    context-path: /api/cif

# External API Configuration
external:
  cif:
    api:
      base-url: http://localhost:8204/api/cif/internal
      create-endpoint: /cif/create
      timeout: 30000
      retry:
        max-attempts: 3
        delay: 1000
        multiplier: 2.0

# Logging Configuration
logging:
  level:
    com.financial.cif: INFO
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: logs/cif-service.log

# Management and Actuator
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when_authorized
  metrics:
    export:
      prometheus:
        enabled: true

# OpenAPI/Swagger Configuration
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    display-request-duration: true
    display-operation-id: true
    default-models-expand-depth: 2
    default-model-expand-depth: 2
    doc-expansion: none
    tags-sorter: alpha
    operations-sorter: alpha
  show-actuator: true

# Application specific configuration
app:
  audit:
    enabled: true
    retention-days: 2555  # 7 years
  security:
    pii-masking:
      enabled: true
      mask-char: "*"
      visible-chars: 4
