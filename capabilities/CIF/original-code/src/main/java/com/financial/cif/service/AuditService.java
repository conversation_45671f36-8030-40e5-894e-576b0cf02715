package com.financial.cif.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.financial.cif.entity.AuditLog;
import com.financial.cif.repository.AuditLogRepository;
import com.financial.cif.util.PiiMaskingUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service for managing audit logs and compliance tracking.
 * Handles logging of all operations with PII masking for security.
 */
@Service
@Transactional
public class AuditService {

    private static final Logger logger = LoggerFactory.getLogger(AuditService.class);

    private final AuditLogRepository auditLogRepository;
    private final PiiMaskingUtil piiMaskingUtil;
    private final ObjectMapper objectMapper;

    @Value("${app.audit.enabled:true}")
    private boolean auditEnabled;

    @Autowired
    public AuditService(AuditLogRepository auditLogRepository, 
                       PiiMaskingUtil piiMaskingUtil,
                       ObjectMapper objectMapper) {
        this.auditLogRepository = auditLogRepository;
        this.piiMaskingUtil = piiMaskingUtil;
        this.objectMapper = objectMapper;
    }

    /**
     * Log the start of an operation.
     * 
     * @param referenceNumber unique reference number for tracking
     * @param operation operation type
     * @param entityType entity type being operated on
     * @param requestData request data (will be masked)
     */
    public void logOperationStart(String referenceNumber, String operation, String entityType, Object requestData) {
        if (!auditEnabled) {
            return;
        }

        try {
            // Set trace ID in MDC for correlation
            MDC.put("traceId", referenceNumber);

            String maskedRequestData = maskSensitiveData(requestData);
            
            AuditLog auditLog = new AuditLog(referenceNumber, operation, entityType, "STARTED");
            auditLog.setRequestData(maskedRequestData);
            
            auditLogRepository.save(auditLog);
            
            logger.info("Operation started - Reference: {}, Operation: {}, Entity: {}", 
                       referenceNumber, operation, entityType);
                       
        } catch (Exception e) {
            logger.error("Failed to log operation start for reference: {}", referenceNumber, e);
        }
    }

    /**
     * Log successful completion of an operation.
     * 
     * @param referenceNumber unique reference number for tracking
     * @param operation operation type
     * @param entityType entity type being operated on
     * @param entityId entity ID (e.g., customer ID)
     * @param responseData response data (will be masked)
     * @param processingTimeMs processing time in milliseconds
     */
    public void logOperationSuccess(String referenceNumber, String operation, String entityType, 
                                   String entityId, Object responseData, Long processingTimeMs) {
        if (!auditEnabled) {
            return;
        }

        try {
            MDC.put("traceId", referenceNumber);

            String maskedResponseData = maskSensitiveData(responseData);
            
            AuditLog auditLog = new AuditLog(referenceNumber, operation, entityType, "SUCCESS");
            auditLog.setEntityId(entityId);
            auditLog.setResponseData(maskedResponseData);
            auditLog.setProcessingTimeMs(processingTimeMs);
            
            auditLogRepository.save(auditLog);
            
            logger.info("Operation completed successfully - Reference: {}, Operation: {}, Entity: {}, EntityId: {}, ProcessingTime: {}ms", 
                       referenceNumber, operation, entityType, entityId, processingTimeMs);
                       
        } catch (Exception e) {
            logger.error("Failed to log operation success for reference: {}", referenceNumber, e);
        }
    }

    /**
     * Log failure of an operation.
     * 
     * @param referenceNumber unique reference number for tracking
     * @param operation operation type
     * @param entityType entity type being operated on
     * @param errorCode error code
     * @param errorMessage error message
     * @param processingTimeMs processing time in milliseconds
     */
    public void logOperationFailure(String referenceNumber, String operation, String entityType, 
                                   String errorCode, String errorMessage, Long processingTimeMs) {
        if (!auditEnabled) {
            return;
        }

        try {
            MDC.put("traceId", referenceNumber);

            AuditLog auditLog = new AuditLog(referenceNumber, operation, entityType, "FAILURE");
            auditLog.setErrorCode(errorCode);
            auditLog.setErrorMessage(errorMessage);
            auditLog.setProcessingTimeMs(processingTimeMs);
            
            auditLogRepository.save(auditLog);
            
            logger.error("Operation failed - Reference: {}, Operation: {}, Entity: {}, ErrorCode: {}, ErrorMessage: {}, ProcessingTime: {}ms", 
                        referenceNumber, operation, entityType, errorCode, errorMessage, processingTimeMs);
                        
        } catch (Exception e) {
            logger.error("Failed to log operation failure for reference: {}", referenceNumber, e);
        }
    }

    /**
     * Log retry attempt for an operation.
     * 
     * @param referenceNumber unique reference number for tracking
     * @param operation operation type
     * @param entityType entity type being operated on
     * @param attemptNumber retry attempt number
     * @param reason reason for retry
     */
    public void logRetryAttempt(String referenceNumber, String operation, String entityType, 
                               int attemptNumber, String reason) {
        if (!auditEnabled) {
            return;
        }

        try {
            MDC.put("traceId", referenceNumber);

            AuditLog auditLog = new AuditLog(referenceNumber, operation + "_RETRY", entityType, "RETRY");
            auditLog.setErrorMessage(String.format("Retry attempt %d: %s", attemptNumber, reason));
            
            auditLogRepository.save(auditLog);
            
            logger.warn("Operation retry - Reference: {}, Operation: {}, Entity: {}, Attempt: {}, Reason: {}", 
                       referenceNumber, operation, entityType, attemptNumber, reason);
                       
        } catch (Exception e) {
            logger.error("Failed to log retry attempt for reference: {}", referenceNumber, e);
        }
    }

    /**
     * Get audit trail for a specific reference number.
     * 
     * @param referenceNumber the reference number
     * @return list of audit logs
     */
    @Transactional(readOnly = true)
    public List<AuditLog> getAuditTrail(String referenceNumber) {
        return auditLogRepository.findByReferenceNumber(referenceNumber);
    }

    /**
     * Get failed operations within a date range for analysis.
     * 
     * @param startDate start date
     * @param endDate end date
     * @return list of failed audit logs
     */
    @Transactional(readOnly = true)
    public List<AuditLog> getFailedOperations(LocalDateTime startDate, LocalDateTime endDate) {
        return auditLogRepository.findFailedOperations(startDate, endDate);
    }

    /**
     * Clean up old audit logs based on retention policy.
     * 
     * @param retentionDate date before which logs should be cleaned up
     * @return number of logs cleaned up
     */
    public int cleanupOldAuditLogs(LocalDateTime retentionDate) {
        List<AuditLog> logsToCleanup = auditLogRepository.findAuditLogsForRetentionCleanup(retentionDate);
        int count = logsToCleanup.size();
        
        if (count > 0) {
            auditLogRepository.deleteAll(logsToCleanup);
            logger.info("Cleaned up {} old audit logs before date: {}", count, retentionDate);
        }
        
        return count;
    }

    /**
     * Mask sensitive data in objects before logging.
     * 
     * @param data the data object to mask
     * @return masked JSON string
     */
    private String maskSensitiveData(Object data) {
        if (data == null) {
            return null;
        }

        try {
            String jsonData = objectMapper.writeValueAsString(data);
            
            // Apply PII masking to common sensitive fields
            jsonData = jsonData.replaceAll("\"pan\"\\s*:\\s*\"([^\"]+)\"", 
                                         "\"pan\":\"" + piiMaskingUtil.maskPan("$1") + "\"");
            jsonData = jsonData.replaceAll("\"email\"\\s*:\\s*\"([^\"]+)\"", 
                                         "\"email\":\"" + piiMaskingUtil.maskEmail("$1") + "\"");
            jsonData = jsonData.replaceAll("\"phoneNumber\"\\s*:\\s*\"([^\"]+)\"", 
                                         "\"phoneNumber\":\"" + piiMaskingUtil.maskPhoneNumber("$1") + "\"");
            jsonData = jsonData.replaceAll("\"firstName\"\\s*:\\s*\"([^\"]+)\"", 
                                         "\"firstName\":\"" + piiMaskingUtil.maskName("$1") + "\"");
            jsonData = jsonData.replaceAll("\"lastName\"\\s*:\\s*\"([^\"]+)\"", 
                                         "\"lastName\":\"" + piiMaskingUtil.maskName("$1") + "\"");
            jsonData = jsonData.replaceAll("\"maidenNameOfMother\"\\s*:\\s*\"([^\"]+)\"", 
                                         "\"maidenNameOfMother\":\"" + piiMaskingUtil.maskName("$1") + "\"");
            jsonData = jsonData.replaceAll("\"addressLine1\"\\s*:\\s*\"([^\"]+)\"", 
                                         "\"addressLine1\":\"" + piiMaskingUtil.maskAddress("$1") + "\"");
            jsonData = jsonData.replaceAll("\"addressLine2\"\\s*:\\s*\"([^\"]+)\"", 
                                         "\"addressLine2\":\"" + piiMaskingUtil.maskAddress("$1") + "\"");
            
            return jsonData;
            
        } catch (JsonProcessingException e) {
            logger.warn("Failed to serialize data for masking, using toString: {}", e.getMessage());
            return piiMaskingUtil.maskGeneric(data.toString(), 10, 10);
        }
    }

    /**
     * Clear MDC trace ID.
     */
    public void clearTraceId() {
        MDC.remove("traceId");
    }
}
