package com.financial.cif.service;

import com.financial.cif.dto.request.CreateCustomerIdRequest;
import com.financial.cif.dto.response.CifCreationResponse;
import com.financial.cif.dto.response.CustomerCreationResponse;
import com.financial.cif.entity.*;
import com.financial.cif.enums.AddressCategory;
import com.financial.cif.enums.CustomerStatus;
import com.financial.cif.repository.CustomerRepository;
import com.financial.cif.service.ExternalCifApiClient.CifApiException;
import com.financial.cif.util.ReferenceNumberGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Main service for CIF (Customer ID) creation operations.
 * Handles the complete customer creation workflow including validation,
 * external API integration, and data persistence.
 */
@Service
@Transactional
public class CifService {

    private static final Logger logger = LoggerFactory.getLogger(CifService.class);
    private static final String FALLBACK_MESSAGE = "We are unable to proceed with your loan application request right now. Please try again later.";

    private final CustomerRepository customerRepository;
    private final ExternalCifApiClient externalCifApiClient;
    private final AuditService auditService;
    private final ReferenceNumberGenerator referenceNumberGenerator;

    @Autowired
    public CifService(CustomerRepository customerRepository,
                     ExternalCifApiClient externalCifApiClient,
                     AuditService auditService,
                     ReferenceNumberGenerator referenceNumberGenerator) {
        this.customerRepository = customerRepository;
        this.externalCifApiClient = externalCifApiClient;
        this.auditService = auditService;
        this.referenceNumberGenerator = referenceNumberGenerator;
    }

    /**
     * Create customer ID through the complete CIF creation workflow.
     * 
     * @param request the customer creation request
     * @return customer creation response
     */
    public CustomerCreationResponse createCustomerId(CreateCustomerIdRequest request) {
        String referenceNumber = referenceNumberGenerator.generateReferenceNumber();
        long startTime = System.currentTimeMillis();

        try {
            logger.info("Starting CIF creation process - Reference: {}", referenceNumber);
            auditService.logOperationStart(referenceNumber, "CREATE_CUSTOMER_ID", "CUSTOMER", request);

            // Phase 1: Validate and prepare customer data
            Customer customer = validateAndPrepareCustomerData(request, referenceNumber);
            
            // Save customer with PENDING status
            customer = customerRepository.save(customer);
            logger.info("Customer saved with PENDING status - Reference: {}, CustomerId: {}", 
                       referenceNumber, customer.getId());

            // Phase 2: Call external CIF API
            String channelId = referenceNumberGenerator.generateChannelId();
            CifCreationResponse cifResponse = callExternalCifApi(request, channelId, referenceNumber);

            // Phase 3: Process CIF response
            CustomerCreationResponse response = processCifResponse(customer, cifResponse, referenceNumber, startTime);

            auditService.clearTraceId();
            return response;

        } catch (ValidationException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            auditService.logOperationFailure(referenceNumber, "CREATE_CUSTOMER_ID", "CUSTOMER", 
                                           "VALIDATION_ERROR", e.getMessage(), processingTime);
            auditService.clearTraceId();
            return CustomerCreationResponse.validationError(referenceNumber, e.getMessage());

        } catch (CifApiException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            auditService.logOperationFailure(referenceNumber, "CREATE_CUSTOMER_ID", "CUSTOMER", 
                                           e.getErrorCode(), e.getMessage(), processingTime);
            auditService.clearTraceId();
            return CustomerCreationResponse.serviceUnavailable(referenceNumber);

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            auditService.logOperationFailure(referenceNumber, "CREATE_CUSTOMER_ID", "CUSTOMER", 
                                           "UNEXPECTED_ERROR", e.getMessage(), processingTime);
            logger.error("Unexpected error in CIF creation - Reference: {}", referenceNumber, e);
            auditService.clearTraceId();
            return CustomerCreationResponse.serviceUnavailable(referenceNumber);
        }
    }

    /**
     * Validate request data and prepare customer entity.
     */
    private Customer validateAndPrepareCustomerData(CreateCustomerIdRequest request, String referenceNumber) {
        var customerDetails = request.getBody().getCreateCustomerIdRequest().getRequestBody();

        // Check for duplicate PAN
        if (customerRepository.existsByPan(customerDetails.getPan())) {
            throw new ValidationException("Customer with PAN " + customerDetails.getPan() + " already exists");
        }

        // Create customer entity
        Customer customer = new Customer(
            referenceNumber,
            customerDetails.getSalutation(),
            customerDetails.getFirstName(),
            customerDetails.getLastName(),
            customerDetails.getDateOfBirth(),
            customerDetails.getPan(),
            customerDetails.getMaidenNameOfMother()
        );

        // Set additional customer details
        customer.setLanguage(customerDetails.getLanguage());
        customer.setMaritalStatus(customerDetails.getMaritalStatus());
        customer.setNationality(customerDetails.getNationality());
        customer.setIsMinor(customerDetails.getIsMinor());
        customer.setIsCustomerNRE(customerDetails.getIsCustomerNRE());
        customer.setDefaultAddressType(customerDetails.getDefaultAddressType());
        customer.setGender(customerDetails.getGender());
        customer.setNativeLanguageCode(customerDetails.getNativeLanguageCode());
        customer.setOccupation(customerDetails.getOccupation());
        customer.setBranchId(customerDetails.getBranchId());
        customer.setIsStaff(customerDetails.getIsStaff());
        customer.setTaxDeductionTable(customerDetails.getTaxDeductionTable());
        customer.setCustHealthCode(customerDetails.getCustHealthCode());

        // Add addresses
        List<Address> addresses = new ArrayList<>();
        for (var addressReq : customerDetails.getAddressDetails()) {
            Address address = new Address(
                customer,
                addressReq.getAddressLine1(),
                AddressCategory.fromCode(addressReq.getAddressCategory()),
                addressReq.getCity(),
                addressReq.getState(),
                addressReq.getPostalCode()
            );
            address.setAddressLine2(addressReq.getAddressLine2());
            address.setCountry(addressReq.getCountry());
            addresses.add(address);
        }
        customer.setAddresses(addresses);

        // Add contact details
        List<ContactDetail> contactDetails = new ArrayList<>();
        for (var contactReq : customerDetails.getContactDetails()) {
            ContactDetail contact = new ContactDetail(customer, contactReq.getPhoneNumber(), contactReq.getEmail());
            contact.setContactType(contactReq.getContactType());
            contact.setContactSubType(contactReq.getContactSubType());
            contact.setCountryCode(contactReq.getCountryCode());
            contact.setIsPreferredContact(contactReq.getIsPreferredContact());
            contactDetails.add(contact);
        }
        customer.setContactDetails(contactDetails);

        // Add document details
        List<DocumentDetail> documentDetails = new ArrayList<>();
        for (var docReq : customerDetails.getEntityDocDetails()) {
            DocumentDetail document = new DocumentDetail(customer, docReq.getDocumentCode(), docReq.getTypeCode());
            document.setCountryOfIssue(docReq.getCountryOfIssue());
            document.setIssueDate(docReq.getIssueDate());
            document.setExpiryDate(docReq.getExpiryDate());
            document.setTypeDescription(docReq.getTypeDescription());
            document.setPlaceOfIssue(docReq.getPlaceOfIssue());
            document.setIsMandatory(docReq.getIsMandatory());
            document.setReceivedDate(docReq.getReceivedDate());
            documentDetails.add(document);
        }
        customer.setDocumentDetails(documentDetails);

        // Add psychographic data
        List<PsychographicData> psychographicData = new ArrayList<>();
        for (var psychoReq : customerDetails.getPsychographicData()) {
            PsychographicData psycho = new PsychographicData(customer, psychoReq.getCurrencyCode());
            psychographicData.add(psycho);
        }
        customer.setPsychographicData(psychographicData);

        return customer;
    }

    /**
     * Call external CIF API with retry logic.
     */
    private CifCreationResponse callExternalCifApi(CreateCustomerIdRequest request, String channelId, String referenceNumber) {
        int maxAttempts = 3;
        int attempt = 1;

        while (attempt <= maxAttempts) {
            try {
                if (attempt > 1) {
                    auditService.logRetryAttempt(referenceNumber, "EXTERNAL_CIF_API_CALL", "CIF_API", 
                                               attempt, "Retrying CIF API call");
                }

                return externalCifApiClient.createCustomerId(request, channelId, referenceNumber);

            } catch (CifApiException e) {
                if (attempt == maxAttempts) {
                    logger.error("CIF API call failed after {} attempts - Reference: {}", maxAttempts, referenceNumber);
                    throw e;
                }

                logger.warn("CIF API call attempt {} failed - Reference: {}, Error: {}", 
                           attempt, referenceNumber, e.getMessage());
                attempt++;

                // Wait before retry
                try {
                    Thread.sleep(1000 * attempt);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Interrupted during retry wait", ie);
                }
            }
        }

        throw new CifApiException("All retry attempts exhausted", "MAX_RETRIES_EXCEEDED");
    }

    /**
     * Process CIF API response and update customer status.
     */
    private CustomerCreationResponse processCifResponse(Customer customer, CifCreationResponse cifResponse, 
                                                       String referenceNumber, long startTime) {
        long processingTime = System.currentTimeMillis() - startTime;

        if (cifResponse.isSuccess()) {
            // Update customer with CIF response
            customer.setCustomerId(cifResponse.getCustomerId());
            customer.setStatus(CustomerStatus.SUCCESS);
            customer = customerRepository.save(customer);

            auditService.logOperationSuccess(referenceNumber, "CREATE_CUSTOMER_ID", "CUSTOMER", 
                                           customer.getCustomerId(), cifResponse, processingTime);

            logger.info("CIF creation completed successfully - Reference: {}, CustomerId: {}", 
                       referenceNumber, customer.getCustomerId());

            return CustomerCreationResponse.success(referenceNumber, customer.getCustomerId(), processingTime);

        } else {
            // Update customer status to FAILURE
            customer.setStatus(CustomerStatus.FAILURE);
            customerRepository.save(customer);

            auditService.logOperationFailure(referenceNumber, "CREATE_CUSTOMER_ID", "CUSTOMER", 
                                           "CIF_CREATION_FAILED", cifResponse.getDescription(), processingTime);

            logger.error("CIF creation failed - Reference: {}, Description: {}", 
                        referenceNumber, cifResponse.getDescription());

            return CustomerCreationResponse.failure(referenceNumber, FALLBACK_MESSAGE, processingTime);
        }
    }

    /**
     * Get customer by reference number.
     */
    @Transactional(readOnly = true)
    public Customer getCustomerByReferenceNumber(String referenceNumber) {
        return customerRepository.findByReferenceNumber(referenceNumber).orElse(null);
    }

    /**
     * Get customer by customer ID.
     */
    @Transactional(readOnly = true)
    public Customer getCustomerByCustomerId(String customerId) {
        return customerRepository.findByCustomerId(customerId).orElse(null);
    }

    /**
     * Custom validation exception.
     */
    public static class ValidationException extends RuntimeException {
        public ValidationException(String message) {
            super(message);
        }
    }
}
