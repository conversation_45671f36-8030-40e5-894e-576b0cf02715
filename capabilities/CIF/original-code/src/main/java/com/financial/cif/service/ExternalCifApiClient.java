package com.financial.cif.service;

import com.financial.cif.dto.request.CreateCustomerIdRequest;
import com.financial.cif.dto.response.CifCreationResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientException;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import java.time.Duration;

/**
 * Client service for interacting with external CIF creation API.
 * Handles HTTP communication, retries, and error handling for CIF operations.
 */
@Service
public class ExternalCifApiClient {

    private static final Logger logger = LoggerFactory.getLogger(ExternalCifApiClient.class);

    private final WebClient webClient;
    private final AuditService auditService;

    @Value("${external.cif.api.base-url}")
    private String baseUrl;

    @Value("${external.cif.api.create-endpoint}")
    private String createEndpoint;

    @Value("${external.cif.api.timeout:30000}")
    private int timeoutMs;

    public ExternalCifApiClient(WebClient.Builder webClientBuilder, AuditService auditService) {
        this.auditService = auditService;
        this.webClient = webClientBuilder
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                .build();
    }

    /**
     * Create customer ID by calling external CIF API.
     * 
     * @param request the CIF creation request
     * @param channelId unique channel ID for tracking
     * @param referenceNumber reference number for audit trail
     * @return CIF creation response
     * @throws CifApiException if API call fails
     */
    @Retryable(
        value = {WebClientException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2.0)
    )
    public CifCreationResponse createCustomerId(CreateCustomerIdRequest request, String channelId, String referenceNumber) {
        long startTime = System.currentTimeMillis();
        
        try {
            logger.info("Calling external CIF API - Reference: {}, ChannelId: {}", referenceNumber, channelId);
            
            auditService.logOperationStart(referenceNumber, "EXTERNAL_CIF_API_CALL", "CIF_API", request);

            String fullUrl = baseUrl + createEndpoint;
            
            CifCreationResponse response = webClient
                    .post()
                    .uri(fullUrl)
                    .header("channelId", channelId)
                    .header("X-Reference-Number", referenceNumber)
                    .body(Mono.just(request), CreateCustomerIdRequest.class)
                    .retrieve()
                    .bodyToMono(CifCreationResponse.class)
                    .timeout(Duration.ofMillis(timeoutMs))
                    .block();

            long processingTime = System.currentTimeMillis() - startTime;

            if (response == null) {
                throw new CifApiException("Received null response from CIF API", "CIF_NULL_RESPONSE");
            }

            // Validate response
            if (response.hasNullStatus()) {
                auditService.logOperationFailure(referenceNumber, "EXTERNAL_CIF_API_CALL", "CIF_API", 
                                                "CIF_NULL_STATUS", "Received null status from CIF API", processingTime);
                throw new CifApiException("Received null status from CIF API", "CIF_NULL_STATUS");
            }

            if (response.isFailure()) {
                auditService.logOperationFailure(referenceNumber, "EXTERNAL_CIF_API_CALL", "CIF_API", 
                                                "CIF_CREATION_FAILED", response.getDescription(), processingTime);
                throw new CifApiException("CIF creation failed: " + response.getDescription(), "CIF_CREATION_FAILED");
            }

            if (response.isSuccess()) {
                auditService.logOperationSuccess(referenceNumber, "EXTERNAL_CIF_API_CALL", "CIF_API", 
                                               response.getCustomerId(), response, processingTime);
                logger.info("CIF API call successful - Reference: {}, CustomerId: {}, ProcessingTime: {}ms", 
                           referenceNumber, response.getCustomerId(), processingTime);
            }

            return response;

        } catch (WebClientResponseException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            String errorMessage = String.format("HTTP %d: %s", e.getStatusCode().value(), e.getResponseBodyAsString());
            
            auditService.logOperationFailure(referenceNumber, "EXTERNAL_CIF_API_CALL", "CIF_API", 
                                            "HTTP_ERROR", errorMessage, processingTime);
            
            logger.error("CIF API HTTP error - Reference: {}, Status: {}, Response: {}", 
                        referenceNumber, e.getStatusCode(), e.getResponseBodyAsString());
            
            throw new CifApiException("CIF API HTTP error: " + errorMessage, "HTTP_ERROR", e);

        } catch (WebClientException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            String errorMessage = "Network error: " + e.getMessage();
            
            auditService.logOperationFailure(referenceNumber, "EXTERNAL_CIF_API_CALL", "CIF_API", 
                                            "NETWORK_ERROR", errorMessage, processingTime);
            
            logger.error("CIF API network error - Reference: {}, Error: {}", referenceNumber, e.getMessage());
            
            throw new CifApiException(errorMessage, "NETWORK_ERROR", e);

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            String errorMessage = "Unexpected error: " + e.getMessage();
            
            auditService.logOperationFailure(referenceNumber, "EXTERNAL_CIF_API_CALL", "CIF_API", 
                                            "UNEXPECTED_ERROR", errorMessage, processingTime);
            
            logger.error("CIF API unexpected error - Reference: {}, Error: {}", referenceNumber, e.getMessage(), e);
            
            throw new CifApiException(errorMessage, "UNEXPECTED_ERROR", e);
        }
    }

    /**
     * Check if the external CIF API is available.
     * 
     * @return true if API is available
     */
    public boolean isApiAvailable() {
        try {
            String healthUrl = baseUrl + "/health";
            
            String response = webClient
                    .get()
                    .uri(healthUrl)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofMillis(5000))
                    .block();

            return response != null;

        } catch (Exception e) {
            logger.warn("CIF API health check failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Custom exception for CIF API errors.
     */
    public static class CifApiException extends RuntimeException {
        private final String errorCode;

        public CifApiException(String message, String errorCode) {
            super(message);
            this.errorCode = errorCode;
        }

        public CifApiException(String message, String errorCode, Throwable cause) {
            super(message, cause);
            this.errorCode = errorCode;
        }

        public String getErrorCode() {
            return errorCode;
        }
    }
}
