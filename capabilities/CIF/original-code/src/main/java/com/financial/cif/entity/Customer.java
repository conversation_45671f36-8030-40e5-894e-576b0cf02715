package com.financial.cif.entity;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.financial.cif.enums.CustomerStatus;
import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Customer entity representing the main customer information in CIF creation process.
 * Contains all mandatory and optional customer details as per business requirements.
 */
@Entity
@Table(name = "customers")
public class Customer {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "customer_id", unique = true, length = 50)
    private String customerId;

    @Column(name = "reference_number", nullable = false, unique = true, length = 25)
    private String referenceNumber;

    @Column(name = "salutation", nullable = false, length = 10)
    private String salutation;

    @Column(name = "first_name", nullable = false, length = 100)
    private String firstName;

    @Column(name = "last_name", nullable = false, length = 100)
    private String lastName;

    @Column(name = "date_of_birth", nullable = false)
    private LocalDate dateOfBirth;

    @Column(name = "language", nullable = false, length = 10)
    private String language = "ENG";

    @Column(name = "marital_status", nullable = false, length = 1)
    private String maritalStatus = "Y";

    @Column(name = "nationality", nullable = false, length = 10)
    private String nationality = "IN";

    @Column(name = "is_minor", nullable = false, length = 1)
    private String isMinor = "N";

    @Column(name = "is_customer_nre", nullable = false, length = 1)
    private String isCustomerNRE = "N";

    @Column(name = "default_address_type", nullable = false, length = 20)
    private String defaultAddressType = "Mailing";

    @Column(name = "gender", nullable = false, length = 10)
    private String gender = "Male";

    @Column(name = "native_language_code", nullable = false, length = 10)
    private String nativeLanguageCode = "ENG01";

    @Column(name = "occupation", nullable = false, length = 10)
    private String occupation = "A102";

    @Column(name = "pan", nullable = false, length = 10)
    private String pan;

    @Column(name = "branch_id", nullable = false, length = 10)
    private String branchId = "887";

    @Column(name = "is_staff", nullable = false, length = 1)
    private String isStaff = "N";

    @Column(name = "tax_deduction_table", nullable = false, length = 10)
    private String taxDeductionTable = "TDS11";

    @Column(name = "maiden_name_of_mother", nullable = false, length = 100)
    private String maidenNameOfMother;

    @Column(name = "cust_health_code", nullable = false, length = 10)
    private String custHealthCode = "AB01";

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private CustomerStatus status = CustomerStatus.PENDING;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "created_by", nullable = false, length = 50)
    private String createdBy = "SYSTEM";

    @Column(name = "updated_by", nullable = false, length = 50)
    private String updatedBy = "SYSTEM";

    @OneToMany(mappedBy = "customer", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonManagedReference("customer-addresses")
    private List<Address> addresses = new ArrayList<>();

    @OneToMany(mappedBy = "customer", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonManagedReference("customer-contacts")
    private List<ContactDetail> contactDetails = new ArrayList<>();

    @OneToMany(mappedBy = "customer", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonManagedReference("customer-documents")
    private List<DocumentDetail> documentDetails = new ArrayList<>();

    @OneToMany(mappedBy = "customer", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonManagedReference("customer-psychographic")
    private List<PsychographicData> psychographicData = new ArrayList<>();

    // Default constructor
    public Customer() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    // Constructor with required fields
    public Customer(String referenceNumber, String salutation, String firstName, String lastName,
                   LocalDate dateOfBirth, String pan, String maidenNameOfMother) {
        this();
        this.referenceNumber = referenceNumber;
        this.salutation = salutation;
        this.firstName = firstName;
        this.lastName = lastName;
        this.dateOfBirth = dateOfBirth;
        this.pan = pan;
        this.maidenNameOfMother = maidenNameOfMother;
    }

    @PrePersist
    protected void onCreate() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getSalutation() {
        return salutation;
    }

    public void setSalutation(String salutation) {
        this.salutation = salutation;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getIsMinor() {
        return isMinor;
    }

    public void setIsMinor(String isMinor) {
        this.isMinor = isMinor;
    }

    public String getIsCustomerNRE() {
        return isCustomerNRE;
    }

    public void setIsCustomerNRE(String isCustomerNRE) {
        this.isCustomerNRE = isCustomerNRE;
    }

    public String getDefaultAddressType() {
        return defaultAddressType;
    }

    public void setDefaultAddressType(String defaultAddressType) {
        this.defaultAddressType = defaultAddressType;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getNativeLanguageCode() {
        return nativeLanguageCode;
    }

    public void setNativeLanguageCode(String nativeLanguageCode) {
        this.nativeLanguageCode = nativeLanguageCode;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getIsStaff() {
        return isStaff;
    }

    public void setIsStaff(String isStaff) {
        this.isStaff = isStaff;
    }

    public String getTaxDeductionTable() {
        return taxDeductionTable;
    }

    public void setTaxDeductionTable(String taxDeductionTable) {
        this.taxDeductionTable = taxDeductionTable;
    }

    public String getMaidenNameOfMother() {
        return maidenNameOfMother;
    }

    public void setMaidenNameOfMother(String maidenNameOfMother) {
        this.maidenNameOfMother = maidenNameOfMother;
    }

    public String getCustHealthCode() {
        return custHealthCode;
    }

    public void setCustHealthCode(String custHealthCode) {
        this.custHealthCode = custHealthCode;
    }

    public CustomerStatus getStatus() {
        return status;
    }

    public void setStatus(CustomerStatus status) {
        this.status = status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public List<Address> getAddresses() {
        return addresses;
    }

    public void setAddresses(List<Address> addresses) {
        this.addresses = addresses;
    }

    public List<ContactDetail> getContactDetails() {
        return contactDetails;
    }

    public void setContactDetails(List<ContactDetail> contactDetails) {
        this.contactDetails = contactDetails;
    }

    public List<DocumentDetail> getDocumentDetails() {
        return documentDetails;
    }

    public void setDocumentDetails(List<DocumentDetail> documentDetails) {
        this.documentDetails = documentDetails;
    }

    public List<PsychographicData> getPsychographicData() {
        return psychographicData;
    }

    public void setPsychographicData(List<PsychographicData> psychographicData) {
        this.psychographicData = psychographicData;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Customer customer = (Customer) o;
        return Objects.equals(id, customer.id) &&
               Objects.equals(referenceNumber, customer.referenceNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, referenceNumber);
    }

    @Override
    public String toString() {
        return "Customer{" +
                "id=" + id +
                ", customerId='" + customerId + '\'' +
                ", referenceNumber='" + referenceNumber + '\'' +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", status=" + status +
                '}';
    }
}
