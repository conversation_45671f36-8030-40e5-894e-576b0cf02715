package com.financial.cif.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;

/**
 * OpenAPI configuration for CIF Service.
 * Customizes the OpenAPI documentation with proper server URLs, contact information,
 * and API metadata.
 */
@Configuration
public class OpenApiConfig {

    @Value("${server.port:8104}")
    private String serverPort;

    @Value("${server.servlet.context-path:/api/cif}")
    private String contextPath;

    @Bean
    public OpenAPI cifServiceOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("CIF (Customer Information File) Service API")
                        .description("""
                                Customer ID (CIF) Creation Service for loan application processing.
                                This service handles the complete customer creation workflow including validation,
                                external API integration, and data persistence.
                                
                                ## Features
                                - Customer ID creation with comprehensive validation
                                - Integration with external CIF systems
                                - Retry logic for resilient API calls
                                - Complete audit trail and logging
                                - Health monitoring endpoints
                                
                                ## Business Flow
                                1. **Phase 1**: Validate request data and prepare customer entity
                                2. **Phase 2**: Call external CIF API with retry logic
                                3. **Phase 3**: Process CIF response and persist customer data
                                
                                ## Error Handling
                                - Validation errors return 400 with detailed error messages
                                - Service unavailable scenarios return 503 with retry guidance
                                - Internal errors return 500 with generic error messages
                                - All errors include reference numbers for tracking
                                """)
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("CIF Service Team")
                                .email("<EMAIL>")
                                .url("https://financial.com/contact"))
                        .license(new License()
                                .name("Proprietary")
                                .url("https://financial.com/license")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + contextPath)
                                .description("Local development server"),
                        new Server()
                                .url("https://dev-api.financial.com" + contextPath)
                                .description("Development server"),
                        new Server()
                                .url("https://staging-api.financial.com" + contextPath)
                                .description("Staging server"),
                        new Server()
                                .url("https://api.financial.com" + contextPath)
                                .description("Production server")
                ));
    }
}
