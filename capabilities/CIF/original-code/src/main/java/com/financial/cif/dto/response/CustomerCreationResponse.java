package com.financial.cif.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;

/**
 * DTO representing the response for customer creation operations.
 * Used for internal API responses and client communication.
 */
public class CustomerCreationResponse {

    @JsonProperty("referenceNumber")
    private String referenceNumber;

    @JsonProperty("customerId")
    private String customerId;

    @JsonProperty("status")
    private String status;

    @JsonProperty("message")
    private String message;

    @JsonProperty("timestamp")
    private LocalDateTime timestamp;

    @JsonProperty("processingTimeMs")
    private Long processingTimeMs;

    // Default constructor
    public CustomerCreationResponse() {
        this.timestamp = LocalDateTime.now();
    }

    // Constructor with required fields
    public CustomerCreationResponse(String referenceNumber, String status, String message) {
        this();
        this.referenceNumber = referenceNumber;
        this.status = status;
        this.message = message;
    }

    // Constructor with all fields
    public CustomerCreationResponse(String referenceNumber, String customerId, String status, 
                                   String message, Long processingTimeMs) {
        this();
        this.referenceNumber = referenceNumber;
        this.customerId = customerId;
        this.status = status;
        this.message = message;
        this.processingTimeMs = processingTimeMs;
    }

    // Static factory methods for common responses
    public static CustomerCreationResponse success(String referenceNumber, String customerId, 
                                                  Long processingTimeMs) {
        return new CustomerCreationResponse(referenceNumber, customerId, "SUCCESS", 
                                          "Customer ID created successfully", processingTimeMs);
    }

    public static CustomerCreationResponse failure(String referenceNumber, String message, 
                                                  Long processingTimeMs) {
        return new CustomerCreationResponse(referenceNumber, null, "FAILURE", message, processingTimeMs);
    }

    public static CustomerCreationResponse validationError(String referenceNumber, String message) {
        return new CustomerCreationResponse(referenceNumber, "VALIDATION_ERROR", message);
    }

    public static CustomerCreationResponse serviceUnavailable(String referenceNumber) {
        return new CustomerCreationResponse(referenceNumber, "SERVICE_UNAVAILABLE", 
                                          "We are unable to proceed with your loan application request right now. Please try again later.");
    }

    // Getters and Setters
    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public Long getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(Long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }

    @Override
    public String toString() {
        return "CustomerCreationResponse{" +
                "referenceNumber='" + referenceNumber + '\'' +
                ", customerId='" + customerId + '\'' +
                ", status='" + status + '\'' +
                ", message='" + message + '\'' +
                ", timestamp=" + timestamp +
                ", processingTimeMs=" + processingTimeMs +
                '}';
    }
}
