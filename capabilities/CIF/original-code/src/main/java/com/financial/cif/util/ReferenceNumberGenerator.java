package com.financial.cif.util;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Utility class for generating unique reference numbers for tracking and audit purposes.
 * Generates reference numbers in the format: CIF + YYYYMMDD + HHMMSS + sequence
 */
@Component
public class ReferenceNumberGenerator {

    private static final String PREFIX = "CIF";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HHmmss");
    private static final AtomicLong sequence = new AtomicLong(1000);

    /**
     * Generate a unique reference number.
     * Format: CIF + YYYYMMDD + HHMMSS + sequence (e.g., CIF202312251430001001)
     * 
     * @return unique reference number
     */
    public String generateReferenceNumber() {
        LocalDateTime now = LocalDateTime.now();
        String datePart = now.format(DATE_FORMATTER);
        String timePart = now.format(TIME_FORMATTER);
        long sequenceNumber = sequence.getAndIncrement();
        
        // Reset sequence if it gets too large (daily reset)
        if (sequenceNumber > 999999) {
            sequence.set(1000);
            sequenceNumber = sequence.getAndIncrement();
        }
        
        return String.format("%s%s%s%06d", PREFIX, datePart, timePart, sequenceNumber);
    }

    /**
     * Generate a reference number with custom prefix.
     * 
     * @param customPrefix custom prefix to use instead of default "CIF"
     * @return unique reference number with custom prefix
     */
    public String generateReferenceNumber(String customPrefix) {
        LocalDateTime now = LocalDateTime.now();
        String datePart = now.format(DATE_FORMATTER);
        String timePart = now.format(TIME_FORMATTER);
        long sequenceNumber = sequence.getAndIncrement();
        
        // Reset sequence if it gets too large
        if (sequenceNumber > 999999) {
            sequence.set(1000);
            sequenceNumber = sequence.getAndIncrement();
        }
        
        return String.format("%s%s%s%06d", customPrefix, datePart, timePart, sequenceNumber);
    }

    /**
     * Generate a channel ID for external API calls.
     * Format: CHN + YYYYMMDD + HHMMSS + sequence
     * 
     * @return unique channel ID
     */
    public String generateChannelId() {
        return generateReferenceNumber("CHN");
    }

    /**
     * Extract date from reference number.
     * 
     * @param referenceNumber the reference number
     * @return date part of the reference number or null if invalid format
     */
    public String extractDateFromReference(String referenceNumber) {
        if (referenceNumber == null || referenceNumber.length() < 11) {
            return null;
        }
        
        try {
            // Skip prefix (3 chars) and extract date (8 chars)
            return referenceNumber.substring(3, 11);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Extract time from reference number.
     * 
     * @param referenceNumber the reference number
     * @return time part of the reference number or null if invalid format
     */
    public String extractTimeFromReference(String referenceNumber) {
        if (referenceNumber == null || referenceNumber.length() < 17) {
            return null;
        }
        
        try {
            // Skip prefix (3 chars) and date (8 chars) and extract time (6 chars)
            return referenceNumber.substring(11, 17);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Extract sequence from reference number.
     * 
     * @param referenceNumber the reference number
     * @return sequence part of the reference number or null if invalid format
     */
    public String extractSequenceFromReference(String referenceNumber) {
        if (referenceNumber == null || referenceNumber.length() < 23) {
            return null;
        }
        
        try {
            // Extract last 6 characters as sequence
            return referenceNumber.substring(17);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Validate reference number format.
     * 
     * @param referenceNumber the reference number to validate
     * @return true if the reference number has valid format
     */
    public boolean isValidReferenceNumber(String referenceNumber) {
        if (referenceNumber == null || referenceNumber.length() != 23) {
            return false;
        }
        
        try {
            // Check prefix
            String prefix = referenceNumber.substring(0, 3);
            if (!prefix.equals("CIF") && !prefix.equals("CHN")) {
                return false;
            }
            
            // Check date part (8 digits)
            String datePart = referenceNumber.substring(3, 11);
            if (!datePart.matches("\\d{8}")) {
                return false;
            }
            
            // Check time part (6 digits)
            String timePart = referenceNumber.substring(11, 17);
            if (!timePart.matches("\\d{6}")) {
                return false;
            }
            
            // Check sequence part (6 digits)
            String sequencePart = referenceNumber.substring(17);
            if (!sequencePart.matches("\\d{6}")) {
                return false;
            }
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Get current sequence value (for testing purposes).
     * 
     * @return current sequence value
     */
    public long getCurrentSequence() {
        return sequence.get();
    }

    /**
     * Reset sequence to initial value (for testing purposes).
     */
    public void resetSequence() {
        sequence.set(1000);
    }
}
