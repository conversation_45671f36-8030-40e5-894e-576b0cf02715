package com.financial.cif.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * DTO representing psychographic data in the CIF creation request.
 */
public class PsychographicDataRequest {

    @JsonProperty("currencyCode")
    @NotBlank(message = "Currency code is mandatory")
    @Size(max = 10, message = "Currency code must not exceed 10 characters")
    private String currencyCode = "INR";

    // Default constructor
    public PsychographicDataRequest() {
    }

    // Constructor with currency code
    public PsychographicDataRequest(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    // Getters and Setters
    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    @Override
    public String toString() {
        return "PsychographicDataRequest{" +
                "currencyCode='" + currencyCode + '\'' +
                '}';
    }
}
