package com.financial.cif.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * DTO representing document details in the CIF creation request.
 */
public class EntityDocDetailsRequest {

    @JsonProperty("countryOfIssue")
    @NotBlank(message = "Country of issue is mandatory")
    @Size(max = 10, message = "Country of issue must not exceed 10 characters")
    private String countryOfIssue = "IN";

    @JsonProperty("documentCode")
    @NotBlank(message = "Document code is mandatory")
    @Size(max = 50, message = "Document code must not exceed 50 characters")
    private String documentCode;

    @JsonProperty("issueDate")
    @NotNull(message = "Issue date is mandatory")
    private LocalDateTime issueDate;

    @JsonProperty("expiryDate")
    @NotNull(message = "Expiry date is mandatory")
    private LocalDateTime expiryDate;

    @JsonProperty("typeCode")
    @NotBlank(message = "Type code is mandatory")
    @Size(max = 50, message = "Type code must not exceed 50 characters")
    private String typeCode;

    @JsonProperty("typeDescription")
    @Size(max = 100, message = "Type description must not exceed 100 characters")
    private String typeDescription;

    @JsonProperty("placeOfIssue")
    @NotBlank(message = "Place of issue is mandatory")
    @Size(max = 50, message = "Place of issue must not exceed 50 characters")
    private String placeOfIssue = "HRD";

    @JsonProperty("isMandatory")
    @NotBlank(message = "Mandatory flag is mandatory")
    @Pattern(regexp = "[YN]", message = "Mandatory flag must be Y or N")
    private String isMandatory = "Y";

    @JsonProperty("receivedDate")
    @NotNull(message = "Received date is mandatory")
    private LocalDateTime receivedDate;

    // Default constructor
    public EntityDocDetailsRequest() {
        this.issueDate = LocalDateTime.of(2000, 12, 31, 0, 0, 0);
        this.expiryDate = LocalDateTime.of(2099, 12, 31, 0, 0, 0);
        this.receivedDate = LocalDateTime.now();
    }

    // Constructor with required fields
    public EntityDocDetailsRequest(String documentCode, String typeCode) {
        this();
        this.documentCode = documentCode;
        this.typeCode = typeCode;
    }

    // Getters and Setters
    public String getCountryOfIssue() {
        return countryOfIssue;
    }

    public void setCountryOfIssue(String countryOfIssue) {
        this.countryOfIssue = countryOfIssue;
    }

    public String getDocumentCode() {
        return documentCode;
    }

    public void setDocumentCode(String documentCode) {
        this.documentCode = documentCode;
    }

    public LocalDateTime getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(LocalDateTime issueDate) {
        this.issueDate = issueDate;
    }

    public LocalDateTime getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(LocalDateTime expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getTypeDescription() {
        return typeDescription;
    }

    public void setTypeDescription(String typeDescription) {
        this.typeDescription = typeDescription;
    }

    public String getPlaceOfIssue() {
        return placeOfIssue;
    }

    public void setPlaceOfIssue(String placeOfIssue) {
        this.placeOfIssue = placeOfIssue;
    }

    public String getIsMandatory() {
        return isMandatory;
    }

    public void setIsMandatory(String isMandatory) {
        this.isMandatory = isMandatory;
    }

    public LocalDateTime getReceivedDate() {
        return receivedDate;
    }

    public void setReceivedDate(LocalDateTime receivedDate) {
        this.receivedDate = receivedDate;
    }

    @Override
    public String toString() {
        return "EntityDocDetailsRequest{" +
                "countryOfIssue='" + countryOfIssue + '\'' +
                ", documentCode='" + documentCode + '\'' +
                ", issueDate=" + issueDate +
                ", expiryDate=" + expiryDate +
                ", typeCode='" + typeCode + '\'' +
                ", typeDescription='" + typeDescription + '\'' +
                ", placeOfIssue='" + placeOfIssue + '\'' +
                ", isMandatory='" + isMandatory + '\'' +
                ", receivedDate=" + receivedDate +
                '}';
    }
}
