package com.financial.cif.enums;

/**
 * Enumeration for address category types.
 */
public enum AddressCategory {
    HOME("Home", "Home address"),
    MAILING("Mailing", "Mailing address"),
    OFFICE("Office", "Office address"),
    PERMANENT("Permanent", "Permanent address");

    private final String code;
    private final String description;

    AddressCategory(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static AddressCategory fromCode(String code) {
        for (AddressCategory category : values()) {
            if (category.code.equalsIgnoreCase(code)) {
                return category;
            }
        }
        throw new IllegalArgumentException("Unknown address category code: " + code);
    }
}
