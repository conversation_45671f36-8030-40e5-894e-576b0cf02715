package com.financial.cif.repository;

import com.financial.cif.entity.Customer;
import com.financial.cif.enums.CustomerStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Customer entity operations.
 * Provides data access methods for customer management.
 */
@Repository
public interface CustomerRepository extends JpaRepository<Customer, Long> {

    /**
     * Find customer by reference number.
     * @param referenceNumber the unique reference number
     * @return Optional containing the customer if found
     */
    Optional<Customer> findByReferenceNumber(String referenceNumber);

    /**
     * Find customer by customer ID.
     * @param customerId the customer ID from external system
     * @return Optional containing the customer if found
     */
    Optional<Customer> findByCustomerId(String customerId);

    /**
     * Find customer by PAN number.
     * @param pan the PAN number
     * @return Optional containing the customer if found
     */
    Optional<Customer> findByPan(String pan);

    /**
     * Find customers by status.
     * @param status the customer status
     * @return List of customers with the given status
     */
    List<Customer> findByStatus(CustomerStatus status);

    /**
     * Find customers created between given dates.
     * @param startDate start date
     * @param endDate end date
     * @return List of customers created in the date range
     */
    List<Customer> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find customers by status and created date range.
     * @param status the customer status
     * @param startDate start date
     * @param endDate end date
     * @return List of customers matching criteria
     */
    List<Customer> findByStatusAndCreatedAtBetween(CustomerStatus status, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Check if customer exists by PAN.
     * @param pan the PAN number
     * @return true if customer exists with the given PAN
     */
    boolean existsByPan(String pan);

    /**
     * Check if customer exists by reference number.
     * @param referenceNumber the reference number
     * @return true if customer exists with the given reference number
     */
    boolean existsByReferenceNumber(String referenceNumber);

    /**
     * Find customers pending for retry.
     * @param maxRetryTime maximum time for retry attempts
     * @return List of customers eligible for retry
     */
    @Query("SELECT c FROM Customer c WHERE c.status = :status AND c.updatedAt < :maxRetryTime")
    List<Customer> findPendingRetryCustomers(@Param("status") CustomerStatus status, 
                                           @Param("maxRetryTime") LocalDateTime maxRetryTime);

    /**
     * Count customers by status.
     * @param status the customer status
     * @return count of customers with the given status
     */
    long countByStatus(CustomerStatus status);

    /**
     * Find customers for data retention cleanup.
     * @param retentionDate date before which data should be cleaned up
     * @return List of customers eligible for cleanup
     */
    @Query("SELECT c FROM Customer c WHERE c.createdAt < :retentionDate AND c.status IN :finalStatuses")
    List<Customer> findCustomersForRetentionCleanup(@Param("retentionDate") LocalDateTime retentionDate,
                                                   @Param("finalStatuses") List<CustomerStatus> finalStatuses);
}
