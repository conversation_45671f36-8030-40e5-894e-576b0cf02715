package com.financial.cif;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main application class for CIF (Customer ID) Service.
 * 
 * This service handles the creation of Customer IDs for loan applications
 * by integrating with external CIF creation APIs and managing the complete
 * customer onboarding workflow.
 */
@SpringBootApplication
@EnableRetry
@EnableTransactionManagement
public class CifServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(CifServiceApplication.class, args);
    }
}
