package com.financial.cif.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * DTO representing the response from CIF creation API.
 * Contains the customer ID and status information.
 */
public class CifCreationResponse {

    @JsonProperty("channelId")
    private String channelId;

    @JsonProperty("customerId")
    private String customerId;

    @JsonProperty("description")
    private String description;

    @JsonProperty("entity")
    private String entity;

    @JsonProperty("status")
    private String status;

    // Default constructor
    public CifCreationResponse() {
    }

    // Constructor with all fields
    public CifCreationResponse(String channelId, String customerId, String description, 
                              String entity, String status) {
        this.channelId = channelId;
        this.customerId = customerId;
        this.description = description;
        this.entity = entity;
        this.status = status;
    }

    // Static factory methods for common responses
    public static CifCreationResponse success(String channelId, String customerId) {
        return new CifCreationResponse(channelId, customerId, "Customer ID created successfully", 
                                     "Customer", "SUCCESS");
    }

    public static CifCreationResponse failure(String channelId, String description) {
        return new CifCreationResponse(channelId, null, description, "Customer", "FAILURE");
    }

    public static CifCreationResponse nullStatus(String channelId) {
        return new CifCreationResponse(channelId, null, "Service unavailable", "Customer", null);
    }

    // Getters and Setters
    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getEntity() {
        return entity;
    }

    public void setEntity(String entity) {
        this.entity = entity;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    // Utility methods for validation
    public boolean isSuccess() {
        return "SUCCESS".equals(status) && customerId != null && !customerId.trim().isEmpty();
    }

    public boolean isFailure() {
        return "FAILURE".equals(status) || customerId == null || customerId.trim().isEmpty();
    }

    public boolean hasNullStatus() {
        return status == null;
    }

    @Override
    public String toString() {
        return "CifCreationResponse{" +
                "channelId='" + channelId + '\'' +
                ", customerId='" + customerId + '\'' +
                ", description='" + description + '\'' +
                ", entity='" + entity + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
