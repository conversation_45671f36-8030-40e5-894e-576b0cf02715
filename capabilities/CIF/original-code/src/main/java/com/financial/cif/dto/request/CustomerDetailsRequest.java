package com.financial.cif.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.time.LocalDate;
import java.util.List;

/**
 * DTO representing customer details in the CIF creation request.
 * Contains all customer information including personal details, addresses, contacts, and documents.
 */
public class CustomerDetailsRequest {

    @JsonProperty("salutation")
    @NotBlank(message = "Salutation is mandatory")
    @Size(max = 10, message = "Salutation must not exceed 10 characters")
    private String salutation = "Mr";

    @JsonProperty("firstName")
    @NotBlank(message = "First name is mandatory")
    @Size(max = 100, message = "First name must not exceed 100 characters")
    private String firstName;

    @JsonProperty("lastName")
    @NotBlank(message = "Last name is mandatory")
    @Size(max = 100, message = "Last name must not exceed 100 characters")
    private String lastName;

    @JsonProperty("dateOfBirth")
    @NotNull(message = "Date of birth is mandatory")
    @Past(message = "Date of birth must be in the past")
    private LocalDate dateOfBirth;

    @JsonProperty("language")
    @NotBlank(message = "Language is mandatory")
    @Size(max = 10, message = "Language must not exceed 10 characters")
    private String language = "ENG";

    @JsonProperty("maritalStatus")
    @NotBlank(message = "Marital status is mandatory")
    @Pattern(regexp = "[YN]", message = "Marital status must be Y or N")
    private String maritalStatus = "Y";

    @JsonProperty("nationality")
    @NotBlank(message = "Nationality is mandatory")
    @Size(max = 10, message = "Nationality must not exceed 10 characters")
    private String nationality = "IN";

    @JsonProperty("isMinor")
    @NotBlank(message = "Minor status is mandatory")
    @Pattern(regexp = "[YN]", message = "Minor status must be Y or N")
    private String isMinor = "N";

    @JsonProperty("isCustomerNRE")
    @NotBlank(message = "Customer NRE status is mandatory")
    @Pattern(regexp = "[YN]", message = "Customer NRE status must be Y or N")
    private String isCustomerNRE = "N";

    @JsonProperty("defaultAddressType")
    @NotBlank(message = "Default address type is mandatory")
    @Size(max = 20, message = "Default address type must not exceed 20 characters")
    private String defaultAddressType = "Mailing";

    @JsonProperty("gender")
    @NotBlank(message = "Gender is mandatory")
    @Size(max = 10, message = "Gender must not exceed 10 characters")
    private String gender = "Male";

    @JsonProperty("nativeLanguageCode")
    @NotBlank(message = "Native language code is mandatory")
    @Size(max = 10, message = "Native language code must not exceed 10 characters")
    private String nativeLanguageCode = "ENG01";

    @JsonProperty("occupation")
    @NotBlank(message = "Occupation is mandatory")
    @Size(max = 10, message = "Occupation must not exceed 10 characters")
    private String occupation = "A102";

    @JsonProperty("pan")
    @NotBlank(message = "PAN is mandatory")
    @Pattern(regexp = "[A-Z]{5}[0-9]{4}[A-Z]{1}", message = "PAN must be in valid format (e.g., **********)")
    private String pan;

    @JsonProperty("branchid")
    @NotBlank(message = "Branch ID is mandatory")
    @Size(max = 10, message = "Branch ID must not exceed 10 characters")
    private String branchId = "887";

    @JsonProperty("isStaff")
    @NotBlank(message = "Staff status is mandatory")
    @Pattern(regexp = "[YN]", message = "Staff status must be Y or N")
    private String isStaff = "N";

    @JsonProperty("taxDeductionTable")
    @NotBlank(message = "Tax deduction table is mandatory")
    @Size(max = 10, message = "Tax deduction table must not exceed 10 characters")
    private String taxDeductionTable = "TDS11";

    @JsonProperty("maidenNameOfMother")
    @NotBlank(message = "Mother's maiden name is mandatory")
    @Size(max = 100, message = "Mother's maiden name must not exceed 100 characters")
    private String maidenNameOfMother;

    @JsonProperty("custHealthCode")
    @NotBlank(message = "Customer health code is mandatory")
    @Size(max = 10, message = "Customer health code must not exceed 10 characters")
    private String custHealthCode = "AB01";

    @JsonProperty("addressDetails")
    @NotNull(message = "Address details are mandatory")
    @Size(min = 1, max = 2, message = "Must provide 1-2 address details")
    @Valid
    private List<AddressDetailsRequest> addressDetails;

    @JsonProperty("contactDetails")
    @NotNull(message = "Contact details are mandatory")
    @Size(min = 1, message = "At least one contact detail is required")
    @Valid
    private List<ContactDetailsRequest> contactDetails;

    @JsonProperty("entityDocDetails")
    @NotNull(message = "Document details are mandatory")
    @Size(min = 1, message = "At least one document detail is required")
    @Valid
    private List<EntityDocDetailsRequest> entityDocDetails;

    @JsonProperty("psychographicData")
    @NotNull(message = "Psychographic data is mandatory")
    @Size(min = 1, message = "At least one psychographic data entry is required")
    @Valid
    private List<PsychographicDataRequest> psychographicData;

    // Default constructor
    public CustomerDetailsRequest() {
    }

    // Getters and Setters
    public String getSalutation() {
        return salutation;
    }

    public void setSalutation(String salutation) {
        this.salutation = salutation;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getIsMinor() {
        return isMinor;
    }

    public void setIsMinor(String isMinor) {
        this.isMinor = isMinor;
    }

    public String getIsCustomerNRE() {
        return isCustomerNRE;
    }

    public void setIsCustomerNRE(String isCustomerNRE) {
        this.isCustomerNRE = isCustomerNRE;
    }

    public String getDefaultAddressType() {
        return defaultAddressType;
    }

    public void setDefaultAddressType(String defaultAddressType) {
        this.defaultAddressType = defaultAddressType;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getNativeLanguageCode() {
        return nativeLanguageCode;
    }

    public void setNativeLanguageCode(String nativeLanguageCode) {
        this.nativeLanguageCode = nativeLanguageCode;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getIsStaff() {
        return isStaff;
    }

    public void setIsStaff(String isStaff) {
        this.isStaff = isStaff;
    }

    public String getTaxDeductionTable() {
        return taxDeductionTable;
    }

    public void setTaxDeductionTable(String taxDeductionTable) {
        this.taxDeductionTable = taxDeductionTable;
    }

    public String getMaidenNameOfMother() {
        return maidenNameOfMother;
    }

    public void setMaidenNameOfMother(String maidenNameOfMother) {
        this.maidenNameOfMother = maidenNameOfMother;
    }

    public String getCustHealthCode() {
        return custHealthCode;
    }

    public void setCustHealthCode(String custHealthCode) {
        this.custHealthCode = custHealthCode;
    }

    public List<AddressDetailsRequest> getAddressDetails() {
        return addressDetails;
    }

    public void setAddressDetails(List<AddressDetailsRequest> addressDetails) {
        this.addressDetails = addressDetails;
    }

    public List<ContactDetailsRequest> getContactDetails() {
        return contactDetails;
    }

    public void setContactDetails(List<ContactDetailsRequest> contactDetails) {
        this.contactDetails = contactDetails;
    }

    public List<EntityDocDetailsRequest> getEntityDocDetails() {
        return entityDocDetails;
    }

    public void setEntityDocDetails(List<EntityDocDetailsRequest> entityDocDetails) {
        this.entityDocDetails = entityDocDetails;
    }

    public List<PsychographicDataRequest> getPsychographicData() {
        return psychographicData;
    }

    public void setPsychographicData(List<PsychographicDataRequest> psychographicData) {
        this.psychographicData = psychographicData;
    }

    @Override
    public String toString() {
        return "CustomerDetailsRequest{" +
                "salutation='" + salutation + '\'' +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", dateOfBirth=" + dateOfBirth +
                ", language='" + language + '\'' +
                ", maritalStatus='" + maritalStatus + '\'' +
                ", nationality='" + nationality + '\'' +
                ", isMinor='" + isMinor + '\'' +
                ", isCustomerNRE='" + isCustomerNRE + '\'' +
                ", defaultAddressType='" + defaultAddressType + '\'' +
                ", gender='" + gender + '\'' +
                ", nativeLanguageCode='" + nativeLanguageCode + '\'' +
                ", occupation='" + occupation + '\'' +
                ", pan='" + pan + '\'' +
                ", branchId='" + branchId + '\'' +
                ", isStaff='" + isStaff + '\'' +
                ", taxDeductionTable='" + taxDeductionTable + '\'' +
                ", maidenNameOfMother='" + maidenNameOfMother + '\'' +
                ", custHealthCode='" + custHealthCode + '\'' +
                '}';
    }
}
