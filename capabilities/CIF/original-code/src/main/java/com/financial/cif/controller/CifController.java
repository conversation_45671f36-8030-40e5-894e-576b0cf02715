package com.financial.cif.controller;

import com.financial.cif.dto.request.CreateCustomerIdRequest;
import com.financial.cif.dto.response.CustomerCreationResponse;
import com.financial.cif.entity.Customer;
import com.financial.cif.service.CifService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;

/**
 * REST controller for CIF (Customer ID) creation operations.
 * Provides endpoints for customer creation and inquiry.
 */
@RestController
@RequestMapping("/cif")
public class CifController {

    private static final Logger logger = LoggerFactory.getLogger(CifController.class);

    private final CifService cifService;

    @Autowired
    public CifController(CifService cifService) {
        this.cifService = cifService;
    }

    /**
     * Create customer ID endpoint.
     * 
     * @param request the customer creation request
     * @param bindingResult validation results
     * @return customer creation response
     */
    @PostMapping("/create")
    public ResponseEntity<CustomerCreationResponse> createCustomerId(
            @Valid @RequestBody CreateCustomerIdRequest request,
            BindingResult bindingResult) {

        logger.info("Received CIF creation request");

        // Check for validation errors
        if (bindingResult.hasErrors()) {
            String errorMessage = bindingResult.getFieldErrors().stream()
                    .map(error -> error.getField() + ": " + error.getDefaultMessage())
                    .collect(Collectors.joining(", "));

            logger.warn("Validation errors in CIF creation request: {}", errorMessage);

            CustomerCreationResponse errorResponse = CustomerCreationResponse.validationError(
                    "VALIDATION_ERROR", "Validation failed: " + errorMessage);

            return ResponseEntity.badRequest().body(errorResponse);
        }

        try {
            CustomerCreationResponse response = cifService.createCustomerId(request);

            // Determine HTTP status based on response status
            HttpStatus httpStatus = switch (response.getStatus()) {
                case "SUCCESS" -> HttpStatus.OK;
                case "VALIDATION_ERROR" -> HttpStatus.BAD_REQUEST;
                case "SERVICE_UNAVAILABLE" -> HttpStatus.SERVICE_UNAVAILABLE;
                case "FAILURE" -> HttpStatus.INTERNAL_SERVER_ERROR;
                default -> HttpStatus.INTERNAL_SERVER_ERROR;
            };

            logger.info("CIF creation request processed - Reference: {}, Status: {}", 
                       response.getReferenceNumber(), response.getStatus());

            return ResponseEntity.status(httpStatus).body(response);

        } catch (Exception e) {
            logger.error("Unexpected error in CIF creation endpoint", e);

            CustomerCreationResponse errorResponse = CustomerCreationResponse.serviceUnavailable("SYSTEM_ERROR");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Get customer by reference number.
     * 
     * @param referenceNumber the reference number
     * @return customer details or not found
     */
    @GetMapping("/customer/{referenceNumber}")
    public ResponseEntity<Customer> getCustomerByReferenceNumber(@PathVariable String referenceNumber) {
        logger.info("Retrieving customer by reference number: {}", referenceNumber);

        try {
            Customer customer = cifService.getCustomerByReferenceNumber(referenceNumber);

            if (customer == null) {
                logger.warn("Customer not found for reference number: {}", referenceNumber);
                return ResponseEntity.notFound().build();
            }

            logger.info("Customer found for reference number: {}, CustomerId: {}", 
                       referenceNumber, customer.getCustomerId());

            return ResponseEntity.ok(customer);

        } catch (Exception e) {
            logger.error("Error retrieving customer by reference number: {}", referenceNumber, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get customer by customer ID.
     * 
     * @param customerId the customer ID
     * @return customer details or not found
     */
    @GetMapping("/customer/id/{customerId}")
    public ResponseEntity<Customer> getCustomerByCustomerId(@PathVariable String customerId) {
        logger.info("Retrieving customer by customer ID: {}", customerId);

        try {
            Customer customer = cifService.getCustomerByCustomerId(customerId);

            if (customer == null) {
                logger.warn("Customer not found for customer ID: {}", customerId);
                return ResponseEntity.notFound().build();
            }

            logger.info("Customer found for customer ID: {}, ReferenceNumber: {}", 
                       customerId, customer.getReferenceNumber());

            return ResponseEntity.ok(customer);

        } catch (Exception e) {
            logger.error("Error retrieving customer by customer ID: {}", customerId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Health check endpoint.
     * 
     * @return health status
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("CIF Service is healthy");
    }
}
