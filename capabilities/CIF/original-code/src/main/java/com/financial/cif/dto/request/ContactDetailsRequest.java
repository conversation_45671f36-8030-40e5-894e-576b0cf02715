package com.financial.cif.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * DTO representing contact details in the CIF creation request.
 */
public class ContactDetailsRequest {

    @JsonProperty("contactType")
    @NotBlank(message = "Contact type is mandatory")
    @Size(max = 20, message = "Contact type must not exceed 20 characters")
    private String contactType = "Phone";

    @JsonProperty("contactSubType")
    @NotBlank(message = "Contact sub type is mandatory")
    @Size(max = 20, message = "Contact sub type must not exceed 20 characters")
    private String contactSubType = "Cell";

    @JsonProperty("countryCode")
    @NotBlank(message = "Country code is mandatory")
    @Size(max = 10, message = "Country code must not exceed 10 characters")
    private String countryCode = "+91";

    @JsonProperty("phoneNumber")
    @NotBlank(message = "Phone number is mandatory")
    @Pattern(regexp = "\\d{10}", message = "Phone number must be 10 digits")
    private String phoneNumber;

    @JsonProperty("isPreferredContact")
    @NotBlank(message = "Preferred contact flag is mandatory")
    @Pattern(regexp = "Yes|No", message = "Preferred contact must be Yes or No")
    private String isPreferredContact = "Yes";

    @JsonProperty("email")
    @NotBlank(message = "Email is mandatory")
    @Email(message = "Email must be in valid format")
    @Size(max = 255, message = "Email must not exceed 255 characters")
    private String email;

    // Default constructor
    public ContactDetailsRequest() {
    }

    // Constructor with required fields
    public ContactDetailsRequest(String phoneNumber, String email) {
        this.phoneNumber = phoneNumber;
        this.email = email;
    }

    // Getters and Setters
    public String getContactType() {
        return contactType;
    }

    public void setContactType(String contactType) {
        this.contactType = contactType;
    }

    public String getContactSubType() {
        return contactSubType;
    }

    public void setContactSubType(String contactSubType) {
        this.contactSubType = contactSubType;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getIsPreferredContact() {
        return isPreferredContact;
    }

    public void setIsPreferredContact(String isPreferredContact) {
        this.isPreferredContact = isPreferredContact;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Override
    public String toString() {
        return "ContactDetailsRequest{" +
                "contactType='" + contactType + '\'' +
                ", contactSubType='" + contactSubType + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", phoneNumber='" + phoneNumber + '\'' +
                ", isPreferredContact='" + isPreferredContact + '\'' +
                ", email='" + email + '\'' +
                '}';
    }
}
