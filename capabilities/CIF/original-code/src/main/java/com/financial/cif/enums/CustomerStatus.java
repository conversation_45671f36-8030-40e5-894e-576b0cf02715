package com.financial.cif.enums;

/**
 * Enumeration for customer status values.
 * Represents the current state of a customer in the CIF creation process.
 */
public enum CustomerStatus {
    PENDING("PENDING", "Customer creation request is pending"),
    SUCCESS("SUCCESS", "Customer created successfully"),
    FAILURE("FAILURE", "Customer creation failed"),
    RETRY("RETRY", "Customer creation is being retried");

    private final String code;
    private final String description;

    CustomerStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static CustomerStatus fromCode(String code) {
        for (CustomerStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown customer status code: " + code);
    }
}
