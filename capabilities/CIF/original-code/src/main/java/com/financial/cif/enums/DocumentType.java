package com.financial.cif.enums;

/**
 * Enumeration for document types used in CIF creation.
 */
public enum DocumentType {
    AADHAR_IDENTIFICATION("AADHAR_IDN_OF", "Identification", "<PERSON><PERSON>har card for identification"),
    AADHAR_ADDRESS("AADHAR_ADR_OF", "Address Proof", "Aadhar card for address proof"),
    PAN_CARD("PAN_CARD", "Identification", "PAN card for identification"),
    PASSPORT("PASSPORT", "Identification", "Passport for identification"),
    DRIVING_LICENSE("DRIVING_LICENSE", "Identification", "Driving license for identification"),
    VOTER_ID("VOTER_ID", "Identification", "Voter ID for identification"),
    UTILITY_BILL("UTILITY_BILL", "Address Proof", "Utility bill for address proof"),
    BANK_STATEMENT("BANK_STATEMENT", "Address Proof", "Bank statement for address proof");

    private final String documentCode;
    private final String typeCode;
    private final String description;

    DocumentType(String documentCode, String typeCode, String description) {
        this.documentCode = documentCode;
        this.typeCode = typeCode;
        this.description = description;
    }

    public String getDocumentCode() {
        return documentCode;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public String getDescription() {
        return description;
    }

    public static DocumentType fromDocumentCode(String documentCode) {
        for (DocumentType type : values()) {
            if (type.documentCode.equals(documentCode)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown document code: " + documentCode);
    }
}
