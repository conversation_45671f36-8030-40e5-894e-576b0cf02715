package com.financial.cif.repository;

import com.financial.cif.entity.AuditLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository interface for AuditLog entity operations.
 * Provides data access methods for audit trail management.
 */
@Repository
public interface AuditLogRepository extends JpaRepository<AuditLog, Long> {

    /**
     * Find audit logs by reference number.
     * @param referenceNumber the reference number
     * @return List of audit logs for the reference number
     */
    List<AuditLog> findByReferenceNumber(String referenceNumber);

    /**
     * Find audit logs by operation type.
     * @param operation the operation type
     * @return List of audit logs for the operation
     */
    List<AuditLog> findByOperation(String operation);

    /**
     * Find audit logs by status.
     * @param status the operation status
     * @return List of audit logs with the given status
     */
    List<AuditLog> findByStatus(String status);

    /**
     * Find audit logs created between given dates.
     * @param startDate start date
     * @param endDate end date
     * @return List of audit logs created in the date range
     */
    List<AuditLog> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find audit logs by reference number and operation.
     * @param referenceNumber the reference number
     * @param operation the operation type
     * @return List of audit logs matching criteria
     */
    List<AuditLog> findByReferenceNumberAndOperation(String referenceNumber, String operation);

    /**
     * Find audit logs by entity type and entity ID.
     * @param entityType the entity type
     * @param entityId the entity ID
     * @return List of audit logs for the entity
     */
    List<AuditLog> findByEntityTypeAndEntityId(String entityType, String entityId);

    /**
     * Find failed operations for analysis.
     * @param startDate start date for analysis
     * @param endDate end date for analysis
     * @return List of failed audit logs
     */
    @Query("SELECT a FROM AuditLog a WHERE a.status = 'FAILURE' AND a.createdAt BETWEEN :startDate AND :endDate")
    List<AuditLog> findFailedOperations(@Param("startDate") LocalDateTime startDate, 
                                       @Param("endDate") LocalDateTime endDate);

    /**
     * Find audit logs for data retention cleanup.
     * @param retentionDate date before which data should be cleaned up
     * @return List of audit logs eligible for cleanup
     */
    @Query("SELECT a FROM AuditLog a WHERE a.createdAt < :retentionDate")
    List<AuditLog> findAuditLogsForRetentionCleanup(@Param("retentionDate") LocalDateTime retentionDate);

    /**
     * Count operations by status in a date range.
     * @param status the operation status
     * @param startDate start date
     * @param endDate end date
     * @return count of operations with the given status
     */
    @Query("SELECT COUNT(a) FROM AuditLog a WHERE a.status = :status AND a.createdAt BETWEEN :startDate AND :endDate")
    long countByStatusAndDateRange(@Param("status") String status, 
                                  @Param("startDate") LocalDateTime startDate, 
                                  @Param("endDate") LocalDateTime endDate);

    /**
     * Find average processing time by operation.
     * @param operation the operation type
     * @param startDate start date
     * @param endDate end date
     * @return average processing time in milliseconds
     */
    @Query("SELECT AVG(a.processingTimeMs) FROM AuditLog a WHERE a.operation = :operation AND a.createdAt BETWEEN :startDate AND :endDate")
    Double findAverageProcessingTimeByOperation(@Param("operation") String operation,
                                               @Param("startDate") LocalDateTime startDate,
                                               @Param("endDate") LocalDateTime endDate);
}
