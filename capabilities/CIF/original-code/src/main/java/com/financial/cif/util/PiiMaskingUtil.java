package com.financial.cif.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Utility class for masking Personally Identifiable Information (PII) in logs and audit trails.
 * Implements configurable masking strategies to protect sensitive customer data.
 */
@Component
public class PiiMaskingUtil {

    @Value("${app.security.pii-masking.enabled:true}")
    private boolean maskingEnabled;

    @Value("${app.security.pii-masking.mask-char:*}")
    private String maskChar;

    @Value("${app.security.pii-masking.visible-chars:4}")
    private int visibleChars;

    /**
     * Mask PAN number showing only last 4 characters.
     * @param pan the PAN number to mask
     * @return masked PAN number
     */
    public String maskPan(String pan) {
        if (!maskingEnabled || pan == null || pan.trim().isEmpty()) {
            return pan;
        }
        
        if (pan.length() <= visibleChars) {
            return maskChar.repeat(pan.length());
        }
        
        String visiblePart = pan.substring(pan.length() - visibleChars);
        String maskedPart = maskChar.repeat(pan.length() - visibleChars);
        return maskedPart + visiblePart;
    }

    /**
     * Mask email address showing only domain and first character.
     * @param email the email address to mask
     * @return masked email address
     */
    public String maskEmail(String email) {
        if (!maskingEnabled || email == null || email.trim().isEmpty()) {
            return email;
        }
        
        int atIndex = email.indexOf('@');
        if (atIndex <= 0) {
            return maskChar.repeat(email.length());
        }
        
        String localPart = email.substring(0, atIndex);
        String domainPart = email.substring(atIndex);
        
        if (localPart.length() <= 1) {
            return maskChar + domainPart;
        }
        
        String maskedLocal = localPart.charAt(0) + maskChar.repeat(localPart.length() - 1);
        return maskedLocal + domainPart;
    }

    /**
     * Mask phone number showing only last 4 digits.
     * @param phoneNumber the phone number to mask
     * @return masked phone number
     */
    public String maskPhoneNumber(String phoneNumber) {
        if (!maskingEnabled || phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return phoneNumber;
        }
        
        // Remove any non-digit characters for processing
        String digitsOnly = phoneNumber.replaceAll("\\D", "");
        
        if (digitsOnly.length() <= visibleChars) {
            return maskChar.repeat(phoneNumber.length());
        }
        
        String visiblePart = digitsOnly.substring(digitsOnly.length() - visibleChars);
        String maskedPart = maskChar.repeat(digitsOnly.length() - visibleChars);
        
        // Preserve original formatting by replacing digits
        String result = phoneNumber;
        String replacement = maskedPart + visiblePart;
        int replacementIndex = 0;
        
        StringBuilder maskedPhone = new StringBuilder();
        for (char c : phoneNumber.toCharArray()) {
            if (Character.isDigit(c) && replacementIndex < replacement.length()) {
                maskedPhone.append(replacement.charAt(replacementIndex++));
            } else {
                maskedPhone.append(c);
            }
        }
        
        return maskedPhone.toString();
    }

    /**
     * Mask name showing only first and last character.
     * @param name the name to mask
     * @return masked name
     */
    public String maskName(String name) {
        if (!maskingEnabled || name == null || name.trim().isEmpty()) {
            return name;
        }
        
        if (name.length() <= 2) {
            return maskChar.repeat(name.length());
        }
        
        return name.charAt(0) + maskChar.repeat(name.length() - 2) + name.charAt(name.length() - 1);
    }

    /**
     * Mask address showing only first few characters.
     * @param address the address to mask
     * @return masked address
     */
    public String maskAddress(String address) {
        if (!maskingEnabled || address == null || address.trim().isEmpty()) {
            return address;
        }
        
        if (address.length() <= visibleChars) {
            return maskChar.repeat(address.length());
        }
        
        String visiblePart = address.substring(0, visibleChars);
        String maskedPart = maskChar.repeat(address.length() - visibleChars);
        return visiblePart + maskedPart;
    }

    /**
     * Mask any generic sensitive data.
     * @param data the data to mask
     * @param showFirst number of characters to show at the beginning
     * @param showLast number of characters to show at the end
     * @return masked data
     */
    public String maskGeneric(String data, int showFirst, int showLast) {
        if (!maskingEnabled || data == null || data.trim().isEmpty()) {
            return data;
        }
        
        if (data.length() <= (showFirst + showLast)) {
            return maskChar.repeat(data.length());
        }
        
        String firstPart = data.substring(0, showFirst);
        String lastPart = data.substring(data.length() - showLast);
        String maskedPart = maskChar.repeat(data.length() - showFirst - showLast);
        
        return firstPart + maskedPart + lastPart;
    }

    /**
     * Completely mask sensitive data.
     * @param data the data to mask
     * @return completely masked data
     */
    public String maskCompletely(String data) {
        if (!maskingEnabled || data == null || data.trim().isEmpty()) {
            return data;
        }
        
        return maskChar.repeat(data.length());
    }

    /**
     * Check if masking is enabled.
     * @return true if masking is enabled
     */
    public boolean isMaskingEnabled() {
        return maskingEnabled;
    }
}
