package com.financial.cif.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * DTO representing address details in the CIF creation request.
 */
public class AddressDetailsRequest {

    @JsonProperty("addressLine1")
    @NotBlank(message = "Address line 1 is mandatory")
    @Size(max = 255, message = "Address line 1 must not exceed 255 characters")
    private String addressLine1;

    @JsonProperty("addressLine2")
    @Size(max = 255, message = "Address line 2 must not exceed 255 characters")
    private String addressLine2;

    @JsonProperty("addressCategory")
    @NotBlank(message = "Address category is mandatory")
    @Size(max = 20, message = "Address category must not exceed 20 characters")
    private String addressCategory;

    @JsonProperty("city")
    @NotBlank(message = "City is mandatory")
    @Size(max = 100, message = "City must not exceed 100 characters")
    private String city;

    @JsonProperty("state")
    @NotBlank(message = "State is mandatory")
    @Size(max = 100, message = "State must not exceed 100 characters")
    private String state;

    @JsonProperty("postalCode")
    @NotBlank(message = "Postal code is mandatory")
    @Size(max = 20, message = "Postal code must not exceed 20 characters")
    private String postalCode;

    @JsonProperty("country")
    @NotBlank(message = "Country is mandatory")
    @Size(max = 10, message = "Country must not exceed 10 characters")
    private String country = "IN";

    // Default constructor
    public AddressDetailsRequest() {
    }

    // Constructor with required fields
    public AddressDetailsRequest(String addressLine1, String addressCategory, String city, 
                                String state, String postalCode) {
        this.addressLine1 = addressLine1;
        this.addressCategory = addressCategory;
        this.city = city;
        this.state = state;
        this.postalCode = postalCode;
    }

    // Getters and Setters
    public String getAddressLine1() {
        return addressLine1;
    }

    public void setAddressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    public String getAddressLine2() {
        return addressLine2;
    }

    public void setAddressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    public String getAddressCategory() {
        return addressCategory;
    }

    public void setAddressCategory(String addressCategory) {
        this.addressCategory = addressCategory;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    @Override
    public String toString() {
        return "AddressDetailsRequest{" +
                "addressLine1='" + addressLine1 + '\'' +
                ", addressLine2='" + addressLine2 + '\'' +
                ", addressCategory='" + addressCategory + '\'' +
                ", city='" + city + '\'' +
                ", state='" + state + '\'' +
                ", postalCode='" + postalCode + '\'' +
                ", country='" + country + '\'' +
                '}';
    }
}
