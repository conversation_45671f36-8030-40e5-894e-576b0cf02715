package com.financial.cif.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * Main request DTO for CIF creation API.
 * Wraps the customer creation request body as per API specification.
 */
public class CreateCustomerIdRequest {

    @JsonProperty("body")
    @NotNull(message = "Request body cannot be null")
    @Valid
    private RequestBody body;

    // Default constructor
    public CreateCustomerIdRequest() {
    }

    // Constructor
    public CreateCustomerIdRequest(RequestBody body) {
        this.body = body;
    }

    // Getters and Setters
    public RequestBody getBody() {
        return body;
    }

    public void setBody(RequestBody body) {
        this.body = body;
    }

    @Override
    public String toString() {
        return "CreateCustomerIdRequest{" +
                "body=" + body +
                '}';
    }

    /**
     * Inner class representing the request body structure.
     */
    public static class RequestBody {

        @JsonProperty("createCustomerIdRequest")
        @NotNull(message = "Create customer ID request cannot be null")
        @Valid
        private CustomerIdRequestBody createCustomerIdRequest;

        // Default constructor
        public RequestBody() {
        }

        // Constructor
        public RequestBody(CustomerIdRequestBody createCustomerIdRequest) {
            this.createCustomerIdRequest = createCustomerIdRequest;
        }

        // Getters and Setters
        public CustomerIdRequestBody getCreateCustomerIdRequest() {
            return createCustomerIdRequest;
        }

        public void setCreateCustomerIdRequest(CustomerIdRequestBody createCustomerIdRequest) {
            this.createCustomerIdRequest = createCustomerIdRequest;
        }

        @Override
        public String toString() {
            return "RequestBody{" +
                    "createCustomerIdRequest=" + createCustomerIdRequest +
                    '}';
        }
    }

    /**
     * Inner class representing the customer ID request body.
     */
    public static class CustomerIdRequestBody {

        @JsonProperty("requestBody")
        @NotNull(message = "Request body cannot be null")
        @Valid
        private CustomerDetailsRequest requestBody;

        // Default constructor
        public CustomerIdRequestBody() {
        }

        // Constructor
        public CustomerIdRequestBody(CustomerDetailsRequest requestBody) {
            this.requestBody = requestBody;
        }

        // Getters and Setters
        public CustomerDetailsRequest getRequestBody() {
            return requestBody;
        }

        public void setRequestBody(CustomerDetailsRequest requestBody) {
            this.requestBody = requestBody;
        }

        @Override
        public String toString() {
            return "CustomerIdRequestBody{" +
                    "requestBody=" + requestBody +
                    '}';
        }
    }
}
