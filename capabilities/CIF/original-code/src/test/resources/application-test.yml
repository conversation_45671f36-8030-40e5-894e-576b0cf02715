spring:
  application:
    name: cif-service-test
  
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  flyway:
    enabled: false  # Disable Flyway for tests, use JPA DDL
  
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

server:
  port: 0  # Random port for tests

# External API Configuration (Mock)
external:
  cif:
    api:
      base-url: http://localhost:8081/api/cif/internal
      create-endpoint: /cif/create
      timeout: 5000
      retry:
        max-attempts: 3
        delay: 100
        multiplier: 1.5

# Logging Configuration
logging:
  level:
    com.financial.cif: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"

# Application specific configuration
app:
  audit:
    enabled: true
    retention-days: 30  # Shorter retention for tests
  security:
    pii-masking:
      enabled: true
      mask-char: "*"
      visible-chars: 2
