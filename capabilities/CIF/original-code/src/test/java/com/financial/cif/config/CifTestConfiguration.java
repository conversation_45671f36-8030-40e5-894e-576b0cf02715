package com.financial.cif.config;

import com.financial.cif.dto.request.CreateCustomerIdRequest;
import com.financial.cif.dto.response.CifCreationResponse;
import com.financial.cif.service.ExternalCifApiClient;
import com.financial.cif.service.AuditService;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * Test configuration that provides simple stub implementations for testing.
 * Uses standard Spring Boot testing approaches as per project guidelines.
 * No Mockito - only standard Spring Boot testing tools and JUnit 5.
 */
@TestConfiguration
@Profile("test")
public class CifTestConfiguration {

    /**
     * Test stub implementation of ExternalCifApiClient.
     * Extends the real class but provides minimal dependencies to avoid issues.
     */
    @Bean
    @Primary
    public ExternalCifApiClient testExternalCifApiClient() {
        return new TestExternalCifApiClient();
    }

    /**
     * Test stub implementation that extends the real ExternalCifApiClient.
     * Provides predictable responses for testing without complex dependencies.
     */
    public static class TestExternalCifApiClient extends ExternalCifApiClient {

        public TestExternalCifApiClient() {
            // Provide minimal working dependencies for testing
            super(WebClient.builder(), createTestAuditService());
        }

        @Override
        public CifCreationResponse createCustomerId(CreateCustomerIdRequest request, String channelId, String referenceNumber) {
            // Return a successful test response
            CifCreationResponse response = new CifCreationResponse();
            response.setChannelId(channelId);
            response.setCustomerId("CUST123456789");
            response.setStatus("SUCCESS");
            response.setDescription("Customer created successfully");
            response.setEntity("Customer");
            return response;
        }

        @Override
        public boolean isApiAvailable() {
            return true;
        }

        /**
         * Create a minimal AuditService for testing that doesn't perform actual operations.
         */
        private static AuditService createTestAuditService() {
            // Return a no-op audit service for testing
            return new AuditService(null, null, null) {
                @Override
                public void logOperationStart(String referenceNumber, String operation, String entityType, Object requestData) {
                    // No-op for testing
                }

                @Override
                public void logOperationSuccess(String referenceNumber, String operation, String entityType,
                                               String entityId, Object responseData, Long processingTimeMs) {
                    // No-op for testing
                }

                @Override
                public void logOperationFailure(String referenceNumber, String operation, String entityType,
                                               String errorCode, String errorMessage, Long processingTimeMs) {
                    // No-op for testing
                }
            };
        }
    }
}
