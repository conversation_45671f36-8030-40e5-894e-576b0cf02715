package com.financial.cif.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for PiiMaskingUtil.
 */
class PiiMaskingUtilTest {

    private PiiMaskingUtil piiMaskingUtil;

    @BeforeEach
    void setUp() {
        piiMaskingUtil = new PiiMaskingUtil();
        ReflectionTestUtils.setField(piiMaskingUtil, "maskingEnabled", true);
        ReflectionTestUtils.setField(piiMaskingUtil, "maskChar", "*");
        ReflectionTestUtils.setField(piiMaskingUtil, "visibleChars", 4);
    }

    @Test
    void testMaskPan_ValidPan() {
        String pan = "**********";
        String masked = piiMaskingUtil.maskPan(pan);
        assertEquals("******234F", masked);
    }

    @Test
    void testMaskPan_ShortPan() {
        String pan = "ABC";
        String masked = piiMaskingUtil.maskPan(pan);
        assertEquals("***", masked);
    }

    @Test
    void testMaskPan_NullPan() {
        String masked = piiMaskingUtil.maskPan(null);
        assertNull(masked);
    }

    @Test
    void testMaskPan_EmptyPan() {
        String masked = piiMaskingUtil.maskPan("");
        assertEquals("", masked);
    }

    @Test
    void testMaskEmail_ValidEmail() {
        String email = "<EMAIL>";
        String masked = piiMaskingUtil.maskEmail(email);
        assertEquals("j*******@example.com", masked);
    }

    @Test
    void testMaskEmail_ShortLocalPart() {
        String email = "<EMAIL>";
        String masked = piiMaskingUtil.maskEmail(email);
        assertEquals("*@example.com", masked);
    }

    @Test
    void testMaskEmail_InvalidEmail() {
        String email = "invalid-email";
        String masked = piiMaskingUtil.maskEmail(email);
        assertEquals("*************", masked);
    }

    @Test
    void testMaskPhoneNumber_ValidPhone() {
        String phone = "9876543210";
        String masked = piiMaskingUtil.maskPhoneNumber(phone);
        assertEquals("******3210", masked);
    }

    @Test
    void testMaskPhoneNumber_FormattedPhone() {
        String phone = "+91-9876543210";
        String masked = piiMaskingUtil.maskPhoneNumber(phone);
        assertEquals("+**-******3210", masked);
    }

    @Test
    void testMaskPhoneNumber_ShortPhone() {
        String phone = "123";
        String masked = piiMaskingUtil.maskPhoneNumber(phone);
        assertEquals("***", masked);
    }

    @Test
    void testMaskName_ValidName() {
        String name = "John";
        String masked = piiMaskingUtil.maskName(name);
        assertEquals("J**n", masked);
    }

    @Test
    void testMaskName_ShortName() {
        String name = "Jo";
        String masked = piiMaskingUtil.maskName(name);
        assertEquals("**", masked);
    }

    @Test
    void testMaskName_SingleChar() {
        String name = "J";
        String masked = piiMaskingUtil.maskName(name);
        assertEquals("*", masked);
    }

    @Test
    void testMaskAddress_ValidAddress() {
        String address = "123 Main Street";
        String masked = piiMaskingUtil.maskAddress(address);
        assertEquals("123 ***********", masked);
    }

    @Test
    void testMaskGeneric_CustomMasking() {
        String data = "ABCDEFGHIJ";
        String masked = piiMaskingUtil.maskGeneric(data, 2, 2);
        assertEquals("AB******IJ", masked);
    }

    @Test
    void testMaskGeneric_ShortData() {
        String data = "ABC";
        String masked = piiMaskingUtil.maskGeneric(data, 2, 2);
        assertEquals("***", masked);
    }

    @Test
    void testMaskCompletely() {
        String data = "SensitiveData";
        String masked = piiMaskingUtil.maskCompletely(data);
        assertEquals("*************", masked);
    }

    @Test
    void testMaskingDisabled() {
        ReflectionTestUtils.setField(piiMaskingUtil, "maskingEnabled", false);
        
        String pan = "**********";
        String masked = piiMaskingUtil.maskPan(pan);
        assertEquals(pan, masked);
    }

    @Test
    void testIsMaskingEnabled() {
        assertTrue(piiMaskingUtil.isMaskingEnabled());
        
        ReflectionTestUtils.setField(piiMaskingUtil, "maskingEnabled", false);
        assertFalse(piiMaskingUtil.isMaskingEnabled());
    }
}
