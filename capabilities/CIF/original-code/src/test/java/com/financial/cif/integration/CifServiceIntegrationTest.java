package com.financial.cif.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.financial.cif.dto.request.*;
import com.financial.cif.dto.response.CustomerCreationResponse;
import com.financial.cif.config.CifTestConfiguration;
import com.financial.cif.dto.response.CifCreationResponse;
import com.financial.cif.entity.Customer;
import com.financial.cif.enums.CustomerStatus;
import com.financial.cif.repository.CustomerRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for CIF Service.
 * Tests the complete workflow from controller to database.
 */
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
@Import(CifTestConfiguration.class)
class CifServiceIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CustomerRepository customerRepository;

    @Test
    void testCreateCustomerId_ValidRequest_Success() throws Exception {
        // Prepare test request
        CreateCustomerIdRequest request = createValidCifRequest();
        String requestJson = objectMapper.writeValueAsString(request);

        // Execute request
        String responseJson = mockMvc.perform(post("/cif/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.referenceNumber").exists())
                .andExpect(jsonPath("$.status").value("SUCCESS"))
                .andExpect(jsonPath("$.customerId").exists())
                .andReturn()
                .getResponse()
                .getContentAsString();

        // Verify response
        CustomerCreationResponse response = objectMapper.readValue(responseJson, CustomerCreationResponse.class);
        assertNotNull(response.getReferenceNumber());
        assertEquals("SUCCESS", response.getStatus());
        assertNotNull(response.getCustomerId());
        assertNotNull(response.getProcessingTimeMs());

        // Verify database persistence
        Optional<Customer> savedCustomer = customerRepository.findByReferenceNumber(response.getReferenceNumber());
        assertTrue(savedCustomer.isPresent());
        assertEquals(CustomerStatus.SUCCESS, savedCustomer.get().getStatus());
        assertEquals(response.getCustomerId(), savedCustomer.get().getCustomerId());
        assertEquals("**********", savedCustomer.get().getPan());
    }

    @Test
    void testCreateCustomerId_InvalidRequest_ValidationError() throws Exception {
        // Prepare invalid request (missing required fields)
        CreateCustomerIdRequest request = new CreateCustomerIdRequest();
        String requestJson = objectMapper.writeValueAsString(request);

        // Execute request
        mockMvc.perform(post("/cif/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status").value("VALIDATION_ERROR"));
    }

    @Test
    void testCreateCustomerId_DuplicatePan_ValidationError() throws Exception {
        // Create first customer
        CreateCustomerIdRequest request1 = createValidCifRequest();
        String requestJson1 = objectMapper.writeValueAsString(request1);

        mockMvc.perform(post("/cif/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson1))
                .andExpect(status().isOk());

        // Try to create second customer with same PAN
        CreateCustomerIdRequest request2 = createValidCifRequest();
        String requestJson2 = objectMapper.writeValueAsString(request2);

        mockMvc.perform(post("/cif/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson2))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status").value("VALIDATION_ERROR"));
    }

    @Test
    void testGetCustomerByReferenceNumber_ExistingCustomer_Success() throws Exception {
        // Create customer first
        CreateCustomerIdRequest request = createValidCifRequest();
        String requestJson = objectMapper.writeValueAsString(request);

        String responseJson = mockMvc.perform(post("/cif/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();

        CustomerCreationResponse createResponse = objectMapper.readValue(responseJson, CustomerCreationResponse.class);

        // Get customer by reference number
        mockMvc.perform(get("/cif/customer/" + createResponse.getReferenceNumber()))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.referenceNumber").value(createResponse.getReferenceNumber()))
                .andExpect(jsonPath("$.customerId").value(createResponse.getCustomerId()))
                .andExpect(jsonPath("$.pan").value("**********"))
                .andExpect(jsonPath("$.firstName").value("John"))
                .andExpect(jsonPath("$.lastName").value("Doe"));
    }

    @Test
    void testGetCustomerByReferenceNumber_NonExistingCustomer_NotFound() throws Exception {
        mockMvc.perform(get("/cif/customer/NONEXISTENT123"))
                .andExpect(status().isNotFound());
    }

    @Test
    void testGetCustomerByCustomerId_ExistingCustomer_Success() throws Exception {
        // Create customer first
        CreateCustomerIdRequest request = createValidCifRequest();
        String requestJson = objectMapper.writeValueAsString(request);

        String responseJson = mockMvc.perform(post("/cif/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();

        CustomerCreationResponse createResponse = objectMapper.readValue(responseJson, CustomerCreationResponse.class);

        // Get customer by customer ID
        mockMvc.perform(get("/cif/customer/id/" + createResponse.getCustomerId()))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.customerId").value(createResponse.getCustomerId()))
                .andExpect(jsonPath("$.referenceNumber").value(createResponse.getReferenceNumber()));
    }

    @Test
    void testHealthCheck_Success() throws Exception {
        mockMvc.perform(get("/cif/health"))
                .andExpect(status().isOk())
                .andExpect(content().string("CIF Service is healthy"));
    }

    /**
     * Create a valid CIF request for testing.
     */
    private CreateCustomerIdRequest createValidCifRequest() {
        // Create customer details
        CustomerDetailsRequest customerDetails = new CustomerDetailsRequest();
        customerDetails.setSalutation("Mr");
        customerDetails.setFirstName("John");
        customerDetails.setLastName("Doe");
        customerDetails.setDateOfBirth(LocalDate.of(1990, 1, 1));
        customerDetails.setLanguage("ENG");
        customerDetails.setMaritalStatus("Y");
        customerDetails.setNationality("IN");
        customerDetails.setIsMinor("N");
        customerDetails.setIsCustomerNRE("N");
        customerDetails.setDefaultAddressType("Mailing");
        customerDetails.setGender("Male");
        customerDetails.setNativeLanguageCode("ENG01");
        customerDetails.setOccupation("A102");
        customerDetails.setPan("**********");
        customerDetails.setBranchId("887");
        customerDetails.setIsStaff("N");
        customerDetails.setTaxDeductionTable("TDS11");
        customerDetails.setMaidenNameOfMother("Jane Smith");
        customerDetails.setCustHealthCode("AB01");

        // Create address details
        AddressDetailsRequest address = new AddressDetailsRequest();
        address.setAddressLine1("123 Main Street");
        address.setAddressLine2("Apt 4B");
        address.setAddressCategory("Mailing");
        address.setCity("Mumbai");
        address.setState("Maharashtra");
        address.setPostalCode("400001");
        address.setCountry("IN");
        customerDetails.setAddressDetails(List.of(address));

        // Create contact details
        ContactDetailsRequest contact = new ContactDetailsRequest();
        contact.setContactType("Phone");
        contact.setContactSubType("Cell");
        contact.setCountryCode("+91");
        contact.setPhoneNumber("**********");
        contact.setIsPreferredContact("Yes");
        contact.setEmail("<EMAIL>");
        customerDetails.setContactDetails(List.of(contact));

        // Create document details
        EntityDocDetailsRequest document = new EntityDocDetailsRequest();
        document.setCountryOfIssue("IN");
        document.setDocumentCode("AADHAR_IDN_OF");
        document.setIssueDate(LocalDateTime.of(2000, 12, 31, 0, 0, 0));
        document.setExpiryDate(LocalDateTime.of(2099, 12, 31, 0, 0, 0));
        document.setTypeCode("Identification");
        document.setTypeDescription("Aadhar card for identification");
        document.setPlaceOfIssue("HRD");
        document.setIsMandatory("Y");
        document.setReceivedDate(LocalDateTime.now());
        customerDetails.setEntityDocDetails(List.of(document));

        // Create psychographic data
        PsychographicDataRequest psycho = new PsychographicDataRequest();
        psycho.setCurrencyCode("INR");
        customerDetails.setPsychographicData(List.of(psycho));

        // Build the complete request structure
        CreateCustomerIdRequest.CustomerIdRequestBody customerIdRequestBody =
                new CreateCustomerIdRequest.CustomerIdRequestBody(customerDetails);

        CreateCustomerIdRequest.RequestBody requestBody =
                new CreateCustomerIdRequest.RequestBody(customerIdRequestBody);

        return new CreateCustomerIdRequest(requestBody);
    }
}
