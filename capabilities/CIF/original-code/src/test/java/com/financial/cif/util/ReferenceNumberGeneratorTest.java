package com.financial.cif.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ReferenceNumberGenerator.
 */
class ReferenceNumberGeneratorTest {

    private ReferenceNumberGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new ReferenceNumberGenerator();
        generator.resetSequence();
    }

    @Test
    void testGenerateReferenceNumber_Format() {
        String refNumber = generator.generateReferenceNumber();

        assertNotNull(refNumber);
        assertEquals(23, refNumber.length());
        assertTrue(refNumber.startsWith("CIF"));
        assertTrue(generator.isValidReferenceNumber(refNumber));
    }

    @Test
    void testGenerateReferenceNumber_Uniqueness() {
        String refNumber1 = generator.generateReferenceNumber();
        String refNumber2 = generator.generateReferenceNumber();

        assertNotEquals(refNumber1, refNumber2);
    }

    @Test
    void testGenerateReferenceNumber_CustomPrefix() {
        String refNumber = generator.generateReferenceNumber("TEST");

        assertNotNull(refNumber);
        assertEquals(24, refNumber.length());
        assertTrue(refNumber.startsWith("TEST"));
    }

    @Test
    void testGenerateChannelId() {
        String channelId = generator.generateChannelId();

        assertNotNull(channelId);
        assertEquals(23, channelId.length());
        assertTrue(channelId.startsWith("CHN"));
        assertTrue(generator.isValidReferenceNumber(channelId));
    }

    @Test
    void testExtractDateFromReference() {
        String refNumber = generator.generateReferenceNumber();
        String datePart = generator.extractDateFromReference(refNumber);

        assertNotNull(datePart);
        assertEquals(8, datePart.length());
        assertTrue(datePart.matches("\\d{8}"));
    }

    @Test
    void testExtractTimeFromReference() {
        String refNumber = generator.generateReferenceNumber();
        String timePart = generator.extractTimeFromReference(refNumber);

        assertNotNull(timePart);
        assertEquals(6, timePart.length());
        assertTrue(timePart.matches("\\d{6}"));
    }

    @Test
    void testExtractSequenceFromReference() {
        String refNumber = generator.generateReferenceNumber();
        String sequencePart = generator.extractSequenceFromReference(refNumber);

        assertNotNull(sequencePart);
        assertEquals(6, sequencePart.length());
        assertTrue(sequencePart.matches("\\d{6}"));
    }

    @Test
    void testIsValidReferenceNumber_Valid() {
        String refNumber = generator.generateReferenceNumber();
        assertTrue(generator.isValidReferenceNumber(refNumber));
    }

    @Test
    void testIsValidReferenceNumber_Invalid() {
        assertFalse(generator.isValidReferenceNumber(null));
        assertFalse(generator.isValidReferenceNumber(""));
        assertFalse(generator.isValidReferenceNumber("INVALID"));
        assertFalse(generator.isValidReferenceNumber("CIF2023123112345"));
        assertFalse(generator.isValidReferenceNumber("XYZ20231231123456001001"));
    }

    @Test
    void testExtractFromInvalidReference() {
        String invalidRef = "INVALID";

        assertNull(generator.extractDateFromReference(invalidRef));
        assertNull(generator.extractTimeFromReference(invalidRef));
        assertNull(generator.extractSequenceFromReference(invalidRef));
    }

    @Test
    void testSequenceIncrement() {
        long initialSequence = generator.getCurrentSequence();

        generator.generateReferenceNumber();
        long afterFirstGeneration = generator.getCurrentSequence();

        assertEquals(initialSequence + 1, afterFirstGeneration);
    }

    @Test
    void testResetSequence() {
        generator.generateReferenceNumber();
        generator.generateReferenceNumber();

        generator.resetSequence();
        assertEquals(1000, generator.getCurrentSequence());
    }
}
