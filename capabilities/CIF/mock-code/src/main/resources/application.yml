spring:
  application:
    name: mock-cif-api
  
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
    default-property-inclusion: non_null

server:
  port: ${SERVER_PORT:8204}
  servlet:
    context-path: /api/cif/internal

# Mock API Configuration
mock:
  cif:
    failure-probability: 0.2  # 20% chance of failure
    retry-failure-probability: 0.5  # 50% chance of failure on retry
    processing-delay-ms: 1000  # Simulate processing delay
    customer-id-prefix: "CIF"

# Logging Configuration
logging:
  level:
    com.financial.mock: INFO
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/mock-cif-api.log

# Management and Actuator
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# OpenAPI/Swagger Configuration
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    display-request-duration: true
    display-operation-id: true
    default-models-expand-depth: 2
    default-model-expand-depth: 2
    doc-expansion: none
    tags-sorter: alpha
    operations-sorter: alpha
  show-actuator: true
