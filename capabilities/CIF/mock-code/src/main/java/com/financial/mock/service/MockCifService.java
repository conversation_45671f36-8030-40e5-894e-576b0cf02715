package com.financial.mock.service;

import com.financial.mock.dto.MockCifRequest;
import com.financial.mock.dto.MockCifResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Mock CIF service that simulates the behavior of the actual CIF creation service.
 * Provides configurable success/failure scenarios and realistic processing delays.
 */
@Service
public class MockCifService {

    private static final Logger logger = LoggerFactory.getLogger(MockCifService.class);

    private final Random random = new Random();
    private final AtomicLong customerIdSequence = new AtomicLong(100000);

    @Value("${mock.cif.failure-probability:0.2}")
    private double failureProbability;

    @Value("${mock.cif.retry-failure-probability:0.5}")
    private double retryFailureProbability;

    @Value("${mock.cif.processing-delay-ms:1000}")
    private int processingDelayMs;

    @Value("${mock.cif.customer-id-prefix:CIF}")
    private String customerIdPrefix;

    /**
     * Create customer ID with simulated processing.
     * 
     * @param request the CIF creation request
     * @param channelId channel ID for tracking
     * @param retryCount current retry count
     * @return mock CIF creation response
     */
    public MockCifResponse createCustomerId(MockCifRequest request, String channelId, int retryCount) {
        logger.info("Processing mock CIF creation - ChannelId: {}, RetryCount: {}", channelId, retryCount);

        // Simulate processing delay
        simulateProcessingDelay();

        // Determine if this should be a failure based on configuration
        boolean shouldFail = shouldSimulateFailure(retryCount);

        if (shouldFail) {
            return handleFailureScenario(channelId, retryCount);
        } else {
            return handleSuccessScenario(request, channelId);
        }
    }

    /**
     * Handle success scenario.
     */
    private MockCifResponse handleSuccessScenario(MockCifRequest request, String channelId) {
        String customerId = generateCustomerId(request);
        
        logger.info("Mock CIF creation successful - ChannelId: {}, CustomerId: {}", channelId, customerId);
        
        return MockCifResponse.success(channelId, customerId);
    }

    /**
     * Handle failure scenario.
     */
    private MockCifResponse handleFailureScenario(String channelId, int retryCount) {
        // Simulate different types of failures
        double failureType = random.nextDouble();
        
        if (failureType < 0.1) {
            // 10% chance of null status (service unavailable)
            logger.warn("Mock CIF creation failed with null status - ChannelId: {}", channelId);
            return MockCifResponse.nullStatus(channelId);
        } else if (retryCount > 0 && retryCount < 3) {
            // Retriable failure for retry scenarios
            logger.warn("Mock CIF creation failed (retriable) - ChannelId: {}, RetryCount: {}", channelId, retryCount);
            return MockCifResponse.retryableFailure(channelId);
        } else {
            // Regular failure
            logger.warn("Mock CIF creation failed - ChannelId: {}", channelId);
            return MockCifResponse.failure(channelId, "Customer creation failed due to business validation");
        }
    }

    /**
     * Determine if failure should be simulated based on configuration and retry count.
     */
    private boolean shouldSimulateFailure(int retryCount) {
        double currentFailureProbability = (retryCount > 0) ? retryFailureProbability : failureProbability;
        return random.nextDouble() < currentFailureProbability;
    }

    /**
     * Generate a unique customer ID.
     */
    private String generateCustomerId(MockCifRequest request) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        long sequence = customerIdSequence.getAndIncrement();
        
        // Use PAN for some uniqueness
        String panSuffix = "";
        try {
            String pan = request.getBody().getCreateCustomerIdRequest().getRequestBody().getPan();
            if (pan != null && pan.length() >= 4) {
                panSuffix = pan.substring(pan.length() - 4);
            }
        } catch (Exception e) {
            // Fallback if PAN is not available
            panSuffix = String.format("%04d", random.nextInt(10000));
        }
        
        return String.format("%s%s%s%06d", customerIdPrefix, timestamp, panSuffix, sequence);
    }

    /**
     * Simulate processing delay.
     */
    private void simulateProcessingDelay() {
        if (processingDelayMs > 0) {
            try {
                // Add some randomness to the delay (±20%)
                int variance = (int) (processingDelayMs * 0.2);
                int actualDelay = processingDelayMs + random.nextInt(variance * 2) - variance;
                Thread.sleep(Math.max(actualDelay, 0));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.warn("Processing delay interrupted");
            }
        }
    }

    // Configuration getters and setters
    public double getFailureProbability() {
        return failureProbability;
    }

    public void setFailureProbability(double failureProbability) {
        this.failureProbability = failureProbability;
    }

    public double getRetryFailureProbability() {
        return retryFailureProbability;
    }

    public void setRetryFailureProbability(double retryFailureProbability) {
        this.retryFailureProbability = retryFailureProbability;
    }

    public int getProcessingDelayMs() {
        return processingDelayMs;
    }

    public void setProcessingDelayMs(int processingDelayMs) {
        this.processingDelayMs = processingDelayMs;
    }
}
