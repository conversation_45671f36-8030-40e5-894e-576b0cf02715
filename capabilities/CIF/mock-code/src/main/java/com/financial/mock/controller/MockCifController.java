package com.financial.mock.controller;

import com.financial.mock.dto.MockCifRequest;
import com.financial.mock.dto.MockCifResponse;
import com.financial.mock.service.MockCifService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;

/**
 * Mock CIF API controller that simulates the external CIF creation service.
 * Provides configurable success/failure scenarios for testing.
 */
@RestController
@RequestMapping("/cif")
public class MockCifController {

    private static final Logger logger = LoggerFactory.getLogger(MockCifController.class);

    private final MockCifService mockCifService;

    @Autowired
    public MockCifController(MockCifService mockCifService) {
        this.mockCifService = mockCifService;
    }

    /**
     * Mock CIF creation endpoint.
     * 
     * @param request the CIF creation request
     * @param bindingResult validation results
     * @param channelId channel ID from header
     * @param referenceNumber reference number from header
     * @param retryCount retry count from header
     * @return mock CIF creation response
     */
    @PostMapping("/create")
    public ResponseEntity<MockCifResponse> createCustomerId(
            @Valid @RequestBody MockCifRequest request,
            BindingResult bindingResult,
            @RequestHeader(value = "channelId", required = false) String channelId,
            @RequestHeader(value = "X-Reference-Number", required = false) String referenceNumber,
            @RequestHeader(value = "retryCount", required = false, defaultValue = "0") int retryCount) {

        logger.info("Received mock CIF creation request - ChannelId: {}, ReferenceNumber: {}, RetryCount: {}", 
                   channelId, referenceNumber, retryCount);

        // Log request payload (masked)
        logger.info("Request payload received for processing");

        // Check for validation errors
        if (bindingResult.hasErrors()) {
            String errorMessage = bindingResult.getFieldErrors().stream()
                    .map(error -> error.getField() + ": " + error.getDefaultMessage())
                    .collect(Collectors.joining(", "));

            logger.warn("Validation errors in mock CIF request: {}", errorMessage);

            MockCifResponse errorResponse = MockCifResponse.failure(channelId, 
                    "Validation failed: " + errorMessage);

            return ResponseEntity.badRequest().body(errorResponse);
        }

        try {
            MockCifResponse response = mockCifService.createCustomerId(request, channelId, retryCount);

            // Log response payload (masked)
            logger.info("Mock CIF response generated - ChannelId: {}, Status: {}, CustomerId: {}", 
                       response.getChannelId(), response.getStatus(), response.getCustomerId());

            // Determine HTTP status based on response
            HttpStatus httpStatus = switch (response.getStatus()) {
                case "SUCCESS" -> HttpStatus.OK;
                case "FAILURE" -> HttpStatus.BAD_REQUEST;
                case null -> HttpStatus.SERVICE_UNAVAILABLE;
                default -> HttpStatus.INTERNAL_SERVER_ERROR;
            };

            return ResponseEntity.status(httpStatus).body(response);

        } catch (Exception e) {
            logger.error("Unexpected error in mock CIF creation", e);

            MockCifResponse errorResponse = MockCifResponse.failure(channelId, 
                    "Internal server error occurred");

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Health check endpoint.
     * 
     * @return health status
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("Mock CIF API is healthy");
    }

    /**
     * Configuration endpoint to adjust failure probability.
     * 
     * @param failureProbability new failure probability (0.0 to 1.0)
     * @return confirmation message
     */
    @PostMapping("/config/failure-probability")
    public ResponseEntity<String> setFailureProbability(@RequestParam double failureProbability) {
        if (failureProbability < 0.0 || failureProbability > 1.0) {
            return ResponseEntity.badRequest().body("Failure probability must be between 0.0 and 1.0");
        }

        mockCifService.setFailureProbability(failureProbability);
        logger.info("Failure probability updated to: {}", failureProbability);

        return ResponseEntity.ok("Failure probability set to: " + failureProbability);
    }

    /**
     * Configuration endpoint to adjust retry failure probability.
     * 
     * @param retryFailureProbability new retry failure probability (0.0 to 1.0)
     * @return confirmation message
     */
    @PostMapping("/config/retry-failure-probability")
    public ResponseEntity<String> setRetryFailureProbability(@RequestParam double retryFailureProbability) {
        if (retryFailureProbability < 0.0 || retryFailureProbability > 1.0) {
            return ResponseEntity.badRequest().body("Retry failure probability must be between 0.0 and 1.0");
        }

        mockCifService.setRetryFailureProbability(retryFailureProbability);
        logger.info("Retry failure probability updated to: {}", retryFailureProbability);

        return ResponseEntity.ok("Retry failure probability set to: " + retryFailureProbability);
    }

    /**
     * Get current configuration.
     * 
     * @return current configuration
     */
    @GetMapping("/config")
    public ResponseEntity<String> getConfiguration() {
        return ResponseEntity.ok(String.format(
                "Current Configuration:\n" +
                "Failure Probability: %.2f\n" +
                "Retry Failure Probability: %.2f\n" +
                "Processing Delay: %d ms",
                mockCifService.getFailureProbability(),
                mockCifService.getRetryFailureProbability(),
                mockCifService.getProcessingDelayMs()
        ));
    }
}
