package com.financial.mock.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Mock CIF response DTO that matches the expected response format.
 */
public class MockCifResponse {

    @JsonProperty("channelId")
    private String channelId;

    @JsonProperty("customerId")
    private String customerId;

    @JsonProperty("description")
    private String description;

    @JsonProperty("entity")
    private String entity;

    @JsonProperty("status")
    private String status;

    // Default constructor
    public MockCifResponse() {
    }

    // Constructor with all fields
    public MockCifResponse(String channelId, String customerId, String description, 
                          String entity, String status) {
        this.channelId = channelId;
        this.customerId = customerId;
        this.description = description;
        this.entity = entity;
        this.status = status;
    }

    // Static factory methods for common responses
    public static MockCifResponse success(String channelId, String customerId) {
        return new MockCifResponse(channelId, customerId, "Customer ID created successfully", 
                                 "Customer", "SUCCESS");
    }

    public static MockCifResponse failure(String channelId, String description) {
        return new MockCifResponse(channelId, null, description, "Customer", "FAILURE");
    }

    public static MockCifResponse nullStatus(String channelId) {
        return new MockCifResponse(channelId, null, "Service unavailable", "Customer", null);
    }

    public static MockCifResponse retryableFailure(String channelId) {
        return new MockCifResponse(channelId, null, "Temporary service unavailability. Please retry.", 
                                 "Customer", "FAILURE");
    }

    // Getters and Setters
    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getEntity() {
        return entity;
    }

    public void setEntity(String entity) {
        this.entity = entity;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "MockCifResponse{" +
                "channelId='" + channelId + '\'' +
                ", customerId='" + customerId + '\'' +
                ", description='" + description + '\'' +
                ", entity='" + entity + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
