package com.financial.mock.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Mock CIF request DTO that mirrors the actual CIF creation request structure.
 * Includes full validation as per business requirements.
 */
public class MockCifRequest {

    @JsonProperty("body")
    @NotNull(message = "Request body cannot be null")
    @Valid
    private RequestBody body;

    // Default constructor
    public MockCifRequest() {
    }

    // Getters and Setters
    public RequestBody getBody() {
        return body;
    }

    public void setBody(RequestBody body) {
        this.body = body;
    }

    public static class RequestBody {
        @JsonProperty("createCustomerIdRequest")
        @NotNull(message = "Create customer ID request cannot be null")
        @Valid
        private CustomerIdRequestBody createCustomerIdRequest;

        public CustomerIdRequestBody getCreateCustomerIdRequest() {
            return createCustomerIdRequest;
        }

        public void setCreateCustomerIdRequest(CustomerIdRequestBody createCustomerIdRequest) {
            this.createCustomerIdRequest = createCustomerIdRequest;
        }
    }

    public static class CustomerIdRequestBody {
        @JsonProperty("requestBody")
        @NotNull(message = "Request body cannot be null")
        @Valid
        private CustomerDetailsRequest requestBody;

        public CustomerDetailsRequest getRequestBody() {
            return requestBody;
        }

        public void setRequestBody(CustomerDetailsRequest requestBody) {
            this.requestBody = requestBody;
        }
    }

    public static class CustomerDetailsRequest {
        @JsonProperty("salutation")
        @NotBlank(message = "Salutation is mandatory")
        private String salutation = "Mr";

        @JsonProperty("firstName")
        @NotBlank(message = "First name is mandatory")
        @Size(max = 100, message = "First name must not exceed 100 characters")
        private String firstName;

        @JsonProperty("lastName")
        @NotBlank(message = "Last name is mandatory")
        @Size(max = 100, message = "Last name must not exceed 100 characters")
        private String lastName;

        @JsonProperty("dateOfBirth")
        @NotNull(message = "Date of birth is mandatory")
        @Past(message = "Date of birth must be in the past")
        private LocalDate dateOfBirth;

        @JsonProperty("language")
        @NotBlank(message = "Language is mandatory")
        private String language = "ENG";

        @JsonProperty("maritalStatus")
        @NotBlank(message = "Marital status is mandatory")
        @Pattern(regexp = "[YN]", message = "Marital status must be Y or N")
        private String maritalStatus = "Y";

        @JsonProperty("nationality")
        @NotBlank(message = "Nationality is mandatory")
        private String nationality = "IN";

        @JsonProperty("isMinor")
        @NotBlank(message = "Minor status is mandatory")
        @Pattern(regexp = "[YN]", message = "Minor status must be Y or N")
        private String isMinor = "N";

        @JsonProperty("isCustomerNRE")
        @NotBlank(message = "Customer NRE status is mandatory")
        @Pattern(regexp = "[YN]", message = "Customer NRE status must be Y or N")
        private String isCustomerNRE = "N";

        @JsonProperty("defaultAddressType")
        @NotBlank(message = "Default address type is mandatory")
        private String defaultAddressType = "Mailing";

        @JsonProperty("gender")
        @NotBlank(message = "Gender is mandatory")
        private String gender = "Male";

        @JsonProperty("nativeLanguageCode")
        @NotBlank(message = "Native language code is mandatory")
        private String nativeLanguageCode = "ENG01";

        @JsonProperty("occupation")
        @NotBlank(message = "Occupation is mandatory")
        private String occupation = "A102";

        @JsonProperty("pan")
        @NotBlank(message = "PAN is mandatory")
        @Pattern(regexp = "[A-Z]{5}[0-9]{4}[A-Z]{1}", message = "PAN must be in valid format")
        private String pan;

        @JsonProperty("branchid")
        @NotBlank(message = "Branch ID is mandatory")
        private String branchId = "887";

        @JsonProperty("isStaff")
        @NotBlank(message = "Staff status is mandatory")
        @Pattern(regexp = "[YN]", message = "Staff status must be Y or N")
        private String isStaff = "N";

        @JsonProperty("taxDeductionTable")
        @NotBlank(message = "Tax deduction table is mandatory")
        private String taxDeductionTable = "TDS11";

        @JsonProperty("maidenNameOfMother")
        @NotBlank(message = "Mother's maiden name is mandatory")
        @Size(max = 100, message = "Mother's maiden name must not exceed 100 characters")
        private String maidenNameOfMother;

        @JsonProperty("custHealthCode")
        @NotBlank(message = "Customer health code is mandatory")
        private String custHealthCode = "AB01";

        @JsonProperty("addressDetails")
        @NotNull(message = "Address details are mandatory")
        @Size(min = 1, max = 2, message = "Must provide 1-2 address details")
        @Valid
        private List<AddressDetailsRequest> addressDetails;

        @JsonProperty("contactDetails")
        @NotNull(message = "Contact details are mandatory")
        @Size(min = 1, message = "At least one contact detail is required")
        @Valid
        private List<ContactDetailsRequest> contactDetails;

        @JsonProperty("entityDocDetails")
        @NotNull(message = "Document details are mandatory")
        @Size(min = 1, message = "At least one document detail is required")
        @Valid
        private List<EntityDocDetailsRequest> entityDocDetails;

        @JsonProperty("psychographicData")
        @NotNull(message = "Psychographic data is mandatory")
        @Size(min = 1, message = "At least one psychographic data entry is required")
        @Valid
        private List<PsychographicDataRequest> psychographicData;

        // Getters and Setters
        public String getSalutation() { return salutation; }
        public void setSalutation(String salutation) { this.salutation = salutation; }
        public String getFirstName() { return firstName; }
        public void setFirstName(String firstName) { this.firstName = firstName; }
        public String getLastName() { return lastName; }
        public void setLastName(String lastName) { this.lastName = lastName; }
        public LocalDate getDateOfBirth() { return dateOfBirth; }
        public void setDateOfBirth(LocalDate dateOfBirth) { this.dateOfBirth = dateOfBirth; }
        public String getLanguage() { return language; }
        public void setLanguage(String language) { this.language = language; }
        public String getMaritalStatus() { return maritalStatus; }
        public void setMaritalStatus(String maritalStatus) { this.maritalStatus = maritalStatus; }
        public String getNationality() { return nationality; }
        public void setNationality(String nationality) { this.nationality = nationality; }
        public String getIsMinor() { return isMinor; }
        public void setIsMinor(String isMinor) { this.isMinor = isMinor; }
        public String getIsCustomerNRE() { return isCustomerNRE; }
        public void setIsCustomerNRE(String isCustomerNRE) { this.isCustomerNRE = isCustomerNRE; }
        public String getDefaultAddressType() { return defaultAddressType; }
        public void setDefaultAddressType(String defaultAddressType) { this.defaultAddressType = defaultAddressType; }
        public String getGender() { return gender; }
        public void setGender(String gender) { this.gender = gender; }
        public String getNativeLanguageCode() { return nativeLanguageCode; }
        public void setNativeLanguageCode(String nativeLanguageCode) { this.nativeLanguageCode = nativeLanguageCode; }
        public String getOccupation() { return occupation; }
        public void setOccupation(String occupation) { this.occupation = occupation; }
        public String getPan() { return pan; }
        public void setPan(String pan) { this.pan = pan; }
        public String getBranchId() { return branchId; }
        public void setBranchId(String branchId) { this.branchId = branchId; }
        public String getIsStaff() { return isStaff; }
        public void setIsStaff(String isStaff) { this.isStaff = isStaff; }
        public String getTaxDeductionTable() { return taxDeductionTable; }
        public void setTaxDeductionTable(String taxDeductionTable) { this.taxDeductionTable = taxDeductionTable; }
        public String getMaidenNameOfMother() { return maidenNameOfMother; }
        public void setMaidenNameOfMother(String maidenNameOfMother) { this.maidenNameOfMother = maidenNameOfMother; }
        public String getCustHealthCode() { return custHealthCode; }
        public void setCustHealthCode(String custHealthCode) { this.custHealthCode = custHealthCode; }
        public List<AddressDetailsRequest> getAddressDetails() { return addressDetails; }
        public void setAddressDetails(List<AddressDetailsRequest> addressDetails) { this.addressDetails = addressDetails; }
        public List<ContactDetailsRequest> getContactDetails() { return contactDetails; }
        public void setContactDetails(List<ContactDetailsRequest> contactDetails) { this.contactDetails = contactDetails; }
        public List<EntityDocDetailsRequest> getEntityDocDetails() { return entityDocDetails; }
        public void setEntityDocDetails(List<EntityDocDetailsRequest> entityDocDetails) { this.entityDocDetails = entityDocDetails; }
        public List<PsychographicDataRequest> getPsychographicData() { return psychographicData; }
        public void setPsychographicData(List<PsychographicDataRequest> psychographicData) { this.psychographicData = psychographicData; }
    }

    public static class AddressDetailsRequest {
        @JsonProperty("addressLine1")
        @NotBlank(message = "Address line 1 is mandatory")
        private String addressLine1;

        @JsonProperty("addressLine2")
        private String addressLine2;

        @JsonProperty("addressCategory")
        @NotBlank(message = "Address category is mandatory")
        private String addressCategory;

        @JsonProperty("city")
        @NotBlank(message = "City is mandatory")
        private String city;

        @JsonProperty("state")
        @NotBlank(message = "State is mandatory")
        private String state;

        @JsonProperty("postalCode")
        @NotBlank(message = "Postal code is mandatory")
        private String postalCode;

        @JsonProperty("country")
        @NotBlank(message = "Country is mandatory")
        private String country = "IN";

        // Getters and Setters
        public String getAddressLine1() { return addressLine1; }
        public void setAddressLine1(String addressLine1) { this.addressLine1 = addressLine1; }
        public String getAddressLine2() { return addressLine2; }
        public void setAddressLine2(String addressLine2) { this.addressLine2 = addressLine2; }
        public String getAddressCategory() { return addressCategory; }
        public void setAddressCategory(String addressCategory) { this.addressCategory = addressCategory; }
        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }
        public String getState() { return state; }
        public void setState(String state) { this.state = state; }
        public String getPostalCode() { return postalCode; }
        public void setPostalCode(String postalCode) { this.postalCode = postalCode; }
        public String getCountry() { return country; }
        public void setCountry(String country) { this.country = country; }
    }

    public static class ContactDetailsRequest {
        @JsonProperty("contactType")
        @NotBlank(message = "Contact type is mandatory")
        private String contactType = "Phone";

        @JsonProperty("contactSubType")
        @NotBlank(message = "Contact sub type is mandatory")
        private String contactSubType = "Cell";

        @JsonProperty("countryCode")
        @NotBlank(message = "Country code is mandatory")
        private String countryCode = "+91";

        @JsonProperty("phoneNumber")
        @NotBlank(message = "Phone number is mandatory")
        @Pattern(regexp = "\\d{10}", message = "Phone number must be 10 digits")
        private String phoneNumber;

        @JsonProperty("isPreferredContact")
        @NotBlank(message = "Preferred contact flag is mandatory")
        @Pattern(regexp = "Yes|No", message = "Preferred contact must be Yes or No")
        private String isPreferredContact = "Yes";

        @JsonProperty("email")
        @NotBlank(message = "Email is mandatory")
        @Email(message = "Email must be in valid format")
        private String email;

        // Getters and Setters
        public String getContactType() { return contactType; }
        public void setContactType(String contactType) { this.contactType = contactType; }
        public String getContactSubType() { return contactSubType; }
        public void setContactSubType(String contactSubType) { this.contactSubType = contactSubType; }
        public String getCountryCode() { return countryCode; }
        public void setCountryCode(String countryCode) { this.countryCode = countryCode; }
        public String getPhoneNumber() { return phoneNumber; }
        public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }
        public String getIsPreferredContact() { return isPreferredContact; }
        public void setIsPreferredContact(String isPreferredContact) { this.isPreferredContact = isPreferredContact; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
    }

    public static class EntityDocDetailsRequest {
        @JsonProperty("countryOfIssue")
        @NotBlank(message = "Country of issue is mandatory")
        private String countryOfIssue = "IN";

        @JsonProperty("documentCode")
        @NotBlank(message = "Document code is mandatory")
        private String documentCode;

        @JsonProperty("issueDate")
        @NotNull(message = "Issue date is mandatory")
        private LocalDateTime issueDate;

        @JsonProperty("expiryDate")
        @NotNull(message = "Expiry date is mandatory")
        private LocalDateTime expiryDate;

        @JsonProperty("typeCode")
        @NotBlank(message = "Type code is mandatory")
        private String typeCode;

        @JsonProperty("typeDescription")
        private String typeDescription;

        @JsonProperty("placeOfIssue")
        @NotBlank(message = "Place of issue is mandatory")
        private String placeOfIssue = "HRD";

        @JsonProperty("isMandatory")
        @NotBlank(message = "Mandatory flag is mandatory")
        @Pattern(regexp = "[YN]", message = "Mandatory flag must be Y or N")
        private String isMandatory = "Y";

        @JsonProperty("receivedDate")
        @NotNull(message = "Received date is mandatory")
        private LocalDateTime receivedDate;

        // Getters and Setters
        public String getCountryOfIssue() { return countryOfIssue; }
        public void setCountryOfIssue(String countryOfIssue) { this.countryOfIssue = countryOfIssue; }
        public String getDocumentCode() { return documentCode; }
        public void setDocumentCode(String documentCode) { this.documentCode = documentCode; }
        public LocalDateTime getIssueDate() { return issueDate; }
        public void setIssueDate(LocalDateTime issueDate) { this.issueDate = issueDate; }
        public LocalDateTime getExpiryDate() { return expiryDate; }
        public void setExpiryDate(LocalDateTime expiryDate) { this.expiryDate = expiryDate; }
        public String getTypeCode() { return typeCode; }
        public void setTypeCode(String typeCode) { this.typeCode = typeCode; }
        public String getTypeDescription() { return typeDescription; }
        public void setTypeDescription(String typeDescription) { this.typeDescription = typeDescription; }
        public String getPlaceOfIssue() { return placeOfIssue; }
        public void setPlaceOfIssue(String placeOfIssue) { this.placeOfIssue = placeOfIssue; }
        public String getIsMandatory() { return isMandatory; }
        public void setIsMandatory(String isMandatory) { this.isMandatory = isMandatory; }
        public LocalDateTime getReceivedDate() { return receivedDate; }
        public void setReceivedDate(LocalDateTime receivedDate) { this.receivedDate = receivedDate; }
    }

    public static class PsychographicDataRequest {
        @JsonProperty("currencyCode")
        @NotBlank(message = "Currency code is mandatory")
        private String currencyCode = "INR";

        // Getters and Setters
        public String getCurrencyCode() { return currencyCode; }
        public void setCurrencyCode(String currencyCode) { this.currencyCode = currencyCode; }
    }
}
