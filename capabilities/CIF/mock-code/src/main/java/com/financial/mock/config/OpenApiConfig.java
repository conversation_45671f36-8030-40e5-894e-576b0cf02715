package com.financial.mock.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;

/**
 * OpenAPI configuration for Mock CIF Service.
 * Customizes the OpenAPI documentation with proper server URLs, contact information,
 * and API metadata for the mock service.
 */
@Configuration
public class OpenApiConfig {

    @Value("${server.port:8204}")
    private String serverPort;

    @Value("${server.servlet.context-path:/api/cif/internal}")
    private String contextPath;

    @Bean
    public OpenAPI mockCifServiceOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Mock CIF (Customer Information File) API")
                        .description("""
                                Mock API for CIF Creation Service that simulates the behavior of an external 
                                CIF creation service for testing integration without relying on actual backend systems.
                                
                                ## Features
                                - Configurable success/failure simulation (default 20% failure rate)
                                - Retry logic simulation with header-based retry count
                                - Realistic processing delays
                                - Comprehensive request validation
                                - Audit logging with trace ID support
                                - Admin configuration endpoints
                                
                                ## Mock Behavior Rules
                                - 80% of valid requests return SUCCESS with generated customer ID
                                - 20% return FAILURE with "Service unavailable" message
                                - If retryCount header is present and < 3 → return FAILURE with "Temporary failure, please retry"
                                - All requests are logged with masked sensitive data
                                - Processing delay simulation (configurable, default 1000ms)
                                
                                ## Configuration
                                - Failure probability: Configurable (default 0.2)
                                - Retry failure probability: Configurable (default 0.5)
                                - Processing delay: Configurable (default 1000ms)
                                - Customer ID prefix: Configurable (default "CIF")
                                
                                ## Admin Features
                                - View current configuration
                                - Update failure rates and processing delays
                                - Reset configuration to defaults
                                """)
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Mock CIF API Team")
                                .email("<EMAIL>")
                                .url("https://financial.com/contact"))
                        .license(new License()
                                .name("Proprietary")
                                .url("https://financial.com/license")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + contextPath)
                                .description("Local development mock server"),
                        new Server()
                                .url("https://dev-mock-api.financial.com" + contextPath)
                                .description("Development mock server"),
                        new Server()
                                .url("https://staging-mock-api.financial.com" + contextPath)
                                .description("Staging mock server")
                ));
    }
}
