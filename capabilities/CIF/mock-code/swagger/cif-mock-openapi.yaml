openapi: 3.0.3
info:
  title: Mock CIF (Customer Information File) API
  description: |
    Mock API for CIF Creation Service that simulates the behavior of an external 
    CIF creation service for testing integration without relying on actual backend systems.
    
    ## Features
    - Configurable success/failure simulation (default 20% failure rate)
    - Retry logic simulation with header-based retry count
    - Realistic processing delays
    - Comprehensive request validation
    - Audit logging with trace ID support
    - Admin configuration endpoints
    
    ## Mock Behavior Rules
    - 80% of valid requests return SUCCESS with generated customer ID
    - 20% return FAILURE with "Service unavailable" message
    - If retryCount header is present and < 3 → return FAILURE with "Temporary failure, please retry"
    - All requests are logged with masked sensitive data
    - Processing delay simulation (configurable, default 1000ms)
    
    ## Configuration
    - Failure probability: Configurable (default 0.2)
    - Retry failure probability: Configurable (default 0.5)
    - Processing delay: Configurable (default 1000ms)
    - Customer ID prefix: Configurable (default "CIF")
    
  version: 1.0.0
  contact:
    name: Mock CIF API Team
    email: <EMAIL>
  license:
    name: Proprietary
    url: https://financial.com/license

servers:
  - url: http://localhost:8204/api/cif/internal
    description: Local development server
  - url: https://dev-mock-api.financial.com/api/cif/internal
    description: Development mock server
  - url: https://staging-mock-api.financial.com/api/cif/internal
    description: Staging mock server

tags:
  - name: Mock CIF Operations
    description: Mock customer ID creation operations
  - name: Admin Configuration
    description: Mock service configuration and management
  - name: Health Check
    description: Mock service health monitoring endpoints

paths:
  /cif/create:
    post:
      tags:
        - Mock CIF Operations
      summary: Mock CIF Creation
      description: |
        Simulates the external CIF creation service behavior with configurable 
        success/failure scenarios and realistic processing delays.
        
        **Mock Behavior:**
        - Validates request payload with same rules as real service
        - Simulates processing delay (configurable)
        - Returns SUCCESS (80%) or FAILURE (20%) based on configuration
        - Supports retry logic simulation via retryCount header
        - Generates realistic customer IDs for successful requests
        
        **Headers:**
        - `channelId`: Channel identifier (optional)
        - `X-Reference-Number`: Reference number for tracking (optional)
        - `retryCount`: Retry attempt count for retry logic simulation (optional)
        
      operationId: mockCreateCustomerId
      parameters:
        - name: channelId
          in: header
          required: false
          schema:
            type: string
            example: "WEB"
          description: Channel identifier
        - name: X-Reference-Number
          in: header
          required: false
          schema:
            type: string
            example: "REF123456789"
          description: Reference number for request tracking
        - name: retryCount
          in: header
          required: false
          schema:
            type: integer
            default: 0
            minimum: 0
            maximum: 10
            example: 1
          description: Retry attempt count (triggers retry failure simulation if < 3)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MockCifRequest'
            examples:
              valid_request:
                summary: Valid mock CIF creation request
                value:
                  body:
                    createCustomerIdRequest:
                      requestBody:
                        salutation: "Mr"
                        firstName: "Rajesh"
                        lastName: "Kumar"
                        dateOfBirth: "1985-03-15"
                        language: "ENG"
                        maritalStatus: "Y"
                        nationality: "IN"
                        isMinor: "N"
                        isCustomerNRE: "N"
                        defaultAddressType: "Mailing"
                        gender: "Male"
                        nativeLanguageCode: "ENG01"
                        occupation: "A102"
                        pan: "**********"
                        branchId: "887"
                        isStaff: "N"
                        taxDeductionTable: "TDS11"
                        maidenNameOfMother: "Sunita Sharma"
                        custHealthCode: "AB01"
                        addressDetails:
                          - addressLine1: "Plot No 123, Sector 15"
                            addressLine2: "Near City Mall"
                            addressCategory: "Mailing"
                            city: "Gurgaon"
                            state: "Haryana"
                            postalCode: "122001"
                            country: "IN"
                        contactDetails:
                          - contactType: "Phone"
                            contactSubType: "Cell"
                            countryCode: "+91"
                            phoneNumber: "**********"
                            isPreferredContact: "Yes"
                            email: "<EMAIL>"
                        entityDocDetails:
                          - countryOfIssue: "IN"
                            documentCode: "AADHAR_IDN_OF"
                            issueDate: "2010-05-20T00:00:00"
                            expiryDate: "2099-12-31T00:00:00"
                            typeCode: "Identification"
                            typeDescription: "Aadhar card for identification"
                            placeOfIssue: "HRD"
                            isMandatory: "Y"
                            receivedDate: "2024-01-15T10:30:00"
                        psychographicData:
                          - currencyCode: "INR"
      responses:
        '200':
          description: Mock CIF creation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockCifResponse'
              examples:
                success_response:
                  summary: Successful mock response
                  value:
                    channelId: "WEB"
                    customerId: "CIF100001"
                    description: "Customer ID created successfully"
                    entity: "Customer"
                    status: "SUCCESS"
        '400':
          description: Validation error or simulated failure
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockCifResponse'
              examples:
                validation_error:
                  summary: Validation error response
                  value:
                    channelId: "WEB"
                    customerId: null
                    description: "Validation failed: firstName: First name is mandatory"
                    entity: "Customer"
                    status: "FAILURE"
                simulated_failure:
                  summary: Simulated service failure
                  value:
                    channelId: "WEB"
                    customerId: null
                    description: "Service unavailable"
                    entity: "Customer"
                    status: "FAILURE"
                retry_failure:
                  summary: Retry simulation failure
                  value:
                    channelId: "WEB"
                    customerId: null
                    description: "Temporary service unavailability. Please retry."
                    entity: "Customer"
                    status: "FAILURE"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockCifResponse'
              examples:
                internal_error:
                  summary: Internal server error
                  value:
                    channelId: "WEB"
                    customerId: null
                    description: "Internal server error occurred"
                    entity: "Customer"
                    status: null
        '503':
          description: Service unavailable
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockCifResponse'
              examples:
                service_unavailable:
                  summary: Service unavailable
                  value:
                    channelId: "WEB"
                    customerId: null
                    description: "Service unavailable"
                    entity: "Customer"
                    status: null

  /cif/config:
    get:
      tags:
        - Admin Configuration
      summary: Get Current Configuration
      description: |
        Retrieves the current mock service configuration including failure 
        probabilities and processing delays.
        
        **Configuration Parameters:**
        - Failure Probability: Percentage of requests that should fail
        - Retry Failure Probability: Percentage of retry requests that should fail
        - Processing Delay: Simulated processing time in milliseconds
        
      operationId: getMockConfiguration
      responses:
        '200':
          description: Current configuration
          content:
            text/plain:
              schema:
                type: string
                example: |
                  Current Configuration:
                  Failure Probability: 0.20
                  Retry Failure Probability: 0.50
                  Processing Delay: 1000 ms

    post:
      tags:
        - Admin Configuration
      summary: Update Configuration
      description: |
        Updates the mock service configuration parameters.
        
        **Use Cases:**
        - Adjust failure rates for different test scenarios
        - Modify processing delays for performance testing
        - Configure retry behavior simulation
        
      operationId: updateMockConfiguration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfigurationRequest'
            examples:
              update_config:
                summary: Update configuration example
                value:
                  failureProbability: 0.3
                  retryFailureProbability: 0.6
                  processingDelayMs: 1500
      responses:
        '200':
          description: Configuration updated successfully
          content:
            text/plain:
              schema:
                type: string
                example: "Configuration updated successfully"
        '400':
          description: Invalid configuration parameters
          content:
            text/plain:
              schema:
                type: string
                example: "Invalid configuration: failure probability must be between 0.0 and 1.0"

  /cif/admin/reset:
    post:
      tags:
        - Admin Configuration
      summary: Reset Configuration
      description: |
        Resets the mock service configuration to default values.
        
        **Default Values:**
        - Failure Probability: 0.2 (20%)
        - Retry Failure Probability: 0.5 (50%)
        - Processing Delay: 1000ms
        
      operationId: resetMockConfiguration
      responses:
        '200':
          description: Configuration reset to defaults
          content:
            text/plain:
              schema:
                type: string
                example: "Configuration reset to defaults"

  /v1/cif/mock/health:
    get:
      tags:
        - Health Check
      summary: Mock Service Health Check
      description: |
        Comprehensive health status endpoint for the mock CIF service.
        
        **Health Information:**
        - Service status
        - Service name and version
        - Current timestamp
        - Mock-specific status indicators
        
      operationId: mockHealthCheck
      responses:
        '200':
          description: Mock service health status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockHealthStatus'
              examples:
                healthy_status:
                  summary: Healthy mock service
                  value:
                    status: "UP"
                    service: "CIF Mock"
                    timestamp: "2024-01-15T10:30:45.123"
                    version: "1.0.0"

components:
  schemas:
    MockCifRequest:
      type: object
      required:
        - body
      properties:
        body:
          $ref: '#/components/schemas/MockRequestBody'
      example:
        body:
          createCustomerIdRequest:
            requestBody:
              salutation: "Mr"
              firstName: "Rajesh"
              lastName: "Kumar"
              dateOfBirth: "1985-03-15"
              language: "ENG"
              maritalStatus: "Y"
              nationality: "IN"
              isMinor: "N"
              isCustomerNRE: "N"
              defaultAddressType: "Mailing"
              gender: "Male"
              nativeLanguageCode: "ENG01"
              occupation: "A102"
              pan: "**********"
              branchId: "887"
              isStaff: "N"
              taxDeductionTable: "TDS11"
              maidenNameOfMother: "Sunita Sharma"
              custHealthCode: "AB01"
              addressDetails:
                - addressLine1: "Plot No 123, Sector 15"
                  addressCategory: "Mailing"
                  city: "Gurgaon"
                  state: "Haryana"
                  postalCode: "122001"
                  country: "IN"
              contactDetails:
                - contactType: "Phone"
                  contactSubType: "Cell"
                  countryCode: "+91"
                  phoneNumber: "**********"
                  isPreferredContact: "Yes"
                  email: "<EMAIL>"
              entityDocDetails:
                - countryOfIssue: "IN"
                  documentCode: "AADHAR_IDN_OF"
                  issueDate: "2010-05-20T00:00:00"
                  expiryDate: "2099-12-31T00:00:00"
                  typeCode: "Identification"
                  placeOfIssue: "HRD"
                  isMandatory: "Y"
                  receivedDate: "2024-01-15T10:30:00"
              psychographicData:
                - currencyCode: "INR"

    MockRequestBody:
      type: object
      required:
        - createCustomerIdRequest
      properties:
        createCustomerIdRequest:
          $ref: '#/components/schemas/MockCustomerIdRequestBody'

    MockCustomerIdRequestBody:
      type: object
      required:
        - requestBody
      properties:
        requestBody:
          $ref: '#/components/schemas/MockCustomerDetailsRequest'

    MockCustomerDetailsRequest:
      type: object
      required:
        - salutation
        - firstName
        - lastName
        - dateOfBirth
        - language
        - maritalStatus
        - nationality
        - isMinor
        - isCustomerNRE
        - defaultAddressType
        - gender
        - nativeLanguageCode
        - occupation
        - pan
        - branchId
        - isStaff
        - taxDeductionTable
        - maidenNameOfMother
        - custHealthCode
        - addressDetails
        - contactDetails
        - entityDocDetails
        - psychographicData
      properties:
        salutation:
          type: string
          maxLength: 10
          default: "Mr"
          example: "Mr"
          description: Customer salutation
        firstName:
          type: string
          maxLength: 100
          example: "Rajesh"
          description: Customer first name (mandatory)
        lastName:
          type: string
          maxLength: 100
          example: "Kumar"
          description: Customer last name (mandatory)
        dateOfBirth:
          type: string
          format: date
          example: "1985-03-15"
          description: Customer date of birth (must be in the past)
        language:
          type: string
          maxLength: 10
          default: "ENG"
          example: "ENG"
          description: Preferred language code
        maritalStatus:
          type: string
          maxLength: 1
          default: "Y"
          example: "Y"
          description: Marital status (Y/N)
        nationality:
          type: string
          maxLength: 10
          default: "IN"
          example: "IN"
          description: Nationality code
        isMinor:
          type: string
          maxLength: 1
          default: "N"
          example: "N"
          description: Minor status (Y/N)
        isCustomerNRE:
          type: string
          maxLength: 1
          default: "N"
          example: "N"
          description: NRE customer status (Y/N)
        defaultAddressType:
          type: string
          maxLength: 20
          default: "Mailing"
          example: "Mailing"
          description: Default address type
        gender:
          type: string
          maxLength: 10
          default: "Male"
          example: "Male"
          description: Customer gender
        nativeLanguageCode:
          type: string
          maxLength: 10
          default: "ENG01"
          example: "ENG01"
          description: Native language code
        occupation:
          type: string
          maxLength: 10
          default: "A102"
          example: "A102"
          description: Occupation code
        pan:
          type: string
          pattern: '^[A-Z]{5}[0-9]{4}[A-Z]{1}$'
          example: "**********"
          description: PAN (Permanent Account Number) - validated format
        branchId:
          type: string
          maxLength: 10
          default: "887"
          example: "887"
          description: Branch ID
        isStaff:
          type: string
          maxLength: 1
          default: "N"
          example: "N"
          description: Staff member status (Y/N)
        taxDeductionTable:
          type: string
          maxLength: 10
          default: "TDS11"
          example: "TDS11"
          description: Tax deduction table code
        maidenNameOfMother:
          type: string
          maxLength: 100
          example: "Sunita Sharma"
          description: Mother's maiden name (mandatory)
        custHealthCode:
          type: string
          maxLength: 10
          default: "AB01"
          example: "AB01"
          description: Customer health code
        addressDetails:
          type: array
          minItems: 1
          maxItems: 2
          items:
            $ref: '#/components/schemas/MockAddressDetailsRequest'
          description: Customer address details (1-2 addresses required)
        contactDetails:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/MockContactDetailsRequest'
          description: Customer contact details (at least one required)
        entityDocDetails:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/MockEntityDocDetailsRequest'
          description: Customer document details (at least one required)
        psychographicData:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/MockPsychographicDataRequest'
          description: Customer psychographic data (at least one required)

    MockAddressDetailsRequest:
      type: object
      required:
        - addressLine1
        - addressCategory
        - city
        - state
        - postalCode
        - country
      properties:
        addressLine1:
          type: string
          maxLength: 255
          example: "Plot No 123, Sector 15"
          description: Primary address line (mandatory)
        addressLine2:
          type: string
          maxLength: 255
          example: "Near City Mall"
          description: Secondary address line (optional)
        addressCategory:
          type: string
          maxLength: 20
          example: "Mailing"
          description: Address category type (mandatory)
        city:
          type: string
          maxLength: 100
          example: "Gurgaon"
          description: City name (mandatory)
        state:
          type: string
          maxLength: 100
          example: "Haryana"
          description: State name (mandatory)
        postalCode:
          type: string
          maxLength: 20
          example: "122001"
          description: Postal/ZIP code (mandatory)
        country:
          type: string
          maxLength: 10
          default: "IN"
          example: "IN"
          description: Country code (mandatory)

    MockContactDetailsRequest:
      type: object
      required:
        - contactType
        - contactSubType
        - countryCode
        - phoneNumber
        - isPreferredContact
        - email
      properties:
        contactType:
          type: string
          maxLength: 20
          default: "Phone"
          example: "Phone"
          description: Contact type (mandatory)
        contactSubType:
          type: string
          maxLength: 20
          default: "Cell"
          example: "Cell"
          description: Contact sub-type (mandatory)
        countryCode:
          type: string
          maxLength: 10
          default: "+91"
          example: "+91"
          description: Country code for phone number (mandatory)
        phoneNumber:
          type: string
          pattern: '^\\d{10}$'
          example: "**********"
          description: 10-digit phone number (mandatory)
        isPreferredContact:
          type: string
          pattern: '^(Yes|No)$'
          default: "Yes"
          example: "Yes"
          description: Preferred contact flag (Yes/No, mandatory)
        email:
          type: string
          format: email
          maxLength: 255
          example: "<EMAIL>"
          description: Email address (mandatory)

    MockEntityDocDetailsRequest:
      type: object
      required:
        - countryOfIssue
        - documentCode
        - issueDate
        - expiryDate
        - typeCode
        - placeOfIssue
        - isMandatory
        - receivedDate
      properties:
        countryOfIssue:
          type: string
          maxLength: 10
          default: "IN"
          example: "IN"
          description: Country of document issue (mandatory)
        documentCode:
          type: string
          maxLength: 50
          example: "AADHAR_IDN_OF"
          description: Document code identifier (mandatory)
        issueDate:
          type: string
          format: date-time
          example: "2010-05-20T00:00:00"
          description: Document issue date (mandatory)
        expiryDate:
          type: string
          format: date-time
          example: "2099-12-31T00:00:00"
          description: Document expiry date (mandatory)
        typeCode:
          type: string
          maxLength: 50
          example: "Identification"
          description: Document type code (mandatory)
        typeDescription:
          type: string
          maxLength: 100
          example: "Aadhar card for identification"
          description: Document type description (optional)
        placeOfIssue:
          type: string
          maxLength: 50
          default: "HRD"
          example: "HRD"
          description: Place of document issue (mandatory)
        isMandatory:
          type: string
          pattern: '^[YN]$'
          default: "Y"
          example: "Y"
          description: Mandatory document flag (Y/N, mandatory)
        receivedDate:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00"
          description: Date when document was received (mandatory)

    MockPsychographicDataRequest:
      type: object
      required:
        - currencyCode
      properties:
        currencyCode:
          type: string
          maxLength: 10
          default: "INR"
          example: "INR"
          description: Preferred currency code (mandatory)

    MockCifResponse:
      type: object
      properties:
        channelId:
          type: string
          example: "WEB"
          description: Channel identifier from request
        customerId:
          type: string
          example: "CIF100001"
          description: Generated customer ID (null on failure)
        description:
          type: string
          example: "Customer ID created successfully"
          description: Descriptive message about the operation result
        entity:
          type: string
          example: "Customer"
          description: Entity type (always "Customer")
        status:
          type: string
          enum: ["SUCCESS", "FAILURE"]
          example: "SUCCESS"
          description: Status of the mock operation (null for service unavailable)

    ConfigurationRequest:
      type: object
      properties:
        failureProbability:
          type: number
          format: double
          minimum: 0.0
          maximum: 1.0
          example: 0.3
          description: Probability of request failure (0.0 to 1.0)
        retryFailureProbability:
          type: number
          format: double
          minimum: 0.0
          maximum: 1.0
          example: 0.6
          description: Probability of retry request failure (0.0 to 1.0)
        processingDelayMs:
          type: integer
          minimum: 0
          maximum: 10000
          example: 1500
          description: Processing delay in milliseconds (0 to 10000)

    MockHealthStatus:
      type: object
      properties:
        status:
          type: string
          enum: ["UP", "DOWN", "UNKNOWN"]
          example: "UP"
          description: Overall mock service status
        service:
          type: string
          example: "CIF Mock"
          description: Mock service name
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T10:30:45.123"
          description: Health check timestamp
        version:
          type: string
          example: "1.0.0"
          description: Mock service version

  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key for authentication (if required)

    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token authentication (if required)

# Global security (uncomment if authentication is required)
# security:
#   - ApiKeyAuth: []
#   - BearerAuth: []
