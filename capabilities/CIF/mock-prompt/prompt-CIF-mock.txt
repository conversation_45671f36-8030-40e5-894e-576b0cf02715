CIF Creation Mock API Service – Specification
=============================================

Overview
--------
Create a standalone Spring Boot application to simulate the behavior of an external CIF (Customer Information File) creation service for testing integration without relying on actual backend systems.

Purpose
-------
- Simulate end-to-end CIF creation behavior with request validation, random failure handling, and retriable failure logic
- Enable controlled testing scenarios for frontend/backend integration
- Log request/response payloads with traceability and validation

Technical Requirements
----------------------
- Spring Boot version: 3.2.3 (or compatible)
- Java version: 21
- Server Port: 8084
- OpenAPI/Swagger documentation included
- Configurable failure simulation probability (e.g., 20%)
- Header-based retryCount logic (simulate retriable failure for retryCount < 3)
- Comprehensive request validation using annotations
- Logging with trace ID and request/response capture

Implementation Details
----------------------
- DTOs
- Enums (if needed)
- Controller: Handles incoming POST request
- Service: Simulates CIF creation logic
- Utility Classes:
  - Validation
  - Logging (trace ID, request/response)
  - Failure simulation
- Configuration:
  - application.yml (port, failure rate config, etc.)
  - Swagger config
- README with setup and usage instructions

API Endpoint
------------
1. CIF Creation Endpoint
   - Path: /api/cif/internal/cif/create
   - Method: POST
   - Headers:
     - Content-Type: application/json
     - X-Transaction-Id: string (required)
     - retryCount: integer (optional)
   - Request Body:
     {
       "channelId": "WEB",
       "entity": "ABC_BANK",
       "applicant": {
         "name": "John Doe",
         "dob": "1990-01-01",
         "pan": "**********",
         "aadhaar": "************",
         "phone": "**********"
       }
     }

   - Validations:
     - name: @NotBlank
     - dob: @Pattern or LocalDate check
     - pan: @Pattern (e.g., [A-Z]{5}[0-9]{4}[A-Z]{1})
     - aadhaar: @Pattern (12-digit numeric)
     - phone: @Pattern (10-digit numeric)
     - channelId and entity: @NotBlank

   - Response Body:
     {
       "channelId": "WEB",
       "customerId": "CIF12345678" or null,
       "description": "Customer created successfully" / "Temporary failure, please retry" / "Service unavailable",
       "entity": "ABC_BANK",
       "status": "SUCCESS" / "FAILURE"
     }

Mock Behavior Rules
-------------------
- 80% of valid requests return SUCCESS with non-null customerId
- 20% return FAILURE with description: "Service unavailable"
- If retryCount header is present and < 3 → return FAILURE with "Temporary failure, please retry"
- Always log masked request/response with X-Transaction-Id in MDC

Security and Logging
--------------------
- Mask sensitive data (Aadhaar, PAN, phone) in logs
- Include X-Transaction-Id in every log entry for traceability
- Log:
  - Incoming request (masked)
  - Outgoing response
  - Error/exception with reason

Admin & Configuration Interface (Optional)
------------------------------------------
- GET  /api/cif/internal/cif/admin/requests → View recent requests
- POST /api/cif/internal/cif/admin/config   → Change failure simulation config (e.g., set 30% failure rate)
- POST /api/cif/internal/cif/admin/reset    → Clear request history/config to default

Service Layer Components
------------------------
- CifMockService: Core logic for handling request, retry logic, and random failures
- FailureSimulator: Utility to simulate random service failures
- LoggingService: Logs request/response with masking and trace ID
- ValidationUtils: Optional custom validators if needed
- CifConfigProperties: Configurable properties (e.g., failure probability)

Testing Strategy
----------------
- Use Swagger UI or Postman:
  - Send valid requests with various retryCount values
  - Observe SUCCESS/FAILURE based on probability
  - Confirm request/response logging with masked PII
  - Validate random failure and retriable behavior
  - Trace logs using X-Transaction-Id

README Deliverables
-------------------
- Setup instructions (build/run)
- Sample requests for CIF creation
- Retry and failure scenario explanation
- Swagger URL and test data examples
- Admin endpoint instructions (if included)

Extensibility Suggestions
-------------------------
- Add multiple applicant support
- Integrate with in-memory database for request history
- Support different entity configurations
- Expand to simulate full customer onboarding (KYC, address verification)
