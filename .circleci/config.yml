version: 2.1

# Required Environment Variables:
# GOOGLE_PROJECT_ID - Your GCP project ID
# GCP_PROJECT_KEY - Base64 encoded service account key (for Docker registry access)
# Define parameters for dynamic functionality detection

parameters:
  repo_name:
    type: string
    default: "lending-capability-repo"
  google_project_id:
    type: string
    default: "${GOOGLE_PROJECT_ID}"
  skip_build:
    type: boolean
    default: false
    description: "Skip build steps and deploy directly using latest tag images"

# Define executors
executors:
  gcp-docker:
    docker:
      - image: cimg/gcp:2023.05
    environment:
      DOCKER_BUILDKIT: 1

  gcp-helm:
    docker:
      - image: cimg/gcp:2023.05
    environment:
      HELM_VERSION: v3.12.0

# Define commands for reusability
commands:
  setup_gcp_auth:
    description: "Setup GCP authentication and Docker registry"
    steps:
      - run:
          name: Setup GCP Authentication
          command: |
            echo 'export PATH=~$PATH:~/.local/bin' >> $BASH_ENV
            echo "${GCP_PROJECT_KEY}" > $HOME/gcloud-service-key.json
            gcloud auth activate-service-account --key-file=$HOME/gcloud-service-key.json
            gcloud auth configure-docker us-central1-docker.pkg.dev

  setup_gke_auth:
    description: "Setup GKE cluster authentication"
    parameters:
      cluster_name:
        type: string
        default: "twistapp"
      cluster_zone:
        type: string
        default: "us-central1-c"
    steps:
      - run:
          name: Setup GKE Authentication
          command: |
            gcloud container clusters get-credentials << parameters.cluster_name >> \
              --zone << parameters.cluster_zone >> \
              --project $GOOGLE_PROJECT_ID

  install_helm:
    description: "Install Helm"
    steps:
      - run:
          name: Install Helm
          command: |
            curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3
            chmod 700 get_helm.sh
            ./get_helm.sh --version ${HELM_VERSION}
            helm version

  detect_changes:
    description: "Smart change detection for selective Docker image building"
    steps:
      - run:
          name: Detect Changed Capabilities
          command: |
            echo "🔍 Smart Change Detection for Docker Images"
            echo "============================================"

            # Function to normalize names
            normalize_name() {
              echo "$1" | tr '[:upper:]' '[:lower:]' | sed 's/ /-/g' | sed 's/[^a-z0-9-]//g'
            }

            # Initialize files
            > all_capabilities.txt
            > changed_files.txt
            > capabilities_to_build.txt

            # Get changed files since last commit
            if [ -n "$CIRCLE_COMPARE_URL" ]; then
              # Extract commit range from compare URL
              COMMIT_RANGE=$(echo $CIRCLE_COMPARE_URL | sed 's/.*compare\///')
              echo "📋 Checking changes in commit range: $COMMIT_RANGE"
              git diff --name-only $COMMIT_RANGE > changed_files.txt
            else
              # Fallback for first build or when compare URL is not available
              echo "📋 Fallback: Checking changes since last commit"
              git diff --name-only HEAD~1 HEAD > changed_files.txt
            fi

            # Filter out files that shouldn't trigger builds
            echo ""
            echo "📁 All changed files:"
            cat changed_files.txt

            # Only keep capability-related changes and critical infrastructure changes
            echo ""
            echo "🚫 Filtering for capability and infrastructure changes only..."
            # Keep only:
            # - capabilities/ directory changes (code, Dockerfile, config files)
            # - .circleci/ changes (pipeline changes)
            grep -E "^(capabilities/|\.circleci/)" changed_files.txt > filtered_changes.txt || touch filtered_changes.txt

            echo "📁 Filtered changed files (capability and CI/CD changes only):"
            if [[ -s filtered_changes.txt ]]; then
              cat filtered_changes.txt
              echo ""
              echo "📊 Change summary:"
              echo "   - Capability changes: $(grep -c "^capabilities/" filtered_changes.txt || echo "0")"
              echo "   - CI/CD changes: $(grep -c "^\.circleci/" filtered_changes.txt || echo "0")"
            else
              echo "   (no capability or CI/CD changes found)"
            fi
            echo ""

            # Discover all capabilities first
            echo "🔍 Discovering all capabilities..."
            for capability_dir in capabilities/*/; do
              if [[ -d "$capability_dir" ]]; then
                capability_name=$(basename "$capability_dir")
                normalized_name=$(normalize_name "$capability_name")

                # Check for original-code
                if [[ -d "$capability_dir/original-code" && -f "$capability_dir/original-code/Dockerfile" ]]; then
                  echo "${normalized_name}_original|$capability_dir/original-code" >> all_capabilities.txt
                fi

                # Check for mock-code
                if [[ -d "$capability_dir/mock-code" && -f "$capability_dir/mock-code/Dockerfile" ]]; then
                  echo "${normalized_name}_mock|$capability_dir/mock-code" >> all_capabilities.txt
                fi
              fi
            done

            echo "📦 Total capabilities found: $(wc -l < all_capabilities.txt)"

            # Check for global changes that affect all images (using filtered changes)
            echo "🌍 Checking for global changes..."
            global_rebuild=false

            if grep -q -E "^\.circleci/" filtered_changes.txt; then
              echo "🚨 CI/CD pipeline changes detected - will rebuild ALL images"
              echo "   Affected files:"
              grep -E "^\.circleci/" filtered_changes.txt | sed 's/^/   - /'
              global_rebuild=true
            fi

            if [ "$global_rebuild" = true ]; then
              cp all_capabilities.txt capabilities_to_build.txt
            else
              echo "✅ No global changes detected"

              # Check each capability for specific changes
              echo ""
              echo "🔍 Checking individual capability changes..."

              while IFS='|' read -r image_name capability_path; do
                if [[ -n "$image_name" && -n "$capability_path" ]]; then
                  # Extract capability name from path (e.g., capabilities/EKYC/original-code -> EKYC)
                  capability_name=$(echo "$capability_path" | cut -d'/' -f2)

                  # Check if any files in this specific capability changed
                  capability_pattern="^capabilities/${capability_name}/"

                  if grep -q "$capability_pattern" filtered_changes.txt; then
                    echo "🔄 Changes detected in capability: $capability_name ($image_name)"
                    echo "   Changed files:"
                    grep "$capability_pattern" filtered_changes.txt | sed 's/^/   - /'
                    echo "$image_name|$capability_path" >> capabilities_to_build.txt
                  fi
                fi
              done < all_capabilities.txt
            fi

            # Show final results
            echo ""
            echo "📋 FINAL BUILD PLAN:"
            echo "==================="
            if [[ -s capabilities_to_build.txt ]]; then
              echo "🏗️  Images to build: $(wc -l < capabilities_to_build.txt)"
              while IFS='|' read -r image_name capability_path; do
                echo "   ✅ $image_name"
              done < capabilities_to_build.txt
            else
              echo "✨ No images need rebuilding - all up to date!"
            fi

            # Copy for compatibility with existing jobs
            cp capabilities_to_build.txt discovered_functionalities.txt
      - persist_to_workspace:
          root: .
          paths:
            - discovered_functionalities.txt
            - capabilities_to_build.txt
            - changed_files.txt
            - filtered_changes.txt
            - all_capabilities.txt



jobs:
  discover:
    executor: gcp-docker
    steps:
      - checkout
      - detect_changes

  build_images:
    executor: gcp-docker
    steps:
      - checkout
      - setup_remote_docker:
          docker_layer_caching: true
      - attach_workspace:
          at: .
      - setup_gcp_auth
      - run:
          name: Build Changed Images Only
          command: |
            if [[ ! -f discovered_functionalities.txt ]]; then
              echo "No discovered functionalities file found"
              exit 1
            fi

            if [[ ! -s discovered_functionalities.txt ]]; then
              echo "✨ No images need rebuilding - all up to date!"
              echo "Skipping build step."
              exit 0
            fi

            echo "🏗️  Building images for changed functionalities only..."
            echo "Images to build: $(wc -l < discovered_functionalities.txt)"
            while IFS='|' read -r image_name dockerfile_path; do
              if [[ -n "$image_name" && -n "$dockerfile_path" ]]; then
                echo "Processing: $image_name from $dockerfile_path"

                # Set environment variables
                export TAG=${CIRCLE_SHA1}
                export IMAGE_NAME="$image_name"
                export REPO_NAME="<< pipeline.parameters.repo_name >>"

                echo "Building image: $IMAGE_NAME with tag: $TAG"

                # Build Docker image
                docker build \
                  -t us-central1-docker.pkg.dev/$GOOGLE_PROJECT_ID/$REPO_NAME/$IMAGE_NAME:$TAG \
                  -t us-central1-docker.pkg.dev/$GOOGLE_PROJECT_ID/$REPO_NAME/$IMAGE_NAME:latest \
                  "$dockerfile_path"

                # Push both tags
                docker push us-central1-docker.pkg.dev/$GOOGLE_PROJECT_ID/$REPO_NAME/$IMAGE_NAME:$TAG
                docker push us-central1-docker.pkg.dev/$GOOGLE_PROJECT_ID/$REPO_NAME/$IMAGE_NAME:latest

                echo "Successfully built and pushed: $IMAGE_NAME"
              fi
            done < discovered_functionalities.txt

            echo "All images built and pushed successfully!"

  # Test job to validate the built images
  test_images:
    executor: gcp-docker
    steps:
      - checkout
      - setup_remote_docker:
          docker_layer_caching: true
      - attach_workspace:
          at: .
      - setup_gcp_auth
      - run:
          name: Test Built Images
          command: |
            if [[ ! -f discovered_functionalities.txt ]]; then
              echo "No discovered functionalities file found"
              exit 0
            fi

            if [[ ! -s discovered_functionalities.txt ]]; then
              echo "✨ No images were built - skipping tests"
              exit 0
            fi

            echo "🧪 Testing built images..."
            echo "Images to test: $(wc -l < discovered_functionalities.txt)"
            while IFS='|' read -r image_name dockerfile_path; do
              if [[ -n "$image_name" ]]; then
                export TAG=${CIRCLE_SHA1}
                export IMAGE_NAME="$image_name"
                export REPO_NAME="<< pipeline.parameters.repo_name >>"

                echo "Testing image: us-central1-docker.pkg.dev/$GOOGLE_PROJECT_ID/$REPO_NAME/$IMAGE_NAME:$TAG"

                # Pull and inspect the image
                docker pull us-central1-docker.pkg.dev/$GOOGLE_PROJECT_ID/$REPO_NAME/$IMAGE_NAME:$TAG
                docker inspect us-central1-docker.pkg.dev/$GOOGLE_PROJECT_ID/$REPO_NAME/$IMAGE_NAME:$TAG

                echo "Image $IMAGE_NAME tested successfully"
              fi
            done < discovered_functionalities.txt

  # Deploy to GKE using Helm
  deploy_to_gke:
    executor: gcp-helm
    parameters:
      cluster_name:
        type: string
        default: "twistapp"
      cluster_zone:
        type: string
        default: "us-central1-c"
    steps:
      - checkout
      - attach_workspace:
          at: .
      - setup_gcp_auth
      - setup_gke_auth:
          cluster_name: << parameters.cluster_name >>
          cluster_zone: << parameters.cluster_zone >>
      - install_helm
      - run:
          name: Generate Dynamic Helm Values
          command: |
            echo "🔍 Generating dynamic Helm values..."
            echo "Current directory: $(pwd)"
            echo "Directory contents:"
            ls -la

            # Make script executable and run it from project root
            chmod +x scripts/discover-capabilities.sh
            ./scripts/discover-capabilities.sh

            echo "📋 Using generated values file:"
            ls -la helm/lending-capabilities/values-generated.yaml
      - run:
          name: Deploy with Helm
          command: |
            # Set dynamic values
            export PROJECT_ID=$GOOGLE_PROJECT_ID
            
            # Determine image tag based on skip_build parameter
            if [[ "<< pipeline.parameters.skip_build >>" == "true" ]]; then
              export IMAGE_TAG="latest"
              echo "🚀 Deploying using latest tag images (skip_build=true)"
            else
              export IMAGE_TAG=${CIRCLE_SHA1}
              echo "🚀 Deploying using commit-specific images: $IMAGE_TAG"
              
              # Load capabilities to build if not skipping build
              if [[ -f capabilities_to_build.txt ]]; then
                echo "📋 Building selective deployment for changed capabilities only..."
                SELECTIVE_ARGS=""

                while IFS='|' read -r image_name dockerfile_path; do
                  if [[ -n "$image_name" && -n "$dockerfile_path" ]]; then
                    echo "  → Will update image: $image_name to tag: $IMAGE_TAG"

                    # Extract capability name from image name (remove _original or _mock suffix)
                    capability_name=$(echo "$image_name" | sed 's/_original$//' | sed 's/_mock$//')
                    service_type=""

                    if [[ "$image_name" == *"_original" ]]; then
                      service_type="original"
                    elif [[ "$image_name" == *"_mock" ]]; then
                      service_type="mock"
                    fi

                    if [[ -n "$service_type" ]]; then
                      # Add selective image tag override for this specific capability and service type
                      SELECTIVE_ARGS="$SELECTIVE_ARGS --set capabilities.${capability_name}.${service_type}.imageTag=$IMAGE_TAG"
                    fi
                  fi
                done < capabilities_to_build.txt
              fi
            fi

            # Check if Helm release already exists
            echo "🔍 Checking if Helm release 'lending-capabilities' exists..."
            if helm list -n lending-capabilities | grep -q "lending-capabilities"; then
              echo "📦 Helm release 'lending-capabilities' found - performing upgrade"
              HELM_COMMAND="upgrade"
              HELM_ARGS=""
            else
              echo "🆕 Helm release 'lending-capabilities' not found - performing fresh install"
              HELM_COMMAND="install"
              HELM_ARGS="--create-namespace"
            fi

            # Deploy using Helm with generated values
            echo "🚀 Running: helm $HELM_COMMAND lending-capabilities..."
            
            # If skip_build is true, deploy all with latest tag
            if [[ "<< pipeline.parameters.skip_build >>" == "true" ]]; then
              helm $HELM_COMMAND lending-capabilities \
                ./helm/lending-capabilities \
                --values ./helm/lending-capabilities/values-generated.yaml \
                --set global.projectId=$PROJECT_ID \
                --set global.imageTag=$IMAGE_TAG \
                --namespace lending-capabilities \
                $HELM_ARGS \
                --wait \
                --timeout 10m \
                --debug
            else
              # Use selective deployment with specific image tags
              helm $HELM_COMMAND lending-capabilities \
                ./helm/lending-capabilities \
                --values ./helm/lending-capabilities/values-generated.yaml \
                --set global.projectId=$PROJECT_ID \
                $SELECTIVE_ARGS \
                --namespace lending-capabilities \
                $HELM_ARGS \
                --wait \
                --timeout 10m \
                --debug
            fi

            # Show deployment status
            echo "📊 Deployment Status:"
            kubectl get deployments -n lending-capabilities
            echo ""
            echo "🌐 Services:"
            kubectl get services -n lending-capabilities
            echo ""
            echo "🐳 Pods:"
            kubectl get pods -n lending-capabilities

workflows:
  version: 2
  build_test_and_deploy:
    when:
      not: << pipeline.parameters.skip_build >>
    jobs:
      - discover:
          filters:
            branches:
              only: main
      - build_images:
          requires:
            - discover
          filters:
            branches:
              only: main
      - test_images:
          requires:
            - build_images
          filters:
            branches:
              only: main
      - deploy_to_gke:
          requires:
            - test_images
          filters:
            branches:
              only: main
  
  deploy_latest:
    when:
      equal: [true, << pipeline.parameters.skip_build >>]
    jobs:
      - discover:
          filters:
            branches:
              only: main
      - deploy_to_gke:
          name: deploy_latest_to_gke
          requires:
            - discover
          filters:
            branches:
              only: main
